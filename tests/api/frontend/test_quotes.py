import pytest
from decimal import Decimal

from tests.common.mock_mysql import patch_mysql
from tests.common.mock_redis import patch_redis


@pytest.mark.usefixtures('patch_redis')
@pytest.mark.usefixtures('patch_mysql')
def test_assets_prices_resource_get_normal(tcontext):
    with tcontext:
        client = tcontext.app.test_client()
        assets = "BTC,ETH"
        resp = client.get("/res/quotes/real-time/assets", query_string={"assets": assets})
        assert resp.status_code == 200
        data = resp.get_json()["data"]
        assert "BTC" in data
        assert "ETH" in data
        assert data["BTC"]["price_usd"]
        assert data["BTC"]["change_rate"]
        assert data["ETH"]["price_usd"]
        assert data["ETH"]["change_rate"]


@pytest.mark.usefixtures('patch_redis')
@pytest.mark.usefixtures('patch_mysql')
def test_assets_prices_resource_get_with_unknown(tcontext):
    with tcontext:
        client = tcontext.app.test_client()
        assets = "BTC,ETH, UNKNOWN"
        resp = client.get("/res/quotes/real-time/assets", query_string={"assets": assets})
        assert resp.status_code == 200
        result = resp.get_json()
        assert result['code'] == 2
        data = result["data"]
        assert data["BTC"]["price_usd"]
        assert data["BTC"]["change_rate"]
        assert data["ETH"]["price_usd"]
        assert data["ETH"]["change_rate"]
        assert "UNKNOWN" not in data


@pytest.mark.usefixtures('patch_redis')
@pytest.mark.usefixtures('patch_mysql')
def test_assets_prices_resource_post_normal(tcontext):
    with tcontext:
        client = tcontext.app.test_client()
        assets = ['BTC', 'ETH']
        resp = client.post("/res/quotes/real-time/assets", json={"assets": assets})
        assert resp.status_code == 200
        data = resp.get_json()["data"]
        assert "BTC" in data
        assert "ETH" in data
        assert data["BTC"]["price_usd"]
        assert data["BTC"]["change_rate"]
        assert data["ETH"]["price_usd"]
        assert data["ETH"]["change_rate"]


@pytest.mark.usefixtures('patch_redis')
@pytest.mark.usefixtures('patch_mysql')
def test_assets_prices_resource_post_with_unknown(tcontext):
    with tcontext:
        client = tcontext.app.test_client()
        assets = ['BTC', 'ETH', 'UNKNOWN']
        resp = client.post("/res/quotes/real-time/assets", json={"assets": assets})
        assert resp.status_code == 200
        data = resp.get_json()["data"]
        assert "BTC" in data
        assert "ETH" in data
        assert data["BTC"]["price_usd"]
        assert data["BTC"]["change_rate"]
        assert data["ETH"]["price_usd"]
        assert data["ETH"]["change_rate"]
        assert "UNKNOWN" not in data


@pytest.mark.usefixtures('patch_redis')
@pytest.mark.usefixtures('patch_mysql')
def test_assets_prices_online(tcontext):
    with tcontext:
        client = tcontext.app.test_client()
        assets = ["PREMOMO", "CET", "PREMM7"]
        resp = client.post("/res/quotes/real-time/assets", json={"assets": assets})
        assert resp.status_code == 200
        data = resp.get_json()["data"]
        assert "CET" in data
        assert data["CET"]["price_usd"]
        assert data["CET"]["change_rate"]
        assert data["PREMM7"]["price_usd"]
        assert data["PREMM7"]["change_rate"]
        assert "PREMOMO" not in data

