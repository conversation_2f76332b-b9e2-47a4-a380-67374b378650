# -*- coding: utf-8 -*-

import pytest
from unittest.mock import patch

from tests.common.mock_mysql import patch_mysql
from tests.common.mock_redis import patch_redis
from app.models import AdminPermission, db
from app.exceptions import InvalidArgument


class TestAdminPermissionResource:
    """Test cases for AdminPermissionResource.post method"""
    def test_post_rules_comment_online(self, tcontext):
        """Test with empty rules array"""
        with tcontext:
            client = tcontext.app.test_client()
            
            payload = {
                "app": "COMMENT",
                "rules": [{'endpoint': 'admin._admin_comment_resource', 'method': 'GET', 'name': '获取评论列表(管理)', 'rule': '/admin/comment/list'}, {'endpoint': 'admin._admin_comment_resource', 'method': 'PUT', 'name': '评论管理操作', 'rule': '/admin/comment/list'}, {'endpoint': 'admin._admin_comment_review_resource', 'method': 'GET', 'name': '评论-待审核内容', 'rule': '/admin/comment/review'}, {'endpoint': 'admin._admin_comment_review_resource', 'method': 'PUT', 'name': '评论-评论审核', 'rule': '/admin/comment/review'}, {'endpoint': 'admin._admin_comment_detail_resource', 'method': 'GET', 'name': '评论详情', 'rule': '/admin/comment/detail'}, {'endpoint': 'admin._admin_comment_reports_resource', 'method': 'GET', 'name': '评论举报列表', 'rule': '/admin/comment/reports'}, {'endpoint': 'admin._admin_comment_warning_resource', 'method': 'POST', 'name': '评论-添加警告', 'rule': '/admin/comment/warning'}, {'endpoint': 'admin._admin_comment_warning_translation_resource', 'method': 'GET', 'name': '评论-警告翻译', 'rule': '/admin/comment/warning/translation'}, {'endpoint': 'admin._admin_comment_moderation_resource', 'method': 'GET', 'name': '违规内容', 'rule': '/admin/comment/moderation'}, {'endpoint': 'admin._admin_comment_moderation_resource', 'method': 'PUT', 'name': '违规内容审核/批量审核', 'rule': '/admin/comment/moderation'}, {'endpoint': 'admin._admin_user_resource', 'method': 'GET', 'name': '用户信息', 'rule': '/admin/comment/user'}, {'endpoint': 'admin._admin_user_resource', 'method': 'PUT', 'name': '用户禁言', 'rule': '/admin/comment/user'}, {'endpoint': 'admin._admin_stats_resource', 'method': 'GET', 'name': '获取评论统计数据', 'rule': '/admin/comment/stats'}, {'endpoint': 'admin._user_search_resource', 'method': 'GET', 'name': '用户模糊搜索', 'rule': '/admin/comment/user/search'}, {'endpoint': 'admin._admin_comment_moderation_detail_resource', 'method': 'POST', 'name': '获取评论的详细AI审核结果', 'rule': '/admin/comment/moderation/detail'}, {'endpoint': 'admin._comment_translate_resource', 'method': 'POST', 'name': '翻译评论', 'rule': '/admin/comment/translate'}, {'endpoint': 'admin._comment_tips_resource', 'method': 'GET', 'name': '打赏记录列表', 'rule': '/admin/comment/tips'}, {'endpoint': 'admin._comment_tips_report_resource', 'method': 'GET', 'name': '打赏报表', 'rule': '/admin/comment/tip-report'}, {'endpoint': 'admin.Cache_asset_map_resource', 'method': 'GET', 'name': '评论系统-缓存-币种 id-code 对', 'rule': '/admin/comment/cache/asset-map'}, {'endpoint': 'admin.Cache_comment_list_cache_resource', 'method': 'GET', 'name': '评论系统-缓存-评论列表', 'rule': '/admin/comment/cache/comment-list'}, {'endpoint': 'admin.Celery_celery_queues_resource', 'method': 'GET', 'name': '评论系统-Redis队列', 'rule': '/admin/comment/celery/redis-queues'}, {'endpoint': 'admin.Celery_celery_queue_resource', 'method': 'GET', 'name': '评论系统-Redis队列详情', 'rule': '/admin/comment/celery/redis-queues/<queue>'}, {'endpoint': 'admin.Celery_celery_queue_resource', 'method': 'DELETE', 'name': '评论系统-删除Redis队列', 'rule': '/admin/comment/celery/redis-queues/<queue>'}, {'endpoint': 'admin.Enums_sort_types_resource', 'method': 'GET', 'name': '评论系统-排序类型', 'rule': '/admin/comment/enums/sort-types'}]
            }
            
            resp = client.post("/internal/exchange/system/admin/permissions", json=payload)

            assert resp.status_code == 200
