import pytest
from decimal import Decimal
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock
from flask import g
from collections import defaultdict

from app.models.user import User
from app.models.equity_center import UserEquity, UserInvestIncreaseEquity, EquityType
from app.models.investment import InvestmentTransferHistory
from app.models.base import db
from app.utils import now
from tests.common.mock_mysql import patch_mysql
from tests.common.mock_redis import patch_redis
from tests.common.t_common import default_lang


USER_ID = 20044


@pytest.fixture(scope='module')
def module_setup(tcontext):
    try:
        with tcontext:
            g.lang = default_lang
            g.auth_user = g.user = User.query.get(USER_ID)
        yield tcontext
    finally:
        with tcontext:
            pass


# noinspection PyClassHasNoInit
@pytest.mark.usefixtures('module_setup')
class TestNoticeFunctions:

    def test_send_notice(self, tcontext):
        with tcontext:
            from app.business.equity_center.notice import InvestIncreaseMessageSender

            InvestIncreaseMessageSender.send_activation_half_expired_notices()

    def test_send_activation_half_expired_notices(self, tcontext):
        """测试发送激活期限过半通知"""
        with tcontext:
            from app.business.equity_center.notice import InvestIncreaseMessageSender
            
            # 创建测试数据
            now_ = now()
            
            # 创建用户权益记录 - 确保时间设置正确，使得当前时间超过激活期限的一半
            # 激活期限 = finished_at - created_at = 2天
            # 一半时间 = 1天
            # 当前时间应该 >= created_at + 1天 且 < finished_at
            user_equity = UserEquity(
                user_id=USER_ID,
                equity_id=1,
                type=EquityType.INVEST_INCREASE,
                business_id=1,
                business_type=UserEquity.BusinessType.MISSION,
                status=UserEquity.Status.PENDING,  # 未激活状态
                finished_at=now_ + timedelta(hours=12),  # 12小时后过期
                created_at=now_ - timedelta(hours=6)  # 6小时前创建
            )
            db.session.add(user_equity)
            db.session.commit()
            
            # 创建理财加息权益记录
            invest_equity = UserInvestIncreaseEquity(
                user_id=USER_ID,
                user_equity_id=user_equity.id,
                principal_asset="USDT",
                principal_amount_limit=Decimal("1000"),
                finished_at=now_ + timedelta(hours=12),  # 12小时后过期
                activation_days=1,  # 1天激活期限（但实际使用finished_at - created_at计算）
                increase_rate=Decimal("0.1")
            )
            db.session.add(invest_equity)
            db.session.commit()
            
            InvestIncreaseMessageSender.send_activation_half_expired_notices()
                

    def test_send_usage_half_expired_notices(self, tcontext):
        """测试发送使用期限过半通知"""
        with tcontext:
            from app.business.equity_center.notice import InvestIncreaseMessageSender
            
            # Mock InvestmentDataProc.get_dt_asset_user_snap_map 函数
            mock_asset_user_balance_map = {
                "USDT": {USER_ID: Decimal("0")},  # 用户没有USDT余额
                "BTC": {USER_ID: Decimal("0")}    # 用户没有BTC余额
            }
            
            with patch('app.business.equity_center.notice.InvestmentDataProc.get_dt_asset_user_snap_map') as mock_get_snap_map:
                mock_get_snap_map.return_value = mock_asset_user_balance_map
                
                # 创建测试数据
                now_ = now()
                
                # 创建用户权益记录 - 使用中状态
                user_equity = UserEquity(
                    user_id=USER_ID,
                    equity_id=1,
                    type=EquityType.INVEST_INCREASE,
                    business_id=1,
                    business_type=UserEquity.BusinessType.MISSION,
                    status=UserEquity.Status.USING,  # 使用中状态
                    finished_at=now_ + timedelta(days=10),  # 10天后过期
                    created_at=now_ - timedelta(days=15)  # 15天前创建
                )
                db.session.add(user_equity)
                db.session.commit()
                
                # 创建理财加息权益记录
                invest_equity = UserInvestIncreaseEquity(
                    user_id=USER_ID,
                    user_equity_id=user_equity.id,
                    principal_asset="USDT",
                    principal_amount_limit=Decimal("1000"),
                    finished_at=now_ + timedelta(days=10),  # 10天后过期
                    active_at=now_ - timedelta(days=5),  # 5天前激活
                    usable_days=10,  # 10天使用期限
                    investment_asset=None  # 没有选择加息币种
                )
                db.session.add(invest_equity)
                db.session.commit()
                
                # 创建理财转出记录 - 激活后转出
                transfer_history = InvestmentTransferHistory(
                    user_id=USER_ID,
                    asset=EquityType.INVEST_INCREASE.value,
                    amount=Decimal("-100"),
                    opt_type=InvestmentTransferHistory.OptType.OUT,
                    status=InvestmentTransferHistory.StatusType.SUCCESS,
                    created_at=now_ - timedelta(days=3),  # 3天前转出
                    investment_account_id=1
                )
                db.session.add(transfer_history)
                db.session.commit()
                
                # 测试函数调用
                InvestIncreaseMessageSender.send_usage_half_expired_notices()


    def test_send_delivery_notices(self, tcontext):
        """测试发送使用期限过半通知"""
        with tcontext:
            from app.business.equity_center.notice import InvestIncreaseMessageSender

            now_ = now()

            # 创建用户权益记录 - 使用中状态
            user_equity = UserEquity(
                user_id=USER_ID,
                equity_id=1,
                type=EquityType.INVEST_INCREASE,
                business_id=1,
                business_type=UserEquity.BusinessType.MISSION,
                status=UserEquity.Status.USING,  # 使用中状态
                finished_at=now_ + timedelta(days=10),  # 10天后过期
                created_at=now_ - timedelta(days=15)  # 15天前创建
            )
            db.session.add(user_equity)
            db.session.commit()

            # 创建理财加息权益记录
            invest_equity = UserInvestIncreaseEquity(
                user_id=USER_ID,
                user_equity_id=user_equity.id,
                principal_asset="USDT",
                principal_amount_limit=Decimal("1000"),
                finished_at=now_ + timedelta(days=10),  # 10天后过期
                active_at=now_ - timedelta(days=5),  # 5天前激活
                usable_days=10,  # 10天使用期限
                investment_asset=None  # 没有选择加息币种
            )
            db.session.add(invest_equity)
            db.session.commit()

            # 测试函数调用
            InvestIncreaseMessageSender.send_delivery_notices({user_equity.id})
