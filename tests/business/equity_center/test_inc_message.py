from flask_babel import force_locale
import pytest
from decimal import Decimal
from datetime import datetime, timedelta
from unittest.mock import Mock, patch, MagicMock
from flask import g
from pytz import UTC


from app.common.constants import Language
from app.models.user import User
from app.models.mission_center import UserMission, SceneType, MissionCondition
from app.models.equity_center import EquityType, UserInvestIncreaseEquity
from tests.common.mock_mysql import patch_mysql
from tests.common.mock_redis import patch_redis
from tests.common.t_common import default_lang


USER_ID = 20044


@pytest.fixture(scope='module')
def module_setup(tcontext):
    try:
        with tcontext:
            g.lang = default_lang = Language.ZH_HANS_CN.value
            # 模拟用户对象，避免数据库查询
            mock_user = Mock(spec=User)
            mock_user.id = USER_ID
            g.auth_user = g.user = mock_user
        yield tcontext
    finally:
        with tcontext:
            pass


# noinspection PyClassHasNoInit
@pytest.mark.usefixtures('module_setup')
@pytest.mark.usefixtures('patch_redis')
@pytest.mark.usefixtures('patch_mysql')
class TestMessageSenders:

    def test_newbie_mission_expiring_sender_messages(self, tcontext):
        """测试新手任务到期发送器的消息文本"""
        from app.business.mission_center.message import NewbieMissionExpiringSender
        with tcontext:
            sender = NewbieMissionExpiringSender()
            
            # 测试推送标题
            push_title = sender.get_push_title()
            assert push_title == "任务即将到期"
            
            # 测试推送消息 - 投资加息类型
            value = "+10"
            value_type = '%'
            reward_type = EquityType.INVEST_INCREASE
            push_message = sender.get_push_message(value, value_type, reward_type)
            expected_message = f"你的新用户专属任务即将到期，完成即可获得 {value}{value_type}{reward_type.value}奖励！请前往「奖励中心」完成任务。"
            assert push_message == expected_message
            
            # 测试推送消息 - 其他类型
            value = Decimal('100')
            value_type = 'USDT'
            reward_type = EquityType.CASHBACK
            push_message = sender.get_push_message(value, value_type, reward_type)
            expected_message = f"你的新用户专属任务即将到期，完成即可获得 {value} {value_type}{reward_type.value}奖励！请前往「奖励中心」完成任务。"
            assert push_message == expected_message
            
    def test_send_new_mission_notice(self, tcontext):
        """测试发送新任务通知"""
        with tcontext:
            from app.business.mission_center.message import MissionMessageBiz

            user_missions = UserMission.query.filter(
                UserMission.user_id == USER_ID,
            ).all()
            MissionMessageBiz.send_new_mission_notice(user_missions)
            
    def test_send_reward_sent_notice(self, tcontext):
        """测试发送任务奖励通知"""

        with tcontext:
            from app.business.mission_center.message import MissionMessageBiz

            user_missions = UserMission.query.filter(
                UserMission.user_id == USER_ID,
            ).all()
            MissionMessageBiz.send_reward_sent_notice(user_missions)
            
    def test_send_mission_expiring_notice(self, tcontext):
        from app.business.mission_center.message import ExpiringNoticeProcessor
        with tcontext:
            processor = ExpiringNoticeProcessor()
            processor.process_expiring_notices()
        
    def test_send_newbie_middle_expiring_notice(self, tcontext):
        from app.business.mission_center.message import MiddleExpiringNoticeProcessor
        with tcontext:
            processor = MiddleExpiringNoticeProcessor()
            processor.process_expiring_notices()

                            



# noinspection PyClassHasNoInit
@pytest.mark.usefixtures('module_setup')
class TestRealMessageSenders:
    
    def get_params(self, cache_data: dict, user_id, lang: Language):
        from app.business.mission_center.mission import MissionBiz
        from app.business.user import UserPreferences

        UserPreferences(user_id).language = lang
        title = MissionBiz.build_title(cache_data, is_simple=True)
        title_template = MissionBiz.get_title_template(cache_data["mission_condition"], is_simple=True)
        message_params = MissionBiz.get_build_title_params(cache_data)
        return title, title_template, message_params
    
    
    def test_send_all_messages(self, tcontext):
        from app.business.mission_center.message import NewbieNewMissionSender, RoutineMissionRewardSender, NewbieMissionRewardSender \
            , NewbieMissionExpiringSender, RoutineMissionExpiringSender
        from app.business.mission_center.mission import MissionBiz
        from app.caches.mission import MissionCache
        from app.business.equity_center.notice import InvestIncreaseMessageSender, NoticeScene
        from app.business.user import UserPreferences

        with tcontext:
            cache_data = MissionCache.get_cache_data_by_ids([800])[800]
            reward_type = cache_data['reward']['reward_type']
            user_id = 20045
            # for lang in Language:
            #     if lang in [Language.FR_FR, Language.DE_DE, Language.IT_IT, Language.PL_PL]:
            #         continue
            #     with force_locale(lang.value):
                    # title, title_template, message_params = self.get_params(cache_data, user_id, lang)
            #         NewbieNewMissionSender().send_message(user_id, title, title_template, message_params)

            # for lang in Language:
            #     if lang in [Language.FR_FR, Language.DE_DE, Language.IT_IT, Language.PL_PL, Language.TH_TH]:
            #         continue
            #     with force_locale(lang.value):
            #         title, title_template, message_params = self.get_params(cache_data, user_id, lang)
            #         RoutineMissionRewardSender().send_message(user_id, title, title_template, message_params)
                    
            # for lang in Language:
            #     if lang in [Language.FR_FR, Language.DE_DE, Language.IT_IT, Language.PL_PL, Language.TH_TH]:
            #         continue
            #     with force_locale(lang.value):
            #         title, title_template, message_params = self.get_params(cache_data, user_id, lang)
            #         NewbieMissionRewardSender().send_message(user_id, title, title_template, message_params)
            
            # for lang in Language:
            #     if lang in [Language.FR_FR, Language.DE_DE, Language.IT_IT, Language.PL_PL, Language.TH_TH]:
            #         continue
            #     with force_locale(lang.value):
            #         title, title_template, message_params = self.get_params(cache_data, user_id, lang)
            #         RoutineMissionRewardSender().send_message(user_id, title, title_template, message_params)
            
            # for lang in Language:
            #     if lang in [Language.FR_FR, Language.DE_DE, Language.IT_IT, Language.PL_PL, Language.TH_TH]:
            #         continue
            #     with force_locale(lang.value):
            #         title, title_template, message_params = self.get_params(cache_data, user_id, lang)
            #         NewbieMissionExpiringSender().send_single_expired_message(
            #             user_id, 
            #             title, 
            #             cache_data["reward"]["value"],
            #             cache_data["reward"]["value_type"],
            #             EquityType[reward_type],
            #             title_template,
            #             message_params,
            #         )
            
            # for lang in Language:
            #     if lang in [Language.FR_FR, Language.DE_DE, Language.IT_IT, Language.PL_PL, Language.TH_TH]:
            #         continue
            #     with force_locale(lang.value):
            #         title, title_template, message_params = self.get_params(cache_data, user_id, lang)
            #         RoutineMissionExpiringSender().send_single_expired_message(
            #             user_id, 
            #             title, 
            #             cache_data["reward"]["value"],
            #             cache_data["reward"]["value_type"],
            #             EquityType[reward_type],
            #             title_template,
            #             message_params,
            #         )
                    
            # for lang in Language:
            #     if lang in [Language.FR_FR, Language.DE_DE, Language.IT_IT, Language.PL_PL, Language.TH_TH]:
            #         continue
            #     with force_locale(lang.value):
            #         UserPreferences(user_id).language = lang
            #         notice_scene = NoticeScene.ACTIVATION_HALF_EXPIRED
            #         filtered_eq_rows = UserInvestIncreaseEquity.query.get(107)
            #         InvestIncreaseMessageSender()._send_notices(notice_scene, [filtered_eq_rows])
                    
            # for lang in Language:
            #     if lang in [Language.FR_FR, Language.DE_DE, Language.IT_IT, Language.PL_PL, Language.TH_TH]:
            #         continue
            #     with force_locale(lang.value):
            #         UserPreferences(user_id).language = lang
            #         notice_scene = NoticeScene.USAGE_HALF_EXPIRED
            #         filtered_eq_rows = UserInvestIncreaseEquity.query.get(107)
            #         InvestIncreaseMessageSender()._send_notices(notice_scene, [filtered_eq_rows])
                    
            for lang in Language:
                if lang in [Language.FR_FR, Language.DE_DE, Language.IT_IT, Language.PL_PL, Language.TH_TH]:
                    continue
                UserPreferences(user_id).language = lang
                with force_locale(lang.value):
                    user_eq_ids = [774]
                    try:
                        InvestIncreaseMessageSender.send_delivery_notices(user_eq_ids)
                    except Exception as e:
                        print(lang, e)
