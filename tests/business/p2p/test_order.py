from decimal import Decimal
from pprint import pprint
import random
from unittest.mock import patch

import pytest
from bson import ObjectId
from flask import g
from sqlalchemy import or_, and_

from app import Language
from app.caches.p2p import PayChannelCache
from app.common import P2pOrderUserType, P2pBusinessType, P2pAmountType
from app.exceptions import InvalidArgument
from app.models import User, db, P2pUser, P2pMerchant, Withdrawal, UserCheckRequest
from app.models.mongo import Status
from app.models.mongo.p2p.pay_channel import UserPayChannelMySQL, P2pPayChannelMySQL
from app.models.mongo.p2p.advertising import P2pAdvertisingMySQL
from app.models.mongo.p2p.config import P2pAssetConfig, Status as ConfigStatus
from app.models.mongo.p2p.order import P2pOrderCreateSnapMySQL
from app.models.p2p import P2pOrder, P2pO<PERSON><PERSON><PERSON><PERSON><PERSON>t, P2pOrderTransHistory
from app.schedules.p2p.advertising import offline_advertising_by_order_complaint
from app.schedules.p2p.order import fixup_order_trans_unlock_sub, fixup_order_trans_create, fixup_order_stock_finish, \
    fixup_order_complaint_anchor, fixup_order_trans_add, alter_trans_timeout_order, fixup_order_trans_lock, \
    fixup_order_stock_unlock, fixup_no_order_stock_lock, fixup_p2p_sell_risk_req, update_p2p_order_cert_file_cache
from app.utils import new_hex_token, quantize_amount
from app.utils.date_ import now
from tests.common.mock_mysql import patch_mysql
from tests.common.mock_redis import patch_redis
from tests.common.mock_mongo import patch_mongo
from tests.common.t_common import default_lang

USER_ID = 20073
MERCHANT_ID = 1342
MARKET = 'BTCUSDT'


@pytest.fixture(scope='module')
def module_setup(tcontext):
    try:
        # test_users = []
        with tcontext:
            g.auth_user = g.user = User.query.get(USER_ID)
            g.lang = Language.ZH_HANS_CN.value
        yield tcontext
    finally:
        with tcontext:
            pass


def save_pay_channel(user_id):
    item = P2pPayChannelMySQL(
        mongo_id='666f6f2d6261722d71757578',
        name="visa",
        active_status=P2pPayChannelMySQL.ActiveStatus.ACTIVE,
        form=[{
            "key": "q213kh123",
            "name": "文本字段",
            "field_name": "汇款账号",
            "status": "valid",
            "filed_type": "text",
        }],
        config_type=P2pPayChannelMySQL.ConfigType.NORMAL,
        is_need_name=True,
        lang_data={
            "EN_US": {
                "name": "alipay",
                "name_field": "name",
                "form_map": {"q213kh123": "account"}
            },
        },
        color="#FFC107",
    )
    db.session_add_and_commit(item)

    user_pay_channel = UserPayChannelMySQL(
        mongo_id="66004ad80b94aa0b6d692222",
        user_id=user_id,
        pay_channel_id=item.mongo_id,
        pay_form=[{
            "key": "q213kh123",
            "value": "account-123"
        }]
    )
    db.session_add_and_commit(user_pay_channel)
    PayChannelCache.reload()
    return user_pay_channel


def save_adv(merchant_id, user_pay_channel, side=P2pBusinessType.BUY):
    adv = P2pAdvertisingMySQL(
        mongo_id='65fa4c57f8860115924e80a1',
        user_id=merchant_id,
        adv_type=side,
        adv_number="****************",
        base="USDT",
        quote="USD",
        price="10",
        stocks_mode=P2pAdvertisingMySQL.StocksMode.UNLIMITED,
        stocks_quantity=100000,
        max_limit=1000,
        min_limit=100,
        pay_channel_ids=[user_pay_channel.pay_channel_id],
        user_channel_ids=[user_pay_channel.id],
        payment_timeliness=1200,
        status=P2pAdvertisingMySQL.Status.ONLINE,
        limitation_filter=None,
    )
    db.session_add_and_commit(adv)
    return adv


def save_asset_config():
    cfg = P2pAssetConfig(
        mongo_id='65fa4c57f8860115924e8888',
        asset="USDT",
        merchant_fee_rate=Decimal("0.01"),
        max_limit=1000,
        min_limit=10,
        status=ConfigStatus.VALID,
    )
    db.session_add_and_commit(cfg)
    return cfg


def save_user(customer_id, merchant_id):
    customer = P2pUser.get_or_create(user_id=customer_id)
    merchant = P2pUser.get_or_create(user_id=merchant_id)
    if not merchant.nickname:
        merchant.nickname = "234"
        db.session_add_and_commit(merchant)
    item = P2pMerchant.get_or_create(user_id=merchant_id)
    db.session_add_and_commit(item)
    merchant.merchant_id = item.id
    db.session.commit()


def get_order(user_id):
    model = P2pOrder
    order = model.get_user_query(user_id).order_by(model.id.desc()).first()
    return order


def get_order_id(user_id):
    order_id = get_order(user_id).order_id
    return order_id


@pytest.mark.usefixtures('module_setup')
@pytest.mark.usefixtures('patch_redis')
@pytest.mark.usefixtures('patch_mongo')
@pytest.mark.usefixtures('patch_mysql')
class TestP2pOrder:
    model = P2pOrder

    def test_check_user_p2p_limit(self):
        pass

    def test_check_order_operator(self):
        from app.business.p2p.order import P2pOrderBiz

        STATUS = self.model.Status
        order = P2pOrder()
        customer_id, merchant_id = USER_ID, MERCHANT_ID
        order.customer_id = customer_id
        order.merchant_id = merchant_id

        status = STATUS.CREATED
        order.status = status
        order.side = P2pBusinessType.BUY
        P2pOrderBiz(order).check_order_operator(USER_ID, status)
        with pytest.raises(InvalidArgument) as exc_info:
            P2pOrderBiz(order).check_order_operator(merchant_id, status)
        order.side = P2pBusinessType.SELL
        P2pOrderBiz(order).check_order_operator(USER_ID, status)
        with pytest.raises(InvalidArgument) as exc_info:
            P2pOrderBiz(order).check_order_operator(merchant_id, status)

        status = STATUS.CONFIRMED
        order.side = P2pBusinessType.BUY
        with pytest.raises(InvalidArgument) as exc_info:
            P2pOrderBiz(order).check_order_operator(USER_ID, status)
        P2pOrderBiz(order).check_order_operator(merchant_id, status)

        order.side = P2pBusinessType.SELL
        with pytest.raises(InvalidArgument) as exc_info:
            P2pOrderBiz(order).check_order_operator(USER_ID, status)
        P2pOrderBiz(order).check_order_operator(merchant_id, status)

        status = STATUS.PAID
        order.side = P2pBusinessType.BUY
        P2pOrderBiz(order).check_order_operator(USER_ID, status)
        with pytest.raises(InvalidArgument) as exc_info:
            P2pOrderBiz(order).check_order_operator(merchant_id, status)
        order.side = P2pBusinessType.SELL
        with pytest.raises(InvalidArgument) as exc_info:
            P2pOrderBiz(order).check_order_operator(USER_ID, status)
        P2pOrderBiz(order).check_order_operator(merchant_id, status)

        status = STATUS.FINISHED
        order.side = P2pBusinessType.BUY
        P2pOrderBiz(order).check_order_operator(merchant_id, status)
        with pytest.raises(InvalidArgument) as exc_info:
            P2pOrderBiz(order).check_order_operator(USER_ID, status)
        order.side = P2pBusinessType.SELL
        with pytest.raises(InvalidArgument) as exc_info:
            P2pOrderBiz(order).check_order_operator(merchant_id, status)
        P2pOrderBiz(order).check_order_operator(USER_ID, status)

    def test_order(self, tcontext):
        from app.business.p2p.order_factor import P2pOrderFactor
        with tcontext:
            user_pay_channel = save_pay_channel(MERCHANT_ID)
            adv = save_adv(MERCHANT_ID, user_pay_channel, P2pBusinessType.SELL)
            save_user(USER_ID, MERCHANT_ID)
            save_asset_config()
            customer_id = USER_ID
            adv_id = adv.id
            quote_amount = Decimal("100")
            base_amount = round(quote_amount / adv.price, 2)
            channel_id = adv.user_channel_ids[0]
            with patch("app.business.p2p.order.P2pOrderBus.update_newest") as tmp1:
                order = P2pOrderFactor.create(customer_id, adv_id, adv.price, base_amount, quote_amount,
                                              P2pAmountType.QUOTE, channel_id)
                P2pOrderFactor(order).confirm(MERCHANT_ID)
                P2pOrderFactor(order).pay(customer_id)
                P2pOrderFactor(order).to_finish(MERCHANT_ID)


@pytest.mark.usefixtures('module_setup')
# @pytest.mark.usefixtures('patch_redis')
# @pytest.mark.usefixtures('patch_mongo')
class TestOrderApi:
    model = P2pOrder

    order_id = ""

    @classmethod
    def _set_login_user(cls, user_id):
        from app.caches import UserLoginTokenCache
        from app.common import LOGIN_TOKEN_SIZE, LOGIN_STATE_DEFAULT_TTL
        token = new_hex_token(LOGIN_TOKEN_SIZE)
        UserLoginTokenCache(user_id).add_token(token, LOGIN_STATE_DEFAULT_TTL)
        return token

    @staticmethod
    def add_p2p_user(user_id):
        from app.models import P2pUser
        user = P2pUser.get_or_create(user_id=user_id)
        if not user.nickname:
            user.nickname = "noodles"
            db.session_add_and_commit(user)
        return user

    def test_order_list(self, tcontext, user_id=None):
        with tcontext:
            g.auth_user = g.user = User.query.get(user_id or USER_ID)
            client = tcontext.app.test_client()
            resp = client.post("/res/p2p/order/list", json={"user_type": "MERCHANT"})
            pprint(resp.json)
            assert "order" in resp.text

    def test_order_detail(self, tcontext, user_id=None, order_id=""):
        with tcontext:
            client = tcontext.app.test_client()
            user_id = user_id or MERCHANT_ID
            g.auth_user = g.user = User.query.get(user_id)
            if not order_id:
                order_id = get_order_id(user_id)
            resp = client.get(
                f"/res/p2p/order/{order_id}",
            )
            pprint(resp.json)
            assert resp.json["code"] == 0

    def test_adv_sell_lift_cycle(self, tcontext):
        # 对应用户买入
        self.test_create_fake_adv_sell_order(tcontext, USER_ID)
        self.test_order_confirm(tcontext, MERCHANT_ID)
        self.test_order_pay(tcontext, USER_ID)
        self.test_order_finish(tcontext, MERCHANT_ID)

    def test_adv_buy_lift_cycle(self, tcontext):
        # 对应用户卖出
        self.test_create_fake_adv_buy_order(tcontext, USER_ID)
        self.test_order_confirm(tcontext, MERCHANT_ID)
        self.test_order_pay(tcontext, MERCHANT_ID)
        self.test_order_finish(tcontext, USER_ID)

    def test_debug_order(self, tcontext):
        self.test_create_adv_buy_order(tcontext, 798, "660bbc529b0fd7a552b49526")

    def test_create_fake_adv_sell_order(self, tcontext, user_id=None):
        with tcontext:
            # self.add_p2p_user(g.user.id)
            user_pay_channel = save_pay_channel(MERCHANT_ID)
            adv = save_adv(MERCHANT_ID, user_pay_channel, side=P2pBusinessType.SELL)
            client = tcontext.app.test_client()
            base_amount = Decimal("100")
            resp = client.post(
                "/res/p2p/order",
                json={"adv_id": str(adv.id), "price": adv.price, "amount_type": "BASE",
                      "base_amount": base_amount, "quote_amount": quantize_amount(base_amount * adv.price, 2),
                      "user_pay_channel_id": str(adv.user_channel_ids[0]),
                      "base": adv.base, "quote": adv.quote,
                      }
            )
            print(resp.json)
            assert resp.json["code"] == 0

    def test_create_fake_adv_buy_order(self, tcontext, user_id=None):
        with tcontext:
            # self.add_p2p_user(g.user.id)
            user_pay_channel = save_pay_channel(user_id or USER_ID)
            adv = save_adv(MERCHANT_ID, user_pay_channel)
            client = tcontext.app.test_client()
            base_amount = 100
            resp = client.post(
                "/res/p2p/order",
                json={"adv_id": str(adv.id), "price": adv.price, "amount_type": "BASE",
                      "base_amount": base_amount, "quote_amount": base_amount * adv.price,
                      "user_pay_channel_id": str(user_pay_channel.id)}
            )
            print(resp.json)
            assert resp.json["code"] == 0

    def test_create_adv_sell_order(self, tcontext):
        with tcontext:
            user_id = USER_ID
            adv_id = "663ee4aa36b790852f81a270"
            g.auth_user = g.user = User.query.get(user_id)
            adv = P2pAdvertisingMySQL.query.filter_by(mongo_id=adv_id).first()
            client = tcontext.app.test_client()
            quote_amount = 100
            channel_id = adv.pay_channel_ids[0]
            user_pay_channel = UserPayChannelMySQL.query.filter(
                UserPayChannelMySQL.user_id == user_id,
                UserPayChannelMySQL.pay_channel_id == channel_id
            ).first()
            resp = client.post(
                "/res/p2p/order",
                json={"adv_id": str(adv.id), "price": adv.price, "amount_type": "QUOTE",
                      "base_amount": round(quote_amount / adv.price, 2), "quote_amount": quote_amount,
                      "user_pay_channel_id": str(user_pay_channel.id)}
            )
            print(resp.json)
            assert resp.json["code"] == 0

    def test_create_adv_buy_order(self, tcontext, user_id=None, adv_id=None):
        with tcontext:
            g.auth_user = g.user = User.query.get(user_id)
            adv = P2pAdvertisingMySQL.query.filter_by(mongo_id=adv_id).first()
            user_channel = UserPayChannelMySQL.query.filter(
                UserPayChannelMySQL.pay_channel_id.in_(adv.pay_channel_ids),
                UserPayChannelMySQL.user_id == user_id
            ).first()
            client = tcontext.app.test_client()
            base_amount = 100
            resp = client.post(
                "/res/p2p/order",
                json={"adv_id": str(adv.id), "price": adv.price, "amount_type": "BASE",
                      "base_amount": base_amount, "quote_amount": base_amount * adv.price,
                      "user_pay_channel_id": str(user_channel.id)}
            )
            print(resp.json)
            assert resp.json["code"] == 0

    def test_order_confirm(self, tcontext, user_id=None, order_id=""):
        with tcontext:
            client = tcontext.app.test_client()
            g.auth_user = g.user = User.query.get(user_id or MERCHANT_ID)
            if not order_id:
                order_id = self.model.query.filter(
                    self.model.merchant_id == g.user.id,
                    self.model.status == self.model.Status.CREATED
                ).order_by(self.model.id.desc()).first().order_id
            resp = client.put(
                f"/res/p2p/order/{order_id}/confirm"
            )
            pprint(resp.json)
            assert resp.json["code"] == 0

    def test_order_pay(self, tcontext, user_id=None, order_id=""):
        with tcontext:
            client = tcontext.app.test_client()
            if order_id := order_id:
                order = self.model.query.filter(self.model.order_id == order_id).first()
            else:
                order = self.model.get_user_query(USER_ID).filter(
                    self.model.status == self.model.Status.CONFIRMED
                ).order_by(self.model.id.desc()).first()
                order_id = order.order_id
            old_user_id = USER_ID if order.side == P2pBusinessType.BUY else MERCHANT_ID
            user_id = user_id or old_user_id
            g.auth_user = g.user = User.query.get(user_id)
            resp = client.put(
                f"/res/p2p/order/{order_id}/pay",
                json={"file_keys": ["2024-04-05/CFFFD3E2F417B83E11A082D83985D90B.png",
                                    "2024-04-05/CFFFD3E2F417B83E11A082D83985D90B.png"]}
            )
            pprint(resp.json)
            assert resp.json["code"] == 0

    def test_order_finish(self, tcontext, user_id=None, order_id=""):
        with tcontext:
            client = tcontext.app.test_client()
            # if order_id := order_id:
            #     order = self.model.query.filter(self.model.order_id == order_id).first()
            # else:
            #     order = self.model.get_user_query(USER_ID).filter(
            #         self.model.status == self.model.Status.PAID
            #     ).order_by(self.model.id.desc()).first()
            #     order_id = order.order_id
            # old_user_id = order.merchant_id if order.side == P2pBusinessType.BUY else order.customer_id
            # user_id = user_id or old_user_id
            # g.auth_user = g.user = User.query.get(user_id)
            totp = str(random.randint(0, 100000)).zfill(7)
            g.auth_user = g.user = User.query.get(1376)
            order_id = '2025070356212590'
            print(order_id)
            resp = client.put(
                f"/res/p2p/order/{order_id}/finish",
                json={
                    "sms_captcha": totp,
                    "totp_captcha": totp
                }
            )
            pprint(resp.json)
            assert resp.json["code"] == 0

    def test_order_cancel(self, tcontext, user_id=None, order_id=""):
        with tcontext:
            client = tcontext.app.test_client()
            user_id = user_id or USER_ID
            g.auth_user = g.user = User.query.get(user_id)
            if not order_id:
                order_id = get_order_id(user_id)
            resp = client.put(
                f"/res/p2p/order/{order_id}/cancel",
                json={"reason": "NO_WANT_BUY"}
            )
            pprint(resp.json)
            assert resp.json["code"] == 0

    def test_add_user(self, tcontext):
        with tcontext:
            self.add_p2p_user(20069)

    def test_active_orders_count(self, tcontext):
        with tcontext:
            g.auth_user = g.user = User.query.get(1342)
            client = tcontext.app.test_client()
            resp = client.get(
                f"/res/p2p/order/active_count",
            )
            print(resp.text)
            assert resp.json["code"] == 0

    def test_active_orders_status(self, tcontext):
        with tcontext:
            user_id = USER_ID
            client = tcontext.app.test_client()
            order_id = get_order_id(user_id)
            print(order_id)
            resp = client.post(
                f"/res/p2p/order/status",
                json={"order_ids": [str(order_id)]}
            )
            print(resp.text)
            assert resp.json["code"] == 0

    def test_create_info_snap(self, tcontext):
        from app.business.p2p.order import P2pOrderCreateSnapBiz

        with tcontext:
            for _id in [133]:
                order = self.model.query.get(_id)
                adv = P2pAdvertisingMySQL.query.filter_by(mongo_id=order.adv_id).first()
                obj = P2pOrderCreateSnapBiz.save_create_info_snap(order, adv)
                pprint(obj.to_dict())

    def test_fix_create_info(self, tcontext):
        from app.business.p2p.order import P2pOrderCreateSnapBiz

        with tcontext:
            P2pOrderCreateSnapMySQL.query.delete()
            db.session.commit()
            orders = P2pOrder.query.all()
            for order in orders:
                adv = P2pAdvertisingMySQL.query.filter_by(mongo_id=order.adv_id).first()
                P2pOrderCreateSnapBiz.save_create_info_snap(order, adv)

    def test_fix_create_channel(self, tcontext):

        with tcontext:
            rows = P2pOrderCreateSnapMySQL.query.all()
            for row in rows:
                row.pay_channel["pay_channel"]["active_status"] = "ACTIVE"
                row.pay_channel["pay_channel"]["status"] = "VALID"
                db.session.add(row)
            db.session.commit()

    def test_flush_session_id(self, tcontext):
        from app.business.p2p.order_factor import P2pOrderFactor

        with tcontext:
            orders = P2pOrder.query.all()
            for order in orders:
                customer, merchant = P2pOrderFactor.get_p2p_customer_merchant(order.customer_id, order.merchant_id)
                order.session_id = P2pOrderFactor.gen_session_id(customer.biz_user_id, merchant.biz_user_id)
            db.session.commit()

    def test_order_create_im_msg(self, tcontext):
        from app.business.p2p.message import OrderMsg

        with tcontext:
            user_id = USER_ID
            g.auth_user = g.user = User.query.get(user_id)
            order = get_order(user_id)
            OrderMsg().send_create_message(order)

    def test_max_cancel(self, tcontext):
        from app.business.p2p.utils import P2pUtils

        with tcontext:
            user_id = 63
            ret = P2pUtils.check_p2p_cancel_limit_user_id(user_id)
            print(ret)

    def test_order_export(self, tcontext):
        with tcontext:
            g.lang = Language.ZH_HANS_CN.value
            client = tcontext.app.test_client()
            client.set_cookie("lang", g.lang)
            resp = client.post(
                f"/res/p2p/order/list",
                json={"export": True}
            )
            print(resp.text)
            assert resp.json["code"] == 0


@pytest.mark.usefixtures('module_setup')
class TestOrderComplaintApi:
    model = P2pOrder
    c_model = P2pOrderComplaint

    def get_complaint_order_id(self, user_id):
        complaint = self.c_model.query.filter(
            self.c_model.complaint_status.in_([self.c_model.Status.CREATED, self.c_model.Status.PENDING]),
            or_(self.c_model.plaintiff_id == user_id, self.c_model.defendant_id == user_id)
        ).first()
        return complaint.order_id

    def get_complaint(self, user_id):
        complaint = self.c_model.query.filter(
            self.c_model.complaint_status == self.c_model.Status.PENDING,
            and_(self.c_model.plaintiff_id != user_id, self.c_model.defendant_id != user_id)
        ).first()
        return complaint

    def test_order_complaint_create(self, tcontext):
        with tcontext:
            user_id = USER_ID
            g.auth_user = g.user = User.query.get(user_id)
            client = tcontext.app.test_client()
            order = self.model.get_user_query(user_id).filter(
                self.model.status == self.model.Status.FINISHED,
                self.model.complaint_id.is_(None)
            ).order_by(self.model.id.desc()).first()
            order_id = order.order_id
            print(order_id)
            resp = client.post(
                f"/res/p2p/order/{order_id}/complaint",
                json={"reason": "PAID_NOT_RELEASED", "desc": "你好",
                      "image_keys": ["2024-04-05/CFFFD3E2F417B83E11A082D83985D90B.png"],
                      "video_keys": ["2024-04-13/D7C319F9DE34A315D9457E17FA301B9E.mov"],
                      }
            )
            pprint(resp.json)
            assert resp.json["code"] == 0

    def test_order_complaint_reopen(self, tcontext):
        with tcontext:
            user_id = USER_ID
            client = tcontext.app.test_client()
            complaint = self.c_model.query.filter(
                or_(
                    self.c_model.plaintiff_id == user_id,
                    self.c_model.defendant_id == user_id,
                ),
                self.c_model.complaint_status == self.c_model.Status.CANCELED
            ).first()
            order_id = complaint.order_display_id
            print(order_id)
            resp = client.post(
                f"/res/p2p/order/{order_id}/complaint",
                json={"desc": "你好",
                      "image_keys": ["2024-04-05/CFFFD3E2F417B83E11A082D83985D90B.png"],
                      "video_keys": ["2024-04-13/D7C319F9DE34A315D9457E17FA301B9E.mov"],
                      })
            pprint(resp.json)
            assert resp.json["code"] == 0

    def test_put_complaint(self, tcontext):
        with tcontext:
            client = tcontext.app.test_client()
            order_id = self.get_complaint_order_id(USER_ID)
            show_id = self.model.query.get(order_id).order_id
            print(order_id, show_id)
            resp = client.put(
                f"/res/p2p/order/{show_id}/complaint",
                # json={"desc": "补充描述",
                #       "image_keys": ["2024-04-05/CFFFD3E2F417B83E11A082D83985D90B.png",
                #                      "2024-04-05/CFFFD3E2F417B83E11A082D83985D90B.png"]}
                json={"desc": f"{now()}"}
            )
            pprint(resp.json)
            assert resp.json["code"] == 0

    def test_order_complaint_list(self, tcontext):
        with tcontext:
            client = tcontext.app.test_client()
            resp = client.post(
                f"/res/p2p/order/complaint/list",
                json={
                    "user_type": "MERCHANT"
                }
            )
            pprint(resp.json)
            assert resp.json["code"] == 0

    def test_order_complaint_detail(self, tcontext):
        with tcontext:
            client = tcontext.app.test_client()
            user_id = USER_ID
            order_id = self.get_complaint_order_id(user_id)
            show_id = self.model.query.get(order_id).order_id
            user_id = P2pOrder.query.filter(P2pOrder.order_id == show_id).first().customer_id
            g.auth_user = g.user = User.query.get(user_id or MERCHANT_ID)
            resp = client.get(
                f"/res/p2p/order/{show_id}/complaint",
            )
            pprint(resp.json)
            assert resp.json["code"] == 0

    def test_order_complaint_cancel(self, tcontext):
        with tcontext:
            user_id = MERCHANT_ID
            g.auth_user = g.user = User.query.get(user_id)
            client = tcontext.app.test_client()
            order_id = self.get_complaint_order_id(user_id)
            show_id = self.model.query.get(order_id).order_id
            resp = client.put(
                f"/res/p2p/order/{show_id}/complaint/cancel",
            )
            pprint(resp.json)
            assert resp.json["code"] == 0

    def test_admin_complaint_pending(self, tcontext):
        with tcontext:
            client = tcontext.app.test_client()
            order = self.get_complaint_order_id(USER_ID)
            order = self.model.query.get(order) 
            resp = client.put(
                f"/admin/p2p/order/{order.complaint_id}/complaint",
                json={"status": "PENDING"}
            )
            pprint(resp.json)
            assert resp.json["code"] == 0

    def test_admin_send_msg(self, tcontext):
        with tcontext:
            client = tcontext.app.test_client()
            order_id = self.get_complaint_order_id(USER_ID)
            order = self.model.query.get(order_id)
            complaint_id = order.complaint_id
            resp = client.put(
                f"/admin/p2p/order/{complaint_id}/complaint",
                json={"msg": "管理员发送消息", "to_user_id": "1419"}
            )
            pprint(resp.json)
            assert resp.json["code"] == 0

    def test_admin_op(self, tcontext):
        with tcontext:
            client = tcontext.app.test_client()
            complaint = self.get_complaint(USER_ID)
            resp = client.put(
                f"/admin/p2p/order/{complaint.id}/complaint",
                json={"operate": "FREEZE_BUYER"}
            )
            pprint(resp.json)
            assert resp.json["code"] == 0

    def test_admin_complaint_finish(self, tcontext):
        with tcontext:
            client = tcontext.app.test_client()
            complaint_id = self.get_complaint(USER_ID).id
            resp = client.put(
                f"/admin/p2p/order/{complaint_id}/complaint",
                json={"status": "FINISHED", "winner": "BUYER"}
            )
            pprint(resp.json)
            assert resp.json["code"] == 0

    def test_check_user_p2p_cancel_limit(self, tcontext):
        from app.business.p2p.utils import P2pUtils

        with tcontext:
            user_id = 10000228
            g.auth_user = g.user = User.query.get(user_id)
            P2pUtils.check_p2p_cancel_limit_user_id(user_id)

    def test_check_user_complaint_limit(self, tcontext):
        from app.business.p2p.utils import P2pUtils

        with tcontext:
            user_id = USER_ID
            g.auth_user = g.user = User.query.get(user_id)
            ret = P2pUtils.check_user_complaint_limit(user_id)
            print(ret)

    def test_offline_advertising_by_order_complaint(self, tcontext):
        with tcontext:
            offline_advertising_by_order_complaint()


@pytest.mark.usefixtures('module_setup')
class TestOrderEvent:
    model = P2pOrder

    def test_save_order_finish_event(self, tcontext):
        from app.business.p2p.order import P2pOrderEventBiz
        with tcontext:
            orders = self.model.query.filter(self.model.status == self.model.Status.FINISHED)
            for order in orders:
                P2pOrderEventBiz(order).create_finish_event()
                db.session.commit()


class TestConfig:

    def test_video(self, tcontext):
        with tcontext:
            client = tcontext.app.test_client()
            resp = client.get("/res/tutorial/video")
            pprint(resp.json)
            assert resp.json["code"] == 0

    def test_message_config(self, tcontext):
        with tcontext:
            client = tcontext.app.test_client()
            g.lang = Language.EN_US.value
            resp = client.get("/res/p2p/config/im-message")
            pprint(resp.json)
            assert resp.json["code"] == 0


@pytest.mark.usefixtures('module_setup')
class TestFixup:

    def test_fixup_order_trans_unlock_sub(self, tcontext):
        with tcontext:
            fixup_order_trans_unlock_sub()

    def test_fixup_order_trans_create(self, tcontext):
        with tcontext:
            fixup_order_trans_create()

    def test_fixup_order_stock_finish(self, tcontext):
        with tcontext:
            fixup_order_stock_finish()

    def test_fixup_order_complaint_anchor(self, tcontext):
        with tcontext:
            fixup_order_complaint_anchor()

    def test_fixup_order_trans_add(self, tcontext):
        with tcontext:
            fixup_order_trans_add()

    def test_alter_trans_timeout_order(self, tcontext):
        with tcontext:
            alter_trans_timeout_order()

    def test_fixup_order_trans_lock(self, tcontext):
        with tcontext:
            fixup_order_trans_lock()

    def test_unlock_stock(self, tcontext):
        from app.business.p2p.order_factor import P2pStockBiz

        with tcontext:
            row_id = 161
            P2pStockBiz(row_id).unlock()

    def test_fixup_order_stock_unlock(self, tcontext):
        with tcontext:
            fixup_order_stock_unlock()

    def test_fixup_no_order_stock_lock(self, tcontext):
        with tcontext:
            fixup_no_order_stock_lock()

    def test_p2p_sell_risk_req(self, tcontext):
        with tcontext:
            fixup_p2p_sell_risk_req()


@pytest.mark.usefixtures('module_setup')
class TestImMsg:

    def test_fixup_order_trans_unlock_sub(self, tcontext):
        from app.business.clients.im import ImServerClient

        with tcontext:
            user_id = 1681
            user_im_id = P2pUser.get_by_user_id(user_id).biz_user_id
            user_im_id = '8CE1AE96'
            session_id = "si_1D8638BB_8CE1AE96"
            print(user_im_id, session_id)
            seqs = ImServerClient().get_order_unread_seqs(user_im_id, [session_id])
            pprint(seqs)

    def test_get_order_im_msg(self, tcontext):
        from app.business.clients.im import ImServerClient

        with tcontext:
            user_im_id = '4CDE0A27'
            session_id = "si_4CDE0A27_7492A46C"
            msgs = ImServerClient().get_order_im_msg(user_im_id, session_id)
            pprint(msgs)
            print(len(msgs))


@pytest.mark.usefixtures('module_setup')
class TestP2pWithdrawal:

    def test_execute_user_check_request_task(self, tcontext):
        from app.business.risk_control.withdrawal import execute_user_check_request_task

        with tcontext:
            record_id = 5014
            execute_user_check_request_task(record_id)

    def test_add_user_withdrawal_check_request(self, tcontext):

        with tcontext:
            w = Withdrawal.query.get(42823)
            from app.business.risk_control.withdrawal import WithdrawalRiskCheck
            WithdrawalRiskCheck.add_check_request(w)

    def test_audit_withdrawal(self, tcontext):
        from app.schedules.wallet.local import _audit_withdrawal

        with tcontext:
            withdrawal_id = 42818
            _audit_withdrawal(withdrawal_id)

    def test_get_user_daily_withdrawal_limit(self, tcontext):
        from app.business import get_user_daily_withdrawal_limit

        with tcontext:
            user_id = 1376
            user = User.query.get(user_id)
            get_user_daily_withdrawal_limit(user)

    def test_do_cancel_withdrawal(self, tcontext):
        from app.business.wallet import WithdrawalHelper

        with tcontext:
            user_id = 1376
            withdrawal_id = 42823
            WithdrawalHelper.do_cancel_withdrawal(user_id, withdrawal_id)


@pytest.mark.usefixtures('module_setup')
class TestP2pMessage:

    def test_send_msg(self, tcontext):
        with tcontext:
            from app.business.p2p.message import ReceivedOrderToPayment, BuyerPaymentDeadline, PaidWaitReleaseAsset, \
                OperationComplaintForCancelOrderToSeller
            from app.business import UserPreferences

            # order1 = P2pOrder.query.get(3464)
            # order2 = P2pOrder.query.get(3492)
            # user_id = 1376

            order1 = P2pOrder.query.get(15)
            order2 = P2pOrder.query.get(23)
            user_id = 20073

            pre = UserPreferences(user_id)
            for cls in [
                ReceivedOrderToPayment,
                BuyerPaymentDeadline,
            ]:
                for lang in Language:
                    pre.language = lang
                    cls().send_message(order1)

            for cls2 in [PaidWaitReleaseAsset, OperationComplaintForCancelOrderToSeller]:
                for lang in Language:
                    pre.language = lang
                    cls2().send_message(order2)

@pytest.mark.usefixtures('module_setup')
class TestP2pOrderFileCache:

    def test_update_p2p_order_cert_file_cache(self, tcontext):
        with tcontext:
            update_p2p_order_cert_file_cache()
            
    def test_get_channel_orders(self, tcontext):
        with tcontext:
            url = '/admin/p2p/order/cert-file'
            resp = tcontext.app.test_client().get(url, query_string={
                "user_pay_channel_id": "685a46ca0e3b72b9c684c1ef"
            })
            pprint(resp.json)
            assert resp.json["code"] == 0