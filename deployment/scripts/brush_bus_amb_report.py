from decimal import Decimal
from itertools import chain
from collections import defaultdict
import os
import sys
import datetime
import time
from datetime import timedelta, date
from tqdm import tqdm
from dateutil.relativedelta import relativedelta
from flask import current_app


abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())


START_DATE = date(2024, 1, 1)
END_DATE = date(2025, 9, 24)


class TempData:

    def init_data(self):
        from app.business.user_tag.helper import get_disabled_user_ids
        from app.business.sub_account import get_all_sub_main_info
        from app.business.market_maker import MarketMakerHelper

        current_app.logger.warning("init_data query sub acc")
        self.disabled_user_ids = get_disabled_user_ids()
        self.sub_main_map, self.main_subs_map = get_all_sub_main_info()
        self.sub_user_ids = set(self.sub_main_map)
        market_makers = MarketMakerHelper.list_all_maker_ids(include_sub_account=False)
        self.market_makers = set(market_makers)

        self._init_amb()

    def _init_amb(self):
        from app.models.referral import ReferralHistory, Ambassador, BusinessAmbassador, TreeAmbassador, BusinessUser
        from app.utils import batch_iter

        current_app.logger.warning("init_data query amb")
        nor_ambs = Ambassador.query.filter(
            Ambassador.status == Ambassador.Status.VALID
        ).with_entities(
            Ambassador.user_id,
            Ambassador.effected_at,
        ).all()
        self.nor_ambs = nor_ambs
        self.nor_amb_ids = {i.user_id for i in nor_ambs}

        bus_ambs = BusinessAmbassador.query.filter(
            BusinessAmbassador.status == BusinessAmbassador.Status.VALID
        ).with_entities(
            BusinessAmbassador.user_id,
            BusinessAmbassador.effected_at,
            BusinessAmbassador.bus_user_id,
        ).all()
        self.bus_ambs = bus_ambs
        self.bus_amb_ids = {i.user_id for i in bus_ambs}

        bus_users = BusinessUser.query.filter(
            BusinessUser.status == BusinessUser.Status.VALID,
        ).with_entities(
            BusinessUser.user_id,
            BusinessUser.team_id,
        ).all()
        team_users_map = defaultdict(list)
        bus_user_team_map = {}
        for bus_u in bus_users:
            team_users_map[bus_u.team_id].append(bus_u.user_id)
            bus_user_team_map[bus_u.user_id] = bus_u.team_id
        self.team_users_map = team_users_map

        team_ambs_map = defaultdict(list)
        for b_amb in bus_ambs:
            team = bus_user_team_map.get(b_amb.bus_user_id)
            team_ambs_map[team].append(b_amb)
        self.team_ambs_map = team_ambs_map

        tree_ambs = TreeAmbassador.query.filter(
            TreeAmbassador.status == TreeAmbassador.Status.VALID
        ).with_entities(
            TreeAmbassador.user_id,
            TreeAmbassador.effected_at,
        ).all()
        self.tree_ambs = tree_ambs

        current_app.logger.warning("init_data query amb ref_his")
        all_amb_ids = {i.user_id for i in chain(nor_ambs, bus_ambs, tree_ambs)}
        ref_his_rows = []
        for ch_amb_ids in batch_iter(all_amb_ids, 3000):
            ch_ref_his_rows = ReferralHistory.query.filter(
                ReferralHistory.referrer_id.in_(ch_amb_ids),
            ).with_entities(
                ReferralHistory.referree_id,
                ReferralHistory.referrer_id,
                ReferralHistory.created_at,
                ReferralHistory.status,
            ).all()
            ref_his_rows.extend(ch_ref_his_rows)
        self.ref_his_rows: list[ReferralHistory] = ref_his_rows
        self.all_ee_ids = {i.referree_id for i in ref_his_rows}


date_active_users_map = {}
date_user_balance_usd_map = {}
date_ref_details_map = {}
temp_data = TempData()


def yield_get_active_user_set(start_date, end_date):
    from app.models.user import SubAccountAssetTransfer
    from app.models.red_packet import RedPacketHistory
    from app.models.wallet import Deposit
    from app.business.utils import yield_query_records_by_time_range
    # same get_active_user_set
    active_user_ids = set()
    for v in yield_query_records_by_time_range(
            SubAccountAssetTransfer,
            start_date,
            end_date,
            [
                SubAccountAssetTransfer.target,
            ],
    ):
        active_user_ids.add(v.target)
    for v in yield_query_records_by_time_range(
            Deposit,
            start_date,
            end_date,
            [
                Deposit.user_id,
            ],
    ):
        active_user_ids.add(v.user_id)
    # 红包记录
    red_packet_data = RedPacketHistory.query.filter(
        RedPacketHistory.effective_at >= start_date,
        RedPacketHistory.effective_at < end_date,
    ).with_entities(RedPacketHistory.user_id).all()
    active_user_ids.update([i.user_id for i in red_packet_data if i.user_id])
    return active_user_ids


def get_date_active_users(dt: date) -> set[int]:
    from app.business.user import filter_active_users

    global date_active_users_map, temp_data

    if dt in date_active_users_map:
        return date_active_users_map[dt]

    t1 = time.time()
    daily_active_users = filter_active_users(dt, dt)
    exclude_user_ids = temp_data.disabled_user_ids | temp_data.sub_user_ids
    daily_active_users -= exclude_user_ids
    daily_active_users = daily_active_users & temp_data.all_ee_ids

    date_active_users_map[dt] = daily_active_users

    t2 = time.time()
    current_app.logger.warning(f'get_date_active_users {dt} length:{len(daily_active_users)} consume {t2-t1}')

    return daily_active_users


def get_date_range_active_users(start_date: date, end_date: date) -> set[int]:
    res = set()
    while start_date <= end_date:
        res.update(get_date_active_users(start_date))
        start_date += timedelta(days=1)
    return res


def get_date_user_balance_usd_map(dt: date) -> dict[int, Decimal]:
    from app.business import ExchangeLogDB
    from app.utils import date_to_datetime

    global date_user_balance_usd_map, temp_data

    if dt in date_user_balance_usd_map:
        return date_user_balance_usd_map[dt]

    t1 = time.time()

    ts = int(date_to_datetime(dt).timestamp())
    table = ExchangeLogDB.user_account_balance_sum_table(ts)
    if not table.exists():
        balance_usd_map = {}
        table2 = ExchangeLogDB.user_account_balance_table(ts)
        if table2.exists():
            records = table2.select(
                'user_id',
                'balance_usd',
            )
            for r in records:
                mid = temp_data.sub_main_map.get(r[0], r[0])
                if mid in temp_data.all_ee_ids:
                    balance_usd_map[mid] = r[1] + balance_usd_map.get(mid, 0)
        else:
            current_app.logger.warning(f'get_date_user_balance_usd_map {dt} table2 {table2} not_exist')
    else:
        records = table.select(
            'user_id',
            'balance_usd',
        )
        balance_usd_map = {}
        for r in records:
            mid = temp_data.sub_main_map.get(r[0], r[0])
            if mid in temp_data.all_ee_ids:
                balance_usd_map[mid] = r[1] + balance_usd_map.get(mid, 0)
    date_user_balance_usd_map[dt] = balance_usd_map

    t2 = time.time()
    current_app.logger.warning(f'get_date_user_balance_usd_map {dt} length:{len(balance_usd_map)} consume {t2-t1}')

    return balance_usd_map


def get_date_ref_details(dt: date) -> list:
    from app.models.referral import ReferralAssetDetail, ReferralAssetHistory

    global date_ref_details_map, temp_data

    if dt in date_ref_details_map:
        return date_ref_details_map[dt]

    t1 = time.time()

    ref_detail_rows = ReferralAssetDetail.query.filter(
        ReferralAssetDetail.date == dt,
        ReferralAssetDetail.type == ReferralAssetHistory.Type.AMBASSADOR,
    ).with_entities(
        ReferralAssetDetail.user_id,
        ReferralAssetDetail.referree_id,
        ReferralAssetDetail.spot_amount,
        ReferralAssetDetail.perpetual_amount,
    ).all()
    res = [i for i in ref_detail_rows if i.referree_id in temp_data.all_ee_ids]

    date_ref_details_map[dt] = res

    t2 = time.time()
    current_app.logger.warning(f'get_date_ref_details {dt} length:{len(res)} consume {t2-t1}')

    return ref_detail_rows


def get_date_range_ref_details(start_date: date, end_date: date) -> list:
    res = []
    while start_date <= end_date:
        res.extend(get_date_ref_details(start_date))
        start_date += timedelta(days=1)
    return res


def main():
    import sys

    global START_DATE, END_DATE

    args = sys.argv[1:]
    if len(args) >= 1:
        START_DATE = datetime.datetime.strptime(args[0], '%Y-%m-%d').date()
    if len(args) >= 2:
        END_DATE = datetime.datetime.strptime(args[1], '%Y-%m-%d').date()
    current_app.logger.warning(f"brush_bus_amb_report {START_DATE} {END_DATE} start")

    temp_data.init_data()

    brush_daily_report()
    brush_monthly_report()
    brush_quarterly_report()
    brush_weekly_report()
    current_app.logger.warning("brush_bus_amb_report all brush done")


def brush_daily_report():
    from app.models import ReferralHistory
    from app.models.daily import db, DailyReferTypeReport, DailyAmbassadorReferralReport, DailyBusinessAmbassadorTotalReferralReport
    from app.utils import quantize_amount

    daily_end_dt = END_DATE

    global temp_data

    start_date = START_DATE
    process_bar = tqdm(total=(daily_end_dt - start_date).days)

    current_app.logger.warning(f'DailyReferTypeReport start ================')
    # update_daily_refer_type_report
    while start_date <= daily_end_dt:
        next_date = start_date + datetime.timedelta(days=1)

        record: DailyReferTypeReport = DailyReferTypeReport.query.filter(
            DailyReferTypeReport.report_date == start_date,
            DailyReferTypeReport.type == DailyReferTypeReport.Type.AMBASSADOR,
        ).first()
        if record:
            invitee_ee_ids = set()
            for r in temp_data.ref_his_rows:
                if r.created_at.date() < next_date and r.referree_id not in temp_data.market_makers and r.status == ReferralHistory.Status.VALID:
                    invitee_ee_ids.add(r.referree_id)
            active_user_ids = get_date_active_users(start_date)
            record.refer_active_user_count = len(invitee_ee_ids & active_user_ids)
            main_user_balance_usd_map = get_date_user_balance_usd_map(start_date)
            record.refer_user_balance_usd = quantize_amount(sum([main_user_balance_usd_map.get(i, 0) for i in invitee_ee_ids]), 2)
            db.session.add(record)
            db.session.commit()
            current_app.logger.warning(f'DailyReferTypeReport {start_date} '
                                       f'{record.refer_active_user_count} {record.refer_user_balance_usd} done')
            del active_user_ids, invitee_ee_ids, main_user_balance_usd_map
        else:
            current_app.logger.warning(f'DailyReferTypeReport {start_date} not exist, continue')

        start_date = next_date
        process_bar.update(1)
    process_bar.close()
    current_app.logger.warning(f'DailyReferTypeReport end ================')

    current_app.logger.warning(f'DailyAmbassadorReferralReport start ================')
    start_date = START_DATE
    process_bar = tqdm(total=(daily_end_dt - start_date).days)
    # DailyAmbassadorReferralReporter
    while start_date <= daily_end_dt:
        next_date = start_date + datetime.timedelta(days=1)

        record: DailyAmbassadorReferralReport = DailyAmbassadorReferralReport.query.filter(
            DailyAmbassadorReferralReport.report_date == start_date,
        ).first()
        if record:
            amb_ref_ee_ids = set()
            for r in temp_data.ref_his_rows:
                if r.created_at.date() < next_date and r.referree_id not in temp_data.market_makers and r.referrer_id in temp_data.nor_amb_ids:
                    amb_ref_ee_ids.add(r.referree_id)
            active_user_ids = get_date_active_users(start_date)
            main_user_balance_usd_map = get_date_user_balance_usd_map(start_date)
            amb_ref_ee_balance_usd = quantize_amount(sum([main_user_balance_usd_map.get(i, 0) for i in amb_ref_ee_ids]), 2)
            record.refer_user_balance_usd = quantize_amount(amb_ref_ee_balance_usd, 2)
            record.refer_active_user_count = len(amb_ref_ee_ids & active_user_ids)
            db.session.add(record)
            db.session.commit()
            current_app.logger.warning(f'DailyAmbassadorReferralReport {start_date} '
                                       f'{record.refer_user_balance_usd} {record.refer_active_user_count} done')
            del active_user_ids, amb_ref_ee_ids, main_user_balance_usd_map
        else:
            current_app.logger.warning(f'DailyAmbassadorReferralReport {start_date} not exist, continue')

        start_date = next_date
        process_bar.update(1)
    process_bar.close()
    current_app.logger.warning(f'DailyReferTypeReport end ================')

    current_app.logger.warning(f'DailyBusinessAmbassadorTotalReferralReport start ================')
    start_date = START_DATE
    process_bar = tqdm(total=(daily_end_dt - start_date).days)
    # update_business_ambassador_total_report
    while start_date <= daily_end_dt:
        next_date = start_date + datetime.timedelta(days=1)

        records = DailyBusinessAmbassadorTotalReferralReport.query.filter(
            DailyBusinessAmbassadorTotalReferralReport.report_date == start_date,
        ).all()
        log_data = []
        if records:
            active_user_ids = get_date_active_users(start_date)
            main_user_balance_usd_map = get_date_user_balance_usd_map(start_date)
            ref_detail_rows = get_date_ref_details(start_date)
            for record in records:
                record: DailyBusinessAmbassadorTotalReferralReport
                if record.team_id:
                    amb_ids = {i.user_id for i in temp_data.team_ambs_map[record.team_id]}
                else:
                    amb_ids = temp_data.bus_amb_ids
                refer_user_ids = set()
                for r in temp_data.ref_his_rows:
                    if r.created_at.date() < next_date and r.status == ReferralHistory.Status.VALID and r.referrer_id in amb_ids:
                        refer_user_ids.add(r.referree_id)
                amb_ref_ee_balance_usd = quantize_amount(sum([main_user_balance_usd_map.get(i, 0) for i in refer_user_ids]), 2)
                record.refer_user_balance_usd = quantize_amount(amb_ref_ee_balance_usd, 2)
                record.refer_active_user_count = len(active_user_ids & refer_user_ids)
                refer_amb_amount = Decimal()
                for ref_detail in ref_detail_rows:
                    if ref_detail.user_id in amb_ids or \
                            (ref_detail.user_id == ref_detail.referree_id and ref_detail.referree_id in refer_user_ids):
                        refer_amb_amount += (ref_detail.spot_amount + ref_detail.perpetual_amount)
                if refer_amb_amount > record.refer_amb_amount:
                    record.refer_amb_amount = refer_amb_amount
                    new_average_refer_rate = quantize_amount(
                        (record.refer_amb_amount + record.refer_agent_amount) / record.fee_usd * 100, 2
                    ) if record.fee_usd != 0 else 0
                    if new_average_refer_rate > record.average_refer_rate:
                        record.average_refer_rate = new_average_refer_rate
                db.session.add(record)
                del refer_user_ids
                if not record.team_id:
                    log_data = [record.refer_user_balance_usd, record.refer_active_user_count]
            db.session.commit()
            del active_user_ids, main_user_balance_usd_map, ref_detail_rows
        current_app.logger.warning(f'DailyBusinessAmbassadorTotalReferralReport {start_date} {log_data} {len(records)} rows done')
        start_date = next_date
        process_bar.update(1)
    process_bar.close()
    current_app.logger.warning(f'DailyBusinessAmbassadorTotalReferralReport end ================')


def brush_monthly_report():
    from app.models import ReferralHistory
    from app.models.monthly import db, MonthlyReferTypeReport, MonthlyAmbassadorReferralReport, MonthlyBusinessAmbassadorTotalReferralReport
    from app.utils import quantize_amount
    from app.utils.date_ import next_month, cur_month, today

    global temp_data

    monthly_end_dt = END_DATE.replace(day=1)
    yes_day = today() - timedelta(days=1)

    start_date = cur_month(START_DATE.year, START_DATE.month)
    process_bar = tqdm(total=(monthly_end_dt - start_date).days)
    current_app.logger.warning(f'MonthlyReferTypeReport start ================')
    # update_daily_refer_type_report
    while start_date <= monthly_end_dt:
        next_date = next_month(start_date.year, start_date.month)
        next_date_ago_day = min(next_date - timedelta(days=1), yes_day)

        record: MonthlyReferTypeReport = MonthlyReferTypeReport.query.filter(
            MonthlyReferTypeReport.report_date == start_date,
            MonthlyReferTypeReport.type == MonthlyReferTypeReport.Type.AMBASSADOR,
        ).first()
        if record:
            invitee_ee_ids = set()
            for r in temp_data.ref_his_rows:
                if r.created_at.date() < next_date and r.referree_id not in temp_data.market_makers and r.status == ReferralHistory.Status.VALID:
                    invitee_ee_ids.add(r.referree_id)
            active_user_ids = get_date_range_active_users(start_date, next_date_ago_day)
            record.refer_active_user_count = len(invitee_ee_ids & active_user_ids)
            main_user_balance_usd_map = get_date_user_balance_usd_map(next_date_ago_day)
            record.refer_user_balance_usd = quantize_amount(sum([main_user_balance_usd_map.get(i, 0) for i in invitee_ee_ids]), 2)
            db.session.add(record)
            db.session.commit()
            del active_user_ids, invitee_ee_ids, main_user_balance_usd_map
            current_app.logger.warning(f'MonthlyReferTypeReport {start_date} '
                                       f'{record.refer_user_balance_usd} {record.refer_active_user_count} done')
        else:
            current_app.logger.warning(f'MonthlyReferTypeReport {start_date} not exist, continue')

        start_date = next_date
        process_bar.update(1)
    process_bar.close()
    current_app.logger.warning(f'MonthlyReferTypeReport end ================')

    current_app.logger.warning(f'MonthlyAmbassadorReferralReport start ================')
    start_date = START_DATE
    process_bar = tqdm(total=(monthly_end_dt - start_date).days)
    # DailyAmbassadorReferralReporter
    while start_date <= monthly_end_dt:
        next_date = next_month(start_date.year, start_date.month)
        next_date_ago_day = min(next_date - timedelta(days=1), yes_day)

        record: MonthlyAmbassadorReferralReport = MonthlyAmbassadorReferralReport.query.filter(
            MonthlyAmbassadorReferralReport.report_date == start_date,
        ).first()
        if record:
            amb_ref_ee_ids = set()
            for r in temp_data.ref_his_rows:
                if r.created_at.date() < next_date and r.referree_id not in temp_data.market_makers and r.referrer_id in temp_data.nor_amb_ids:
                    amb_ref_ee_ids.add(r.referree_id)
            active_user_ids = get_date_range_active_users(start_date, next_date_ago_day)
            main_user_balance_usd_map = get_date_user_balance_usd_map(next_date_ago_day)
            amb_ref_ee_balance_usd = quantize_amount(sum([main_user_balance_usd_map.get(i, 0) for i in amb_ref_ee_ids]), 2)
            record.refer_user_balance_usd = quantize_amount(amb_ref_ee_balance_usd, 2)
            record.refer_active_user_count = len(amb_ref_ee_ids & active_user_ids)
            db.session.add(record)
            db.session.commit()
            del active_user_ids, amb_ref_ee_ids, main_user_balance_usd_map
            current_app.logger.warning(f'MonthlyAmbassadorReferralReport {start_date} '
                                       f'{record.refer_user_balance_usd} {record.refer_active_user_count}done')
        else:
            current_app.logger.warning(f'MonthlyAmbassadorReferralReport {start_date} not exist,  continue')

        start_date = next_date
        process_bar.update(1)
    process_bar.close()
    current_app.logger.warning(f'MonthlyAmbassadorReferralReport end ================')

    current_app.logger.warning(f'MonthlyBusinessAmbassadorTotalReferralReport start ================')
    start_date = START_DATE
    process_bar = tqdm(total=(monthly_end_dt - start_date).days)
    # update_business_ambassador_total_report
    while start_date <= monthly_end_dt:
        next_date = next_month(start_date.year, start_date.month)
        next_date_ago_day = min(next_date - timedelta(days=1), yes_day)

        records = MonthlyBusinessAmbassadorTotalReferralReport.query.filter(
            MonthlyBusinessAmbassadorTotalReferralReport.report_date == start_date,
        ).all()
        log_data = []
        if records:
            active_user_ids = get_date_range_active_users(start_date, next_date_ago_day)
            main_user_balance_usd_map = get_date_user_balance_usd_map(next_date_ago_day)
            ref_detail_rows = get_date_range_ref_details(start_date, next_date_ago_day)
            for record in records:
                record: MonthlyBusinessAmbassadorTotalReferralReport
                if record.team_id:
                    amb_ids = {i.user_id for i in temp_data.team_ambs_map[record.team_id]}
                else:
                    amb_ids = temp_data.bus_amb_ids
                refer_user_ids = set()
                for r in temp_data.ref_his_rows:
                    if r.created_at.date() < next_date and r.status == ReferralHistory.Status.VALID and r.referrer_id in amb_ids:
                        refer_user_ids.add(r.referree_id)
                amb_ref_ee_balance_usd = quantize_amount(sum([main_user_balance_usd_map.get(i, 0) for i in refer_user_ids]), 2)
                record.refer_user_balance_usd = quantize_amount(amb_ref_ee_balance_usd, 2)
                record.refer_active_user_count = len(active_user_ids & refer_user_ids)
                refer_amb_amount = Decimal()
                for ref_detail in ref_detail_rows:
                    if ref_detail.user_id in amb_ids or \
                            (ref_detail.user_id == ref_detail.referree_id and ref_detail.referree_id in refer_user_ids):
                        refer_amb_amount += (ref_detail.spot_amount + ref_detail.perpetual_amount)
                if refer_amb_amount > record.refer_amb_amount:
                    record.refer_amb_amount = refer_amb_amount
                    new_average_refer_rate = quantize_amount(
                        (record.refer_amb_amount + record.refer_agent_amount) / record.fee_usd * 100, 2
                    ) if record.fee_usd != 0 else 0
                    if new_average_refer_rate > record.average_refer_rate:
                        record.average_refer_rate = new_average_refer_rate
                db.session.add(record)
                if not record.team_id:
                    log_data = [record.refer_user_balance_usd, record.refer_active_user_count]
            db.session.commit()
            del active_user_ids, main_user_balance_usd_map
        current_app.logger.warning(f'MonthlyBusinessAmbassadorTotalReferralReport {start_date} {len(records)} rows {log_data} done')
        start_date = next_date
        process_bar.update(1)
    process_bar.close()
    current_app.logger.warning(f'MonthlyBusinessAmbassadorTotalReferralReport end ================')


def brush_quarterly_report():
    from app.models import ReferralHistory
    from app.models.quarterly import db, QuarterlyBusinessAmbassadorTotalReferralReport
    from app.utils import quantize_amount
    from app.utils.date_ import today

    global temp_data
    yes_day = today() - timedelta(days=1)

    monthly_end_dt = END_DATE.replace(day=1)
    current_app.logger.warning(f'QuarterlyBusinessAmbassadorTotalReferralReport start ================')
    start_date = START_DATE
    process_bar = tqdm(total=(monthly_end_dt - start_date).days)
    # update_business_ambassador_total_report
    while start_date <= monthly_end_dt:
        next_date = start_date + relativedelta(months=3)
        next_date_ago_day = min(next_date - timedelta(days=1), yes_day)

        records = QuarterlyBusinessAmbassadorTotalReferralReport.query.filter(
            QuarterlyBusinessAmbassadorTotalReferralReport.report_date == start_date,
        ).all()
        log_data = []
        if records:
            active_user_ids = get_date_range_active_users(start_date, next_date_ago_day)
            main_user_balance_usd_map = get_date_user_balance_usd_map(next_date_ago_day)
            ref_detail_rows = get_date_range_ref_details(start_date, next_date_ago_day)
            for record in records:
                record: QuarterlyBusinessAmbassadorTotalReferralReport
                if record.team_id:
                    amb_ids = {i.user_id for i in temp_data.team_ambs_map[record.team_id]}
                else:
                    amb_ids = temp_data.bus_amb_ids
                refer_user_ids = set()
                for r in temp_data.ref_his_rows:
                    if r.created_at.date() < next_date and r.status == ReferralHistory.Status.VALID and r.referrer_id in amb_ids:
                        refer_user_ids.add(r.referree_id)
                amb_ref_ee_balance_usd = quantize_amount(sum([main_user_balance_usd_map.get(i, 0) for i in refer_user_ids]), 2)
                record.refer_user_balance_usd = quantize_amount(amb_ref_ee_balance_usd, 2)
                record.refer_active_user_count = len(active_user_ids & refer_user_ids)
                refer_amb_amount = Decimal()
                for ref_detail in ref_detail_rows:
                    if ref_detail.user_id in amb_ids or \
                            (ref_detail.user_id == ref_detail.referree_id and ref_detail.referree_id in refer_user_ids):
                        refer_amb_amount += (ref_detail.spot_amount + ref_detail.perpetual_amount)
                if refer_amb_amount > record.refer_amb_amount:
                    record.refer_amb_amount = refer_amb_amount
                    new_average_refer_rate = quantize_amount(
                        (record.refer_amb_amount + record.refer_agent_amount) / record.fee_usd * 100, 2
                    ) if record.fee_usd != 0 else 0
                    if new_average_refer_rate > record.average_refer_rate:
                        record.average_refer_rate = new_average_refer_rate
                db.session.add(record)
                if not record.team_id:
                    log_data = [record.refer_user_balance_usd, record.refer_active_user_count]
            db.session.commit()
            del active_user_ids, main_user_balance_usd_map, ref_detail_rows
        current_app.logger.warning(f'QuarterlyBusinessAmbassadorTotalReferralReport {start_date} {len(records)} rows {log_data} done')
        start_date = next_date
        process_bar.update(1)
    process_bar.close()
    current_app.logger.warning(f'QuarterlyBusinessAmbassadorTotalReferralReport end ================')


def brush_weekly_report():
    from app.models import ReferralHistory
    from app.models.weekly import db, WeeklyBusinessAmbassadorTotalReferralReport
    from app.utils import quantize_amount
    from app.utils.date_ import today

    global temp_data

    yes_day = today() - timedelta(days=1)
    weekly_end_dt = END_DATE
    current_app.logger.warning(f'WeeklyBusinessAmbassadorTotalReferralReport start ================')
    start_date = START_DATE

    first_week_row = WeeklyBusinessAmbassadorTotalReferralReport.query.filter(
        WeeklyBusinessAmbassadorTotalReferralReport.report_date >= START_DATE
    ).order_by(WeeklyBusinessAmbassadorTotalReferralReport.report_date.asc()).with_entities(
        WeeklyBusinessAmbassadorTotalReferralReport.report_date
    ).first()
    if first_week_row:
        start_date = first_week_row.report_date

    process_bar = tqdm(total=(weekly_end_dt - start_date).days)
    # update_business_ambassador_total_report
    while start_date <= weekly_end_dt:
        next_date = start_date + datetime.timedelta(days=7)
        next_date_ago_day = min(next_date - timedelta(days=1), yes_day)

        records = WeeklyBusinessAmbassadorTotalReferralReport.query.filter(
            WeeklyBusinessAmbassadorTotalReferralReport.report_date == start_date,
        ).all()
        log_data = []
        if records:
            active_user_ids = get_date_range_active_users(start_date, next_date_ago_day)
            main_user_balance_usd_map = get_date_user_balance_usd_map(next_date_ago_day)
            ref_detail_rows = get_date_range_ref_details(start_date, next_date_ago_day)
            for record in records:
                record: WeeklyBusinessAmbassadorTotalReferralReport
                if record.team_id:
                    amb_ids = {i.user_id for i in temp_data.team_ambs_map[record.team_id]}
                else:
                    amb_ids = temp_data.bus_amb_ids
                refer_user_ids = set()
                for r in temp_data.ref_his_rows:
                    if r.created_at.date() < next_date and r.status == ReferralHistory.Status.VALID and r.referrer_id in amb_ids:
                        refer_user_ids.add(r.referree_id)
                amb_ref_ee_balance_usd = quantize_amount(sum([main_user_balance_usd_map.get(i, 0) for i in refer_user_ids]), 2)
                record.refer_user_balance_usd = quantize_amount(amb_ref_ee_balance_usd, 2)
                record.refer_active_user_count = len(active_user_ids & refer_user_ids)
                refer_amb_amount = Decimal()
                for ref_detail in ref_detail_rows:
                    if ref_detail.user_id in amb_ids or \
                            (ref_detail.user_id == ref_detail.referree_id and ref_detail.referree_id in refer_user_ids):
                        refer_amb_amount += (ref_detail.spot_amount + ref_detail.perpetual_amount)
                if refer_amb_amount > record.refer_amb_amount:
                    record.refer_amb_amount = refer_amb_amount
                    new_average_refer_rate = quantize_amount(
                        (record.refer_amb_amount + record.refer_agent_amount) / record.fee_usd * 100, 2
                    ) if record.fee_usd != 0 else 0
                    if new_average_refer_rate > record.average_refer_rate:
                        record.average_refer_rate = new_average_refer_rate
                db.session.add(record)
                if not record.team_id:
                    log_data = [record.refer_user_balance_usd, record.refer_active_user_count]
            db.session.commit()
            del active_user_ids, main_user_balance_usd_map, ref_detail_rows
        current_app.logger.warning(f'WeeklyBusinessAmbassadorTotalReferralReport {start_date} {len(records)} rows {log_data} done')
        start_date = next_date
        process_bar.update(1)
    process_bar.close()
    current_app.logger.warning(f'WeeklyBusinessAmbassadorTotalReferralReport end ================')


if __name__ == '__main__':
    from app import create_app

    with create_app().app_context():
        main()
