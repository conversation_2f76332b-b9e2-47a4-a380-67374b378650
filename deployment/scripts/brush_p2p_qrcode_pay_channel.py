import os
import sys
import time

abspath = os.path.dirname(__file__)
os.chdir(abspath)
os.chdir('../../')
sys.path.append(os.getcwd())


from init_base import app

from app.models import db
from app.common.constants import Language
from app.models.mongo.p2p.pay_channel import P2pPayChannelMySQL, FormModel
from app.models.mongo import Status
from app.caches.p2p import PayChannelCache


pay_channel_text = """GCash
Maya
CoinsPH
UPI
IMPS
Debit Card/credit card
Google Pay (GPay)
Paytm
PhonePe
Mobikwik
Digital eRupee
Amazon Pay
Samsung Wallet (Samsung Pay)
Cred
PayZapp
Opay Digital Services
Moniepoint
Stanbic IBTC
Palm Pay
EasyPaisa
Jazz Cash
UPaisa
NayaPay
Sadapay
Zindagi
Raast
Mcb
Apple Pay
Google Pay
Samsung Pay
ABSA
Bkash
Nagad
Rocket
Neteller
Airtm
Perfect Money
Upay
Credit Card
YooMoney
SBP (система быстрых платежей)
Payeer
GEO Pay
Enpara
Papara
OLDUBIL
Faster Pay
Gpay
Paycell
Ecopayz
FAST
Paysera
Wise
LINE Pay
BenefitPay
stc pay
Kuwait Finance House (KFH)
Cash Wallet
ZainCash
Al-Rafidain QiServices
FastPay
ZainCash - Business
Asia Hawala
NEO Pay Iraq
Switch
Western Union
AirTM
Blu
JoMoPay
U-money
rCBDC
Boubyan
Zain Kuwait
Whish MONEY
Digital payment Adoption
CBDC
HSBC
M Pay
O!Money
CB Pay
QNB
Cash app
Cashpack
Skrill (Moneybookers)
GCash (PH)
Moneygram
Pyypl
urpay
Liv. KSA
Payme.io
InstaPay
Vodafone cash
Banque Misr
Etisalat Cash
Orange Cash
telda
Orange Money - OM
Banque du Caire
Credit Agricole
Instant Transfer
AYA Pay
CashU
Emirates NBD
alBaraka
eZCash
Lemon Cash
Airtime Mobile Top-Up
The TND
CBUEA
ZarinPal
Shepa
trapay
PayPing
NextPay
Jibimo
Pay
RayanPay
BitPay
IdPay
IraniCard
ZaloPay
MoMo
Viettel Money
VNPAY
ShopeePay
Cake
VNPT Money
DANA
OVO
GoPay
ShopeePay（Indonesia）
True Money
Shopee Pay-SEA
Kakao Pay
PayPal
Sumsung Pay
开发测试使用
BBVA
Santander
Bizum
N26
SEPA
Bancolombia
Itaú Colombia
Ualá Colombia
Davivienda
Citibanamex
BBVA Mexico
Banco Azteca
Santander México
STP
Mercado Pago
Mercantil
Banesco
Banco de Venezuela
Bancamiga
BBVA Provincial
Pago Móvil
BTG Pactual
Banco do Brasil
Banco PAN
Caixa Econômica Federal
Bradesco
Itaú Unibanco
Banco Safra
Banco Inter
PicPay
Next
Nomad
Will
BIZUM
ZEN
PAYSERA
SEPA Instant
Revolut
SOFORT
Girocard
Pay Pal
Paypal
Lydia
MTN Mobile Money
Orange money
Moov money
Wave mobile money
Santander（Brazil）
Mercado Pago（Brazil）
Samsung Pay（Brazil）
Google Pay（Brazil）
Apple Pay（Brazil）
Banco Santander
Banco Galicia
Banco del Sol
Ualá
Naranja X
Reba
Prex
Mercado Pago（Argentina）
BBVA（Argentina）
Lemon Cash（Argentina）
HSBC（Argentina）
Orange Money - OM (Arab)
ZainCash(Arab)
CliQ
Asia Hawala
Al-Rafidain QiServices
Samsung Pay（France）
Sohar International
ВТБ
Google Pay（France）
Apple Pay（France）
Flouci
Postepay
Algérie poste - Baridimob
La Banque postale
Alipay（Indonesia）
Touch n Go
Taiwan Pay
Kuvettürk
Pix
Transfermóvil - ETECSA
M-PESA Kenya(Safaricom)
M-PESA Paybill
Airtel money
Momo
Enzona - ETECSA
International Wire (SWIFT)
FPS
PayMe
Zelle
m10
Zinli
Venmo
Cash App"""

lang_qrcode_translation = """
en_us	QR Code
zh_hans_cn	二维码
zh_hant_hk	二維碼
ja_jp	QRコード
ru_kz	QR-код
ko_kp	QR 코드
id_id	Kode QR
es_es	Código QR
fa_ir	کد QR
tr_tr	QR Kodu
vi_vn	Mã QR
ar_ae	رمز الاستجابة السريعة
fr_fr	QR Code
pt_pt	QR Code
de_de	QR Code
th_th	QR Code
it_it	Codice QR
pl_pl	Kod QR
"""


def get_pay_channel_names():
    """解析支付方式文本，返回支付方式名称列表"""
    names = []
    for line in pay_channel_text.strip().split('\n'):
        name = line.strip()
        if name:
            names.append(name)
    return names


def get_qrcode_translations():
    """解析二维码翻译数据"""
    translations = {}
    for line in lang_qrcode_translation.strip().split('\n'):
        if line.strip():
            parts = line.split('\t')
            if len(parts) == 2:
                lang_code = parts[0].strip().upper()
                translation = parts[1].strip()
                translations[lang_code] = translation
    return translations


def generate_qrcode_key():
    """生成二维码字段的key，使用毫秒级时间戳最后8位"""
    timestamp = int(time.time() * 1000)  # 获取毫秒级时间戳
    key = str(timestamp)[-8:]  # 取最后8位
    return key


def create_qrcode_form_field(key, translations):
    """创建二维码表单字段"""

    return FormModel(
        filed_type=FormModel.FiledType.QR_CODE.name,
        key=key,
        name="二维码",  # 默认中文名称
        field_name="二维码",  # 默认中文字段名
        status=Status.VALID.name,
        required=False
    )


def create_lang_data(pay_channel_name, translations, qrcode_key):
    """创建多语言数据"""

    lang_data = {}

    # 为每种语言创建数据
    for lang_code, qrcode_name in translations.items():
        lang_data[lang_code] = {
            "name": pay_channel_name,
            "name_field": "姓名" if lang_code.startswith("ZH") else "Name",
            "form_map": {
                "account": "支付账号" if lang_code.startswith("ZH") else "Payment Account",
                qrcode_key: qrcode_name
            }
        }

    return lang_data


def create_or_update_pay_channel(pay_channel_name, translations):
    """为指定支付方式创建或更新支付渠道，添加二维码字段"""

    # 检查支付渠道是否已存在
    existing_channel = P2pPayChannelMySQL.query.filter(
        P2pPayChannelMySQL.name == pay_channel_name,
        P2pPayChannelMySQL.status == Status.VALID,
        P2pPayChannelMySQL.active_status == P2pPayChannelMySQL.ActiveStatus.ACTIVE
    ).first()

    if not existing_channel:
        print(f"支付渠道 '{pay_channel_name}' 不存在，创建...")
        return None

    # 检查是否已经有二维码字段
    has_qrcode_field = False
    for form_field in existing_channel.form:
        if form_field.filed_type == FormModel.FiledType.QR_CODE.name:
            has_qrcode_field = True
            break

    if has_qrcode_field:
        print(f"支付渠道 '{pay_channel_name}' 已包含二维码字段，跳过")
        return existing_channel
    else:
        print(f"为支付渠道 '{pay_channel_name}' 添加二维码字段...")

        # 生成唯一的二维码字段key
        qrcode_key = generate_qrcode_key()
        print(f"生成的二维码字段key: {qrcode_key}")

        # 创建新的form列表，添加二维码字段
        new_form = list(existing_channel.form)
        qrcode_field = create_qrcode_form_field(key=qrcode_key, translations=translations)
        new_form.append(qrcode_field)

        # 更新语言数据，添加二维码字段的翻译
        updated_lang_data = dict(existing_channel.lang_data or {})
        for lang_code, qrcode_name in translations.items():
            if lang_code not in updated_lang_data:
                continue
            if "form_map" not in updated_lang_data[lang_code]:
                continue
            updated_lang_data[lang_code]["form_map"][qrcode_key] = qrcode_name
        # 更新支付渠道
        existing_channel.form = new_form
        existing_channel.lang_data = updated_lang_data
        from sqlalchemy.orm.attributes import flag_modified
        flag_modified(existing_channel, 'lang_data')
        flag_modified(existing_channel, 'form')
        db.session.flush()
        db.session.commit()

        # 更新缓存
        PayChannelCache.save_one(existing_channel)

        print(f"成功为支付渠道 '{pay_channel_name}' 添加二维码字段 (key: {qrcode_key})")
        return existing_channel


def main():
    """主函数：为所有支付方式添加二维码字段"""
    print("开始为P2P支付渠道添加二维码字段...")

    pay_channel_names = get_pay_channel_names()
    translations = get_qrcode_translations()
    success_count = 0
    error_count = 0

    for i, name in enumerate(pay_channel_names, 1):
        try:
            print(f"\n[{i}/{len(pay_channel_names)}] 处理支付方式: {name}")
            create_or_update_pay_channel(name, translations)
            success_count += 1
        except Exception as e:
            print(f"处理支付方式 '{name}' 时发生错误: {str(e)}")
            error_count += 1
            # 回滚当前事务
            db.session.rollback()

    print(f"\n处理完成！")
    print(f"成功: {success_count} 个")
    print(f"失败: {error_count} 个")
    print(f"总计: {len(pay_channel_names)} 个")


if __name__ == '__main__':
    with app.app_context():
        main()
