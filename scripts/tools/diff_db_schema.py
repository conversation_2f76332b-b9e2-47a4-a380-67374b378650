#!/usr/bin/env python3
import argparse
import pymysql
from typing import Dict, List, Tuple

# ---------- 连接 ----------
def open_conn(dsn: str):
    dsn = dsn.replace("mysql+pymysql://", "")
    user_pass, host_port_db = dsn.split("@")
    user, pwd = user_pass.split(":", 1)
    host_port, db = host_port_db.split("/", 1)
    host, port = host_port.split(":", 1) if ":" in host_port else (host_port, 3306)
    return pymysql.connect(
        host=host,
        port=int(port),
        user=user,
        password=pwd,
        database=db,
        charset="utf8mb4",
    )

# ---------- 模型 ----------
class Column:
    def __init__(self, name: str, type_: str, nullable: bool, default, comment: str):
        self.name = name.lower()
        self.type_ = type_.lower()
        self.nullable = nullable
        self.default = default
        self.comment = comment

    def __eq__(self, other):
        return (
            self.name == other.name
            and self.type_ == other.type_
            and self.nullable == other.nullable
            and self.default == other.default
            and self.comment == other.comment
        )

class Index:
    def __init__(self, name: str, unique: bool, columns: Tuple[str, ...]):
        self.name = name.lower()
        self.unique = unique
        self.columns = tuple(c.lower() for c in columns)

    def __eq__(self, other):
        return (self.unique == other.unique
            and self.columns == other.columns
        )
    
    def __hash__(self):
        return hash((self.unique, self.columns))

class ForeignKey:
    def __init__(
        self,
        name: str,
        cols: Tuple[str, ...],
        ref_table: str,
        ref_cols: Tuple[str, ...],
    ):
        self.name = name.lower()
        self.cols = tuple(cols)
        self.ref_table = ref_table.lower()
        self.ref_cols = tuple(ref_cols)

    def __eq__(self, other):
        return (self.cols == other.cols
            and self.ref_table == other.ref_table
            and self.ref_cols == other.ref_cols
        )
    
    def __hash__(self):
        return hash((self.cols, self.ref_table, self.ref_cols))

class Table:
    def __init__(self, name: str):
        self.name = name.lower()
        self.columns: Dict[str, Column] = {}
        self.indexes: Dict[str, Index] = {}
        self.foreign_keys: Dict[str, ForeignKey] = {}

# ---------- 抓取 ----------
def load_schema(conn, db: str) -> Dict[str, Table]:
    tables: Dict[str, Table] = {}
    cur = conn.cursor(pymysql.cursors.DictCursor)

    # tables
    cur.execute(
        """
        SELECT table_name
        FROM information_schema.tables
        WHERE table_schema = %s
          AND table_type = 'BASE TABLE'
        """,
        (db,),
    )
    for row in cur.fetchall():
        tname = row["TABLE_NAME"]
        tables[tname] = Table(tname)

    # columns
    cur.execute(
        """
        SELECT table_name, column_name, column_type,
               is_nullable, column_default, column_comment
        FROM information_schema.columns
        WHERE table_schema = %s
        """,
        (db,),
    )
    for row in cur.fetchall():
        tname = row["TABLE_NAME"]
        col = Column(
            row["COLUMN_NAME"],
            row["COLUMN_TYPE"],
            row["IS_NULLABLE"].upper() == "YES",
            row["COLUMN_DEFAULT"],
            row["COLUMN_COMMENT"],
        )
        tables[tname].columns[col.name] = col

    # indexes
    cur.execute(
        """
        SELECT table_name, index_name, non_unique, column_name, seq_in_index
        FROM information_schema.statistics
        WHERE table_schema = %s
        ORDER BY table_name, index_name, seq_in_index
        """,
        (db,),
    )
    for row in cur.fetchall():
        tname = row["TABLE_NAME"]
        idx_name = row["INDEX_NAME"]
        if idx_name == "PRIMARY":
            continue
        tbl = tables[tname]
        if idx_name not in tbl.indexes:
            tbl.indexes[idx_name] = Index(
                idx_name, row["NON_UNIQUE"] == 0, [row["COLUMN_NAME"]]
            )
        else:
            lst = list(tbl.indexes[idx_name].columns)
            lst.append(row["COLUMN_NAME"])
            tbl.indexes[idx_name].columns = tuple(lst)

    # foreign keys
    cur.execute(
        """
        SELECT k.constraint_name, k.table_name, k.column_name, k.ordinal_position,
               k.referenced_table_name, k.referenced_column_name
        FROM information_schema.key_column_usage k
        JOIN information_schema.referential_constraints r
          ON k.constraint_name = r.constraint_name
         AND k.table_schema = r.constraint_schema
        WHERE k.table_schema = %s
        ORDER BY k.table_name, k.constraint_name, k.ordinal_position
        """,
        (db,),
    )
    for row in cur.fetchall():
        tname = row["TABLE_NAME"]
        fk_name = row["CONSTRAINT_NAME"]
        tbl = tables[tname]
        if fk_name not in tbl.foreign_keys:
            tbl.foreign_keys[fk_name] = ForeignKey(
                fk_name,
                [row["COLUMN_NAME"]],
                row["REFERENCED_TABLE_NAME"],
                [row["REFERENCED_COLUMN_NAME"]],
            )
        else:
            fk = tbl.foreign_keys[fk_name]
            fk.cols += (row["COLUMN_NAME"],)
            fk.ref_cols += (row["REFERENCED_COLUMN_NAME"],)

    cur.close()
    return tables

# ---------- 对比 ----------
def diff_schema(a: Dict[str, Table], b: Dict[str, Table]):
    sql_lines: List[str] = []

    # 新增 / 删除表
    for tname in a.keys() - b.keys():
        pass
        # sql_lines.append(make_create_table(a[tname]))
    for tname in b.keys() - a.keys():
        if tname == 'alembic_version':
            continue
        sql_lines.append(f"DROP TABLE `{tname}`;")

    # 共同表
    for tname in a.keys() & b.keys():
        sql_lines.extend(diff_table(a[tname], b[tname]))

    return sql_lines

def diff_table(ta: Table, tb: Table) -> List[str]:
    lines = []

    # 列
    for colname in ta.columns.keys() - tb.columns.keys():
        col = ta.columns[colname]
        lines.append(f"ALTER TABLE `{ta.name}` ADD COLUMN {col_ddl(col)};")
    for colname in tb.columns.keys() - ta.columns.keys():
        lines.append(f"ALTER TABLE `{ta.name}` DROP COLUMN `{colname}`;")
    for colname in ta.columns.keys() & tb.columns.keys():
        ca, cb = ta.columns[colname], tb.columns[colname]
        if ca != cb:
            lines.append(f"ALTER TABLE `{ta.name}` MODIFY COLUMN {col_ddl(ca)};")

    # 索引
    ta_ixs = {x.columns: x for x in ta.indexes.values()}
    tb_ixs = {x.columns: x for x in tb.indexes.values()}
    for idxname in ta_ixs.keys() - tb_ixs.keys():
        idx = ta_ixs[idxname]
        lines.append(f"ALTER TABLE `{tb.name}` ADD {index_ddl(idx)};")
    for idxname in tb_ixs.keys() - ta_ixs.keys():
        idx = tb_ixs[idxname]
        lines.append(f"ALTER TABLE `{tb.name}` DROP INDEX `{idx.name}`;")
    for idxname in ta_ixs.keys() & tb_ixs.keys():
        ia, ib = ta_ixs[idxname], tb_ixs[idxname]
        if ia != ib:
            lines.append(f"ALTER TABLE `{tb.name}` DROP INDEX `{ib.name}`, ADD {index_ddl(ia)};")
        elif ia.name != ib.name:
            lines.append(f"ALTER TABLE `{tb.name}` RENAME INDEX `{ib.name}` TO `{ia.name}`;")

    # 外键
    ta_keys = {x.cols: x for x in ta.foreign_keys.values()}
    tb_keys = {x.cols: x for x in tb.foreign_keys.values()}
    for fkname in ta_keys.keys() - tb_keys.keys():
        fk = ta_keys[fkname]
        lines.append(f"ALTER TABLE `{tb.name}` ADD {fk_ddl(fk)};")
    for fkname in tb_keys.keys() - ta_keys.keys():
        fk = tb_keys[fkname]
        lines.append(f"ALTER TABLE `{tb.name}` DROP FOREIGN KEY `{fk.name}`;")
    for fkname in ta_keys.keys() & tb_keys.keys():
        fa, fb = ta_keys[fkname], tb_keys[fkname]
        if fa != fb:
            lines.append(f"ALTER TABLE `{tb.name}` DROP FOREIGN KEY `{fb.name}`, ADD {fk_ddl(fa)};")

    return lines

# ---------- DDL 辅助 ----------
def col_ddl(col: Column) -> str:
    null_part = "NULL" if col.nullable else "NOT NULL"
    default_part = "" if col.default is None else f" DEFAULT {quote(col.default)}"
    comment = quote(col.comment)
    return f"`{col.name}` {col.type_} {null_part}{default_part} COMMENT {comment}"

def index_ddl(idx: Index) -> str:
    unique = "UNIQUE " if idx.unique else ""
    cols = ",".join(f"`{c}`" for c in idx.columns)
    return f"{unique}INDEX `{idx.name}` ({cols})"

def fk_ddl(fk: ForeignKey) -> str:
    cols = ",".join(f"`{c}`" for c in fk.cols)
    ref_cols = ",".join(f"`{c}`" for c in fk.ref_cols)
    return (
        f"CONSTRAINT FOREIGN KEY ({cols}) "
        f"REFERENCES `{fk.ref_table}` ({ref_cols}) "
    )

def quote(v):
    if isinstance(v, str):
        return f"'{v}'"
    return str(v)

def make_create_table(t: Table) -> str:
    col_lines = [col_ddl(c) for c in t.columns.values()]
    idx_lines = [index_ddl(i) for i in t.indexes.values()]
    fk_lines = [fk_ddl(f) for f in t.foreign_keys.values()]
    all_lines = col_lines + idx_lines + fk_lines
    return f"CREATE TABLE `{t.name}` (\n  " + ",\n  ".join(all_lines) + "\n);"

# ---------- CLI ----------
def main():
    parser = argparse.ArgumentParser(description="Compare two MySQL schemas. Make 'to' schema same as 'from' schema.")
    parser.add_argument("--from", dest="from_dsn", required=True)
    parser.add_argument("--to", dest="to_dsn", required=True)
    args = parser.parse_args()

    conn_a = open_conn(args.from_dsn)
    conn_b = open_conn(args.to_dsn)

    a = load_schema(conn_a, args.from_dsn.split("/")[-1])
    b = load_schema(conn_b, args.to_dsn.split("/")[-1])

    print('SET FOREIGN_KEY_CHECKS = 0;')
    sql_lines = diff_schema(a, b)
    for line in sql_lines:
        print(line)

    conn_a.close()
    conn_b.close()


if __name__ == "__main__":
    main()