#!/usr/bin/env bash

cd "$(dirname "$0")" || exit 1

function info() {
    echo -e "\e[01;34m$1\e[0m"
}
function error() {
    echo -e "\e[01;31m$1\e[0m"
    exit 1
}

OS_RELEASE=/etc/os-release
if [[ ! -f "${OS_RELEASE}" ]] || [[ "$(grep -m1 -oP "(?<=ID=)(.*)" "${OS_RELEASE}")" != "ubuntu" ]]; then
    error "This script is for Ubuntu only"
fi

sudo apt update

PY_VERSION=3.11
info "Checking Python version..."
if [[ -x "$(command -v python${PY_VERSION})" ]]; then
    info "  Python ${PY_VERSION} already exists"
else
    info "  Installing Python ${PY_VERSION}..."
    sudo apt install -y software-properties-common
    sudo add-apt-repository ppa:deadsnakes/ppa
    sudo apt update
    sudo apt install -y python${PY_VERSION}
    sudo apt install -y python${PY_VERSION}-dev
    sudo apt install -y gcc libpython3.11-dev python3.11-distutils build-essential automake pkg-config libtool libffi-dev libgmp-dev libsecp256k1-dev libpango-1.0-0 libpangoft2-1.0-0
fi


info "Checking virtualenv..."
if [[ -x "$(command -v virtualenv)" ]]; then
    info "  virtualenv $(virtualenv --version) already exists"
else
    info "  Installing virtualenv..."
    sudo apt install -y virtualenv
fi

VENV_DIR=venv
info "Checking virtual environment..."
if [[ -d "${VENV_DIR}" ]]; then
    info "  Virtual environment already exists in '${VENV_DIR}'"
else
    info "  Setting up virtual environment in '${VENV_DIR}'..."
    virtualenv --python=python${PY_VERSION} "${VENV_DIR}"
    venv_ret=$?
    if [[ ${venv_ret} -ne 0 ]]; then
        rm -fr "${VENV_DIR}"
        error "'virtualenv' exited with status ${venv_ret}"
    fi
fi

info "update pip..."
pip install --upgrade pip

info "Installing requirements..."
curl -sS https://bootstrap.pypa.io/get-pip.py | python3.11
source ${VENV_DIR}/bin/activate && pip install -r requirements.txt

info "Downloading GeoLite2-City..."
curl -L -u 1231420:**************************************** 'https://download.maxmind.com/geoip/databases/GeoLite2-City/download?suffix=tar.gz' -o GeoLite2-City.tar.gz
tar -xzf GeoLite2-City.tar.gz
directory=`ls | xargs -d ' ' | grep GeoLite2-City_* | grep -v tar.gz`
mkdir -p data/geoip/
mv $directory/GeoLite2-City.mmdb data/geoip/
rm -r $directory GeoLite2-City.tar.gz

info "Downloading Font..."
wget "https://file.coinexstatic.com/coinex-fonts.tar.gz" -O coinex-fonts.tar.gz
tar -xzf coinex-fonts.tar.gz
directory=fonts
rm -rf data/fonts/
mkdir -p data/fonts/
mv $directory/* data/fonts/
rm -r $directory coinex-fonts.tar.gz

info "Setup finished!"
