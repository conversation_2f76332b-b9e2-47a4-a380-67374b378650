from enum import Enum

from collections import namedtuple

from app.utils.onchain import normalise_time


class Chain(Enum):
    SOL = 'sol'
    ERC20 = 'erc20'
    BSC = 'bsc'

    def get_display_name(self):
        return {self.ERC20: 'ETH'}.get(self, self.name)

    @classmethod
    def get_display_name_list(cls):
        return [i.get_display_name() for i in cls]

    @classmethod
    def display_name_to_enum(cls, display_name: str):
        return cls[{'ETH': cls.ERC20.name}.get(display_name, display_name)]

    def get_display_full_name(self):
        return {
            self.SOL: 'Solana',
            self.ERC20: 'Ethereum(ERC20)',
            self.BSC: 'BNB Smart Chain(BEP20)',
        }[self]


CHAIN_MONEY_MAPPING = {
    Chain.SOL: {
        'SOL': {'name': 'Solana', 'contract': '', 'decimals': 9, 'is_native_token': True},
        'USDT': {'name': 'Tether', 'contract': 'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB', 'decimals': 6},
    },
    Chain.ERC20: {
        'ETH': {'name': 'Ethereum', 'contract': '', 'decimals': 18, 'is_native_token': True},
        'USDT': {'name': 'Tether', 'contract': '******************************************', 'decimals': 6},
    },
    Chain.BSC: {
        'BNB': {'name': 'Binance Coin', 'contract': '', 'decimals': 18, 'is_native_token': True},
        'USDT': {'name': 'Tether', 'contract': '******************************************', 'decimals': 6},
    },
}

CHAIN_MAIN_COIN_MAPPER = {
    Chain.ERC20: 'ETH',
    Chain.BSC: 'BNB',
    Chain.SOL: 'SOL',
}

CHAIN_EXCHANGER_ID_MAPPING = {
    Chain.SOL: ['jupiter', 'raydium'],  # 询价结果列表根据支持兑换商的索引排序，优先级 jupiter > raydium
    Chain.BSC: ['1inch'],
    Chain.ERC20: ['1inch']
}

class OrderSide(Enum):
    BUY = 'buy'
    SELL = 'sell'


class KlinePeriod(Enum):
    MIN = '1m'
    HOUR = '1h'
    DAY = '1d'

    @property
    def seconds(self) -> int:
        return {
            self.MIN: 60,
            self.HOUR: 3600,
            self.DAY: 86400,
        }[self]

    @property
    def expired_seconds(self) -> int:
        """最新K线时间超过该阈值即认为K线缓存过期"""
        return {
            self.MIN: 604800,  # 1周
            self.HOUR: 7776000,  # 3个月
            self.DAY: 94608000,  # 3年
        }[self]

    @property
    def delete_old_key_seconds(self) -> int:
        """定时删除超过这个秒数的K线Key(参考现货线上配置)"""
        return {
            self.MIN: 2592000,  # 1个月
            self.HOUR: 63072000,  # 2年
            self.DAY: 315360000,  # 10年
        }[self]

    def normalise_time(self, t: int, is_end_time: bool = False) -> int:
        return normalise_time(self.seconds, t, is_end_time)


TokenData = namedtuple(
    'TokenData',
    [
        'chain', 'contract', 'top_pool', 'symbol', 'name', 'decimals', 'logo', 'total_supply', 'circulating_supply',
        'amount_24h', 'price', 'change_24h', 'highest_24h', 'lowest_24h',
        'top_pool_launch_time', 'top_pool_name', 'top_pool_liquidity',
    ],
)

TokenBase = namedtuple(
    'TokenBase',
    ['chain', 'contract', 'symbol', 'name', 'decimals', 'logo'],
)

TokenInfo = namedtuple(
    'TokenInfo',
    ['chain', 'contract', 'website', 'community', 'about', 'holders_count', 'top10_percentage'],
)

TokenQuote = namedtuple(
    'TokenQuote',
    ['chain', 'contract', 'amount_24h', 'price', 'change_24h', 'highest_24h', 'lowest_24h'],
)

TokenKline = namedtuple(
    'TokenKline',
    ['t', 'o', 'h', 'l', 'c', 'v']
)
