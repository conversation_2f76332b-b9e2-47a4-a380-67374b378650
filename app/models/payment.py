# -*- coding: utf-8 -*-
from enum import Enum
from decimal import Decimal

from app.models.base import db, ModelBase
from app.utils import quantize_amount


class PaymentQrcode(ModelBase):
    """ 用户收款码 """

    __table_args__ = (db.Index('idx_user_id_status_asset', 'user_id', 'status', 'asset'),)

    class Status(Enum):
        VALID = "valid"
        INVALID = "invalid"

    id = db.Column(db.BigInteger, primary_key=True)
    code = db.Column(db.String(64), nullable=False, unique=True)  # 用于和前端交互，返回给前端用于生成二维码 或 前端用它来获取信息
    user_id = db.Column(db.Integer, nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID)
    asset = db.Column(db.String(32), nullable=False, index=True)  # 必填
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=True)  # 选填
    remark = db.Column(db.String(1024), nullable=True)  # 选填


class PaymentTransaction(ModelBase):
    """ 用户付款｜收款 记录表 """

    class Type(Enum):
        NORMAL = "普通付款"

    class Status(Enum):
        CREATED = '待扣款'  # 付款人待扣款
        PROCESSING = '处理中'  # 付款人已扣款，收款人增加资产中
        FAILED = '扣款失败'  # 付款人余额不足
        FINISHED = '已完成'  # 收款人已收款

    payer_id = db.Column(db.Integer, nullable=False, index=True)  # 付款人
    pay_asset = db.Column(db.String(32), nullable=False, index=True)  # 付款币种
    pay_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 付款币种数目
    pay_fee_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 付款币-手续费，收付款币种

    receiver_id = db.Column(db.Integer, nullable=False, index=True)  # 收款人
    receive_asset = db.Column(db.String(32), nullable=False, index=True)  # 收款币种，如果 pay_asset != receive_asset 则需要写对冲记录
    receive_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 收款数目

    type = db.Column(db.StringEnum(Type), nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, index=True, default=Status.CREATED)
    remark = db.Column(db.String(128), nullable=False, default='')
    finished_at = db.Column(db.MYSQL_DATETIME_6, index=True)  # 完成时间（收款完成时间）

    @property
    def is_diff_asset(self) -> bool:
        return self.pay_asset != self.receive_asset

    @property
    def is_finished(self) -> bool:
        return self.status == self.Status.FINISHED


class PaymentTransferHistory(ModelBase):
    """ 用户收付款-划转记录 """

    __table_args__ = (
        db.UniqueConstraint("transaction_id", "type", name="transaction_id_type_unique"),
    )

    class Type(Enum):
        TRANSFER_PAY_ASSET = "划转付款币种"
        TRANSFER_RECEIVE_ASSET = "划转收款币种"

    class Status(Enum):
        CREATED = "待划转"
        DEDUCTED = "已扣减"
        FAILED = "失败"
        FINISHED = "已完成"

    transaction_id = db.Column(db.Integer, nullable=False)
    user_id = db.Column(db.Integer, nullable=False, index=True)
    type = db.Column(db.StringEnum(Type), nullable=False)
    asset = db.Column(db.String(32), nullable=False, index=True)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED)
    deducted_at = db.Column(db.MYSQL_DATETIME_6)
    finished_at = db.Column(db.MYSQL_DATETIME_6)


class PaymentAssetHedgingAmount(ModelBase):
    """ 收付款币种-对冲数目（实时状态值，记账：当前垫付数目） """

    asset = db.Column(db.String(32), nullable=False, unique=True)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    # 新增对冲记录，对应target_asset记录 amount += PaymentAssetHedgingHistory.target_amount
    # 完成对冲记录，对应target_asset记录 amount -= PaymentAssetHedgingHistory.target_filled_amount


class PaymentAssetHedgingAmountChangeHistory(ModelBase):
    """ 收付款币种-对冲数目-变更历史
    会先写ChangeHistory，再异步更新PaymentAssetHedgingAmount。
    不直接更新PaymentAssetHedgingAmount 避免要处理并发更新
    """

    class Status(Enum):
        CREATED = 'created'
        FINISHED = 'finished'

    class ChangeType(Enum):
        ADD = '增加'
        SUB = '减少'

    class BizType(Enum):
        CREATE_HEDGING_HIS = '新增对冲记录'
        FINISHED_HEDGING_HIS = '完成对冲记录'

    POSITIVE_CHANGE_TYPES = (ChangeType.ADD, )
    NEGATIVE_CHANGE_TYPES = (ChangeType.SUB, )

    asset = db.Column(db.String(32), nullable=False, index=True)
    change_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 本次变更的数目
    after_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 本次变更之后的数目
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED)
    change_type = db.Column(db.StringEnum(ChangeType), nullable=False)
    biz_id = db.Column(db.Integer, nullable=False, index=True)  # 执行变更操作的业务id，比如对冲记录ID
    biz_type = db.Column(db.StringEnum(BizType), nullable=False)
    remark = db.Column(db.String(128), nullable=False, default='')


class PaymentAssetHedgingHistory(ModelBase):
    """ 收付款币种-对冲记录 """

    __table_args__ = (
        db.Index('idx_status_source_target', 'status', 'source_asset', 'target_asset'),
        db.Index('idx_status_target_source', 'status', 'target_asset', 'source_asset'),
    )

    class Status(Enum):
        CREATED = "待对冲"  # 等待 分配系统子帐号，并生成SysAssetFixedExchangeHistory
        PROCESSING = "处理中"  # 等待 划转资产到系统子账号
        EXCHANGING = "兑换中"  # 兑换中
        FINISHED = "已完成"

    transaction_id = db.Column(db.Integer, nullable=False, unique=True)

    # 源币种、可交易数目、实际交易数目
    # source_filled_amount <= source_amount
    source_asset = db.Column(db.String(32), nullable=False, index=True)
    source_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # PayTransaction.pay_amount
    source_filled_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)

    # 目标币种、期望得到数目、实际交易得到数目
    target_asset = db.Column(db.String(32), nullable=False, index=True)
    target_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    target_filled_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)

    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED)
    finished_at = db.Column(db.MYSQL_DATETIME_6, index=True)

    @property
    def profit_loss_data(self) -> dict[str, Decimal]:
        """利润或亏损的数目"""
        # target_filled_amount >= target_amount，则表示有利润
        # 利润 = (source_amount - source_filled_amount) + (target_filled_amount - target_amount)
        # target_filled_amount < target_amount，则表示有亏损，此时source_amount一定用完了
        # 亏损 = target_amount - target_filled_amount
        source_pl = self.source_amount - self.source_filled_amount  # 利润1 兑换币种剩余
        target_pl = self.target_filled_amount - self.target_amount  # 利润2 超出的目标币种
        return {
            self.source_asset: source_pl,
            self.target_asset: target_pl,
        }


class PaymentAssetHedgingTransferHistory(ModelBase):
    """ 对冲记录-划转记录 """

    class Type(Enum):
        TRANSFER_PAY_ASSET = "划转付款币种到子账号"  # 只增加
        HEDGE_RECEIVE_ASSET = "扣减子帐号收款币种"  # 只扣减
        TRANSFER_PROFIT_ASSET = "划转子帐号盈利币种"  # 从子账号划转到用户0

    class Status(Enum):
        CREATED = "待划转"
        DEDUCTED = "已扣减"
        FAILED = "失败"
        FINISHED = "已完成"

    hedging_his_id = db.Column(db.Integer, nullable=False, index=True)
    user_id = db.Column(db.Integer, nullable=False, index=True)  # sys_user_id
    type = db.Column(db.StringEnum(Type), nullable=False)
    asset = db.Column(db.String(32), nullable=False, index=True)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED)
    deducted_at = db.Column(db.MYSQL_DATETIME_6)
    finished_at = db.Column(db.MYSQL_DATETIME_6)


class AssetFixedExchangeSysUser(ModelBase):
    """系统-Fixed兑换-子帐号"""

    class Status(Enum):
        USABLE = "可用"
        UNUSABLE = "不可用"

    user_id = db.Column(db.Integer, unique=True)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.USABLE, index=True)


class SysAssetFixedExchangeHistory(ModelBase):
    """系统-Fixed兑换记录（使用源币种兑换，直到买够目标资产数量 或 源币种用完）"""

    __table_args__ = (
        db.UniqueConstraint("biz_id", "biz_type", name="biz_id_type_unique"),
    )

    class BizType(Enum):
        PAYMENT_HEDGING = "付款对冲"

    class Status(Enum):
        CREATED = "待兑换"
        EXCHANGING = "兑换中"
        FINISHED = "已完成"

    biz_id = db.Column(db.Integer, nullable=False)
    biz_type = db.Column(db.StringEnum(BizType), nullable=False)
    sys_user_id = db.Column(db.Integer, index=True, nullable=False)  # 系统子账号, AssetFixedExchangeSysUser.user_id

    # 兑换相关字段
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED, index=True)
    source_asset_data = db.Column(db.MYSQL_JSON, nullable=False)  # 要卖出的币种和数目 {"CET": Decimal("300")}
    target_asset_data = db.Column(db.MYSQL_JSON, nullable=False)  # 要买入的币种和数目 {"USDT": Decimal("10")}
    fee_data = db.Column(db.MYSQL_JSON, nullable=True)  # 手续费数据
    started_at = db.Column(db.MYSQL_DATETIME_6, nullable=True)  # 兑换开始时间
    finished_at = db.Column(db.MYSQL_DATETIME_6, nullable=True)  # 兑换完成时间
    started_balance_data = db.Column(db.MYSQL_JSON, nullable=True)  # 兑换开始时的余额信息
    finished_balance_data = db.Column(db.MYSQL_JSON, nullable=True)  # 兑换完成时的余额信息

    def get_asset_exchanged_data(
        self,
        finished_balance_data: dict[str, Decimal] = None,
    ) -> tuple[dict[str, Decimal], dict[str, Decimal], dict[str, Decimal]]:
        """ 返回 已使用的source币种和数目，交易得到的target币种和数目，盈利币种数目 """
        finished_balance_data = finished_balance_data or self.finished_balance_data
        assert self.started_balance_data and finished_balance_data
        source_asset_data = {k: Decimal(v) for k, v in self.source_asset_data.items()}
        target_asset_data = {k: Decimal(v) for k, v in self.target_asset_data.items()}
        started_balance_data = {k: Decimal(v) for k, v in self.started_balance_data.items()}
        finished_balance_data = {k: Decimal(v) for k, v in finished_balance_data.items()}

        used_source_data = {}
        filled_target_data = {}
        profit_data = {}
        for s_asset, s_amount in source_asset_data.items():
            start_amount = started_balance_data.get(s_asset, Decimal())
            fin_amount = finished_balance_data.get(s_asset, Decimal())
            used_amount = max(quantize_amount(start_amount - fin_amount, 8), Decimal())
            used_amount = min(s_amount, used_amount)  # 最多用s_amount
            used_source_data[s_asset] = used_amount
        for t_asset, t_amount in target_asset_data.items():
            start_amount = started_balance_data.get(t_asset, Decimal())
            fin_amount = finished_balance_data.get(t_asset, Decimal())
            filled_amount = max(quantize_amount(fin_amount - start_amount, 8), Decimal())
            filled_amount = min(filled_amount, t_amount)  # 最多买t_amount
            filled_target_data[t_asset] = filled_amount
        for b_asset, b_amount in finished_balance_data.items():
            if b_asset in filled_target_data:
                # 去掉要买入的目标币种数目，多出的余额都是盈利
                profit_amount = b_amount - filled_target_data[b_asset]
            else:
                profit_amount = b_amount
            profit_amount = quantize_amount(profit_amount, 8)
            if profit_amount > 0:
                profit_data[b_asset] = profit_amount
        return used_source_data, filled_target_data, profit_data


class SysAssetFixedExchangeOrderHistory(ModelBase):
    """系统-Fixed兑换-交易过程中的订单"""

    exchange_id = db.Column(db.Integer, nullable=False, index=True)
    market = db.Column(db.String(32), nullable=False, index=True)
    order_id = db.Column(db.BigInteger, unique=True, nullable=False)
    side = db.Column(db.Integer, nullable=False)
    type = db.Column(db.Integer, nullable=False)  # 订单类型, 市价单或者限价单
    price = db.Column(db.MYSQL_DECIMAL_PRICE, nullable=False)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    price_deviation = db.Column(db.MYSQL_DECIMAL_PRICE, nullable=False)  # price基于最新价的价格偏差
