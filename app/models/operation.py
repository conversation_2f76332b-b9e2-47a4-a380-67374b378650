# -*- coding: utf-8 -*-

from __future__ import annotations

import json
from collections import defaultdict
from datetime import datetime, timedelta
from decimal import Decimal
from enum import Enum
from functools import cached_property
from typing import List, Set, Dict
from urllib.parse import urljoin

from flask_babel import gettext
from pyroaring import BitMap
from sqlalchemy import func, or_

from .base import ModelBase, db
from .system import AppJumpList
from ..common import Language, TradeBusinessType, TradeType, Media, COUNTRY_CODE_CN_NAME_DIC
from ..common.push import PushBroadcastLimitGroup
from ..config import config
from ..exceptions import InvalidArgument
from ..utils import (new_hex_token, now, AWSBucketPublic, format_percent, quantize_amount)
from ..utils.date_ import today, datetime_to_str, timestamp_to_datetime


class PushUrlConfig(ModelBase):
    class Business(Enum):
        EMAIL_PUSH = 'email_push'

    business = db.Column(db.Enum(Business), nullable=False)
    url = db.Column(db.String(8192), nullable=False)
    remark = db.Column(db.String(256), nullable=False, default='')

    def get_target_url(self, business_id: int) -> str:
        base_url = urljoin(config["SITE_URL"], "/res/short_links/redirect")
        token = '{{user_token}}'
        url_params = f'?token={token}&business={self.business.name}&business_id={business_id}&url_id={self.id}'
        return base_url + url_params


class EmailPush(ModelBase):
    class PushType(Enum):
        SYSTEM = '系统默认'
        ANNOUNCEMENT = '上新公告'
        ACTIVITY = '福利活动'
        BLOG = '博客'

    class PushScopeType(Enum):
        EXCLUDE_UNSUBSCRIBE = '不含退订用户'
        INCLUDE_UNSUBSCRIBE = '含退订用户'

    class PushTimeType(Enum):
        REALTIME = '即时'
        TIMING = '定时'

    class UserType(Enum):
        ALL_USER = '全部用户'
        TARGET_USER = '定向用户'

    class Type(Enum):
        OFFLINE_ASSET = '下架资产'
        NORMAL_PUSH = '普通推送'

    class Status(Enum):
        DRAFT = '未提交'
        CREATED = '待审核'
        AUDITED = '待推送'
        REJECTED = '未通过'
        PROCESSING = '处理中'
        FINISHED = '已推送'
        CANCELLED = '已取消'
        DELETED = '已删除'
        FAILED = '推送失败'

    DUE_LIMIT = timedelta(days=3)

    name = db.Column(db.String(512), nullable=False, index=True)
    push_type = db.Column(db.Enum(PushType), nullable=False)
    type = db.Column(db.StringEnum(Type), default=Type.NORMAL_PUSH)
    # 仅 PushType.SYSTEM，push_scope_type 才有意义
    push_scope_type = db.Column(db.Enum(PushScopeType), nullable=False, default=PushScopeType.EXCLUDE_UNSUBSCRIBE)
    push_time_type = db.Column(db.Enum(PushTimeType), nullable=False)
    push_time = db.Column(db.MYSQL_DATETIME_6, nullable=False, default=now, index=True)
    remark = db.Column(db.String(512), nullable=False, default='')
    user_type = db.Column(db.Enum(UserType), nullable=False)
    # 迁移客群后保留（仅历史记录 admin 前端展示使用
    user_filters = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False, default='')
    # 格式：[用户分群 id]
    groups = db.Column(db.TEXT, nullable=False, default='')
    # 格式：'1370,250'
    whitelist_enabled = db.Column(db.Boolean, nullable=False, default=False)
    user_whitelist = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False, default='')
    user_count = db.Column(db.Integer, nullable=False, default=0, comment='客群数')
    # 使用 bitmap 存储用户 id
    user_ids = db.Column(db.MYSQL_MEDIUM_BLOB, nullable=False, default=b'', comment='客群数 id')
    subscribe_count = db.Column(db.Integer, nullable=False, default=0, comment='客群中订阅数')
    user_read_count = db.Column(db.Integer, nullable=False, default=0, comment='邮件打开数')
    user_click_count = db.Column(db.Integer, nullable=False, default=0, comment='去重的链接点击数')
    unsubscribe_count = db.Column(db.Integer, nullable=False, default=0, comment='邮件退订数')
    # 格式：[1, 2, 3] - PushUrlConfig
    urls = db.Column(db.String(512), nullable=False, default='', comment='关联 urls')
    status = db.Column(db.Enum(Status), nullable=False, default=Status.DRAFT)

    created_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True, index=True)
    audited_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)
    audited_at = db.Column(db.MYSQL_DATETIME_6)
    auditor_remark = db.Column(db.String(512), nullable=False, default='')
    finished_at = db.Column(db.MYSQL_DATETIME_6)

    # 若设置，则按参数中的模板进行推送，否则按默认方式推送
    # 格式：{'business': business.name, 'template_kwargs': {user_id: {**business_kwargs}}}
    system_template_kwargs = db.Column(db.TEXT, nullable=True, default='', comment='系统自动设置的邮件模板相关参数')

    def get_system_template_kwargs(self) -> {}:
        if not self.system_template_kwargs:
            return {}
        ret = json.loads(self.system_template_kwargs)
        ret['template_kwargs'] = {int(k): v for k, v in ret['template_kwargs'].items()}
        return ret

    @classmethod
    def list_pushable(cls) -> List[EmailPush]:
        _now = now()
        return cls.query.filter(
            cls.status == cls.Status.AUDITED,
            cls.push_time <= _now,
            cls.push_time > _now - cls.DUE_LIMIT
        ).all()

    def get_target_users(self) -> Set[int]:
        if self.type == EmailPush.Type.OFFLINE_ASSET:
            return set(self.cached_user_ids)
        return self.subscribe_users

    def update_users(self):
        if self.type == EmailPush.Type.OFFLINE_ASSET:
            return
        self.user_count = len(self.users)
        self.subscribe_count = len(self.subscribe_users)

    @property
    def users(self) -> Set[int]:
        """客群用户"""
        if self.type == EmailPush.Type.OFFLINE_ASSET:
            return set(self.cached_user_ids)
        return self.parsed_email_push.parsed_user_ids

    @property
    def subscribe_users(self) -> Set[int]:
        """订阅用户"""
        return self.parsed_email_push.parsed_subscribe_user_ids

    @cached_property
    def parsed_email_push(self):
        from app.business.push_statistic import EmailPushUserParser
        parser = EmailPushUserParser(self)
        parser.parse()
        return parser

    def user_read_rate(self, count) -> str:
        """邮件打开率"""
        if not self.subscribe_count:
            return '0%'
        amount = Decimal(count / self.subscribe_count)
        return format_percent(amount, 2)

    @property
    def user_unsubscribe_rate(self) -> str:
        """邮件退订率"""
        if not self.subscribe_count:
            return '0%'
        amount = Decimal(self.unsubscribe_count / self.subscribe_count)
        return format_percent(amount, 2)

    def user_click_rate(self, count) -> str:
        """链接点击率"""
        if not self.subscribe_count:
            return '0%'
        amount = Decimal(count / self.subscribe_count)
        return format_percent(amount, 2)

    @cached_property
    def cached_user_whitelist(self) -> list:
        if not self.user_whitelist:
            return []
        return [int(user_id) for user_id in self.user_whitelist.split(',')]

    @cached_property
    def cached_user_filters(self) -> list:
        if not self.user_filters:
            return []
        return json.loads(self.user_filters)

    @cached_property
    def cached_url_ids(self) -> list:
        if not self.urls:
            return []
        return json.loads(self.urls)

    @cached_property
    def cached_user_ids(self) -> list:
        """客群 user_ids"""
        if not self.user_ids:
            return []
        bm = BitMap.deserialize(self.user_ids)
        if not bm:
            return []
        return list(bm)

    def set_user_ids(self, user_ids: list):
        """设置客群 user_ids"""
        self.user_ids = BitMap(user_ids).serialize()

    def get_groups(self):
        if not self.groups:
            return []
        return json.loads(self.groups)


class EmailPushContent(db.Model):
    AVAILABLE_LANGS = (Language.EN_US, Language.ZH_HANS_CN, Language.ZH_HANT_HK,
                       Language.JA_JP, Language.KO_KP, Language.RU_KZ, Language.ES_ES,
                       Language.ID_ID, Language.FA_IR, Language.VI_VN, Language.TR_TR,
                       Language.AR_AE, Language.FR_FR, Language.DE_DE, Language.PT_PT,
                       Language.TH_TH, Language.PL_PL, Language.IT_IT
                       )

    id = db.Column(db.Integer, primary_key=True)
    email_push_id = db.Column(db.Integer, db.ForeignKey('email_push.id'), nullable=False)
    lang = db.Column(db.StringEnum(Language), nullable=False)
    title = db.Column(db.Text, nullable=False)
    content = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False)

    email_push = db.relationship(
        'EmailPush', backref=db.backref('contents', lazy='dynamic'))

    __table_args__ = (
        db.UniqueConstraint('email_push_id', 'lang', name='email_push_lang_uniq'),
    )


class EmailPushUnsubscription(db.Model):
    class Status(Enum):
        CREATED = 'created'
        FINISHED = 'finished'

    id = db.Column(db.Integer, primary_key=True)
    created_at = db.Column(db.MYSQL_DATETIME_6, default=datetime.utcnow)
    finished_at = db.Column(db.MYSQL_DATETIME_6)
    user_id = db.Column(db.Integer, nullable=False, index=True)
    token = db.Column(db.String(128), nullable=False, unique=True)
    status = db.Column(db.Enum(Status), nullable=False, default=Status.CREATED)

    push_id = db.Column(db.Integer, index=True, nullable=True)

    __table_args__ = (
        db.Index('finished_at_asc_idx', finished_at.asc()),
    )

    @classmethod
    def get_or_new(cls, user_id: int, push_id: int = None) -> str:
        if push_id is not None:
            query = cls.query.filter(cls.user_id == user_id, cls.push_id == push_id)
            row = query.first()
            if row:
                return row.token
            token = new_hex_token(64)
            row = cls(user_id=user_id, token=token, push_id=push_id)
            db.session.add(row)
            db.session.commit()
            return token

        query = cls.query.filter(cls.user_id == user_id, cls.push_id.isnot(None))
        row = query.first()
        if row:
            return row.token
        token = new_hex_token(64)
        obj = cls(user_id=user_id, token=token)
        db.session.add(obj)
        db.session.commit()
        return token


class AssetMarketOfflineEmailPush(ModelBase):
    """下架币种/市场邮件推送"""
    """
    作用是在EmailPush基础上拓展一些字段，实际的推送仍是基于EmailPush完成的
    """

    class Type(Enum):
        ASSET = '币种'
        MARGIN_MARKET = '杠杆市场'
        PERPETUAL_MARKET = '合约市场'
        AMM_MARKET = 'AMM市场'

        PLEDGE_LOAN_ASSET = '下架借贷-借币'
        PLEDGE_ASSET = '下架借贷-质押币'

    push_id = db.Column(db.Integer, db.ForeignKey('email_push.id'), nullable=False, unique=True)
    type = db.Column(db.StringEnum(Type), nullable=False)  # key
    # 下架币种/市场列表
    key = db.Column(db.String(1024), nullable=False)  # value
    # Bitmap, 保存所有符合条件的用户, 实际的计划推送用户存在EmailPush表中
    user_ids = db.Column(db.MYSQL_MEDIUM_BLOB, nullable=False, default=b'')
    started_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    ended_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)

    @cached_property
    def cached_user_ids(self) -> list:
        """客群 user_ids"""
        if not self.user_ids:
            return []
        bm = BitMap.deserialize(self.user_ids)
        if not bm:
            return []
        return list(bm)

    def set_user_ids(self, user_ids: list):
        """设置客群 user_ids"""
        self.user_ids = BitMap(user_ids).serialize()


class AppPush(ModelBase):
    class PushType(Enum):
        SYSTEM = '系统默认'
        ANNOUNCEMENT = '上新公告'
        ACTIVITY = '福利活动'
        BLOG = '博客'
        INFORMATION = '资讯'

    class UserType(Enum):
        TARGET_USER = '定向用户'
        ALL_USER = '全部用户'

    class Status(Enum):
        DRAFT = '未提交'
        CREATED = '待审核'
        AUDITED = '待推送'
        REJECTED = '未通过'
        PROCESSING = '推送中'
        FINISHED = '已推送'
        CANCELLED = '已取消'
        SUSPENDING = '暂停中'
        SUSPENDED = '已暂停'
        DELETED = '已删除'
        FAILED = '推送失败'

    class PushScopeType(Enum):
        EXCLUDE_UNSUBSCRIBE = '不含退订用户'
        INCLUDE_UNSUBSCRIBE = '含退订用户'

    # 结合推送任务的ttl，一天没有推送成功的不再推送，避免推送任务重入
    DUE_LIMIT = timedelta(days=1)

    name = db.Column(db.String(512), nullable=False, index=True)
    # 仅 PushType.SYSTEM，push_scope_type 才有意义
    push_scope_type = db.Column(db.StringEnum(PushScopeType), nullable=False, default=PushScopeType.EXCLUDE_UNSUBSCRIBE)
    push_type = db.Column(db.StringEnum(PushType), nullable=False)
    push_time = db.Column(db.MYSQL_DATETIME_6, nullable=False, default=now, index=True)  # sub.. at
    remark = db.Column(db.String(512), nullable=False, default='')
    user_type = db.Column(db.StringEnum(UserType), nullable=False)
    # 迁移客群后保留（仅历史记录 admin 前端展示使用
    user_filters = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False, default='')
    # 格式：[用户分群 id]
    groups = db.Column(db.TEXT, nullable=False, default='')
    jump_id = db.Column(db.Integer, db.ForeignKey('app_jump_list.id'), nullable=False)
    ttl = db.Column(db.Integer, nullable=True)

    # 格式：'1370,250'
    whitelist_enabled = db.Column(db.Boolean, nullable=False, default=False)
    user_whitelist = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False, default='')
    user_count = db.Column(db.Integer, nullable=False, default=0, comment='客群数')
    send_user_count = db.Column(db.Integer, nullable=False, default=0, comment='送达客群数')
    subscribe_count = db.Column(db.Integer, nullable=False, default=0, comment='客群中订阅数')  # 订阅数
    # 使用 bitmap 存储用户 id
    user_ids = db.Column(db.MYSQL_MEDIUM_BLOB, nullable=False, default=b'')
    send_user_ids = db.Column(db.MYSQL_MEDIUM_BLOB, nullable=False, default=b'')
    user_read_count = db.Column(db.Integer, nullable=False, default=0, comment='邮件打开数')
    # 格式：[1, 2, 3] - PushUrlConfig
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.DRAFT)

    created_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True, index=True)
    audited_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)
    audited_at = db.Column(db.MYSQL_DATETIME_6)
    auditor_remark = db.Column(db.String(512), nullable=False, default='')

    def get_groups(self):
        if not self.groups:
            return []
        return json.loads(self.groups)

    @classmethod
    def list_pushable(cls) -> List[AppPush]:
        _now = now()
        return cls.query.filter(
            cls.status == cls.Status.AUDITED,
            cls.push_time <= _now,
            cls.push_time > _now - cls.DUE_LIMIT
        ).all()

    @classmethod
    def list_processing_pushable(cls) -> List[AppPush]:
        _now = now()
        return cls.query.filter(
            cls.status == cls.Status.PROCESSING,
            cls.push_time <= _now,
            cls.push_time > _now - cls.DUE_LIMIT
        ).all()

    @property
    def users(self) -> Set[int]:
        """客群用户"""
        return self.parsed_app_push.parsed_user_ids

    @property
    def subscribe_users(self) -> Set[int]:
        """订阅用户"""
        return self.parsed_app_push.parsed_subscribe_user_ids

    @cached_property
    def parsed_app_push(self):
        from app.business.push_statistic import AppPushUserParser
        parser = AppPushUserParser(self)
        parser.parse()
        return parser

    @property
    def user_read_rate(self) -> str:
        """消息推送打开率"""
        if not self.send_user_count:
            return '0%'
        amount = Decimal(self.user_read_count / self.send_user_count)
        return format_percent(amount, 2)

    @property
    def user_send_rate(self) -> str:
        """消息推送成功率"""
        if not self.subscribe_count:
            return '0%'
        amount = Decimal(self.send_user_count / self.subscribe_count)
        return format_percent(amount, 2)

    @cached_property
    def cached_user_whitelist(self) -> list:
        if not self.user_whitelist:
            return []
        return [int(user_id) for user_id in self.user_whitelist.split(',')]

    @cached_property
    def cached_user_ids(self) -> list:
        """客群 user_ids"""
        if not self.user_ids:
            return []
        bm = BitMap.deserialize(self.user_ids)
        if not bm:
            return []
        return list(bm)

    @property
    def list_send_user_ids(self) -> list[int]:
        """发送客群 user_ids"""
        if not self.send_user_ids:
            return []
        bm = BitMap.deserialize(self.send_user_ids)
        if not bm:
            return []
        return list(bm)

    @cached_property
    def url(self):
        from app.models.system import AppJumpList
        q = AppJumpList.query.get(self.jump_id)
        return q.jump_data if q else ''

    def set_user_ids(self, user_ids: list):
        """设置客群 user_ids"""
        self.user_ids = BitMap(user_ids).serialize()

    def set_send_user_ids(self, send_user_ids: list):
        """设置发送客群 user_ids"""
        self.send_user_ids = BitMap(send_user_ids).serialize()

    @cached_property
    def cached_user_filters(self) -> list:
        if not self.user_filters:
            return []
        return json.loads(self.user_filters)


class AppPushContent(db.Model):
    AVAILABLE_LANGS = (Language.EN_US, Language.ZH_HANS_CN, Language.ZH_HANT_HK,
                       Language.JA_JP, Language.KO_KP, Language.RU_KZ, Language.ES_ES,
                       Language.ID_ID, Language.FA_IR, Language.VI_VN, Language.TR_TR,
                       Language.AR_AE, Language.FR_FR, Language.DE_DE, Language.PT_PT,
                       Language.TH_TH, Language.PL_PL, Language.IT_IT
                       )

    id = db.Column(db.Integer, primary_key=True)
    app_push_id = db.Column(db.Integer, db.ForeignKey('app_push.id'), nullable=False)
    lang = db.Column(db.StringEnum(Language), nullable=False)
    title = db.Column(db.Text, nullable=False)
    url = db.Column(db.String(512))
    content = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False)

    app_push = db.relationship(
        'AppPush', backref=db.backref('contents', lazy='dynamic'))

    __table_args__ = (
        db.UniqueConstraint('app_push_id', 'lang', name='app_push_lang_uniq'),
    )


class AutoPushHistory(ModelBase):
    class PushType(Enum):
        EmailPush = 'EmailPush'
        AppPush = 'AppPush'
        NotificationBar = 'NotificationBar'

    class Business(Enum):
        TradeRankStart = '交易排位赛开始'
        TradeRankEnd = '交易排位赛结束'
        AirdropStart = '空投活动开始'
        AirdropEnd = '空投活动结束'
        DiscountStart = '打折购活动开始'
        DiscountEnd = '打折购活动结束'
        LaunchPoolMiningPre = '挖矿活动前期'
        LaunchPoolMiningStart = '挖矿活动正式期'
        LaunchPoolMiningEnd = '挖矿活动后期'

    class Status(Enum):
        CREATED = '已创建'
        SUCCESS = '成功'
        FAILED = '失败'

    push_type = db.Column(db.StringEnum(PushType), nullable=False)
    business = db.Column(db.StringEnum(Business), nullable=False)
    # key 是活动id等关键字, 可为空
    business_key = db.Column(db.String(32), nullable=False, default='')
    expired_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    status = db.Column(db.StringEnum(Status), default=Status.CREATED)
    push_id = db.Column(db.Integer)  # AppPush/EmailPush id


class AppAutoPushHistory(ModelBase):
    """
    App自动触达记录(广播)
    """

    class Type(Enum):
        RISE_FALL = '大幅涨跌'
        BREAKTHROUGH = '价格突破'
        TRENDING_ASSET = '热搜榜大幅涨跌'
        TRENDING_ASSET_DAILY_REPORT = '热搜榜日报'
        NEW_ASSET_RISE = '新币上涨'

    type = db.Column(db.StringEnum(Type), nullable=False)

    # key 是asset,market等关键字, 可为空
    key = db.Column(db.String(32), nullable=False, default='')
    title = db.Column(db.String(128), nullable=False)
    content = db.Column(db.Text, nullable=False)
    send_count = db.Column(db.Integer, nullable=False, default=0)
    read_count = db.Column(db.Integer, nullable=False, default=0)

    # json:额外信息
    extra = db.Column(db.String(256), nullable=True)


class StrategyTagPushAbstract(ModelBase):
    __abstract__ = True

    PushType = AppPush.PushType
    UserType = AppPush.UserType
    PushScopeType = AppPush.PushScopeType

    user_type = db.Column(db.StringEnum(UserType), nullable=False, default=UserType.TARGET_USER)
    groups = db.Column(db.TEXT, nullable=False)
    push_type = db.Column(db.StringEnum(PushType), nullable=False,
                          default=PushType.SYSTEM)  # 此字段为了适配subscribe_users的获取
    whitelist_enabled = db.Column(db.Boolean, nullable=False, default=False)
    user_whitelist = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False, default='')
    push_scope_type = db.Column(db.StringEnum(PushScopeType), nullable=False,
                                default=PushScopeType.EXCLUDE_UNSUBSCRIBE)  # 此字段为了适配subscribe_users的获取

    def get_groups(self):
        if not self.groups:
            return []
        return json.loads(self.groups)

    @property
    def users(self) -> Set[int]:
        """客群用户"""
        return self.parsed_app_push.parsed_user_ids

    @property
    def subscribe_users(self) -> Set[int]:
        """订阅用户"""
        return self.parsed_app_push.parsed_subscribe_user_ids

    @cached_property
    def parsed_app_push(self):
        from app.business.push_statistic import AppPushUserParser
        parser = AppPushUserParser(self)
        parser.parse()
        return parser

    @cached_property
    def cached_user_whitelist(self) -> list:
        if not self.user_whitelist:
            return []
        return [int(user_id) for user_id in self.user_whitelist.split(',')]


class AutoPushStrategy(ModelBase):
    class Status(Enum):
        CREATED = '待审核'
        AUDITED = '生效中'
        REJECTED = '已拒绝'
        SUSPENDING = '暂停中'
        SUSPENDED = '已暂停'
        DELETED = '已删除'

    class Type(Enum):
        PRICE_BROADCAST = '广播类-行情触发'
        PRICE_TAG_PUSH = '非广播类-行情触发'
        CONTENT_TAG_PUSH = '非广播类-标签触发'

    name = db.Column(db.String(512), nullable=False, index=True)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED)
    push_group = db.Column(db.StringEnum(PushBroadcastLimitGroup), nullable=False)
    type = db.Column(db.StringEnum(Type), nullable=False)

    created_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False, index=True)
    audited_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)
    audited_at = db.Column(db.MYSQL_DATETIME_6)
    auditor_remark = db.Column(db.String(512), nullable=False, default='')


class BillBoard(Enum):
    ASSET_CIRCULATION = '市值榜'
    DEAL = '成交榜'
    PRICE_RISE = '涨幅榜'
    PRICE_DOWN = '跌幅榜'
    ASSETS_TRENDING = '热搜榜'
    NEW_ASSET = '新币榜'


BillboardRankRankKeyMapping = {
    BillBoard.ASSET_CIRCULATION: 'real_circulation_usd',
    BillBoard.DEAL: 'volume_usd',
    BillBoard.PRICE_RISE: 'change_rate_desc',
    BillBoard.PRICE_DOWN: 'change_rate_asc',
    BillBoard.ASSETS_TRENDING: 'trending',
    BillBoard.NEW_ASSET: 'new_recent',
}

BillboardConfExcludeAssets = ['BTC', 'ETH', 'USDT', 'USDC', 'OKB', 'BGB', 'KCS', 'GT', 'HT', 'FTT', 'MX']


class AssetsType(Enum):
    CHOSEN = '手动添加'
    BILL_BOARD = '榜单'


class TriggerPriceInterval(Enum):
    """价格时间跨度"""
    PRICE_24_HOURS = gettext('24小时')
    PRICE_7_DAYS = gettext('7天')
    PRICE_15_DAYS = gettext('15天')
    PRICE_30_DAYS = gettext('30天')


TriggerIntervalToKeyMapping = {
    TriggerPriceInterval.PRICE_24_HOURS: 'one_day',
    TriggerPriceInterval.PRICE_7_DAYS: 'seven_day',
    TriggerPriceInterval.PRICE_15_DAYS: 'fifteen_day',
    TriggerPriceInterval.PRICE_30_DAYS: 'one_month',
}  # 价格时间跨度与缓存查询时的key值映射


class PriceBroadcastAutoPushStrategyDetail(ModelBase):
    """价格触发的app广播策略"""

    strategy_id = db.Column(db.Integer, db.ForeignKey('auto_push_strategy.id'), nullable=False)
    assets_type = db.Column(db.StringEnum(AssetsType), nullable=False)
    assets = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=True)  # 监控币种：按币种（与按榜单监控互斥）

    bill_board = db.Column(db.StringEnum(BillBoard), nullable=True)  # 监控币种：按榜单
    bill_board_rank = db.Column(db.Integer, nullable=True)  # 榜单排名前 x 位
    exclude_assets = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=True)  # 排除监控币种
    trigger_price_interval = db.Column(db.StringEnum(TriggerPriceInterval), nullable=False)  # 触发价格的涨跌幅比较基准：
    # 24小时/7天/15天/30天时间跨度
    trigger_rise_threshold = db.Column(db.MYSQL_DECIMAL_26_8, nullable=True)  # 触发条件：涨幅阈值
    trigger_fall_threshold = db.Column(db.MYSQL_DECIMAL_26_8, nullable=True)  # 触发条件：跌幅阈值


class PriceTagAutoPushStrategyDetail(StrategyTagPushAbstract):
    """价格触发的按人群标签推送"""

    strategy_id = db.Column(db.Integer, db.ForeignKey('auto_push_strategy.id'), nullable=False)
    assets_type = db.Column(db.StringEnum(AssetsType), nullable=False)
    assets = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=True)  # 监控币种：按币种（与按榜单监控互斥）

    bill_board = db.Column(db.StringEnum(BillBoard), nullable=True)  # 监控币种：按榜单
    bill_board_rank = db.Column(db.Integer, nullable=True)  # 榜单排名前 x 位
    exclude_assets = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=True)  # 排除监控币种
    trigger_price_interval = db.Column(db.StringEnum(TriggerPriceInterval), nullable=False)  # 触发价格的涨跌幅比较基准：
    # 24小时/7天/15天/30天时间跨度
    trigger_rise_threshold = db.Column(db.MYSQL_DECIMAL_26_8, nullable=True)  # 触发条件：涨幅阈值
    trigger_fall_threshold = db.Column(db.MYSQL_DECIMAL_26_8, nullable=True)  # 触发条件：跌幅阈值


class ContentAppPushStrategyDetail(StrategyTagPushAbstract):
    """内容触发的app推送策略"""
    daily_send_range = 10 * 60

    class ExecuteDuration(Enum):
        ALWAYS = '长期执行'
        RANGE = '有效期内执行'

    class PushLimitInterval(Enum):
        ONE_DAY = '1天'
        SEVEN_DAY = '7天'
        FIFTEEN_DAY = '15天'
        FOREVER = '永久'

    interval_delta_days_dic = {
        PushLimitInterval.ONE_DAY: 1,
        PushLimitInterval.SEVEN_DAY: 7,
        PushLimitInterval.FIFTEEN_DAY: 15,
    }

    strategy_id = db.Column(db.Integer, db.ForeignKey('auto_push_strategy.id'), nullable=False)
    execute_duration = db.Column(db.StringEnum(ExecuteDuration), nullable=False)
    start_time = db.Column(db.MYSQL_DATETIME_6, nullable=True)
    end_time = db.Column(db.MYSQL_DATETIME_6, nullable=True)
    daily_plan_send_at = db.Column(db.Integer, nullable=False)
    push_limit_interval = db.Column(db.StringEnum(PushLimitInterval), nullable=False)
    send_at = db.Column(db.MYSQL_DATETIME_6, nullable=True)
    jump_id = db.Column(db.Integer, db.ForeignKey('app_jump_list.id'), nullable=True)

    @cached_property
    def url(self):
        from app.models.system import AppJumpList
        q = AppJumpList.query.get(self.jump_id)
        return q.jump_data if q else ''


class ContentPushStrategyContent(ModelBase):
    """内容推送类别push文案"""

    __table_args__ = (
        db.UniqueConstraint('strategy_detail_id', 'lang', name='app_strategy_detail_id_lang_uniq'),
    )

    AVAILABLE_LANGS = (Language.EN_US, Language.ZH_HANS_CN, Language.ZH_HANT_HK,
                       Language.JA_JP, Language.KO_KP, Language.RU_KZ, Language.ES_ES,
                       Language.ID_ID, Language.FA_IR, Language.VI_VN, Language.TR_TR,
                       Language.AR_AE, Language.FR_FR, Language.DE_DE, Language.PT_PT,
                       Language.TH_TH, Language.PL_PL, Language.IT_IT
                       )

    strategy_detail_id = db.Column(db.Integer, db.ForeignKey('content_app_push_strategy_detail.id'), nullable=False)
    lang = db.Column(db.StringEnum(Language), nullable=False)
    title = db.Column(db.Text, nullable=False)
    url = db.Column(db.String(512))
    content = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False)

    strategy_push = db.relationship(
        'ContentAppPushStrategyDetail', backref=db.backref('contents', lazy='dynamic'))


class AutoPushStrategySendHistory(ModelBase):
    """自动push策略发送记录"""
    strategy_id = db.Column(db.Integer, db.ForeignKey('auto_push_strategy.id'), nullable=False)
    send_at = db.Column(db.MYSQL_DATETIME_6, nullable=False, index=True)
    send_users = db.Column(db.MYSQL_MEDIUM_BLOB, nullable=True)
    trigger_asset = db.Column(db.String(64), nullable=True)
    user_count = db.Column(db.Integer, nullable=True)
    send_user_count = db.Column(db.Integer, nullable=True)
    user_read_count = db.Column(db.Integer, nullable=True)

    def get_user_ids(self) -> list:
        if not self.send_users:
            return []
        bm = BitMap.deserialize(self.send_users)
        if not bm:
            return []
        return bm


class AppPushBusinessHistory(ModelBase):
    """APP push推送业务及所属组发送记录"""

    business = db.Column(db.String(128), nullable=False, index=True)
    send_time = db.Column(db.Integer, nullable=False)
    send_users = db.Column(db.MYSQL_MEDIUM_BLOB, nullable=False)
    expire_at = db.Column(db.Integer, nullable=False, index=True)

    def get_send_user_ids(self) -> list:
        if not self.send_users:
            return []
        bm = BitMap.deserialize(self.send_users)
        if not bm:
            return []
        return bm

    @classmethod
    def set_pushed(cls, user_ids: Set, business: str, interval: int, send_time: int):
        rec = cls(
            business=business,
            send_time=send_time,
            send_users=BitMap(user_ids).serialize(),
            expire_at=send_time + interval
        )
        db.session_add_and_commit(rec)


class Activity(db.Model):
    REFERRAL_ID = 3
    BROKER_ID = 100000
    SYSTEM_REWORD_ID = 166
    DISTRIBUTE_INTEREST = 169

    MAKER_CASH_BACK = 'MakerCashBack'

    id = db.Column(db.Integer, primary_key=True)
    created_at = db.Column(db.MYSQL_DATETIME_6, default=datetime.utcnow)
    name = db.Column(db.String(128), nullable=False, unique=True, default='')

    @classmethod
    def _get_or_create_by_name(cls, name: str) -> 'Activity':
        obj = Activity.query.filter_by(name=name).first()
        if not obj:
            obj = Activity(name=name)
            db.session_add_and_commit(obj)
        return obj

    @classmethod
    def get_maker_cash_back_activity(cls):
        return Activity.query.filter_by(name=cls.MAKER_CASH_BACK).first()

    @classmethod
    def get_or_create_fifth_anniversary_activity_id(cls):
        name = "fifth_anniversary_reward"
        obj = Activity.query.filter_by(name=name).first()
        if not obj:
            obj = Activity(name=name)
            db.session_add_and_commit(obj)
        return obj.id

    @classmethod
    def get_or_create_sixth_anniversary_activity_id(cls):
        name = "sixth_anniversary_reward"
        obj = Activity.query.filter_by(name=name).first()
        if not obj:
            obj = Activity(name=name)
            db.session_add_and_commit(obj)
        return obj.id

    @classmethod
    def get_or_create_seventh_anniversary_activity_id(cls):
        name = "seventh_anniversary_reward"
        obj = Activity.query.filter_by(name=name).first()
        if not obj:
            obj = Activity(name=name)
            db.session_add_and_commit(obj)
        return obj.id

    @classmethod
    def get_or_create_wallacy_activity_id(cls):
        name = "wallacy_reward"
        obj = Activity.query.filter_by(name=name).first()
        if not obj:
            obj = Activity(name=name)
            db.session_add_and_commit(obj)
        return obj.id

    @classmethod
    def get_or_create_broker_referral_id(cls):
        name = "broker_referral"  # fixme 提前创建 和REFERRAL_ID一样写死ID？
        obj = Activity.query.filter_by(name=name).first()
        if not obj:
            obj = Activity(name=name)
            db.session_add_and_commit(obj)
        return obj.id

    @classmethod
    def get_or_create_indirect_referral_id(cls) -> int:
        name = "indirect_referral"
        obj = cls._get_or_create_by_name(name)
        return obj.id

    @classmethod
    def get_or_create_ambassador_package_activity_id(cls):
        name = 'ambassador_package'
        obj = Activity.query.filter_by(name=name).first()
        if not obj:
            obj = Activity(name=name)
            db.session_add_and_commit(obj)
        return obj.id
    
    @classmethod
    def get_or_create(cls, **kwargs):
        filters = [getattr(cls, k) == v for k, v in kwargs.items()]
        if record := cls.query.filter(*filters).first():
            return record
        record = cls()
        for k, v in kwargs.items():
            setattr(record, k, v)
        return record


class GiftHistory(ModelBase):
    class Status(Enum):
        CREATED = 'created'
        FAILED = 'failed'
        FROZEN = 'frozen'  # 临时状态，非冻结态
        FINISHED = 'pass'
        CANCELLED = 'cancel'

        REAL_FROZEN = "real_frozen"  # 冻结
        LOCKED = "lock"  # 已锁定
        TO_REVOKE = "to_revoke"  # 准备撤销
        REVOKED = "revoke"  # 奖励撤销

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    activity_id = db.Column(db.Integer, db.ForeignKey(Activity.id),
                            nullable=False, index=True)
    asset = db.Column(db.String(32), nullable=False)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    lock_time = db.Column(db.Integer, nullable=False, default=0)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED, index=True)
    remark = db.Column(db.String(128), nullable=False, default='')
    # default NULL
    admin_history_id = db.Column(db.Integer, db.ForeignKey('admin_gift_balance_history.id'))


class AdminGiftBalanceHistory(ModelBase):
    class Status(Enum):
        CREATED = 'created'
        PROCESSING = 'processing'  # 已扣减admin资产，待增加用户资产
        FINISHED = 'finished'

        TO_REVOKE = 'to_revoke'  # 活动撤销，待加回admin资产
        REVOKED = 'revoke'  # 撤销完成，已加回admin资产

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    asset = db.Column(db.String(32), nullable=False)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    business = db.Column(db.String(128), nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED, index=True)


class Banner(ModelBase):
    AVAILABLE_LANGS = (Language.EN_US, Language.ZH_HANS_CN,
                       Language.ZH_HANT_HK, Language.JA_JP,
                       Language.RU_KZ, Language.KO_KP, Language.ID_ID,
                       Language.ES_ES, Language.FA_IR, Language.TR_TR, Language.VI_VN,
                       Language.AR_AE, Language.FR_FR, Language.DE_DE, Language.PT_PT,
                       Language.TH_TH, Language.PL_PL, Language.IT_IT)

    class Platform(Enum):
        WEB = 'web'
        APP = 'app'

    class Status(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    name = db.Column(db.String(32), nullable=False)
    started_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    ended_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    affect_homepage = db.Column(db.Boolean, nullable=False, default=False)
    affect_calendar = db.Column(db.Boolean, nullable=False, default=False)
    platform = db.Column(db.Enum(Platform), nullable=False)
    params = db.Column(db.Text, nullable=False, default='')  # 已废弃 兼容保留
    url = db.Column(db.String(1024), nullable=False, default='')  # 默认跳转url
    status = db.Column(db.Enum(Status), nullable=False, default=Status.VALID)
    updated_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    sort_id = db.Column(db.Integer, nullable=False, default=(
        lambda: (Banner.query.with_entities(
            func.max(Banner.sort_id).label('max_sort_id')  # type: ignore
        ).first().max_sort_id or 0) + 1
    ))

    __table_args__ = (
        db.Index('sort_id_asc_idx', sort_id.asc()),
    )

    @classmethod
    def build_legacy_params(cls, platform: Platform, url: str) -> Dict:
        # 构建兼容的老数据params
        """
        params格式:
            web:
                只用了 action_type 和 link 字段
                action_type只判断了值为'redirect'，其他值没处理
            app:
                只用了actoin_param['url']字段，没做任何判断
        """
        if platform == cls.Platform.WEB:
            return {"action_type": "redirect", "link": url}
        else:
            # 历史约定 拼写错误
            return {"actoin_type": "go_h5", "actoin_param": {"url": url}}


class BannerContent(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    owner_id = db.Column(db.Integer, db.ForeignKey('banner.id'),
                         nullable=False)
    lang = db.Column(db.StringEnum(Language), nullable=False)
    file_key = db.Column(db.Text, nullable=False)
    url = db.Column(db.String(1024), nullable=False, default='')  # 某个语言的特殊跳转url

    owner = db.relationship(
        'Banner', backref=db.backref('contents', lazy='dynamic'))

    __table_args__ = (
        db.UniqueConstraint('owner_id', 'lang', name='owner_lang_uniq'),
    )

    @property
    def img_src(self) -> str:
        if not (file_key := self.file_key):
            return ''
        return AWSBucketPublic.get_file_url(file_key)


class StartPage(ModelBase):
    __table_args__ = (
        db.Index("idx_start_at_end_at", "start_at", "end_at"),
    )

    AVAILABLE_LANGS = (Language.EN_US, Language.ZH_HANS_CN,
                       Language.ZH_HANT_HK, Language.JA_JP,
                       Language.RU_KZ, Language.KO_KP, Language.ID_ID,
                       Language.ES_ES, Language.FA_IR, Language.TR_TR, Language.VI_VN,
                       Language.AR_AE, Language.FR_FR, Language.DE_DE, Language.PT_PT,
                       Language.TH_TH, Language.PL_PL, Language.IT_IT)

    name = db.Column(db.String(32), nullable=False)
    is_head = db.Column(db.Boolean, nullable=False, default=False)
    show_time = db.Column(db.Integer, nullable=False)
    return_url = db.Column(db.Text)
    start_at = db.Column(db.MYSQL_DATETIME_6)
    end_at = db.Column(db.MYSQL_DATETIME_6)


class StartPageContent(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    page_id = db.Column(db.Integer, db.ForeignKey(StartPage.id), nullable=False)
    lang = db.Column(db.StringEnum(Language), nullable=False)
    file_key = db.Column(db.Text)

    @property
    def img_src(self) -> str:
        if not (file_key := self.file_key):
            return ''
        return AWSBucketPublic.get_file_url(file_key)


class AppStartPage(ModelBase):
    AVAILABLE_LANGS = (Language.EN_US, Language.ZH_HANS_CN, Language.ZH_HANT_HK,
                       Language.JA_JP, Language.KO_KP, Language.RU_KZ, Language.ES_ES,
                       Language.ID_ID, Language.FA_IR, Language.TR_TR, Language.VI_VN,
                       Language.AR_AE, Language.FR_FR, Language.DE_DE, Language.PT_PT,
                       Language.TH_TH, Language.PL_PL, Language.IT_IT
                       )

    class Platform(Enum):
        ALL = 'all'
        ANDROID = 'Android'
        IOS = 'IOS'

    class Frequency(Enum):
        ONCE = 'once'
        EVERY_DAY = 'every_day'
        EVERY_TIME = 'every_time'

    class FilterType(Enum):
        FILTERS = 'filters'
        NONE = 'none'

    class Status(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    name = db.Column(db.String(128), nullable=False)
    show_time = db.Column(db.Integer, nullable=False)
    return_url = db.Column(db.String(512))
    started_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    ended_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    platform = db.Column(db.StringEnum(Platform), nullable=False)
    whitelist_enabled = db.Column(db.Boolean, nullable=False, default=False)
    user_whitelist = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False, default='')
    frequency = db.Column(db.StringEnum(Frequency), nullable=False)
    filter_type = db.Column(db.StringEnum(FilterType), nullable=False)
    filters = db.Column(db.MYSQL_MEDIUM_TEXT)
    groups = db.Column(db.TEXT, nullable=False, default='')
    users = db.Column(db.MYSQL_MEDIUM_TEXT)
    user_bitmap = db.Column(db.MYSQL_MEDIUM_BLOB, comment='用户id Bitmap')
    target_user_number = db.Column(db.Integer, nullable=False, default=0)
    sort_id = db.Column(db.Integer, nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID)
    page_view = db.Column(db.Integer, nullable=False, default=0, comment='浏览量')
    click_count = db.Column(db.Integer, nullable=False, default=0, comment='点击量')

    def get_groups(self):
        if not self.groups:
            return []
        return json.loads(self.groups)

    @cached_property
    def cached_user_whitelist(self) -> list:
        if not self.user_whitelist:
            return []
        return [int(user_id) for user_id in self.user_whitelist.split(',')]


class AppStartPageContent(ModelBase):
    app_start_page_id = db.Column(db.Integer, db.ForeignKey('app_start_page.id'),
                                nullable=False)
    lang = db.Column(db.StringEnum(Language), nullable=False)
    images = db.Column(db.JSON, nullable=True, default=[])


class CoinListingApplication(ModelBase):
    class Type(Enum):
        COIN = 'coin'
        TOKEN = 'token'

    class Status(Enum):
        CREATED = 'created'
        AUDITED = 'audited'
        REJECTED = 'rejected'
        DELETED = 'deleted'

    created_by = db.Column(db.Integer, db.ForeignKey('user.id'),
                           nullable=False)
    updated_by = db.Column(db.Integer, db.ForeignKey('user.id'),
                           nullable=False)

    code = db.Column(db.String(32), nullable=False)
    name = db.Column(db.String(64), nullable=False)
    icon = db.Column(db.String(128), nullable=False)
    type = db.Column(db.Enum(Type), nullable=False)
    issued_date = db.Column(db.DATE)
    issued_price_data = db.Column(db.Text)
    total_supply = db.Column(db.String(64))
    total_circulate = db.Column(db.String(64))
    usd_circulate = db.Column(db.String(64))
    official_website = db.Column(db.String(128))
    white_paper = db.Column(db.String(512))
    source_code = db.Column(db.String(128))
    explorer = db.Column(db.String(128))
    contract_address = db.Column(db.String(256))
    intro_en = db.Column(db.Text)
    intro_cn = db.Column(db.Text)
    fundraising = db.Column(db.Text)
    token_distribution = db.Column(db.Text)
    linkedin_or_cv = db.Column(db.Text)
    applicant_name = db.Column(db.String(128))
    applicant_role = db.Column(db.String(128))
    applicant_email = db.Column(db.String(128))
    applicant_mobile = db.Column(db.String(64))

    telegram = db.Column(db.String(64))
    facebook = db.Column(db.String(64))
    twitter = db.Column(db.String(64))
    reddit = db.Column(db.String(64))
    medium = db.Column(db.String(64))
    discord = db.Column(db.String(64))
    is_first = db.Column(db.Boolean, nullable=False, default=False)
    is_securities = db.Column(db.Boolean, nullable=False, default=False)

    status = db.Column(db.Enum(Status), nullable=False, default=Status.CREATED)


class CoinApplicationFiles(ModelBase):
    class Status(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    class FileType(Enum):
        LABS = 'labs'
        COIN = 'coin'
        LAW = 'law'
        AUDIT = 'audit'
        SECURITIES = 'securities'
        COMMITMENT = 'commitment'

    coin_listing_application_id = db.Column(
        db.Integer, db.ForeignKey('coin_listing_application.id'))
    file_id = db.Column(db.Integer, db.ForeignKey('file.id'))
    name = db.Column(db.String(64), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    file_type = db.Column(db.Enum(FileType), nullable=False)
    status = db.Column(db.Enum(Status), nullable=False, default=Status.VALID)

    user = db.relationship(
        'User', backref=db.backref('coin_application_files', lazy='dynamic'))


class DepositActivity(ModelBase):
    class Status(Enum):
        PASSED = 'pass'
        SUSPENDED = 'suspend'
        FINISHED = 'finish'
        DELETED = 'delete'

    class Type(Enum):
        RATE = 'rate'  # 按比例，充n，送n*x
        FIX = 'fix'  # 按定额, 充值达到n，送x
        PARTITION = 'partition'  # 瓜分, 送n/sum(n)
        RANK = 'rank'  # 排名，top(n)送币

    activity_id = db.Column(db.Integer, db.ForeignKey('activity.id'))
    zendesk_url = db.Column(db.Text, default='')
    name = db.Column(db.String(128), nullable=False, default='')
    type = db.Column(db.Enum(Type), nullable=False)
    started_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    ended_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    total_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    left_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    least_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    deposit_asset = db.Column(db.String(32), nullable=False)
    gift_asset = db.Column(db.String(32), nullable=False)

    updated_by = db.Column(db.String(32), nullable=False)
    status = db.Column(db.Enum(Status), nullable=False)

    def get_left_amount(self):
        record = GiftHistory.query.filter(
            GiftHistory.activity_id == self.activity_id,
            GiftHistory.asset == self.gift_asset,
            GiftHistory.created_at >= self.started_at,
            or_(
                GiftHistory.status == GiftHistory.Status.FINISHED,
                GiftHistory.status == GiftHistory.Status.CREATED
            )
        ).with_entities(
            func.sum(GiftHistory.amount).label('total')
        ).first()
        total_gift = record.total if record.total else Decimal()
        return self.total_amount - total_gift


class DepositActivityRule(ModelBase):
    deposit_activity_id = db.Column(
        db.Integer, db.ForeignKey('deposit_activity.id'), unique=True)
    rule_data = db.Column(db.Text, nullable=False, default='')

    deposit_activity = db.relationship(
        'DepositActivity',
        backref=db.backref('deposit_activity_rule', lazy='dynamic'))


class DepositActivityRank(ModelBase):
    __table_args__ = (
        db.UniqueConstraint('user_id', 'activity_id',
                            name='user_id_activity_id'),
    )

    rank_at = db.Column(
        db.MYSQL_DATETIME_6, default=datetime.utcnow, onupdate=datetime.utcnow,
        nullable=False)
    activity_id = db.Column(db.Integer, db.ForeignKey('activity.id'))
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    rank = db.Column(db.Integer, nullable=False)
    deposit_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    gift_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    gift_asset = db.Column(db.String(32), nullable=False)

    user = db.relationship(
        'User',
        backref=db.backref('deposit_activity_ranks', lazy='dynamic'))


class ActivityWhiteList(ModelBase):
    __table_args__ = (
        db.UniqueConstraint('user_id', 'activity_id',
                            name='user_id_activity_id'),
    )

    class Status(Enum):
        PASSED = 'pass'
        DELETED = 'delete'

    activity_id = db.Column(db.Integer, db.ForeignKey('activity.id'))
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    remark = db.Column(db.String(256))

    status = db.Column(db.Enum(Status), nullable=False, default=Status.PASSED)


class ActivityBlackList(ModelBase):
    __table_args__ = (
        db.UniqueConstraint('user_id', 'activity_id',
                            name='user_id_activity_id'),
    )

    class Status(Enum):
        PASSED = 'pass'
        DELETED = 'delete'

    activity_id = db.Column(db.Integer, db.ForeignKey('activity.id'))
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))

    remark = db.Column(db.String(256), nullable=True)
    status = db.Column(db.Enum(Status), nullable=False, default=Status.PASSED)


class TradeRankActivity(ModelBase):
    """交易排名活动"""

    class Status(Enum):
        ONLINE = '已上架'
        PENDING = '未上架'
        FINISHED = '已完成'
        DELETED = '已下架'

    class Type(Enum):
        SPOT_TRADE = 'spot_trade'
        SPOT_NET_BUY = 'spot_net_buy'
        PERPETUAL_TRADE = 'perpetual_trade'
        PERPETUAL_INCOME = 'perpetual_income'
        PERPETUAL_INCOME_RATE = 'perpetual_income_rate'

    class MarketType(Enum):
        ALL = '全部市场'
        DIRECT_PERPETUAL = '全部正向合约市场'
        INVERSE_PERPETUAL = '全部反向合约市场'

    class FundingSource(Enum):
        COMPANY_EXPENSES = '公司支出'
        PROJECT_EXPENSES = '项目方支出'

    class ActivityCategory(Enum):
        SPOT = 'SPOT'
        PERPETUAL = 'PERPETUAL'

    SPOT_TYPES = [Type.SPOT_TRADE, Type.SPOT_NET_BUY]
    PERPETUAL_TYPES = [Type.PERPETUAL_TRADE, Type.PERPETUAL_INCOME, Type.PERPETUAL_INCOME_RATE]

    activity_id = db.Column(db.Integer, db.ForeignKey('activity.id'))
    type = db.Column(db.StringEnum(Type), nullable=False)
    funding_source = db.Column(db.StringEnum(FundingSource), nullable=False, default=FundingSource.COMPANY_EXPENSES)
    started_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    ended_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    gift_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    left_gift_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    least_trade_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    # 最大瓜分奖励
    max_split_reward_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=True)
    gift_asset = db.Column(db.String(32), nullable=False)
    markets = db.Column(db.Text, nullable=False)  # All market if empty string or None
    market_type = db.Column(db.StringEnum(MarketType), nullable=True)
    gift_rules = db.Column(db.Text, nullable=False, default='')
    cover = db.Column(db.String(128), default='')
    announce_url = db.Column(db.String(256))
    # 用户参与条件
    user_group_condition = db.Column(db.Text, nullable=False, default='')
    updated_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    status = db.Column(db.StringEnum(Status), nullable=False)
    is_checked = db.Column(db.Boolean, default=False, nullable=True)  # 是否已经完成清退用户的排除（派奖前的校验）
    exclude_markets = db.Column(db.Text, nullable=False)

    def get_left_amount(self):
        record = GiftHistory.query.filter(
            GiftHistory.activity_id == self.activity_id,
            GiftHistory.asset == self.gift_asset,
            GiftHistory.created_at >= self.started_at,
            or_(
                GiftHistory.status == GiftHistory.Status.FINISHED,
                GiftHistory.status == GiftHistory.Status.CREATED
            )
        ).with_entities(
            func.sum(GiftHistory.amount).label('total')
        ).first()
        total_gift = record.total if record.total else Decimal()
        return self.gift_amount - total_gift


class TradeRankActivityDetail(ModelBase):
    """交易排名活动详情"""

    trade_activity_id = db.Column(db.Integer, db.ForeignKey('trade_rank_activity.id'))
    lang = db.Column(db.StringEnum(Language), nullable=False)
    title = db.Column(db.String(256), nullable=False)


class ChannelRewardActivity(ModelBase):
    """渠道奖励活动"""

    class Status(Enum):
        ONLINE = '已上架'
        AUDITED = '已审核'
        FINISHED = '已完成'
        DELETED = '已下架'

    class Platform(Enum):
        CommunityOperation = '社群运营'
        CommunityCooperation = '社群合作'
        OfflineDelivery = '线下投放'
        OnlineDelivery = '线上投放'
        MediaCooperation = '媒体合作'
        SixthAnniversary = '六周年专项'
        PublicRelationsMaintenance = '公关关系维护'
        PartnerRelationshipMaintenance = '合伙人关系维护'
        TG = 'TG'
        TikTok = 'TikTok'
        Twitter = 'Twitter'
        KOL = 'KOL粉丝奖励'
        INS = 'INS'
        FB = 'FB'
        YouTube = 'YouTube'
        KOL_Cooperation_Cost = 'KOL合作费用'

    class RewardType(Enum):
        AVERAGE = '均分'
        CUSTOM = '自定义'

    class MessageType(Enum):
        DEFAULT = '默认'
        CUSTOM = '自定义站内信'

    activity_id = db.Column(db.Integer, db.ForeignKey('activity.id'))
    name = db.Column(db.String(256), nullable=False)
    business_name = db.Column(db.String(256))  # 活动业务名称，用于通知
    activity_url = db.Column(db.String(512))
    reward_url = db.Column(db.String(512))
    remark = db.Column(db.String(256))
    started_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    ended_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    languages = db.Column(db.Text, nullable=False)
    platforms = db.Column(db.Text, nullable=False)
    gift_rules = db.Column(db.Text, nullable=False, default='')
    updated_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    creator = db.Column(db.Integer, db.ForeignKey('user.id'))
    auditor = db.Column(db.Integer, db.ForeignKey('user.id'))
    rewarder = db.Column(db.Integer, db.ForeignKey('user.id'))
    status = db.Column(db.StringEnum(Status), nullable=False)
    reward_type = db.Column(db.StringEnum(RewardType), nullable=False, default=RewardType.AVERAGE)
    message_type = db.Column(db.StringEnum(MessageType), nullable=False, default=MessageType.DEFAULT)
    is_checked = db.Column(db.Boolean, default=False)  # 是否已经完成清退用户的排除（派奖前的校验）
    message_temp_id = db.Column(db.Integer, default=0)  # 站内信模板id
    report_at = db.Column(db.MYSQL_DATETIME_6)
    reward_at = db.Column(db.MYSQL_DATETIME_6)


class ChannelRewardHistory(ModelBase):
    """渠道奖励活动历史"""
    FREQUENTLY_GIFT_USERS = '高频获奖用户'
    MULTI_LANG_GIFT_USERS = '多语区获奖用户'

    class Status(Enum):
        VALID = '有效'
        INVALID = '无效'
        DELETED = '已删除'

    channel_reward_activity_id = db.Column(db.Integer, db.ForeignKey('channel_reward_activity.id'))
    email = db.Column(db.String(64), nullable=False)
    user_id = db.Column(db.Integer, nullable=True)
    remark = db.Column(db.String(128), nullable=False, default='')
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID)
    asset = db.Column(db.String(32), nullable=False)


class ChannelRewardPlatform(ModelBase):
    value = db.Column(db.String(64), nullable=False)


class TradeRankActivityJoinUser(ModelBase):
    """交易活动参与用户"""
    __table_args__ = (
        db.UniqueConstraint('trade_activity_id', 'user_id', name='trade_activity_id_user_id'),
    )

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    trade_activity_id = db.Column(db.Integer, db.ForeignKey('trade_rank_activity.id'))
    channel = db.Column(db.String(256), nullable=True, index=True)  # 用户报名渠道，非报名时更新，报名完成后统计任务中异步更新


class TradeRankActivityUserInfo(ModelBase):
    __table_args__ = (
        db.UniqueConstraint('trade_activity_id', 'user_id', name='trade_activity_id_user_id'),
    )

    report_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    trade_activity_id = db.Column(db.Integer, db.ForeignKey('trade_rank_activity.id'))
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    rank = db.Column(db.Integer, nullable=True)  # None: 没有排名
    # target_value 排名所依赖的指标
    # SPOT_TRADE，为现货交易额；
    # SPOT_NET_BUY，为现货净买入量（结束资产-开始资产-期间充值资产-分红资产）
    # PERPETUAL_TRADE，为合约交易额；
    # PERPETUAL_INCOME，为合约收益额（活动期间未实现+已实现盈亏）；
    # PERPETUAL_INCOME_RATE，为合约收益率（（活动期间未实现+已实现盈亏） / 仓位保证金之和）
    target_value = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    # trade_amount 成交额 / 买入量
    trade_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    gift_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    gift_asset = db.Column(db.String(32), nullable=False)

    # 活动市场手续费
    market_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    # 所有市场手续费
    fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    self_deal_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)


class TradeRankActivityPerpetualInfo(ModelBase):
    """交易活动用户合约信息"""

    __table_args__ = (
        db.UniqueConstraint('trade_activity_id', 'user_id', name='trade_activity_id_user_id'),
    )

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    trade_activity_id = db.Column(db.Integer, db.ForeignKey('trade_rank_activity.id'))

    # 活动开始时已实现 + 未实现盈亏
    initial_income_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    # 记录历史仓位盈亏
    history_position_income_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    # 记录历史仓位保证金
    history_position_margin_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)


class TradeRankActivityStatistics(ModelBase):
    """交易排名活动数据统计"""
    trade_activity_id = db.Column(db.Integer, db.ForeignKey('trade_rank_activity.id'))
    report_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    join_user_count = db.Column(db.Integer, nullable=False, default=0)
    trade_user_count = db.Column(db.Integer, nullable=False, default=0)
    qualified_user_count = db.Column(db.Integer, nullable=False, default=0)
    not_trade_user_count = db.Column(db.Integer, nullable=False, default=0)
    new_user_count = db.Column(db.Integer, nullable=False, default=0)
    new_trade_user_count = db.Column(db.Integer, nullable=False, default=0)
    trade_user_relative_ratio = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)

    new_join_user_trade_count = db.Column(db.Integer, nullable=False, default=0)

    total_trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    market_trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)

    total_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    market_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    new_user_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)


class PopupWindow(ModelBase):
    AVAILABLE_LANGS = (Language.EN_US, Language.ZH_HANS_CN, Language.ZH_HANT_HK,
                       Language.JA_JP, Language.KO_KP, Language.RU_KZ, Language.ES_ES,
                       Language.ID_ID, Language.FA_IR, Language.TR_TR, Language.VI_VN,
                       Language.AR_AE, Language.FR_FR, Language.DE_DE, Language.PT_PT,
                       Language.TH_TH, Language.PL_PL, Language.IT_IT
                       )

    class Platform(Enum):
        ALL = 'all'
        WEB = 'web'
        APP = 'app'

    class Frequency(Enum):
        ONCE = 'once'
        EVERY_DAY = 'every_day'
        EVERY_TIME = 'every_time'

    class TriggerPage(Enum):
        ALL = '全部'
        SPOT_MARKET = '币币交易页'
        MARGIN_MARKET = '杠杆交易页'
        PERPETUAL_MARKET = '合约交易页'
        ASSET_DATA = '币种资料页'
        HOME = '首页'
        ASSETS = '资产页'
        FIAT = '法币页'
        QUOTES = '行情页'
        PLEDGE = '借贷专题页'

    class TriggerPageParamType(Enum):
        ASSET = '币种'
        MARKET = '市场'

    class TriggerPageOp(Enum):
        IN = 'in'
        NOT_IN = 'not in'

    class JumpType(Enum):
        NATIVE = '原生'
        URL = 'URL'

    class PopPosition(Enum):
        CENTER = 'center'  # 正中间
        CORNER = 'corner'  # 顶部/右下角

    class ContentStyle(Enum):
        TEXT = 'text'  # 纯文字
        ARTICLE = 'article'  # 图文结合(图指纯文字的背景)
        IMAGE = 'image'  # 纯图片

    class FilterType(Enum):
        FILTERS = 'filters'
        NONE = 'none'

    class Status(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    name = db.Column(db.String(128), nullable=False)
    started_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    ended_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    platform = db.Column(db.StringEnum(Platform), nullable=False)
    # 币种资料页: 选择的是币种; 币币交易或者合约交易: 选择的是市场/币种
    # 格式：[{trigger_page: QUOTES, param_type: ASSET, trigger_page_params: [BTC, CET]},]
    trigger_pages = db.Column(db.Text, default='', nullable=False)
    jump_page_enabled = db.Column(db.Boolean, nullable=False, default=False)
    jump_type = db.Column(db.StringEnum(JumpType))
    jump_id = db.Column(db.Integer, db.ForeignKey('app_jump_list.id'))
    # 格式：'1370,250'
    whitelist_enabled = db.Column(db.Boolean, nullable=False, default=False)
    user_whitelist = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False, default='')
    trigger_page = db.Column(db.StringEnum(TriggerPage))  # 仅兼容 app，不作他用
    frequency = db.Column(db.StringEnum(Frequency), nullable=False)
    filter_type = db.Column(db.StringEnum(FilterType), nullable=False)
    # 迁移客群后保留（仅历史记录 admin 前端展示使用
    filters = db.Column(db.MYSQL_MEDIUM_TEXT)
    # 格式：[用户分群 id]
    groups = db.Column(db.TEXT, nullable=False, default='')
    users = db.Column(db.MYSQL_MEDIUM_TEXT)
    user_bitmap = db.Column(db.MYSQL_MEDIUM_BLOB, comment='用户id Bitmap')
    target_user_number = db.Column(db.Integer, nullable=False, default=0)
    sort_id = db.Column(db.Integer, nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID)
    pop_position = db.Column(db.StringEnum(PopPosition), nullable=False, default=PopPosition.CENTER)
    content_style = db.Column(db.StringEnum(ContentStyle), nullable=False, default=ContentStyle.ARTICLE)
    page_view = db.Column(db.Integer, nullable=False, default=0, comment='浏览量')
    click_count = db.Column(db.Integer, nullable=False, default=0, comment='点击量')

    def get_groups(self):
        if not self.groups:
            return []
        return json.loads(self.groups)

    def get_trigger_pages(self) -> list:
        if not self.trigger_pages:
            return []

        trigger_pages = json.loads(self.trigger_pages)
        for trigger_page_map in trigger_pages:
            trigger_page_params = trigger_page_map['trigger_page_params'] or []
            trigger_page_map['trigger_page_params'] = trigger_page_params
        return trigger_pages

    @cached_property
    def cached_user_whitelist(self) -> list:
        if not self.user_whitelist:
            return []
        return [int(user_id) for user_id in self.user_whitelist.split(',')]


class PopupWindowContent(ModelBase):
    popup_window_id = db.Column(db.Integer, db.ForeignKey('popup_window.id'),
                                nullable=False)
    lang = db.Column(db.StringEnum(Language), nullable=False)
    title = db.Column(db.Text, nullable=False, default='')
    content = db.Column(db.Text, nullable=False, default='')
    url = db.Column(db.String(512))
    images = db.Column(db.JSON, nullable=True, default=[])
    summary = db.Column(db.Text, nullable=False, default='')


class TipBar(ModelBase):
    AVAILABLE_LANGS = (Language.EN_US, Language.ZH_HANS_CN, Language.ZH_HANT_HK,
                       Language.JA_JP, Language.KO_KP, Language.RU_KZ, Language.ES_ES,
                       Language.ID_ID, Language.FA_IR, Language.TR_TR, Language.VI_VN,
                       Language.AR_AE, Language.FR_FR, Language.DE_DE, Language.PT_PT,
                       Language.TH_TH, Language.PL_PL, Language.IT_IT
                       )

    class Platform(Enum):
        ALL = 'all'
        WEB = 'web'
        APP = 'app'

    class TriggerPage(Enum):
        ALL = '全部'
        SPOT_MARKET = '币币市场'
        PERPETUAL_MARKET = '合约市场'
        ASSET_DATA = '币种资料'
        COPY_TRADING_MARKET = '跟单市场'
        ONCHAIN = '链上交易'

    class TriggerPageParamType(Enum):
        MARKET = '市场'
        ASSET = '币种'

    class TriggerPageOp(Enum):
        IN = 'in'

    class JumpType(Enum):
        NATIVE = '原生'
        URL = 'URL'

    class FilterType(Enum):
        FILTERS = 'filters'
        NONE = 'none'

    class Status(Enum):
        DRAFT = '未提交'
        CREATED = '待审核'
        AUDITED = '已通过'
        REJECTED = '未通过'
        FINISHED = '已结束'
        DELETED = '已删除'
        FAILED = '失败'

    class Type(Enum):
        PRICE_DEVIATION = '指数/标记价格偏离'
        NEW_ASSET = '新币上线'
        ASSET_VOLATILITY = '异常波动'
        OTHER = '其他'

    TYPE_DISPLAY = [
        Type.OTHER
    ]

    name = db.Column(db.String(128), nullable=False)
    started_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    ended_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    platform = db.Column(db.StringEnum(Platform), nullable=False)
    # 币种资料页: 选择的是币种; 币币交易或者合约交易: 选择的是市场/币种
    # 格式：[{trigger_page: QUOTES, param_type: ASSET, trigger_page_params: [BTC, CET]},]
    trigger_pages = db.Column(db.Text, default='', nullable=False)
    jump_page_enabled = db.Column(db.Boolean, nullable=False, default=False)
    jump_type = db.Column(db.StringEnum(JumpType))
    jump_id = db.Column(db.Integer, db.ForeignKey('app_jump_list.id'))
    whitelist_enabled = db.Column(db.Boolean, nullable=False, default=False)
    user_whitelist = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False, default='')
    filter_type = db.Column(db.StringEnum(FilterType), nullable=False)
    filters = db.Column(db.MYSQL_MEDIUM_TEXT)
    groups = db.Column(db.TEXT, nullable=False, default='')
    users = db.Column(db.MYSQL_MEDIUM_TEXT)
    target_user_number = db.Column(db.Integer, nullable=False, default=0)

    remark = db.Column(db.String(512), nullable=False, default='')
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    audited_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    audited_at = db.Column(db.MYSQL_DATETIME_6)
    auditor_remark = db.Column(db.String(512), nullable=False, default='')
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.DRAFT)
    type = db.Column(db.StringEnum(Type), nullable=False, default=Type.OTHER)

    def get_groups(self):
        if not self.groups:
            return []
        return json.loads(self.groups)

    @classmethod
    def get_enabled_windows(cls, platform: Platform, user_id: int = None) -> list:
        _now = now()
        rows = cls.query.filter(
            cls.status == cls.Status.VALID,
            or_(
                cls.platform == platform,
                cls.platform == cls.Platform.ALL
            ),
            cls.started_at < _now,
            cls.ended_at > _now
        ).all()
        result = []
        for row in rows:
            if row.filter_type == cls.FilterType.NONE:
                result.append(row)
            elif user_id and user_id in json.loads(row.users):
                result.append(row)
        result.sort(key=lambda x: x.sort_id, reverse=True)
        return result

    def get_trigger_pages(self) -> list:
        if not self.trigger_pages:
            return []

        trigger_pages = json.loads(self.trigger_pages)
        for trigger_page_map in trigger_pages:
            trigger_page_params = trigger_page_map['trigger_page_params'] or []
            trigger_page_map['trigger_page_params'] = trigger_page_params
        return trigger_pages

    @cached_property
    def cached_user_whitelist(self) -> list:
        if not self.user_whitelist:
            return []
        return [int(user_id) for user_id in self.user_whitelist.split(',')]


class TipBarContent(ModelBase):

    tip_bar_id = db.Column(db.Integer, db.ForeignKey('tip_bar.id'), nullable=False)
    lang = db.Column(db.StringEnum(Language), nullable=False)
    content = db.Column(db.Text, nullable=False, default='')


class AssetMaintain(ModelBase):

    class Platform(Enum):
        ALL = 'all'
        WEB = 'web'
        APP = 'app'

    class Feature(Enum):
        TAG = '标签'
        TIP_BAR = '提示条'

    class MaintainType(Enum):
        ST = 'ST'
        OFFLINE = '下架'
        MAINTAIN = '维护（置换、更名）'
        SHORT_TERM_RISK = '短期风险'

    class TagPage(Enum):
        ASSET_DATA = '币种资料'
        DEPOSIT_WITHDRAWAL = '充值&提现页'
        EXCHANGE_ASSET_LIST = '兑换币种选择列表'
        STRATEGY_ASSET_LIST = '策略交易选择列表'

    class JumpType(Enum):
        NATIVE = '原生'
        URL = 'URL'

    class Status(Enum):
        DRAFT = '未提交'
        CREATED = '待审核'
        AUDITED = '已通过'
        REJECTED = '未通过'
        FINISHED = '已结束'
        DELETED = '已删除'

    # 共用配置
    asset = db.Column(db.String(32), nullable=False)
    started_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    ended_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    platform = db.Column(db.StringEnum(Platform), nullable=False)
    jump_page_enabled = db.Column(db.Boolean, nullable=False, default=False)
    jump_type = db.Column(db.StringEnum(JumpType))
    jump_id = db.Column(db.Integer)

    # 标签配置
    maintain_type = db.Column(db.StringEnum(MaintainType), nullable=True)
    # e.g. [TagPage.ASSET_DATA]
    tag_pages = db.Column(db.Text, default='', nullable=False)

    # 提示条配置
    # 复用 TipBar.TriggerPage 相关枚举，格式：[SPOT_MARKET, ]
    tip_pages = db.Column(db.Text, default='', nullable=False)

    # others
    remark = db.Column(db.String(512), nullable=False, default='')
    created_by = db.Column(db.Integer, nullable=False, index=True)
    audited_by = db.Column(db.Integer, index=True)
    audited_at = db.Column(db.MYSQL_DATETIME_6)
    auditor_remark = db.Column(db.String(512), nullable=False, default='')
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.DRAFT)

    @property
    def should_tag_notify(self):
        """ 判断是否配置了标签通知功能 """
        return self.maintain_type and self.get_tag_pages()

    def get_tag_pages(self):
        if not self.tag_pages:
            return []
        return json.loads(self.tag_pages)

    def get_tip_pages(self):
        if not self.tip_pages:
            return []
        return json.loads(self.tip_pages)


class AssetMaintainContent(ModelBase):
    """这里是指将要同步到 TipBarContent 的内容"""
    owner_id = db.Column(db.Integer, nullable=False, index=True)
    lang = db.Column(db.StringEnum(Language), nullable=False)
    content = db.Column(db.Text, nullable=False, default='')


class AssetMaintainRelation(ModelBase):
    """同步关系表：AssetMaintain -> TipBar"""

    maintain_id = db.Column(db.Integer, nullable=False, index=True)
    business_id = db.Column(db.Integer, nullable=False, index=True)


class MiningActivity(ModelBase):
    """ 挖矿活动基础信息表 """

    class StatusType(Enum):
        ONLINE = "已上架"
        OFFLINE = "已下架"  # 下架后不能做任何操作, 不会在前端显示
        CREATED = "未上架"  # 初始状态, 不会在前端显示

    class MiningMode(Enum):
        # 交易挖矿
        TRADE = 'trade'
        # 挂单挖矿
        PENDING = "pending"
        # 质押挖矿
        PLEDGE = "pledge"
        # 做市挖矿
        AMM = "amm"

    class ActiveStatus(Enum):
        """ 活动进行状态 """

        # 排序规则：进行中、未开始、已结束
        STARTED = "started"  # 进行中
        CREATED = "created"  # 未开始
        FINISHED = "finished"  # 已结束

    class JoinUserType(Enum):
        """ 参与用户类型 """

        # 普通用户，表示除了做市商、特殊费率、子账户以外的所有用户；
        NORMAL = "normal"
        # 做市商，包括现货做市商、合约做市商、现货&合约做市商，除了特殊费率、子账户以外的用户
        MAKER = "maker"
        # VIP用户，VIP等级≥VIP1，除了子账户以外的用户
        VIP = "vip"
        # 已完成实名认证用户，已经通过KYC认证，除了子账户以外的用户
        KYC = "kyc"
        # 已完成实名认证的vip用户，已经通过KYC认证，且VIP等级≥VIP1，除了子账户以外的用户
        KYC_VIP = "kyc_vip"
        # 已完成实名认证的做市商，已经通过KYC认证，且身份是现货做市商、合约做市商、现货&合约做市商，除了特殊费率、子账户以外的用户
        KYC_MAKER = "kyc_maker"

    name = db.Column(db.String(128), nullable=False)
    # 挖矿方式, 挂单/交易/质押/做市
    mining_mode = db.Column(db.Enum(MiningMode), nullable=False)
    # 挖矿交易对,所有市场为空字符串
    markets = db.Column(db.String(2048), nullable=False)
    # 挖矿币种
    mining_asset = db.Column(db.String(32), nullable=False)
    # 开始时间，UTC日期
    start_date = db.Column(db.Date, nullable=False)
    # 持续时间, UTC日期,  start_date <= current_date < end_date
    end_date = db.Column(db.Date, nullable=False)
    # 参与用户类型
    join_user_type = db.Column(db.Enum(JoinUserType), nullable=False)

    status = db.Column(db.Enum(StatusType), nullable=False)

    activity_id = db.Column(db.Integer, db.ForeignKey('activity.id'), nullable=False)

    @property
    def duration(self):
        d = self.end_date - self.start_date
        return d.days

    @property
    def valid(self):
        # 活动是否进行中
        return self.active_status == self.ActiveStatus.STARTED and self.status == self.StatusType.ONLINE

    @property
    def active_status(self):
        today_ = today()
        if today_ < self.start_date:
            return self.ActiveStatus.CREATED
        if today_ < self.end_date:
            return self.ActiveStatus.STARTED
        return self.ActiveStatus.FINISHED


class MiningActivityDetail(ModelBase):
    """ 挖矿活动详情（多语言标题、图片） """

    __table_args__ = (db.UniqueConstraint("mining_activity_id", "lang", name="mining_activity_lang_uniq"),)

    AVAILABLE_LANGS = (
        Language.EN_US,
        Language.ZH_HANS_CN,
        Language.ZH_HANT_HK,
        Language.JA_JP,
        Language.KO_KP,
        Language.RU_KZ,
        Language.ES_ES,
        Language.ID_ID,
        Language.FA_IR,
        Language.TR_TR,
        Language.VI_VN,
        Language.AR_AE,
        Language.FR_FR,
        Language.DE_DE,
        Language.PT_PT,
        Language.TH_TH,
        Language.PL_PL,
        Language.IT_IT
    )

    mining_activity_id = db.Column(
        db.Integer,
        db.ForeignKey('mining_activity.id'), nullable=False)

    lang = db.Column(db.StringEnum(Language), nullable=False)
    title = db.Column(db.String(256), nullable=False)
    # s3 file_key
    cover = db.Column(db.String(128), nullable=False)

    @property
    def cover_url(self):
        if not (file_key := self.cover):
            return ''
        return AWSBucketPublic.get_file_url(file_key)


class MiningTradeConfig(ModelBase):
    """ 交易挖矿配置 """
    mining_activity_id = db.Column(db.Integer, db.ForeignKey('mining_activity.id'), nullable=False)
    # 挖矿类型， 永续/币币，挂单挖矿、交易挖矿才有用
    business_type = db.Column(db.Enum(TradeBusinessType), nullable=False)
    # 交易额费项 taker/maker/all, 交易挖矿才有用
    trade_mode = db.Column(db.Enum(TradeType), nullable=True)
    # 每日产量 (可换算成每小时产量)
    daily_output = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 用户每人每日限额, None表示没有限额, 挂单和交易挖矿才有用
    user_daily_max_output = db.Column(db.MYSQL_DECIMAL_26_8, nullable=True)


class MiningPendingConfig(ModelBase):
    """ 挂单挖矿配置 """
    mining_activity_id = db.Column(db.Integer, db.ForeignKey('mining_activity.id'), nullable=False)
    # 挖矿类型， 永续/币币，挂单挖矿、交易挖矿才有用
    business_type = db.Column(db.Enum(TradeBusinessType), nullable=False)
    # 每日产量
    daily_output = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 用户每人每日限额, None表示没有限额, 挂单和交易挖矿才有用
    user_daily_max_output = db.Column(db.MYSQL_DECIMAL_26_8, nullable=True)


class MiningPendingWeightConfig(ModelBase):
    """ 挂单挖矿盘口权重配置 """

    class StatusType(Enum):
        DELETED = 'deleted'
        PASSED = 'passed'

    mining_activity_id = db.Column(db.Integer, db.ForeignKey('mining_activity.id'), nullable=False)
    min_delta = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    max_delta = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    weight = db.Column(db.Integer, nullable=False)
    status = db.Column(db.Enum(StatusType), nullable=False)


class MiningPledgeConfig(ModelBase):
    """ 质押挖矿配置项 """

    mining_activity_id = db.Column(db.Integer, db.ForeignKey("mining_activity.id"), nullable=False)
    # 质押币种
    pledge_asset = db.Column(db.String(32), nullable=False)
    # 每人最多可质押（选填）
    user_max_pledge_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=True)
    # 最少需质押（选填）
    user_min_pledge_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=True)
    # 每小时产量
    hourly_output = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)


class MiningAmmConfig(ModelBase):
    """ 做市挖矿配置项 """

    mining_activity_id = db.Column(db.Integer, db.ForeignKey("mining_activity.id"), nullable=False)
    # 每人最多可提供的有效流动性市值（选填）
    user_max_liquidity_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=True)
    # 每小时产量
    hourly_output = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)


class MiningJoinUser(ModelBase):
    """用户报名记录"""
    __table_args__ = (
        db.UniqueConstraint('mining_activity_id',
                            'user_id',
                            name='activity_user_id_uniq'),
    )

    class StatusType(Enum):
        DELETED = 'deleted'
        PASSED = 'passed'

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    mining_activity_id = db.Column(db.Integer, db.ForeignKey('mining_activity.id'), nullable=False)
    status = db.Column(db.Enum(StatusType), nullable=False)
    remark = db.Column(db.String(256), nullable=False, default='')


class MiningTradeModeHistory(ModelBase):
    """用户交易挖矿历史数据, 每小时插入新数据"""
    __table_args__ = (
        db.UniqueConstraint('mining_activity_id',
                            'history_at',
                            'user_id',
                            name='activity_user_id_history_at_uniq'),
    )
    mining_activity_id = db.Column(db.Integer, db.ForeignKey('mining_activity.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    # 用户成交量
    trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 数据时间, 对齐到整点
    history_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)


class MiningPendingModeHistory(ModelBase):
    """用户挂单挖矿历史数据, 小时内记录上修改"""
    __table_args__ = (
        db.UniqueConstraint('mining_activity_id',
                            'history_at',
                            'user_id',
                            name='activity_user_id_history_at_uniq'),
    )
    mining_activity_id = db.Column(db.Integer, db.ForeignKey('mining_activity.id'), nullable=False)

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    # 挂单积分
    score = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 数据时间, 对齐到整点
    history_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)


class MiningUserPledgeInfo(ModelBase):
    """ 用户质押挖矿信息 """

    __table_args__ = (db.UniqueConstraint("mining_activity_id", "user_id", name="activity_user_id_uniq"),)

    mining_activity_id = db.Column(db.Integer, db.ForeignKey("mining_activity.id"), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=False)
    # 活动的当前质押数量
    pledge_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 累计挖矿奖励数量
    total_gift_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 已提取挖矿奖励数量
    received_gift_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)

    @property
    def remain_gift_amount(self):
        # 剩余可提取挖矿奖励数量
        return self.total_gift_amount - self.received_gift_amount


class MiningUserAmmInfo(ModelBase):
    """ 用户做市挖矿信息 """

    __table_args__ = (db.UniqueConstraint("mining_activity_id", "user_id", name="activity_user_id_uniq"),)

    mining_activity_id = db.Column(db.Integer, db.ForeignKey("mining_activity.id"), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=False)
    # 累计挖矿奖励数量
    total_gift_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 已提取挖矿奖励数量
    received_gift_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)

    @property
    def remain_gift_amount(self):
        # 剩余可提取挖矿奖励数量
        return self.total_gift_amount - self.received_gift_amount


class MiningUserRewordHistory(ModelBase):
    """ 用户交易挖矿、挂单挖矿发放记录，每天发奖一次 """

    __table_args__ = (
        db.UniqueConstraint('mining_activity_id',
                            'report_date',
                            'user_id',
                            name='activity_user_id_report_date_uniq'),
    )

    class StatusType(Enum):
        CREATED = 'created'
        FINISHED = 'finished'

    mining_activity_id = db.Column(
        db.Integer,
        db.ForeignKey('mining_activity.id'), nullable=False)

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    # 挖矿奖励
    gift_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 赠送币种
    gift_asset = db.Column(db.String(32), nullable=False)
    report_date = db.Column(db.Date, nullable=False)
    # 用户数据, 交易为交易量，挂单则为挂单积分
    mining_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 全站数据
    total_mining_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    status = db.Column(db.Enum(StatusType), nullable=False)


class MiningUserPledgeAmmRewardHistory(ModelBase):
    """ 用户质押挖矿、做市挖矿发放记录, 每小时发奖一次 """

    __table_args__ = (
        db.UniqueConstraint("mining_activity_id", "reward_at", "user_id", name="activity_user_id_reward_at_uniq"),
    )

    class StatusType(Enum):
        CREATED = "created"
        FINISHED = "finished"

    mining_activity_id = db.Column(db.Integer, db.ForeignKey("mining_activity.id"), nullable=False)

    user_id = db.Column(db.Integer, db.ForeignKey("user.id"), nullable=False)
    # 挖矿奖励
    gift_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 赠送币种
    gift_asset = db.Column(db.String(32), nullable=False)
    # 对齐到整点
    reward_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    # 用户数据, 质押为质押量，做市为流动性
    mining_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 全站数据
    total_mining_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 对应数据市值, 流动性市值, 仅做市挖矿有用
    mining_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    total_mining_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    status = db.Column(db.Enum(StatusType), nullable=False)


class MiningUserPledgeAmmOperateHistory(ModelBase):
    """ 用户质押、做市挖矿-操作流水表 """

    class OperateType(Enum):
        PLEDGE = 'pledge'  # 质押
        WITHDRAWAL = 'withdrawal'  # 提取(已质押的币)
        WITHDRAWAL_REWARD = 'withdrawal_reward'  # 提取奖励 （做市挖矿只支持这个操作）

    class StatusType(Enum):
        CREATED = 'created'
        FROZEN = 'frozen'
        FAILED = 'failed'
        FINISHED = 'finished'

    mining_activity_id = db.Column(db.Integer, db.ForeignKey("mining_activity.id"), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    operate_type = db.Column(db.Enum(OperateType), nullable=False)
    # 操作币种，如果是质押或提取，则为质押币种，如果是提取奖励，则为奖励币种
    asset = db.Column(db.String(32), nullable=False)
    # 操作币种的数目
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    status = db.Column(db.Enum(StatusType), nullable=False)


class LoginPageMarket(ModelBase):
    class Status(Enum):
        OPEN = '开放'
        CLOSE = '关闭'

    market = db.Column(db.String(32), nullable=False, unique=True)
    sort_id = db.Column(db.SmallInteger, nullable=False)
    status = db.Column(db.Enum(Status), default=Status.OPEN)


class LanguageArea(Enum):
    EN_US = "英语"
    ZH_HANS_CN = "中文"
    ZH_HANT_HK = "繁体中文"
    FA_IR = "波斯语"
    AR_AE = "阿拉伯语"
    RU_KZ = "俄语"
    TR_TR = "土耳其语"
    JA_JP = "日语"
    KO_KP = "韩语"
    VI_VN = "越南语"
    ID_ID = "印尼语"
    ES_ES = "西语"
    PT_PT = "葡语"
    FR_FR = "法语"
    DE_DE = "德语"
    TH_TH = "泰语"
    PL_PL = "波兰语"
    IT_IT = "意大利语"
    IN_SITE = "站内运营"


class ShortLinkInfo(ModelBase):
    """ （活动）短链接信息表 """

    __table_args__ = (
        db.Index('parent_id_trace_code', 'parent_id', 'trace_code'),
    )

    BASE62_OFFSET = 100 * 10000

    GROUPED_PC_LA_COUNTRY = {
        # 宣发渠道语区和国家的对应表，UNSPECIFIED表示不区分，IN_SITE是站内运营
        LanguageArea.EN_US: ['UNSPECIFIED', 'USA', 'GBR', 'IND', 'CAN',
                             'SGP', 'AUS', 'PHL', 'PAK',
                             'ZAF', 'NZL', 'BGD', 'IRL', 'NGA', 'NLD',
                             'GHA'],
        LanguageArea.ZH_HANS_CN: ['UNSPECIFIED', 'CHN'],
        LanguageArea.ZH_HANT_HK: ['UNSPECIFIED', 'HKG', 'TWN', 'MYS',
                                  'MAC'],
        LanguageArea.FA_IR: ['UNSPECIFIED', 'IRN', 'AFG', 'TJK'],
        LanguageArea.AR_AE: ['UNSPECIFIED', 'ARE', 'SAU', 'EGY', 'DZA',
                             'IRQ', 'QAT', 'OMN', 'BHR',
                             'KWT', 'SYR', 'MAR', 'LBN', 'JOR', 'LBY',
                             'TUN', 'YEM', 'MRT', 'SOM'],
        LanguageArea.RU_KZ: ['UNSPECIFIED', 'RUS', 'BLR', 'UKR', 'KAZ',
                             'KGZ'],
        LanguageArea.TR_TR: ['UNSPECIFIED', 'TUR', 'AZE'],
        LanguageArea.JA_JP: ['UNSPECIFIED', 'JPN'],
        LanguageArea.KO_KP: ['UNSPECIFIED', 'KOR'],
        LanguageArea.VI_VN: ['UNSPECIFIED', 'VNM'],
        LanguageArea.ID_ID: ['UNSPECIFIED', 'IDN'],
        LanguageArea.ES_ES: ['UNSPECIFIED', 'ESP', 'ARG', 'BOL', 'CHL',
                             'COL', 'CRI', 'CUB', 'DOM',
                             'ECU', 'SLV', 'GNQ', 'GTM', 'HND', 'MEX',
                             'NIC', 'PAN', 'PRY', 'PER', 'PRI',
                             'URY', 'VEN', 'GIB'],
        LanguageArea.PT_PT: ['UNSPECIFIED', 'BRA', 'PRT'],
        LanguageArea.FR_FR: ['UNSPECIFIED', 'FRA', 'CHE', 'BEL', 'LUX',
                             'MCO', 'MAR', 'DZA', 'TUN',
                             'SDN', 'BEN', 'CIV', 'CMR', 'COD', 'SEN',
                             'MDG', 'MLI', 'REU', 'TCD', 'TGO',
                             'BFA', 'GIN', 'GAB', 'BDI', 'HTI', 'MRT'],
        LanguageArea.DE_DE: ['UNSPECIFIED', 'DEU', 'AUT', 'CHE', 'LUX'],
        LanguageArea.TH_TH: ['UNSPECIFIED', 'THA'],
        LanguageArea.IT_IT: ['UNSPECIFIED', 'ITA'],
        LanguageArea.PL_PL: ['UNSPECIFIED', 'POL'],
        LanguageArea.IN_SITE: ['UNSPECIFIED'],
    }

    class StatusType(Enum):
        VALID = "valid"
        DELETED = "deleted"

    short_url = db.Column(db.String(64), nullable=False, unique=True)  # 不包含host
    original_url = db.Column(db.String(8192), nullable=False)  # 完整url
    title = db.Column(db.String(128), nullable=False, default="")
    remark = db.Column(db.String(128), nullable=False, default="")
    status = db.Column(db.Enum(StatusType), nullable=False, default=StatusType.VALID)
    deleted_at = db.Column(db.MYSQL_DATETIME_6, nullable=True)  # 用于生成报表时筛选
    publicity_channel_id = db.Column(db.Integer, db.ForeignKey('publicity_channel.id'), nullable=False)
    allow_subset = db.Column(db.Boolean, nullable=False, default=False)
    parent_id = db.Column(db.Integer)  # 父级短链接ID
    trace_code = db.Column(db.String(32))  # 子集追踪码
    language_area = db.Column(db.StringEnum(LanguageArea), nullable=True)
    country = db.Column(db.String(64), nullable=True)
    exposure_project_id = db.Column(db.Integer, db.ForeignKey('exposure_project.id'))

    @classmethod
    def full_short_url(cls, short_url):
        return urljoin(urljoin(config["SITE_URL"], "/s/"), short_url)

    @cached_property
    def offset_id(self):
        return self.BASE62_OFFSET + self.id

    @property
    def publicity_channel_name(self):
        """待添加publicity_channel_id外键后重新修改"""
        publicity_channel = PublicityChannel.query.get(self.publicity_channel_id)
        return publicity_channel.fmt_publicity_channel_name


class UserChannelStatistics(ModelBase):
    """ 用户注册渠道维度的统计信息 """
    channel = db.Column(db.String(64), unique=True)
    user_count = db.Column(db.Integer, nullable=False, default=0)  # 注册用户数
    user_bitmap = db.Column(db.MYSQL_MEDIUM_BLOB, nullable=False)  # 注册用户的bitmap
    last_update_time = db.Column(db.MYSQL_DATETIME_6, nullable=False)


class PublicityChannel(ModelBase):
    class Status(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    class Platform(Enum):
        WEB = 'web'
        APP = 'app'

    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID)
    platform = db.Column(db.StringEnum(Platform), nullable=False)
    channel = db.Column(db.String(256), nullable=False)  # 细分渠道
    remark = db.Column(db.String(256), default='')
    category_id = db.Column(db.Integer, db.ForeignKey('publicity_channel_category.id'), nullable=False)

    @property
    def fmt_publicity_channel_name(self):
        category = PublicityChannelCategory.query.get(self.category_id)
        return self.format_pub_name(self, category)

    @staticmethod
    def format_pub_name(row, category):
        return f'{row.platform.value}-' \
               f'{category.business_segment.value}-' \
               f'{category.name}-' \
               f'{row.channel}'

    @classmethod
    def fmt_many_pub_name_map(cls, rows):
        category_ids = list(set(i.category_id for i in rows))
        c_model = PublicityChannelCategory
        category_map = {i.id: i for i in c_model.query.filter(c_model.id.in_(category_ids)).all()}
        return {i.id: cls.format_pub_name(i, category_map[i.category_id]) for i in rows}

    @classmethod
    def get_all_pub_name_map(cls):
        return cls.fmt_many_pub_name_map(cls.query.all())


class BusinessSegment(Enum):
    """业务板块"""
    Default = '其他'
    Advertising = '广告投放'
    BrandSponsorship = '品牌赞助'
    KolKoc = 'KOL/KOC'
    MediaCooperation = '媒体合作'
    OfflineActivity = '线下活动'
    SnsActivity = 'SNS活动'
    CommunityActivity = '社区活动'
    OnsiteActivity = '站内活动'


class PublicityChannelCategory(ModelBase):
    """渠道平台"""

    class Status(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    business_segment = db.Column(db.StringEnum(BusinessSegment), nullable=False, default=BusinessSegment.Default)
    name = db.Column(db.String(256), nullable=False)
    status = db.Column(db.StringEnum(Status), default=Status.VALID)


class PublicityMixin:

    @classmethod
    def get_category_dic(cls):
        c_id_name_dic, c_id_b_segment_dic, b_segment_c_ids_dic, c_id_b_segment_name_dic = dict(), dict(), defaultdict(
            list), dict()
        records = PublicityChannelCategory.query.filter(
            PublicityChannelCategory.status == PublicityChannelCategory.Status.VALID).all()
        for record in records:
            id_ = record.id
            c_id_name_dic[id_] = record.name
            c_id_b_segment_dic[id_] = record.business_segment.value
            c_id_b_segment_name_dic[id_] = record.business_segment.name
            b_segment_c_ids_dic[record.business_segment.name].append(id_)
        return c_id_name_dic, b_segment_c_ids_dic, c_id_b_segment_dic, c_id_b_segment_name_dic

    @classmethod
    def get_category_pub_channels_dic(cls):
        model = PublicityChannel
        recs = model.query.filter(
            model.status == model.Status.VALID
        ).with_entities(
            model.category_id,
            model.id,
            model.channel
        ).all()
        c_id_pub_channels_dic, pub_channel_id_name_dic = defaultdict(list), dict()
        for rec in recs:
            id_ = rec.id
            c_id_pub_channels_dic[rec.category_id].append(id_)
            pub_channel_id_name_dic[id_] = rec.channel
        return c_id_pub_channels_dic, pub_channel_id_name_dic

    @classmethod
    def get_short_url_publicity_channel_fmt_name_dic(cls):
        short_recs = ShortLinkInfo.query.filter(
            ShortLinkInfo.status == ShortLinkInfo.StatusType.VALID
        ).with_entities(
            ShortLinkInfo.short_url,
            ShortLinkInfo.publicity_channel_id,
            ShortLinkInfo.language_area,
            ShortLinkInfo.country,
        ).all()
        publicity_ids = {i.publicity_channel_id for i in short_recs}
        publicities = PublicityChannel.query.filter(
            PublicityChannel.id.in_(publicity_ids)
        ).all()
        publicity_channel_dic = {i.id: i.channel for i in publicities}
        publicity_platform_dic = {i.id: i.platform for i in publicities}
        publicity_category_dic = {i.id: i.category_id for i in publicities}

        publicity_categories = {i.category_id for i in publicities}
        categories = PublicityChannelCategory.query.filter(
            PublicityChannelCategory.id.in_(publicity_categories)
        ).with_entities(
            PublicityChannelCategory.id,
            PublicityChannelCategory.name,
            PublicityChannelCategory.business_segment
        ).all()
        category_name_dic = {i.id: i.name for i in categories}
        category_segment_dic = {i.id: i.business_segment for i in categories}
        res = dict()
        for rec in short_recs:
            publicity_id = rec.publicity_channel_id
            channel = publicity_channel_dic[publicity_id]
            platform = publicity_platform_dic[publicity_id].value
            category_id = publicity_category_dic[publicity_id]
            category_name = category_name_dic[category_id]
            segment = category_segment_dic[category_id].value
            language_area = rec.language_area.value if rec.language_area else ''
            country = COUNTRY_CODE_CN_NAME_DIC.get(rec.country, '')
            res[rec.short_url] = f'{platform}-{language_area}-{country}-{segment}-{category_name}-{channel}'
        return res


class ExposureProject(ModelBase):
    """曝光专项(关联短链接)"""

    class Type(Enum):
        ASSET = "资产曝光"
        CONTENT = "专业内容曝光"

    class Status(Enum):
        VALID = "valid"
        DELETED = "deleted"

    name = db.Column(db.String(256), index=True, nullable=False)
    type = db.Column(db.StringEnum(Type), nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID)
    detail = db.Column(db.String(512), nullable=False)
    remark = db.Column(db.String(256), default='')


class ActivityCondition:

    class ConditionKeys(Enum):
        VIP = 'VIP'
        KYC = 'KYC'
        HOLDING = 'HOLDING'
        BALANCE_VALUE = 'BALANCE_VALUE'
        TRADE_VALUE = 'TRADE_VALUE'
        REGISTERED_VALUE = 'REGISTERED_VALUE'
        USED_VALUE = 'USED_VALUE'
        MARKET_MAKER = 'MARKET_MAKER'

    CONDITION_LIST = [x.name for x in ConditionKeys]

    class MarketMakerTypeKeys(Enum):
        NOT_LIMITED = 'NOT_LIMITED'  # 不限制
        NOT_MARKET_MAKER = 'NOT_MARKET_MAKER'  # 非做市商
        MARKET_MAKER = 'MARKET_MAKER'  # 做市商
        SPOT_MARKET_MAKER = 'SPOT_MARKET_MAKER'  # 现货做市商
        PERPETUAL_MARKET_MAKER = 'PERPETUAL_MARKET_MAKER'  # 合约做市商

    class MarketMakerType(Enum):
        NOT_LIMITED = '不限制'  # 不限制
        NOT_MARKET_MAKER = '非做市商'  # 非做市商
        MARKET_MAKER = '做市商'  # 做市商
        SPOT_MARKET_MAKER = '现货做市商'  # 现货做市商
        PERPETUAL_MARKET_MAKER = '合约做市商'  # 合约做市商

    class HoldingKeys(Enum):
        NEED_HOLDING = 'NEED_HOLDING'
        ASSET_HOLDING = 'ASSET_HOLDING'

    class BalanceValueKey(Enum):
        BALANCE_OP_TYPE = 'BALANCE_OP_TYPE'
        BALANCE_VALUE = 'BALANCE_VALUE'

    class TradeBusinessTypeKey(Enum):
        REAL_TIME = 'REAL_TIME'  # 实时n天内
        BEFORE_START = 'BEFORE_START'  # 开始时间前n天内

    class TradeValueKey(Enum):
        TRADE_OP_TYPE = 'TRADE_OP_TYPE'
        TRADE_VALUE = 'TRADE_VALUE'
        TRADE_DAY_RANGE = 'TRADE_DAY_RANGE'
        TRADE_BUSINESS_TYPE = 'TRADE_BUSINESS_TYPE'
        TRADE_TYPE_RANGE = 'TRADE_TYPE_RANGE'  # 业务范围

    class TradeType(Enum):
        ALL = "全部"
        SPOT = "现货交易"
        PERPETUAL = "合约交易"
        EXCHANGE = "兑换交易"
        SPOT_GRID = "现货网格"
        AMM = "AMM"
        AUTO_INVEST = "定投"

    class RegisteredTimeKey(Enum):
        REGISTERED_VALUE = "REGISTERED_VALUE"
        REGISTERED_OP_TYPE = "REGISTERED_OP_TYPE"

    class KYCType(Enum):
        TRUE = '1'
        FALSE = '0'

    class VIPType(Enum):
        VIP0 = '0'
        VIP1 = '1'
        VIP2 = '2'
        VIP3 = '3'
        VIP4 = '4'
        VIP5 = '5'

    class NeedHoldingType(Enum):
        NOT_LIMITED = '0'  # 不限制
        HOLD = '1'  # 持有过
        NOT_HOLD = '2'  # 未持有过

    class BalanceValueOperateType(Enum):
        NOT_LIMITED = '0'  # 不限制
        GREATER = '1'  # 大于
        LESS = '2'  # 小于

    class TradeValueOperateType(Enum):
        NOT_LIMITED = '0'  # 不限制
        GREATER = '1'  # 大于
        LESS = '2'  # 小于

    class RegisteredTimeType(Enum):
        NOT_LIMITED = '0'  # 不限制
        GREATER = '1'  # 大于
        LESS = '2'  # 小于

    # 币币，合约，兑换，杠杆，法币，定投，现货网格，理财，AMM，借贷 (最多选择3个)
    # 存储 ["SPOT","FUTURE"]
    class UsedType(Enum):
        SPOT = "现货交易"
        PERPETUAL = "合约交易"
        EXCHANGE = "兑换交易"
        MARGIN = "杠杆交易"
        FIAT = "法币"
        AUTO_INVEST = "定投计划"
        SPOT_GRID = "现货网格"
        INVESTMENT = "理财计划"
        AMM = "AMM"
        PLEDGE = "借贷"

    @classmethod
    def build_condition_str(cls, conditions: list) -> str:
        li = []
        for c in conditions:
            key, value = c['key'], c['value']
            method_ = getattr(cls, f'_build_{key.lower()}_condition_str')
            if not method_:
                raise InvalidArgument(message=f"build_{key.lower()}_condition_str method not exist")
            t = method_(value)
            if t:
                li.append(t)
        return ', '.join(li)

    @classmethod
    def _build_vip_condition_str(cls, value) -> str:
        number = value
        if value != cls.VIPType.VIP0.value:
            return gettext("等级为VIP%(number)s及以上", number=number)
        return ''

    @classmethod
    def _build_kyc_condition_str(cls, value) -> str:
        if value == cls.KYCType.TRUE.value:
            return gettext("完成KYC认证")
        return ''

    @classmethod
    def _build_holding_condition_str(cls, value) -> str:
        d = json.loads(value)
        need_holding = d['NEED_HOLDING']
        asset_holding = d['ASSET_HOLDING']
        if need_holding == cls.NeedHoldingType.HOLD.value:
            return gettext("持有过%(asset)s", asset=asset_holding)
        elif need_holding == cls.NeedHoldingType.NOT_HOLD.value:
            return gettext("从未持有过%(asset)s", asset=asset_holding)
        return ''

    @classmethod
    def _build_balance_value_condition_str(cls, value) -> str:
        d = json.loads(value)
        balance_op_type = d['BALANCE_OP_TYPE']
        balance_value = d['BALANCE_VALUE']
        op_type = ''
        if balance_op_type == cls.BalanceValueOperateType.GREATER.value:
            op_type = '>='
        elif balance_op_type == cls.BalanceValueOperateType.LESS.value:
            op_type = '<'
        if op_type:
            return gettext("总资产 %(op_type)s %(balance)s USD", op_type=op_type, balance=balance_value)
        return ''

    @classmethod
    def _build_trade_value_condition_str(cls, value) -> str:
        d = json.loads(value)
        trade_op_type = d['TRADE_OP_TYPE']
        trade_value = d['TRADE_VALUE']
        trade_day_range = d['TRADE_DAY_RANGE']
        trade_business_type = d['TRADE_BUSINESS_TYPE']
        op_type = ''
        if trade_op_type == cls.TradeValueOperateType.GREATER.value:
            op_type = '>='
        elif trade_op_type == cls.TradeValueOperateType.LESS.value:
            op_type = '<'
        if op_type:
            if trade_business_type == cls.TradeBusinessTypeKey.REAL_TIME.value:
                return gettext("近%(trade_day_range)s天累计交易额 %(op_type)s %(trade_value)s USD",
                               trade_day_range=trade_day_range,
                               op_type=op_type,
                               trade_value=trade_value)
            else:
                return gettext("活动开始前近%(trade_day_range)s天累计交易额 %(op_type)s %(trade_value)s USD",
                               trade_day_range=trade_day_range,
                               op_type=op_type,
                               trade_value=trade_value)
        return ''

    @classmethod
    def _build_registered_value_condition_str(cls, value) -> str:
        d = json.loads(value)
        registered_op_type = d['REGISTERED_OP_TYPE']
        registered_value = d['REGISTERED_VALUE']
        if registered_op_type == cls.RegisteredTimeType.GREATER.value:
            registered_time = datetime_to_str(timestamp_to_datetime(registered_value / 1000))
            return gettext("注册时间在%(registered_time)s（UTC）之后", registered_time=registered_time)
        elif registered_op_type == cls.RegisteredTimeType.LESS.value:
            registered_time = datetime_to_str(timestamp_to_datetime(registered_value / 1000))
            return gettext("注册时间在%(registered_time)s（UTC）之前", registered_time=registered_time)
        return ''

    @classmethod
    def _build_used_value_condition_str(cls, value) -> str:
        if value:
            used_li = value.split(",")
            used_map = {
                cls.UsedType.SPOT: gettext('现货交易'),
                cls.UsedType.PERPETUAL: gettext('合约交易'),
                cls.UsedType.EXCHANGE: gettext('兑换'),
                cls.UsedType.MARGIN: gettext('杠杆交易'),
                cls.UsedType.FIAT: gettext('法币'),
                cls.UsedType.AUTO_INVEST: gettext('定投计划'),
                cls.UsedType.SPOT_GRID: gettext('现货网格'),
                cls.UsedType.INVESTMENT: gettext('理财'),
                cls.UsedType.AMM: gettext('AMM'),
                cls.UsedType.PLEDGE: gettext('质押借币'),
            }
            used_functions = ",".join(
                [used_map[cls.UsedType[i]] for i in used_li if cls.UsedType[i] and used_map[cls.UsedType[i]]])
            return gettext("有过：%(used_functions)s", used_functions=used_functions)
        return ''


class AirdropActivity(ModelBase):
    """ 空投活动基础表 """

    LOTTERY_DAY = 1  # 用于预计抽奖时间的一个天数配置

    class StatusType(Enum):
        DRAFT = "未提交"
        PENDING = "待审核"
        REJECTED = "审核未通过"
        ONLINE = "已上架"
        OFFLINE = "未上架"
        FINISHED = "已完成"

    class AirdropMode(Enum):
        # 实时
        REALTIME = 'realtime'
        # 随机
        RANDOM = "random"

    class ActiveStatus(Enum):
        DRAFT = "draft"
        PENDING = "pending"
        REJECTED = "rejected"
        STARTED = "started"  # 进行中
        CREATED = "created"  # 未开始
        PROCESSED = "processed"  # 抽签中
        FINISHED = "finished"  # 已结束

    class LabelType(Enum):
        ASSET = "币种"
        OTHER = "其他"

    class FundingSource(Enum):
        COMPANY_EXPENSES = '公司支出'
        PROJECT_EXPENSES = '项目方支出'

    class EstimateType(Enum):
        EQUAL = "等于"
        APPROXIMATELY_EQUAL = "约等于"

    name = db.Column(db.String(128), nullable=False)
    airdrop_mode = db.Column(db.Enum(AirdropMode), nullable=False)
    funding_source = db.Column(db.StringEnum(FundingSource), nullable=False, default=FundingSource.COMPANY_EXPENSES)
    asset = db.Column(db.String(32), nullable=False)
    total_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    total_count = db.Column(db.Integer, nullable=False)
    lock_day = db.Column(db.Integer, nullable=False)
    start_time = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    end_time = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    more_reward_url = db.Column(db.String(256))
    status = db.Column(db.StringEnum(StatusType), nullable=False)
    activity_id = db.Column(db.Integer, db.ForeignKey('activity.id'), nullable=False)
    label_type = db.Column(db.StringEnum(LabelType), nullable=False, default=LabelType.ASSET)
    info_url = db.Column(db.String(256))
    assets = db.Column(db.String(256))
    is_checked = db.Column(db.Boolean, default=False, nullable=True)
    estimate_type = db.Column(db.StringEnum(EstimateType), nullable=False, default=EstimateType.EQUAL)  # 价值预估类型
    audit_remark = db.Column(db.String(256))  # 审核意见

    @property
    def duration(self):
        d = self.end_time - self.start_time
        return d.days

    @property
    def amount(self):
        return Decimal(self.total_amount / self.total_count)

    @property
    def is_active(self):
        if self.status == self.StatusType.OFFLINE:
            return False
        return self.active_status == self.ActiveStatus.STARTED

    @property
    def active_status(self):
        direct_status_map = {
            self.StatusType.DRAFT: self.ActiveStatus.DRAFT,
            self.StatusType.PENDING: self.ActiveStatus.PENDING,
            self.StatusType.REJECTED: self.ActiveStatus.REJECTED,
        }
        if self.status in direct_status_map:
            return direct_status_map[self.status]
        now_ = now()
        if now_ < self.start_time:
            return self.ActiveStatus.CREATED
        if now_ < self.end_time and self.status == self.StatusType.ONLINE:
            return self.ActiveStatus.STARTED
        if self.end_time < now_ and self.status == self.StatusType.ONLINE \
                and self.airdrop_mode == self.AirdropMode.RANDOM:  # 实时没有抽签中状态
            return self.ActiveStatus.PROCESSED
        return self.ActiveStatus.FINISHED


class CalendarActivity(ModelBase):
    """ 日历活动基础表 """

    class StatusType(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    class ActivityType(Enum):
        # 空投、交易赛、Dock、SNS、法币、5th
        AIRDROP = 'airdrop'
        TRADE = 'trade'
        DOCK = 'dock'
        SNS = 'sns'
        FIAT = 'fiat'
        TH_5 = '5th'

    class ActiveStatus(Enum):

        CREATED = "created"  # 待开始
        STARTED = "started"  # 进行中
        FINISHED = "finished"  # 已结束
        # 以下字段只用于admin展示
        PREPARED = "prepared"  # 待上架
        OFFLINE = "offline"  # 已下架

    name = db.Column(
        db.String(128), nullable=False)
    activity_type = db.Column(db.StringEnum(ActivityType), nullable=False)
    # 跳转链接
    url = db.Column(db.String(1024), nullable=False, default='')
    # 操作人
    updated_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    # 开始时间
    start_time = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    # 结束时间
    end_time = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    # 上架时间
    online_time = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    # 下架时间
    offline_time = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    status = db.Column(db.StringEnum(StatusType), nullable=False)
    activity_id = db.Column(db.Integer, db.ForeignKey('activity.id'),
                            nullable=False)

    @property
    def active_status(self):
        now_ = now()
        if now_ < self.online_time:
            return self.ActiveStatus.PREPARED
        if now_ < self.start_time:
            return self.ActiveStatus.CREATED
        if now_ < self.end_time:
            return self.ActiveStatus.STARTED
        if now_ < self.offline_time:
            return self.ActiveStatus.FINISHED
        return self.ActiveStatus.OFFLINE


class CalendarContent(db.Model):
    AVAILABLE_LANGS = (
        Language.EN_US,
        Language.ZH_HANS_CN,
        Language.ZH_HANT_HK,
        Language.JA_JP,
        Language.KO_KP,
        Language.RU_KZ,
        Language.ES_ES,
        Language.ID_ID,
        Language.FA_IR,
        Language.TR_TR,
        Language.VI_VN,
        Language.AR_AE,
        Language.FR_FR,
        Language.PT_PT,
        Language.DE_DE,
        Language.TH_TH,
        Language.PL_PL,
        Language.IT_IT,
    )

    id = db.Column(db.Integer, primary_key=True)
    owner_id = db.Column(db.Integer, db.ForeignKey('calendar_activity.id'),
                         nullable=False)
    title = db.Column(db.String(512), nullable=False, default='')
    lang = db.Column(db.StringEnum(Language), nullable=False)
    url = db.Column(db.String(1024), nullable=False, default='')


class AirdropActivityDetail(ModelBase):
    __table_args__ = (db.UniqueConstraint("airdrop_activity_id", "lang",
                                          name="airdrop_activity_id_lang_unique"),)

    AVAILABLE_LANGS = (
        Language.EN_US,
        Language.ZH_HANS_CN,
        Language.ZH_HANT_HK,
        Language.JA_JP,
        Language.KO_KP,
        Language.RU_KZ,
        Language.ES_ES,
        Language.ID_ID,
        Language.FA_IR,
        Language.TR_TR,
        Language.VI_VN,
        Language.AR_AE,
        Language.FR_FR,
        Language.PT_PT,
        Language.DE_DE,
        Language.TH_TH,
        Language.PL_PL,
        Language.IT_IT
    )

    airdrop_activity_id = db.Column(
        db.Integer,
        db.ForeignKey('airdrop_activity.id'), nullable=False)

    title = db.Column(db.String(512), nullable=False)
    lang = db.Column(db.StringEnum(Language), nullable=False)
    summary = db.Column(db.String(512), nullable=False)  # 项目简介
    introductions = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False, default='')  # 项目介绍
    cover = db.Column(db.String(256), nullable=False)
    video_url = db.Column(db.String(256))

    @property
    def cover_url(self):
        if not (file_key := self.cover):
            return ''
        return AWSBucketPublic.get_file_url(file_key)


class AirdropActivityUserRecord(ModelBase):
    """ 用户空投活动参与记录 """
    __table_args__ = (
        db.UniqueConstraint(
            'airdrop_activity_id', 'user_id',
            name='airdrop_activity_id_user_id_unique'),
    )

    class Status(Enum):
        CREATED = "待领取"  # 待开奖、未中奖
        FAILED = "领取失败"
        PARTIAL = "部分领取成功"
        SUCCEED = "领取成功"

    airdrop_activity_id = db.Column(db.Integer, db.ForeignKey('airdrop_activity.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    airdrop_mode = db.Column(db.Enum(AirdropActivity.AirdropMode), nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False)


class AirdropActivityLotteryHistory(ModelBase):
    """ 用户空投活动抽奖号记录(随机活动) """
    __table_args__ = (
        db.UniqueConstraint(
            'airdrop_activity_id', 'user_id',
            name='airdrop_activity_id_user_id_unique'),
        db.UniqueConstraint(
            'airdrop_activity_id', 'lottery_number',
            name='airdrop_activity_id_lottery_number_unique'),
    )

    class StatusType(Enum):
        SUCCEED = "已中奖"
        CREATED = "待开奖"
        FAILED = "未中奖"

    class StateType(Enum):
        VALID = "有效"
        INVALID = "无效"

    airdrop_activity_id = db.Column(db.Integer,
                                    db.ForeignKey('airdrop_activity.id'),
                                    nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    user_record_id = db.Column(db.Integer, db.ForeignKey('airdrop_activity_user_record.id'))
    lottery_number = db.Column(db.Integer)
    status = db.Column(db.StringEnum(StatusType), nullable=False, default=StatusType.CREATED)
    remark = db.Column(db.String(64), nullable=True, default='')
    state = db.Column(db.StringEnum(StateType), nullable=False, default=StateType.VALID)

    @property
    def actual_status(self):
        if (self.state == self.StateType.INVALID and
                self.status == self.StatusType.SUCCEED):
            return self.StatusType.FAILED
        return self.status

    @property
    def lottery_number_zfill(self):
        from app.business.activity.lottery_number_map import convert
        zfill_num = 7  # 抽签号生成7位数
        mapping_num = 8
        return convert(self.lottery_number, zfill_num, mapping_num)


class AirdropActivityReward(ModelBase):
    """ 空投活动奖励 """

    class Type(Enum):
        ASSET = '币种'
        COUPON = '卡券'
        EQUITY = '权益'

    airdrop_activity_id = db.Column(db.Integer, db.ForeignKey('airdrop_activity.id'), nullable=False)
    type = db.Column(db.StringEnum(Type), nullable=False)
    asset = db.Column(db.String(32), nullable=True, index=True)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    coupon_apply_id = db.Column(db.Integer, db.ForeignKey("coupon_apply.id"))
    biz_id = db.Column(db.Integer, index=True)  # 业务ID（equity_id），具体什么业务根据type来确定


class AirdropActivityRewardHistory(ModelBase):
    """ 用户空投活动获奖记录 """

    class AirdropMode(Enum):
        # 实时
        REALTIME = 'realtime'
        # 随机
        RANDOM = "random"

    class Status(Enum):
        CREATED = "created"
        FINISHED = "finished"
        FAILED = "failed"

    airdrop_activity_id = db.Column(db.Integer,
                                    db.ForeignKey('airdrop_activity.id'),
                                    nullable=False)
    airdrop_reward_id = db.Column(db.Integer, index=True)  # AirdropActivityReward.id
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    user_record_id = db.Column(db.Integer, db.ForeignKey('airdrop_activity_user_record.id'))
    airdrop_mode = db.Column(db.Enum(AirdropMode),
                             nullable=False, default=AirdropMode.REALTIME)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED)
    asset = db.Column(db.String(32), nullable=True)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    unlocked_at = db.Column(db.MYSQL_DATETIME_6)
    coupon_apply_id = db.Column(db.Integer, db.ForeignKey("coupon_apply.id"))
    reason = db.Column(db.String(512), nullable=True)


class AirdropActivityQuestionBank(ModelBase):
    ANSWER_LIST = ['A', 'B', 'C']

    class StatusType(Enum):
        DELETED = 'deleted'
        PASSED = 'passed'

    airdrop_activity_id = db.Column(db.Integer,
                                    db.ForeignKey('airdrop_activity.id'),
                                    nullable=False)
    question = db.Column(db.String(256), nullable=False)
    answer = db.Column(db.String(256), nullable=False)
    answer_analysis = db.Column(db.String(512))  # 答案解析
    # {"A": "xxx", "B": "xxx", "C": "xxx"}
    options = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False)
    lang = db.Column(db.StringEnum(Language), nullable=False)
    status = db.Column(db.Enum(StatusType), nullable=False, default=StatusType.PASSED)


class AirdropActivityCondition(ModelBase):
    __table_args__ = (
        db.UniqueConstraint(
            'airdrop_activity_id', 'key',
            name='airdrop_activity_id_key_unique'),
    )

    airdrop_activity_id = db.Column(db.Integer, db.ForeignKey('airdrop_activity.id'), nullable=False)
    key = db.Column(db.String(64), index=True, nullable=False)
    value = db.Column(db.String(512), nullable=False)


class AirdropActivityStatistic(ModelBase):
    # 用于需要持久化的交易条件统计

    class BusinessType(Enum):
        TRADE_BEFORE_START = 'TRADE_BEFORE_START'  # 活动开始前统计交易达标人数

    __table_args__ = (
        db.UniqueConstraint('airdrop_activity_id', 'business_type', name='airdrop_activity_business_uniq'),
    )

    airdrop_activity_id = db.Column(db.Integer,
                                    db.ForeignKey('airdrop_activity.id'),
                                    nullable=False)
    business_type = db.Column(db.StringEnum(BusinessType), nullable=False,
                              default=BusinessType.TRADE_BEFORE_START)
    user_bit_map = db.Column(db.MYSQL_MEDIUM_BLOB, nullable=False)
    last_update_time = db.Column(db.MYSQL_DATETIME_6, nullable=False)

    def get_user_ids(self) -> list:
        if not self.user_bit_map:
            return []
        bm = BitMap.deserialize(self.user_bit_map)
        if not bm:
            return []
        return bm


class DiscountActivity(ModelBase):
    """ 打折购活动基础表 """

    LOTTERY_DAY = 1  # 用于预计抽奖时间的一个天数配置
    PAY_ASSET = 'USDT'  # 目前只用USDT作为定价货币

    class StatusType(Enum):
        ONLINE = "已上架"
        OFFLINE = "未上架"
        FINISHED = "已完成"

    class DiscountType(Enum):
        SEVENTY = Decimal('0.7')
        FIFTY = Decimal('0.5')
        THIRTY = Decimal('0.3')
        TEN = Decimal('0.1')

    class ActiveStatus(Enum):
        STARTED = "started"  # 进行中
        CREATED = "created"  # 未开始
        PROCESSED = "processed"  # 抽签中
        FINISHED = "finished"  # 已结束

    name = db.Column(db.String(128), nullable=False)
    asset = db.Column(db.String(32), nullable=False)
    total_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    price = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    total_count = db.Column(db.Integer, nullable=False)
    start_time = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    end_time = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    status = db.Column(db.StringEnum(StatusType), nullable=False)
    discount_type = db.Column(db.StringEnum(DiscountType), nullable=False)
    activity_id = db.Column(db.Integer, db.ForeignKey('activity.id'), nullable=False)
    is_checked = db.Column(db.Boolean, default=False, nullable=True)  # 是否已经完成清退用户的排除（派奖前的校验）

    @property
    def market(self):
        return f'{self.asset}{self.PAY_ASSET}'

    @property
    def amount(self):
        return Decimal(self.total_amount / self.total_count)

    @property
    def discount(self):
        return 1 - self.discount_type.value

    @property
    def discount_price(self):
        precision = abs(self.price.as_tuple().exponent)  # 折扣价保持与价格一样精度
        return quantize_amount(self.discount * self.price, precision)

    @property
    def pay_amount(self):
        return quantize_amount(self.amount * self.price * self.discount, 2)

    @property
    def active_status(self):
        now_ = now()
        if now_ < self.start_time:
            return self.ActiveStatus.CREATED
        if now_ < self.end_time and self.status == self.StatusType.ONLINE:
            return self.ActiveStatus.STARTED
        if self.end_time < now_ and self.status == self.StatusType.ONLINE:
            return self.ActiveStatus.PROCESSED
        return self.ActiveStatus.FINISHED


class DiscountActivityDetail(ModelBase):
    __table_args__ = (db.UniqueConstraint("discount_activity_id", "lang",
                                          name="discount_activity_id_lang_unique"),)

    AVAILABLE_LANGS = (
        Language.EN_US,
        Language.ZH_HANS_CN,
        Language.ZH_HANT_HK,
        Language.JA_JP,
        Language.KO_KP,
        Language.RU_KZ,
        Language.ES_ES,
        Language.ID_ID,
        Language.FA_IR,
        Language.TR_TR,
        Language.VI_VN,
        Language.AR_AE,
        Language.FR_FR,
        Language.PT_PT,
        Language.DE_DE,
        Language.TH_TH,
        Language.PL_PL,
        Language.IT_IT
    )

    discount_activity_id = db.Column(
        db.Integer,
        db.ForeignKey('discount_activity.id'), nullable=False)

    title = db.Column(db.String(512), nullable=False)
    lang = db.Column(db.StringEnum(Language), nullable=False)


class DiscountActivityCondition(ModelBase):
    discount_activity_id = db.Column(db.Integer, db.ForeignKey('discount_activity.id'),
                                     nullable=False)
    key = db.Column(db.String(64), index=True, nullable=False)
    value = db.Column(db.String(512), nullable=False)


class DiscountActivityLotteryHistory(ModelBase):
    """ 用户Dibs活动抽奖号记录 """
    __table_args__ = (
        db.UniqueConstraint(
            'discount_activity_id', 'user_id',
            name='discount_activity_id_user_id_unique'),
        db.UniqueConstraint(
            'discount_activity_id', 'lottery_number',
            name='discount_activity_id_lottery_number_unique'),
    )

    class StatusType(Enum):
        SUCCEED = "已中奖"
        CREATED = "待开奖"
        FAILED = "未中奖"

    class StateType(Enum):
        VALID = "有效"
        INVALID = "无效"

    discount_activity_id = db.Column(db.Integer,
                                     db.ForeignKey('discount_activity.id'),
                                     nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    lottery_number = db.Column(db.Integer)
    status = db.Column(db.StringEnum(StatusType), nullable=False, default=StatusType.CREATED)
    state = db.Column(db.StringEnum(StateType), nullable=False, default=StateType.VALID)
    remark = db.Column(db.String(64), nullable=True, default='')

    @property
    def actual_status(self):
        if (self.state == self.StateType.INVALID and
                self.status == self.StatusType.SUCCEED):
            return self.StatusType.FAILED
        return self.status

    @property
    def lottery_number_zfill(self):
        from app.business.activity.lottery_number_map import convert
        zfill_num = 7  # 抽签号生成7位数
        mapping_num = 8
        return convert(self.lottery_number, zfill_num, mapping_num)


class DiscountActivityRewardHistory(ModelBase):
    """ 用户Dibs活动申购获奖记录 """

    __table_args__ = (
        db.UniqueConstraint(
            'discount_activity_id', 'user_id',
            name='discount_activity_id_user_id_unique'),
    )

    discount_activity_id = db.Column(db.Integer,
                                     db.ForeignKey('discount_activity.id'),
                                     nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    asset = db.Column(db.String(32), nullable=False)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)


class DiscountActivityStatistic(ModelBase):
    # 打折购用于需要持久化的交易条件统计

    class BusinessType(Enum):
        TRADE_BEFORE_START = 'TRADE_BEFORE_START'  # 活动开始前统计交易达标人数

    __table_args__ = (
        db.UniqueConstraint('discount_activity_id', 'business_type', name='discount_activity_business_uniq'),
    )

    discount_activity_id = db.Column(db.Integer,
                                     db.ForeignKey('discount_activity.id'),
                                     nullable=False)
    business_type = db.Column(db.StringEnum(BusinessType), nullable=False,
                              default=BusinessType.TRADE_BEFORE_START)
    user_bit_map = db.Column(db.MYSQL_MEDIUM_BLOB, nullable=False)
    last_update_time = db.Column(db.MYSQL_DATETIME_6, nullable=False)

    def get_user_ids(self) -> list:
        if not self.user_bit_map:
            return []
        bm = BitMap.deserialize(self.user_bit_map)
        if not bm:
            return []
        return bm


class DiscountActivityOrder(ModelBase):
    """ 用户Dibs活动申购记录,记录用户申购状态表单"""

    class AssetStatusType(Enum):
        PENDING_DEDUCT = '待扣款'
        DEDUCTED = '已扣款'
        FINISHED = '已完成'

    class StatusType(Enum):
        CREATED = '创建中'
        VALID = '申购中'
        FINISHED = '已结束'  # 抽签完处理完资金

    discount_activity_id = db.Column(db.Integer, db.ForeignKey('discount_activity.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    status = db.Column(db.StringEnum(StatusType), nullable=False, default=StatusType.CREATED)
    asset_status = db.Column(db.StringEnum(AssetStatusType), nullable=False, default=AssetStatusType.PENDING_DEDUCT)


class DiscountActivitySystemOrder(ModelBase):
    """系统生成Dibs买入申购币种成交订单"""

    class Status(Enum):
        PROCESSING = "processing"
        FINISHED = "finished"

    class Result(Enum):
        FAILED = "成交失败"
        PARTIAL = "部分成交"
        ALL = "完全成交"

    discount_activity_id = db.Column(db.Integer, db.ForeignKey('discount_activity.id'), nullable=False)
    market = db.Column(db.String(32), nullable=False)
    order_id = db.Column(db.BigInteger, unique=True, nullable=True)

    source_asset = db.Column(db.String(32), nullable=False)  # 需要交易的币种
    source_asset_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 需要交易的币种数目
    source_asset_traded_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 已交易的币种数目
    target_asset = db.Column(db.String(32), nullable=False)  # 得到的目标币种
    target_asset_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 需要交易的币种数目
    target_asset_traded_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 得到的目标币种数目

    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.PROCESSING)
    timeout_at = db.Column(db.MYSQL_DATETIME_6)
    result = db.Column(db.StringEnum(Result), nullable=True)
    price = db.Column(db.MYSQL_DECIMAL_PRICE, nullable=False)

    @property
    def is_timeout(self) -> bool:
        return self.timeout_at and self.timeout_at <= now()


class IeoListingApplication(ModelBase):
    class Type(Enum):
        COIN = 'coin'
        TOKEN = 'token'

    class Status(Enum):
        CREATED = 'created'
        AUDITED = 'audited'
        REJECTED = 'rejected'
        DELETED = 'deleted'

    created_by = db.Column(db.Integer, db.ForeignKey('user.id'),
                           nullable=False)
    updated_by = db.Column(db.Integer, db.ForeignKey('user.id'),
                           nullable=False)

    code = db.Column(db.String(32), nullable=False)
    name = db.Column(db.String(64), nullable=False)
    icon = db.Column(db.String(128), nullable=False)
    type = db.Column(db.Enum(Type), nullable=False)
    issued_date = db.Column(db.DATE)
    issued_price_data = db.Column(db.Text)
    total_supply = db.Column(db.String(64))
    total_circulate = db.Column(db.String(64))
    usd_circulate = db.Column(db.String(64))
    official_website = db.Column(db.String(128))
    white_paper = db.Column(db.String(512))
    source_code = db.Column(db.String(128))
    explorer = db.Column(db.String(128))
    contract_address = db.Column(db.String(256))
    fundraising = db.Column(db.Text)
    token_distribution = db.Column(db.Text)
    linkedin_or_cv = db.Column(db.Text)
    applicant_name = db.Column(db.String(128))
    applicant_role = db.Column(db.String(128))
    applicant_email = db.Column(db.String(128))
    applicant_telegram = db.Column(db.String(64))

    telegram = db.Column(db.String(64))
    facebook = db.Column(db.String(64))
    twitter = db.Column(db.String(64))
    reddit = db.Column(db.String(64))
    medium = db.Column(db.String(64))
    discord = db.Column(db.String(64))
    is_first = db.Column(db.Boolean, nullable=False, default=False)
    is_securities = db.Column(db.Boolean, nullable=False, default=False)

    avg_price_data = db.Column(db.Text)
    discount = db.Column(db.String(64))
    is_locked = db.Column(db.Boolean, default=False)
    unlock_condition = db.Column(db.String(256))
    product_url = db.Column(db.String(512))
    test_code = db.Column(db.String(64))
    support_chain_url = db.Column(db.String(512))

    status = db.Column(db.Enum(Status), nullable=False, default=Status.CREATED)
    intro_en = db.Column(db.Text)
    intro_cn = db.Column(db.Text)


class IeoApplicationFiles(ModelBase):
    class Status(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    class FileType(Enum):
        LABS = 'labs'
        COIN = 'coin'
        LAW = 'law'
        AUDIT = 'audit'
        SECURITIES = 'securities'
        COMMITMENT = 'commitment'

    ieo_listing_application_id = db.Column(
        db.Integer, db.ForeignKey('ieo_listing_application.id'))
    file_id = db.Column(db.Integer, db.ForeignKey('file.id'))
    name = db.Column(db.String(64), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    file_type = db.Column(db.Enum(FileType), nullable=False)
    status = db.Column(db.Enum(Status), nullable=False, default=Status.VALID)

    user = db.relationship(
        'User', backref=db.backref('ieo_application_files', lazy='dynamic'))


class IeoAssetInformation(ModelBase):
    """ IEO活动币种信息 """

    ieo_activity_id = db.Column(db.Integer, db.ForeignKey('ieo_activity.id'), nullable=False)
    asset = db.Column(db.String(32), nullable=False)
    name = db.Column(db.String(64), nullable=False)
    # 页面顶部logo
    icon = db.Column(db.String(128), nullable=False)
    # 项目资料logo
    project_icon = db.Column(db.String(128), nullable=False)
    # 流通总量
    circulation = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    issued_time = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    total_supply = db.Column(db.String(64), nullable=False)
    total_supply_value = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    official_website = db.Column(db.String(128))
    white_paper = db.Column(db.String(512))
    source_code = db.Column(db.String(128))
    explorer = db.Column(db.String(128))
    rule_url = db.Column(db.String(128))
    rule_detail_url = db.Column(db.String(128))
    contract_address = db.Column(db.String(256))
    explorer_list = db.Column(db.MYSQL_MEDIUM_TEXT)

    telegram = db.Column(db.String(64))
    facebook = db.Column(db.String(64))
    twitter = db.Column(db.String(64))
    reddit = db.Column(db.String(64))
    medium = db.Column(db.String(64))
    discord = db.Column(db.String(64))

    @property
    def social_url(self):
        data = []
        if self.twitter:
            data.append(dict(media='Twitter', url=self.twitter))
        if self.telegram:
            data.append(dict(media='Telegram', url=self.telegram))
        if self.facebook:
            data.append(dict(media='Facebook', url=self.facebook))
        if self.reddit:
            data.append(dict(media='Reddit', url=self.reddit))
        if self.medium:
            data.append(dict(media='Medium', url=self.medium))
        if self.discord:
            data.append(dict(media='Discord', url=self.discord))
        return data


class IeoActivity(ModelBase):
    """ IEO活动基础表 """

    ALLOW_CONDITION_LIST = [
        ActivityCondition.ConditionKeys.VIP,
        ActivityCondition.ConditionKeys.KYC,
    ]

    class StatusType(Enum):
        ONLINE = "已上架"
        OFFLINE = "未上架"
        FINISHED = "已完成"

    class ActiveStatus(Enum):

        CREATED = "created"  # 未开始
        STARTED = "started"  # 进行中
        PROCESSED = "processed"  # 抽签中
        FINISHED = "finished"  # 已结束

    MAX_CANCEL_COUNT = 3  # 最大撤销数

    name = db.Column(db.String(128), nullable=False)
    # 投资币种
    subscribe_asset = db.Column(db.String(32), nullable=False)
    subscribe_total_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    subscribe_total_count = db.Column(db.Integer, nullable=False)
    max_subscribe_count = db.Column(db.Integer, nullable=False)
    # 质押币种
    pledge_asset = db.Column(db.String(32), nullable=False)
    pledge_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    # 支付币种
    pay_asset = db.Column(db.String(32), nullable=False)
    pay_amount = db.Column(db.MYSQL_DECIMAL_PRICE, nullable=False)
    # 预热时间
    ready_time = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    start_time = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    end_time = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    lock_hour = db.Column(db.Integer, nullable=False)
    # 黑名单国家
    black_country_list = db.Column(db.MYSQL_MEDIUM_TEXT)  # ["CHN", "US"]
    status = db.Column(db.Enum(StatusType), nullable=False)
    activity_id = db.Column(db.Integer, db.ForeignKey('activity.id'),
                            nullable=False)
    cover = db.Column(db.String(256), nullable=False)

    @property
    def cover_url(self):
        if not (file_key := self.cover):
            return ''
        return AWSBucketPublic.get_file_url(file_key)

    @property
    def duration(self):
        d = self.end_time - self.start_time
        return d.days

    @property
    def subscribe_amount(self):
        return Decimal(self.subscribe_total_amount / self.subscribe_total_count)

    @property
    def total_pay_amount(self):
        return Decimal(self.subscribe_amount * self.pay_amount)

    @property
    def is_active(self):
        if self.status == self.StatusType.OFFLINE:
            return False
        return self.active_status == self.ActiveStatus.STARTED

    @property
    def active_status(self):
        now_ = now()
        if now_ < self.start_time:
            return self.ActiveStatus.CREATED
        if now_ < self.end_time and self.status == self.StatusType.ONLINE:
            return self.ActiveStatus.STARTED
        if self.end_time < now_ and self.status == self.StatusType.ONLINE:
            return self.ActiveStatus.PROCESSED
        return self.ActiveStatus.FINISHED


class IeoActivityDetail(ModelBase):
    __table_args__ = (db.UniqueConstraint("ieo_activity_id", "lang",
                                          name="ieo_activity_id_lang_unique"),)

    AVAILABLE_LANGS = (
        Language.EN_US,
        Language.ZH_HANS_CN,
        Language.ZH_HANT_HK,
        Language.JA_JP,
        Language.KO_KP,
        Language.RU_KZ,
        Language.ES_ES,
        Language.ID_ID,
        Language.FA_IR,
        Language.TR_TR,
        Language.VI_VN,
        Language.AR_AE,
        Language.FR_FR,
        Language.PT_PT,
        Language.DE_DE,
        Language.TH_TH,
        Language.PL_PL,
        Language.IT_IT
    )

    ieo_activity_id = db.Column(
        db.Integer,
        db.ForeignKey('ieo_activity.id'), nullable=False)

    lang = db.Column(db.StringEnum(Language), nullable=False)
    summary = db.Column(db.String(512), nullable=False)
    introduction = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False, default='')
    # 研究报告地址
    report_url = db.Column(db.String(512), nullable=False)


class IeoActivityCondition(ModelBase):
    ieo_activity_id = db.Column(db.Integer, db.ForeignKey('ieo_activity.id'), nullable=False)
    key = db.Column(db.String(64), index=True, nullable=False)
    value = db.Column(db.String(512), nullable=False)


class IeoActivityLotteryHistory(ModelBase):
    class StatusType(Enum):
        SUCCEED = "已中奖"
        FAILED = "未中奖"

    lottery_number = db.Column(db.Integer)
    order_id = db.Column(db.Integer, db.ForeignKey('ieo_activity_order.id'), nullable=False)
    status = db.Column(db.Enum(StatusType), nullable=False, default=StatusType.FAILED)

    @property
    def lottery_number_zfill(self):
        return str(self.lottery_number).zfill(7)


class NewerGuide(ModelBase):
    """新手指引"""
    AVAILABLE_LANGS = (Language.EN_US, Language.ZH_HANS_CN, Language.ZH_HANT_HK,
                       Language.JA_JP, Language.KO_KP, Language.RU_KZ, Language.ES_ES,
                       Language.ID_ID, Language.FA_IR, Language.TR_TR, Language.VI_VN,
                       Language.AR_AE, Language.FR_FR, Language.DE_DE, Language.PT_PT,
                       Language.TH_TH, Language.PL_PL, Language.IT_IT
                       )

    class Category(Enum):
        BASIC = 'basic'
        ADVANCED = 'advanced'

    class ContentType(Enum):
        VIDEO = 'video'
        ARTICLE = 'article'

    class Status(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    category = db.Column(db.Enum(Category), nullable=False, default=Category.BASIC)
    lang = db.Column(db.StringEnum(Language), nullable=False)
    sort_id = db.Column(db.Integer, nullable=False)
    title = db.Column(db.String(128), nullable=False)
    content_type = db.Column(db.Enum(ContentType), nullable=False, default=ContentType.ARTICLE)
    url = db.Column(db.String(256), nullable=False)
    cover = db.Column(db.String(256), nullable=False)
    status = db.Column(db.Enum(Status), nullable=False, default=Status.VALID)


class IeoActivityOrder(ModelBase):
    class OrderStatus(Enum):
        LOTTERY = '已中签'
        UNLOTTERY = '未中签'
        SUBSCRIBED = '已申购'
        LOTTERYING = '抽签中'
        CANCELED = '已撤销'  # 抽签完处理完资金

    class AssetStatusType(Enum):
        PENDING_DEDUCT = '待扣款'
        DEDUCTED = '已扣款'
        PENDING_FREEZE = '待冻结'
        FINISHED = '已完成'

    class AssetCancelStatusType(Enum):
        PENDING_UNFREEZE = '待解冻'
        PENDING_DEDUCT = '待扣款'
        DEDUCTED = '已扣款'
        FINISHED = '已完成'

    class StatusType(Enum):
        CREATED = '创建中'
        CANCELLING = '撤销中'
        VALID = '申购中'
        CANCELLED = '已撤销'
        FINISHED = '已结束'  # 抽签完处理完资金

    ieo_activity_id = db.Column(db.Integer, db.ForeignKey('ieo_activity.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    status = db.Column(db.Enum(StatusType), nullable=False, default=StatusType.CREATED)
    asset_status = db.Column(db.Enum(AssetStatusType), nullable=False, default=AssetStatusType.PENDING_DEDUCT)
    cancel_status = db.Column(db.Enum(AssetCancelStatusType), nullable=False,
                              default=AssetCancelStatusType.PENDING_UNFREEZE)
    # 申购份数
    subscribe_count = db.Column(db.Integer, nullable=False)
    # 中签份数
    lottery_count = db.Column(db.Integer, nullable=False, default=0)
    unlocked_at = db.Column(db.MYSQL_DATETIME_6)

    def get_order_status(self, end_time):
        if self.lottery_count > 0:
            return self.OrderStatus.LOTTERY.name
        elif self.status in [IeoActivityOrder.StatusType.CANCELLED, IeoActivityOrder.StatusType.CANCELLING]:
            return self.OrderStatus.CANCELED.name
        elif self.status == IeoActivityOrder.StatusType.FINISHED and self.lottery_count == 0:
            return self.OrderStatus.UNLOTTERY.name
        elif self.status == IeoActivityOrder.StatusType.VALID and end_time < now():
            return self.OrderStatus.LOTTERYING.name
        else:
            return self.OrderStatus.SUBSCRIBED.name


class Portrait(ModelBase):
    """头像"""

    class Validity(Enum):
        """这是给管理员查询用的状态，表示在当前时间是否有效，不在数据库中记录"""
        PENDING = 'pending'  # 待上架：当前时间<开始时间
        ONLINE = 'online'  # 上架：开始时间<=当前时间<=结束时间
        OFFLINE = 'offline'  # 已下架：当前时间>结束时间

    class Status(Enum):
        """这是在数据库中记录的状态，表达是否被删除"""
        VALID = 'valid'
        DELETED = 'deleted'

    class Platform(Enum):
        ALL = '全部'
        WEB = 'WEB'
        APP = 'APP'

    IMAGE_KEY_MAP = {
        "file_key": "file_url",
        "night_key": "night_url",
        "day_border_key": "day_border_url",
        "night_border_key": "night_border_url",
    }

    name = db.Column(db.String(32), nullable=False)
    started_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    ended_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)

    file_key = db.Column(db.Text, nullable=False)
    night_key = db.Column(db.Text, nullable=False, comment="头像(夜间)")
    day_border_key = db.Column(db.Text, nullable=False, default="", comment="头像边框(白天)")
    night_border_key = db.Column(db.Text, nullable=False, default="", comment="头像边框(夜间)")

    platform = db.Column(db.StringEnum(Platform), nullable=False, comment="平台")

    status = db.Column(db.Enum(Status), nullable=False, default=Status.VALID)
    updated_by = db.Column(db.Integer, db.ForeignKey('user.id'))

    @property
    def validity(self):
        _now: datetime = now()
        if self.started_at > _now:
            validity = self.Validity.PENDING
        elif self.ended_at <= _now:
            validity = self.Validity.OFFLINE
        else:
            validity = self.Validity.ONLINE
        return validity

    def to_dict(self, *, with_hook: bool = True, enum_to_name: bool = False):
        ret_dict = super().to_dict(with_hook=with_hook, enum_to_name=enum_to_name)
        ret_dict['validity'] = self.validity.value
        return ret_dict

    @classmethod
    def get_current(cls) -> List[Portrait]:
        _now: datetime = now()
        query = cls.query.filter(
            cls.status == cls.Status.VALID,
            cls.started_at < _now,
            cls.ended_at > _now
        )
        return query.all()


class OperationTemplate(ModelBase):
    """运营模版"""

    class Business(Enum):
        EMAIL_PUSH = '邮件推送'
        NOTIFICATION_BAR = '通知栏'
        APP_PUSH = 'APP通知'
        POPUP_WINDOW = '弹窗'
        MESSAGE = '站内信'
        DEPOSIT_WITHDRAW_WINDOW = '充提弹窗'
        TIP_BAR = '提示条'
        COINEX_WALLET_TRAFFIC = "Coinex Wallet 引流运营位"
        PRE_TRADING = '盘前交易'
        C_BOX_PROMOTION = 'C-Box活动区'
        P2P_MER_ACT = 'p2p商家活动'

    class Status(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    title = db.Column(db.Text, nullable=False)
    business = db.Column(db.StringEnum(Business), nullable=False)
    enabled = db.Column(db.Boolean, nullable=False, default=True)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True, index=True)
    status = db.Column(db.Enum(Status), nullable=False, default=Status.VALID)
    remark = db.Column(db.String(512), nullable=False, default='')


class OperationTemplateContent(db.Model):
    """运营模版内容"""

    id = db.Column(db.Integer, primary_key=True)
    template_id = db.Column(db.Integer, db.ForeignKey('operation_template.id'), nullable=False, index=True)
    lang = db.Column(db.StringEnum(Language), nullable=False)
    title = db.Column(db.Text, nullable=False)
    content = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False)
    summary = db.Column(db.Text)
    url = db.Column(db.String(512))

    __table_args__ = (
        db.UniqueConstraint('template_id', 'lang', name='template_lang_uniq'),
    )


class NotificationBar(ModelBase):
    """通知栏"""

    class Platform(Enum):
        ALL = 'all'
        WEB = 'web'
        APP = 'app'

    class TriggerPage(Enum):
        QUOTES = '行情页'
        FIAT = '法币页'
        ASSET_DATA = '币种资料页'
        SPOT_MARKET = '币币交易页'
        PERPETUAL_MARKET = '合约交易页'
        ACCOUNT_ASSET = '资产页'
        ONCHAIN = '链上交易页'

    class JumpType(Enum):
        NATIVE = '原生'
        URL = 'URL'

    class Status(Enum):
        DRAFT = '未提交'
        CREATED = '待审核'
        AUDITED = '已审核'

        REJECTED = '未通过'
        FINISHED = '已结束'
        FAILED = '推送失败'

        DELETED = '已删除'

    name = db.Column(db.String(256), nullable=False, index=True)
    platform = db.Column(db.StringEnum(Platform), nullable=False)
    # 币种资料页: 选择的是币种; 币币交易或者合约交易: 选择的是市场
    # 格式：[{trigger_page: QUOTES, trigger_page_params: [BTC, CET]},]
    trigger_pages = db.Column(db.Text, default='', nullable=False)

    jump_page_enabled = db.Column(db.Boolean, nullable=False, default=False)
    jump_type = db.Column(db.StringEnum(JumpType))
    jump_id = db.Column(db.Integer, db.ForeignKey('app_jump_list.id'))

    begin_at = db.Column(db.MYSQL_DATETIME_6, nullable=False, index=True)
    end_at = db.Column(db.MYSQL_DATETIME_6, nullable=False, index=True)

    remark = db.Column(db.String(512), nullable=False, default='')
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    audited_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    audited_at = db.Column(db.MYSQL_DATETIME_6)
    auditor_remark = db.Column(db.String(512), nullable=False, default='')
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.DRAFT)

    def get_trigger_pages(self) -> list:
        if not self.trigger_pages:
            return []

        trigger_pages = json.loads(self.trigger_pages)
        for trigger_page_map in trigger_pages:
            trigger_page_params = trigger_page_map['trigger_page_params'] or []
            trigger_page_map['trigger_page_params'] = trigger_page_params
        return trigger_pages


class NotificationBarContent(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    notification_bar_id = db.Column(db.Integer, db.ForeignKey('notification_bar.id'), nullable=False)
    lang = db.Column(db.StringEnum(Language), nullable=False)
    title = db.Column(db.Text, nullable=False)
    content = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False)
    summary = db.Column(db.Text)


class CharityBanner(ModelBase):
    """慈善Banner"""

    MAX_BANNER_COUNT = 3

    class Status(Enum):
        ONLINE = '上架'
        OFFLINE = '下架'

    name = db.Column(db.String(256), nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.ONLINE)
    sort_id = db.Column(db.Integer, nullable=False, default=0)


class CharityBannerContent(db.Model):
    """慈善Banner详情"""

    __table_args__ = (
        db.UniqueConstraint('owner_id', 'lang', name='owner_id_lang_unique'),
    )

    id = db.Column(db.Integer, primary_key=True)
    owner_id = db.Column(db.Integer, db.ForeignKey(CharityBanner.id),
                         nullable=False)
    lang = db.Column(db.StringEnum(Language), nullable=False)
    file_key = db.Column(db.String(256), nullable=False)


class CharityDonationData(ModelBase):
    """慈善捐助数据"""

    class DataType(Enum):
        BENEFICIARIES_COUNT = '受益人数'
        DONATION_AMOUNT = '捐助总额'
        DONATION_AREA_COUNT = '捐助地区'

    data_type = db.Column(db.StringEnum(DataType), nullable=False, unique=True)
    data_value = db.Column(db.String(128), nullable=False)


class CharityActivity(ModelBase):
    """慈善活动"""

    MIN_ACTIVITY_COUNT = 4

    class Status(Enum):
        ONLINE = '上架'
        OFFLINE = '下架'

    name = db.Column(db.String(256), nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.ONLINE)
    sort_id = db.Column(db.Integer, nullable=False, default=0)


class CharityActivityContent(db.Model):
    """慈善活动详情"""

    __table_args__ = (
        db.UniqueConstraint('owner_id', 'lang', name='owner_id_lang_unique'),
    )

    id = db.Column(db.Integer, primary_key=True)
    owner_id = db.Column(db.Integer, db.ForeignKey(CharityActivity.id), nullable=False)
    title = db.Column(db.String(256), nullable=False)
    desc = db.Column(db.Text)
    lang = db.Column(db.StringEnum(Language), nullable=False)
    file_key = db.Column(db.String(256), nullable=False)


class CharityVideo(ModelBase):
    """慈善活动视频"""
    AVAILABLE_LANGS = (Language.EN_US, Language.ZH_HANS_CN)

    class Status(Enum):
        ONLINE = '上架'
        OFFLINE = '下架'

    category_id = db.Column(db.Integer, db.ForeignKey('charity_category.id'))
    name = db.Column(db.String(256), nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.ONLINE)
    sort_id = db.Column(db.Integer, nullable=False, default=0)
    title = db.Column(db.String(256), nullable=False)
    url = db.Column(db.Text, nullable=False)
    lang = db.Column(db.StringEnum(Language), nullable=False)
    file_key = db.Column(db.String(256), nullable=False)

    @property
    def cover_url(self):
        if not (file_key := self.file_key):
            return ''
        return AWSBucketPublic.get_file_url(file_key)


class CharityVideoContent(db.Model):
    """慈善活动视频详情"""
    # TODO: 上线后删除此表
    id = db.Column(db.Integer, primary_key=True)
    owner_id = db.Column(db.Integer, db.ForeignKey(CharityVideo.id), nullable=False)
    title = db.Column(db.String(256), nullable=False)
    url = db.Column(db.Text, nullable=False)
    lang = db.Column(db.StringEnum(Language), nullable=False)
    file_key = db.Column(db.String(256), nullable=False)


class CharityCategory(ModelBase):
    """慈善类别管理"""
    AVAILABLE_LANGS = (Language.EN_US, Language.ZH_HANS_CN)

    class Status(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    class Type(Enum):
        FOOTPRINT = '足迹'
        ACTIVITY_VIDEO = '活动视频'

    __table_args__ = (
        db.Index('type_lang_idx', 'type', 'lang'),
    )

    name = db.Column(db.String(32), nullable=False)
    type = db.Column(db.StringEnum(Type), nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID)
    sort_id = db.Column(db.Integer, nullable=False)
    lang = db.Column(db.StringEnum(Language), nullable=False)
    remark = db.Column(db.String(32), nullable=False)


class CharityFootprintCategory(ModelBase):
    """慈善足迹分类"""

    class Status(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    cn_name = db.Column(db.String(32), nullable=False)
    en_name = db.Column(db.String(32), nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID)
    sort_id = db.Column(db.Integer, nullable=False)
    remark = db.Column(db.String(32), nullable=False)


class CharityFootprint(ModelBase):
    AVAILABLE_LANGS = (Language.EN_US, Language.ZH_HANS_CN)

    class Status(Enum):
        ONLINE = '上架'
        OFFLINE = '下架'

    category_id = db.Column(db.Integer, db.ForeignKey('charity_footprint_category.id'), nullable=False)
    is_use_blog = db.Column(db.Boolean, default=False)
    display_name = db.Column(db.String(256), nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.OFFLINE)
    sort_id = db.Column(db.Integer, nullable=False, default=0)
    read_count = db.Column(db.Integer, nullable=False, default=0)


class CharityFootprintContent(ModelBase):
    footprint_id = db.Column(db.Integer, db.ForeignKey('charity_footprint.id'), nullable=False)
    lang = db.Column(db.StringEnum(Language), nullable=False, index=True)
    blog_id = db.Column(db.Integer)
    # seo相关字段
    name = db.Column(db.String(256), nullable=False)
    seo_url_keyword = db.Column(db.String(1024), nullable=False, default="")  # URL关键词
    seo_title = db.Column(db.String(1024), nullable=False, default="")  # 网页标题
    title = db.Column(db.Text, nullable=False)
    content = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False)
    abstract = db.Column(db.Text, nullable=False)
    cover = db.Column(db.String(256), nullable=False)

    @property
    def cover_url(self):
        if not (file_key := self.cover):
            return ''
        return AWSBucketPublic.get_file_url(file_key)


class PageInset(ModelBase):
    """ 页面插画(图片)配置 """

    class Status(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    class Page(Enum):
        LOGIN = "登录页"
        REGISTER = "注册页"

    name = db.Column(db.String(256), nullable=False)
    begin_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    end_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    page = db.Column(db.StringEnum(Page), nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'))


class PageInsetContent(db.Model):
    """ 页面插画(图片)配置内容 """
    __table_args__ = (db.UniqueConstraint("owner_id", "lang", name="owner_id_lang_unique"),)

    id = db.Column(db.Integer, primary_key=True)
    owner_id = db.Column(db.Integer, db.ForeignKey('page_inset.id'), nullable=False)
    lang = db.Column(db.StringEnum(Language), nullable=False)
    file_key = db.Column(db.Text, nullable=False)

    @property
    def img_src(self) -> str:
        if not (file_key := self.file_key):
            return ''
        return AWSBucketPublic.get_file_url(file_key)


class MarketingBanner(ModelBase):
    class Status(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    class Type(Enum):
        DEFAULT = "默认"
        MARKETING = "营销"

    name = db.Column(db.String(256), nullable=False)
    type = db.Column(db.StringEnum(Type), nullable=False)
    begin_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    end_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    lang = db.Column(db.StringEnum(Language))  # 语区
    code = db.Column(db.String(32), unique=True)
    refer_code = db.Column(db.String(32), nullable=False)  # 投放邀请码
    url = db.Column(db.String(512))  # banner 链接
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID)
    updated_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    sort_id = db.Column(db.Integer, nullable=False)

    __table_args__ = (
        db.Index('sort_id_asc_idx', sort_id.asc()),
    )

    @classmethod
    def new_sort_id(cls) -> int:
        max_sort_id = MarketingBanner.query.with_entities(
            func.max(MarketingBanner.sort_id).label('max_sort_id')
        ).first().max_sort_id or 0
        return max_sort_id + 1


class MarketingBannerContent(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    owner_id = db.Column(db.Integer, db.ForeignKey('marketing_banner.id'),
                         nullable=False)
    lang = db.Column(db.StringEnum(Language), nullable=False)
    file_key = db.Column(db.Text, nullable=False)

    @property
    def img_src(self) -> str:
        if not (file_key := self.file_key):
            return ''
        return AWSBucketPublic.get_file_url(file_key)


class FooterConfig(ModelBase):
    """
    优化为 SNSConfig，需求参考：
    https://app.clickup.com/t/86eq77bf7
    """

    class Status(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    class Business(Enum):
        SNS_SITE = '社媒-主站'
        SNS_CHARITY = '社媒-慈善'

    class DisplayPosition(Enum):
        FOOTER = '首页-footer'
        BLOG = '博客'
        CONTACT_US = '联系我们'
        EMAIL = '邮件'

    __table_args__ = (
        db.Index('business_lang', 'business', 'lang'),
    )

    business = db.Column(db.StringEnum(Business), nullable=False)
    # json: ['FOOTER', 'BLOG']
    # Business.SNS_CHARITY 不支持位置展示配置
    display_positions = db.Column(db.Text, default='', nullable=False)
    media = db.Column(db.StringEnum(Media), nullable=False)
    lang = db.Column(db.StringEnum(Language), nullable=False)
    url = db.Column(db.String(512), nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)

    @classmethod
    def gen_media_lang_dic(cls):
        """ 获取邮件底部展示的社媒 """
        records = cls.query.filter(
            cls.status == cls.Status.VALID,
            cls.business == cls.Business.SNS_SITE,
        ).all()
        res = defaultdict(dict)
        for record in records:
            if cls.DisplayPosition.EMAIL.name not in record.display_positions:
                continue
            res[record.media.value][record.lang.value] = record.url
        return res


class InsetConfig(ModelBase):  # 旧表待删除

    class Status(Enum):
        PENDING = '待上架'
        ONLINE = '上架中'
        OFFLINE = '已下架'  # 展示的状态
        DELETED = '已删除'

    name = db.Column(db.String(64), nullable=False)
    begin_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    end_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    file_key_day = db.Column(db.Text, nullable=False)
    file_key_night = db.Column(db.Text, nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.PENDING)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)

    @property
    def inset_daytime(self):
        """插画(白天）"""
        return self._get_file(self.file_key_day)

    @property
    def inset_night(self):
        """插画（夜间）"""
        return self._get_file(self.file_key_night)

    @staticmethod
    def _get_file(file_key):
        if not file_key:
            return ''
        return AWSBucketPublic.get_file_url(file_key)


class NewInsetConfig(ModelBase):
    AVAILABLE_LANGS = (Language.EN_US, Language.ZH_HANS_CN,
                       Language.ZH_HANT_HK, Language.JA_JP,
                       Language.KO_KP, Language.RU_KZ, Language.ES_ES,
                       Language.ID_ID, Language.FA_IR, Language.TR_TR,
                       Language.VI_VN, Language.AR_AE, Language.FR_FR,
                       Language.DE_DE, Language.PT_PT, Language.TH_TH,
                       Language.PL_PL, Language.IT_IT
                       )

    class Status(Enum):
        VALID = 'valid'
        DELETE = 'delete'

    class ContentStyle(Enum):
        ACTIVITY = '活动'  # 图文结合
        IMAGE = '情感化'  # 纯图片

    display_name = db.Column(db.Text, nullable=False)
    begin_at = db.Column(db.MYSQL_DATETIME_6, nullable=False, index=True)
    end_at = db.Column(db.MYSQL_DATETIME_6, nullable=False, index=True)
    url = db.Column(db.String(256), default='')
    content_style = db.Column(db.StringEnum(ContentStyle), nullable=False, default=ContentStyle.ACTIVITY)
    status = db.Column(db.StringEnum(Status), default=Status.VALID, index=True)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)


class NewInsetConfigContent(ModelBase):
    inset_config_id = db.Column(db.Integer, db.ForeignKey('new_inset_config.id'), nullable=False)
    lang = db.Column(db.StringEnum(Language), nullable=False)
    title = db.Column(db.String(256), nullable=False, default='')
    sub_title = db.Column(db.String(256), nullable=False, default='')
    button_text = db.Column(db.String(128), nullable=False, default='')
    light_background_color = db.Column(db.String(128))
    light_title_color = db.Column(db.String(128))
    light_subtitle_color = db.Column(db.String(128))
    dark_background_color = db.Column(db.String(128))
    dark_title_color = db.Column(db.String(128))
    dark_subtitle_color = db.Column(db.String(128))
    light_pic_id = db.Column(db.Integer, db.ForeignKey('file.id'), nullable=True)
    dark_pic_id = db.Column(db.Integer, db.ForeignKey('file.id'), nullable=True)
    url = db.Column(db.String(256), default='')

    @property
    def light_pic_url(self):
        from app.models import File
        if not self.light_pic_id:
            return ''
        return File.query.get(self.light_pic_id).static_url

    @property
    def dark_pic_url(self):
        from app.models import File
        if not self.dark_pic_id:
            return ''
        return File.query.get(self.dark_pic_id).static_url


class PerpetualActivity(ModelBase):
    """合约交易页活动配置"""

    class Status(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    display_name = db.Column(db.String(128), nullable=False)
    jump_id = db.Column(db.Integer, db.ForeignKey('app_jump_list.id'), nullable=True)
    light_pic_id = db.Column(db.Integer, db.ForeignKey('file.id'), nullable=False)
    dark_pic_id = db.Column(db.Integer, db.ForeignKey('file.id'), nullable=False)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    begin_at = db.Column(db.MYSQL_DATETIME_6, nullable=False, index=True)
    end_at = db.Column(db.MYSQL_DATETIME_6, nullable=False, index=True)
    status = db.Column(db.StringEnum(Status), default=Status.VALID, index=True)


class PerpetualActivityContent(ModelBase):
    activate_id = db.Column(db.Integer, db.ForeignKey('perpetual_activity.id'),
                            nullable=False)
    lang = db.Column(db.StringEnum(Language), nullable=False)
    title = db.Column(db.String(128), nullable=False, default='')
    sub_title = db.Column(db.String(128), nullable=False, default='')


class ReferralActivityBanner(ModelBase):
    """推荐返佣活动 Banner"""

    class Status(Enum):
        VALID = 'valid'
        DELETE = 'delete'

    display_name = db.Column(db.Text, nullable=False)
    web_jump_type = db.Column(db.StringEnum(AppJumpList.JumpType), nullable=True)
    web_jump_id = db.Column(db.Integer, db.ForeignKey('app_jump_list.id'), nullable=True)
    app_jump_type = db.Column(db.StringEnum(AppJumpList.JumpType), nullable=True)
    app_jump_id = db.Column(db.Integer, db.ForeignKey('app_jump_list.id'), nullable=True)
    light_pic_id = db.Column(db.Integer, db.ForeignKey('file.id'), nullable=False)
    dark_pic_id = db.Column(db.Integer, db.ForeignKey('file.id'), nullable=False)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    begin_at = db.Column(db.MYSQL_DATETIME_6, nullable=False, index=True)
    end_at = db.Column(db.MYSQL_DATETIME_6, nullable=False, index=True)
    sort_id = db.Column(db.SmallInteger, nullable=False, index=True)
    status = db.Column(db.StringEnum(Status), default=Status.VALID)

    @property
    def light_pic_url(self):
        return self.get_file_url(self.light_pic_id)

    @property
    def dark_pic_url(self):
        return self.get_file_url(self.dark_pic_id)

    @staticmethod
    def get_file_url(file_id):
        if not file_id:
            return ''
        from app.models import File
        return File.query.get(file_id).static_url


class ReferralActivityBannerContent(ModelBase):
    activity_id = db.Column(db.Integer, db.ForeignKey('referral_activity_banner.id'), nullable=False)
    lang = db.Column(db.StringEnum(Language), nullable=False)
    title = db.Column(db.Text, nullable=False, default='')
    sub_title = db.Column(db.Text, nullable=False, default='')
    copywriting = db.Column(db.String(128), default='', comment='跳转按钮文案')


class OperationAmbassadorActivity(ModelBase):
    """大使活动管理"""
    COVER_FILE_KEYS = (
        'operation_ambassador_activity_cover1.png',
        'operation_ambassador_activity_cover2.png',
        'operation_ambassador_activity_cover3.png',
    )

    class Status(Enum):
        ONLINE = '已上架'
        PENDING = '未上架'
        FINISHED = '已完成'
        DELETED = '已下架'

    class Type(Enum):
        FEE_AMOUNT = '新用户手续费'
        PERP_TRADE_AMOUNT = '新用户合约交易额'
        ALL_FEE_AMOUNT = '全部用户手续费'
        ALL_PERP_TRADE_AMOUNT = '全部用户合约交易额'

    activity_id = db.Column(db.Integer, db.ForeignKey('activity.id'), nullable=False)
    type = db.Column(db.StringEnum(Type), nullable=False)
    cover_index = db.Column(db.Integer, nullable=False)
    started_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    ended_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    applying_ended_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    '''
    {
        min_gift_amount: 100, top: [100, 50, 30], other: 20, 其余用户每人瓜分奖励上限
        grades: [
            {count: 100, amount: 500, top: [100, 50, 30], other: 20},
            {count: 200, amount: 1000, top: [100, 50, 30], other: 20}
        ]
    }
    '''
    gift_rules = db.Column(db.Text, nullable=False, default='')
    updated_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    status = db.Column(db.StringEnum(Status), nullable=False)
    is_checked = db.Column(db.Boolean, default=False, nullable=True)  # 是否已经完成清退用户的排除（派奖前的校验）
    normal_amb_max_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=True)
    business_amb_max_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=True)
    gift_asset = db.Column(db.String(32), nullable=False)

    @classmethod
    def get_new_cover_index(cls):
        row = cls.query.order_by(cls.id.desc()).first()
        if not row:
            return 0
        index = row.cover_index + 1
        max_index = len(cls.COVER_FILE_KEYS) - 1
        if index > max_index:
            index = 0
        return index

    @property
    def cover_url(self):
        file_key = self.COVER_FILE_KEYS[self.cover_index]
        return AWSBucketPublic.get_file_url(file_key)

    def left_gift_amount(self, user_count):
        """总奖励数余量"""
        record = GiftHistory.query.filter(
            GiftHistory.activity_id == self.activity_id,
            GiftHistory.asset == self.gift_asset,
            GiftHistory.created_at >= self.started_at,
            or_(
                GiftHistory.status == GiftHistory.Status.FINISHED,
                GiftHistory.status == GiftHistory.Status.CREATED
            )
        ).with_entities(
            func.sum(GiftHistory.amount).label('total')
        ).first()
        total_gift = record.total if record.total else Decimal()
        return self.gift_amount(user_count) - total_gift

    def gift_amount(self, user_count):
        """总奖励数"""
        min_amount = self.cached_gift_rules['min_gift_amount']
        grades = self.cached_gift_rules['grades']
        amount = Decimal(min_amount)
        for grade in grades:
            if user_count >= grade['count']:
                amount = Decimal(grade['amount'])
            else:
                break
        return amount

    def gift_rule(self, user_count):
        """实际发奖规则"""
        min_amount = self.cached_gift_rules['min_gift_amount']
        top = self.cached_gift_rules.get('top')
        other = self.cached_gift_rules.get('other')
        grades = self.cached_gift_rules['grades']
        amount = Decimal(min_amount)
        for grade in grades:
            if user_count >= grade['count']:
                amount = Decimal(grade['amount'])
                top = grade.get('top')
                other = grade.get('other')
            else:
                break
        if top:
            top = [Decimal(_amount) for _amount in top]
        if other:
            other = Decimal(other)
        return dict(
            amount=amount,
            top=top,
            other=other,
        )

    @property
    def max_gift_amount(self):
        min_amount = self.cached_gift_rules['min_gift_amount']
        grades = self.cached_gift_rules['grades']
        if grades:
            grade = grades[-1]
            max_amount = Decimal(grade['amount'])
        else:
            max_amount = Decimal(min_amount)
        return max_amount

    @property
    def max_grade_gift(self) -> (list, str):
        top = self.cached_gift_rules['top']
        other = self.cached_gift_rules['other']
        grades = self.cached_gift_rules['grades']
        if grades:
            grade = grades[-1]
            top = grade['top']
            other = grade['other']
        return top, other

    @property
    def gift_amount_range(self):
        """浮动奖池"""
        min_amount = self.cached_gift_rules['min_gift_amount']
        grades = self.cached_gift_rules['grades']
        if grades:
            max_amount = grades[-1]['amount']
        else:
            max_amount = min_amount
        return min_amount, max_amount

    @cached_property
    def cached_gift_rules(self):
        return json.loads(self.gift_rules)


class OperationAmbassadorActivityContent(ModelBase):
    """大使活动内容"""

    activity_id = db.Column(db.Integer, db.ForeignKey('operation_ambassador_activity.id'), nullable=False)
    lang = db.Column(db.StringEnum(Language), nullable=False)
    title = db.Column(db.String(256), nullable=False)


class AmbassadorActivityApplyUser(ModelBase):
    """大使活动报名用户"""
    __table_args__ = (
        db.UniqueConstraint('activity_id', 'user_id', name='activity_id_user_id'),
    )

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    activity_id = db.Column(db.Integer, db.ForeignKey('operation_ambassador_activity.id'), nullable=False)


class AmbassadorActivityUserInfo(ModelBase):
    class AmbassadorType(Enum):
        NORMAL = "平台大使"
        BUS = "商务大使"

    __table_args__ = (
        db.UniqueConstraint('activity_id', 'user_id', name='activity_id_user_id'),
    )

    report_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    activity_id = db.Column(db.Integer, db.ForeignKey('operation_ambassador_activity.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    amb_type = db.Column(db.StringEnum(AmbassadorType))
    rank = db.Column(db.Integer, nullable=True)  # None: 没有排名
    referral_count = db.Column(db.Integer, nullable=False, default=0)  # 邀请新用户数
    all_referral_count = db.Column(db.Integer, nullable=False, default=0)  # 邀请全部用户数
    spot_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 邀请新用户现货交易额
    all_spot_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 全部用户现货交易额
    perp_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 邀请新用户合约交易额
    all_perp_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 全部用户合约交易额
    exchange_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 邀请新用户兑换交易额
    all_exchange_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 全部用户兑换交易额
    spot_fee_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 邀请新用户现货手续费
    perp_fee_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 邀请新用户合约手续费
    fee_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 邀请新用户手续费
    all_spot_fee_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 全部用户现货手续费
    all_perp_fee_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 全部用户合约手续费
    all_fee_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 全部用户手续费
    ratio = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    gift_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    ambassador_level = db.Column(db.String(32))  # Ambassador.Level.name

    valid_referral_count = db.Column(db.Integer, nullable=False, default=0)  # 有效邀请用户数，详情数据落库后回写


class AmbassadorActivityUserDetailInfo(ModelBase):
    class Status(Enum):
        VALID = '有效'
        INVALID = '无效'

    class Type(Enum):
        NEW = '新用户'
        OLD = '老用户'

    __table_args__ = (
        db.Index('activity_id_ambassador_user_id', 'activity_id', 'ambassador_user_id'),
        db.Index('activity_id_user_id', 'activity_id', 'user_id'),
    )

    activity_id = db.Column(db.Integer, nullable=False)  # operation_ambassador_activity.id
    user_id = db.Column(db.Integer, nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID)
    type = db.Column(db.StringEnum(Type), nullable=False)
    ambassador_user_id = db.Column(db.Integer, nullable=False)
    register_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    api_enabled = db.Column(db.Boolean, nullable=False, default=False)
    spot_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 邀请新用户现货交易额
    perp_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 邀请新用户合约交易额
    exchange_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 邀请新用户兑换交易额
    fee_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 邀请新用户手续费
    remark = db.Column(db.String(128), nullable=False, default='')


class APPDynamicFalling(ModelBase):
    # 该表已废弃，待后续清理
    class Platform(Enum):
        ANDROID = '安卓'
        IOS = "iOS"
        ALL = '安卓+iOS'

    class DynamicFalling(Enum):
        HALLOWEEN = '万圣节'
        CHRISTMAS = '圣诞节'
        NEW_YEARS_DAY = '元旦节'
        VALENTINES_DAY = '情人节'
        PIZZAS_DAY = '披萨节'

    class Status(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    DYNAMIC_FALLING_MAPPING = {
        DynamicFalling.HALLOWEEN.name: AWSBucketPublic.get_file_url('admin_halloween.gif'),
        DynamicFalling.CHRISTMAS.name: AWSBucketPublic.get_file_url('admin_christmas.gif'),
        DynamicFalling.NEW_YEARS_DAY.name: AWSBucketPublic.get_file_url('admin_new_years_day.gif'),
        DynamicFalling.VALENTINES_DAY.name: AWSBucketPublic.get_file_url('admin_valentines_day.gif'),
        DynamicFalling.PIZZAS_DAY.name: AWSBucketPublic.get_file_url('admin_pizzas_day.gif'),
    }

    name = db.Column(db.String(32), nullable=False)
    platform = db.Column(db.StringEnum(Platform), nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID)
    dynamic_falling = db.Column(db.StringEnum(DynamicFalling), nullable=False)
    begin_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    end_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False, index=True)


class DynamicFalling(ModelBase):
    class Platform(Enum):
        ANDROID = '安卓'
        IOS = "iOS"
        ALL = '安卓+iOS'
        WEB = "WEB"

    class Type(Enum):
        DynamicFalling = "全屏悬浮"
        DynamicLogo = "logo动效"

    class Status(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    name = db.Column(db.String(32), nullable=False)
    platform = db.Column(db.StringEnum(Platform), nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID)
    type = db.Column(db.StringEnum(Type), nullable=False, default=Type.DynamicFalling)
    light_file_id = db.Column(db.Integer, nullable=False)
    light_file_name = db.Column(db.String(32), nullable=False)
    dark_file_id = db.Column(db.Integer, nullable=True)
    dark_file_name = db.Column(db.String(32), nullable=True)
    begin_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    end_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False, index=True)


class ExposureActivitySetting(ModelBase):
    # 站内曝光活动设置

    class Menus(Enum):
        FIAT = '买币'
        QUOTES = '行情'
        SPOT = '币币'
        PERPETUAL = '合约'
        FINANCE = '金融'
        ACTIVITY = '活动'
        MORE = '更多'

    class SubMenus(Enum):
        # 法币
        P2P = "p2p"
        ONCHAIN = "链上交易"
        THIRD_PARTY = "第三方"
        DIRECT_FIAT = "快捷买币"
        # 行情
        QUOTES_SUMMARY = '行情总览'
        QUOTES_DATA = '行情数据'
        QUOTES_INFO = '行情资讯'
        QUOTES_INSIGHT = "洞见"
        # 币币
        EXCHANGE = '兑换'
        SPOT_TRADE = '现货交易'
        MARGIN_TRADE = '杠杆交易'
        STRATEGY = '策略'
        PRE_TRADING = '盘前交易'
        # 合约
        PERPETUAL_TRADE = '合约交易'
        PERPETUAL_SUMMARY = '合约总览'
        PERPETUAL_MARKET_STATISTIC = '合约数据'
        PERPETUAL_MARKET_INFO = '市场信息'
        PERPETUAL_COPY_TRADING = '合约跟单'
        DEMO_TRADING = '合约模拟盘'
        # 金融
        INVESTMENT = '理财'
        PLEDGE = '质押'
        AMM = 'AMM'
        DOCK = 'Dock'
        LOAN = '借贷'
        # 活动
        TRADE_RANK = '交易排位赛'
        MINING = 'CoinEx挖矿'
        AIRDROP = '空投'
        DIBS = 'Dibs'
        DEPOSIT_BONUS = '充值福利'
        # 更多
        REFER = '推荐返佣'
        AMBASSADOR = 'CoinEx大使'
        VIP = 'VIP'
        BROKER = '经纪商'
        BLOG = '博客'
        HELP = '帮助中心'
        ANNOUNCEMENT = '公告中心'

    class StateType(Enum):
        VALID = "有效"
        INVALID = "无效"

    class StatusType(Enum):
        ONLINE = "已上架"
        OFFLINE = "待上架"
        FINISHED = "已下架"

    class ActiveStatus(Enum):
        STARTED = "started"  # 上架中
        CREATED = "created"  # 待上架
        FINISHED = "finished"  # 已下架

    name = db.Column(db.String(128), nullable=False)
    start_time = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    end_time = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    state = db.Column(db.StringEnum(StateType), nullable=False, default=StateType.VALID)
    menu = db.Column(db.StringEnum(Menus))
    submenu = db.Column(db.StringEnum(SubMenus))

    @property
    def active_status(self):
        now_ = now()
        if now_ < self.start_time:
            return self.ActiveStatus.CREATED
        if now_ < self.end_time:
            return self.ActiveStatus.STARTED
        return self.ActiveStatus.FINISHED


class WallacyActivityUser(ModelBase):
    """越南 Wallacy 钱包 活动用户"""

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    signature = db.Column(db.Text, nullable=False)
    code = db.Column(db.String(8), nullable=False, unique=True)

    @classmethod
    def generate_code(cls):
        while True:
            code = new_hex_token(8)
            if not cls.query.filter(cls.code == code).first():
                return code


class WallacyActivityGiftHistory(ModelBase):
    """越南 Wallacy 钱包 活动礼物领取记录"""

    class GiftType(Enum):
        CET = "cet"
        COUPON = "coupon"

    class Status(Enum):
        CREATED = "created"  # 待发放
        SENT = "sent"  # 已发放

    class GiftTiming(Enum):
        GROUP_STAGE = 'group stage'  # 小组赛
        ROUND_OF_16 = 'round of 16'  # 16强赛
        QUARTER_FINALS = 'quarter finals'  # 4强赛
        SEMI_FINALS = 'semi finals'  # 半决赛
        FINAL = 'final'  # 决赛

    __table_args__ = (
        db.UniqueConstraint('gift_timing', 'ranking', 'gift_date', name='gift_timing_ranking_date_unique_idx'),
        db.UniqueConstraint('gift_timing', 'user_id', 'gift_date', name='gift_timing_user_id_date_unique_idx'),
    )

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    gift_date = db.Column(db.Date, nullable=False)
    gift_type = db.Column(db.StringEnum(GiftType), nullable=False)
    gift_timing = db.Column(db.StringEnum(GiftTiming), nullable=False)  # 奖励发放时机
    ranking = db.Column(db.Integer, nullable=False)  # 用户排名
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    asset = db.Column(db.String(16), nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED)


class P2pActivity(ModelBase):
    class Platform(Enum):
        APP = "app"
        WEB = "web"
        ALL = 'all'

    class ShowType(Enum):
        USER = "普通用户"
        MERCHANT = "商家"

    class ContentType(Enum):
        ACTIVITY = "活动推广"
        OTHER = "其他"

    class Status(Enum):
        PENDING = '待上架'
        ONLINE = '上架中'
        OFFLINE = '已下架'
        DELETED = '已删除'

    name = db.Column(db.String(128), nullable=False)
    platform = db.Column(db.StringEnum(Platform, nullable=False))
    display_to_merchant = db.Column(db.Boolean, nullable=False, default=False)
    content_type = db.Column(db.StringEnum(ContentType), nullable=False)
    fiats = db.Column(db.TEXT, nullable=False, default='')  # 指定法币区
    start_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    end_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    sort_id = db.Column(db.SmallInteger, nullable=False, index=True)
    status = db.Column(db.StringEnum(Status), nullable=False)
    jump_id = db.Column(db.Integer, db.ForeignKey('app_jump_list.id'))
    updated_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)


class P2pActivityBanner(ModelBase):
    owner_id = db.Column(db.Integer, db.ForeignKey('p2p_activity.id'), nullable=False)
    lang = db.Column(db.StringEnum(Language), nullable=False)
    content = db.Column(db.String(256))
    daylight_file_key = db.Column(db.Text, nullable=False)  # 白天模式
    night_file_key = db.Column(db.Text, nullable=False)  # 夜间模式


class DepositBonusActivity(ModelBase):
    """ 充值福利活动基础表 """

    class StatusType(Enum):
        DRAFT = '待提交'
        CREATED = '待审核'
        ONLINE = '已上架'
        PENDING = '待上架'
        FINISHED = '已完成'
        OFFLINE = '已下架'
        REJECTED = '未通过'
        DELETED = '已删除'

    class ActiveStatus(Enum):
        CREATED = "未开始"
        STARTED = "进行中"
        FINISHED = "已结束"

    name = db.Column(db.String(512), nullable=False)
    start_time = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    end_time = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    zendesk_url = db.Column(db.Text, default='', comment='公告链接')

    asset = db.Column(db.String(32), nullable=False, comment='充值币种')
    # 格式 ['BTC', 'TRC20']
    chains = db.Column(db.String(256), nullable=False, comment='充值公链(需计算矿池转入)', default='')
    threshold = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, comment='最小净充值阈值')

    status = db.Column(db.StringEnum(StatusType), nullable=False, default=StatusType.DRAFT)
    is_checked = db.Column(db.Boolean, default=False, nullable=False, comment='是否已经完成清退用户的排除（派奖前的校验）')
    audit_remark = db.Column(db.String(128), nullable=True, comment='审核备注')

    cover = db.Column(db.String(128), nullable=False)

    @property
    def cover_url(self):
        if not (file_key := self.cover):
            return ''
        return AWSBucketPublic.get_file_url(file_key)

    def get_chains(self) -> list:
        if not self.chains:
            return []
        return json.loads(self.chains)

    @property
    def active_status(self):
        now_ = now()
        if now_ < self.start_time:
            return self.ActiveStatus.CREATED
        if now_ < self.end_time:
            return self.ActiveStatus.STARTED
        return self.ActiveStatus.FINISHED


class DepositBonusActivityContent(ModelBase):
    """充值福利活动内容"""

    __table_args__ = (
        db.Index('deposit_bonus_id_idx', 'deposit_bonus_id'),
    )

    deposit_bonus_id = db.Column(db.Integer, nullable=False, comment='DepositBonusActivity.id')
    lang = db.Column(db.StringEnum(Language), nullable=False)
    title = db.Column(db.String(512), nullable=False)


class DepositBonusActivityConfig(ModelBase):
    """ 充值福利具体活动配置 """

    class ActivityMode(Enum):
        PROPORTION = "比例赠送"
        SHARE = "占比瓜分"

    class Flag(Enum):
        NONE = "无"
        NEW_USER = "新用户专属"

    class GiftType(Enum):
        ASSET = "币种"
        COUPON = "卡券"
        EQUITY = "权益"

    __table_args__ = (
        db.UniqueConstraint('deposit_bonus_id', 'activity_id', name='deposit_bonus_activity_id'),
    )

    deposit_bonus_id = db.Column(db.Integer, nullable=False, comment='DepositBonusActivity.id')
    # 子活动 id
    activity_id = db.Column(db.Integer, nullable=False, comment='Activity.id')
    mode = db.Column(db.StringEnum(ActivityMode), nullable=False, comment='活动方式')
    flag = db.Column(db.StringEnum(Flag), nullable=False, comment='活动标识', default=Flag.NONE)
    '''
        [
            {
                gift_type: ASSET,
                gift_asset: CET,
                proportion: 0.03,
                rank_min: 1,
                rank_max: 100,
                rank_limit: 10000, 每人上限
                rank_total: 100000, 活动总额
            },
            {
                gift_type: COUPON,
                coupon_apply_id: 1,
                rank_min: 1,
                rank_max: 100,
                rank_limit: 1,  固定 1
                rank_total: 100,
            },
            {
                gift_type: EQUITY,
                equity_id: 1,
                rank_min: 1,
                rank_max: 100,
                rank_limit: 1,  固定 1
                rank_total: 100,
            }
        ]
    '''
    gift_rules = db.Column(db.Text, nullable=False, comment='奖励规则')

    @classmethod
    def get_configs_by_sum_gift(cls, activity_ids: list[int]) -> tuple[dict, dict, dict]:
        """ 计算总奖池，多个子活动汇总 """
        gift_assets = defaultdict(lambda: defaultdict(Decimal))
        gift_coupons = defaultdict(lambda: defaultdict(int))
        gift_equities = defaultdict(lambda: defaultdict(int))
        rows = cls.query.filter(
            cls.deposit_bonus_id.in_(activity_ids)
        ).all()
        for row in rows:
            for asset, amount in row.get_gift_assets().items():
                gift_assets[row.deposit_bonus_id][asset] += amount
            for apply_id, amount in row.get_gift_coupon_applys().items():
                # 同一个卡券发放【总数固定】，不应该累计
                gift_coupons[row.deposit_bonus_id][apply_id] = amount
            for equity_id, amount in row.get_gift_equities().items():
                gift_equities[row.deposit_bonus_id][equity_id] = amount
        return gift_assets, gift_coupons, gift_equities

    def get_gift_coupon_applys(self) -> dict:
        """ 找出总奖励卡券数量"""
        ret = defaultdict(int)
        for gift_rule in self.cached_gift_rules:
            if gift_rule['gift_type'] == self.GiftType.COUPON.name:
                # 同一个卡券发放【总数固定】，不应该累计
                ret[gift_rule['coupon_apply_id']] = gift_rule['rank_total']
        return ret
    
    def get_gift_equities(self) -> dict:
        """ 找出权益总奖励数量 """
        ret = defaultdict(int)
        for gift_rule in self.cached_gift_rules:
            if gift_rule['gift_type'] == self.GiftType.EQUITY.name:
                ret[gift_rule['equity_id']] = gift_rule['rank_total']
        return ret

    def get_gift_assets(self) -> dict:
        """ 找出币种总奖励数量 """
        ret = defaultdict(Decimal)
        for gift_rule in self.cached_gift_rules:
            if gift_rule['gift_type'] == self.GiftType.ASSET.name:
                ret[gift_rule['gift_asset']] += Decimal(gift_rule['rank_total'])
        return ret

    @cached_property
    def cached_gift_rules(self) -> list:
        if not self.gift_rules:
            return []
        ret = json.loads(self.gift_rules)
        for gift_rule in ret:
            gift_rule['rank_min'] = int(gift_rule['rank_min'])
            gift_rule['rank_max'] = int(gift_rule['rank_max'])
            if gift_rule['gift_type'] == self.GiftType.ASSET.name:
                gift_rule['rank_limit'] = Decimal(gift_rule['rank_limit'])
                gift_rule['rank_total'] = Decimal(gift_rule['rank_total'])
                if self.mode is self.ActivityMode.PROPORTION:
                    gift_rule['proportion'] = Decimal(gift_rule['proportion'])
            elif gift_rule['gift_type'] == self.GiftType.EQUITY.name:
                gift_rule['rank_limit'] = int(gift_rule['rank_limit'])
                gift_rule['rank_total'] = int(gift_rule['rank_total'])
                gift_rule['equity_id'] = int(gift_rule['equity_id'])
            else:
                gift_rule['rank_limit'] = int(gift_rule['rank_limit'])
                gift_rule['rank_total'] = int(gift_rule['rank_total'])
                gift_rule['coupon_apply_id'] = int(gift_rule['coupon_apply_id'])
        return ret

    def left_gift_amount(self, total: Decimal, gift_asset: str) -> Decimal:
        """对应奖励币种余量"""
        record = GiftHistory.query.filter(
            GiftHistory.activity_id == self.activity_id,
            GiftHistory.asset == gift_asset,
            or_(
                GiftHistory.status == GiftHistory.Status.FINISHED,
                GiftHistory.status == GiftHistory.Status.CREATED
            )
        ).with_entities(
            func.sum(GiftHistory.amount).label('total')
        ).first()
        total_gift = record.total if record.total else Decimal()
        return total - total_gift


class DepositBonusActivityCondition(ModelBase):
    """ 充值福利具体活动配置活动参与条件 """
    __table_args__ = (
        db.UniqueConstraint('activity_config_id', 'key', name='activity_config_id_key_unique'),
    )

    activity_config_id = db.Column(db.Integer, nullable=False, comment='DepositBonusActivityConfig.id')
    key = db.Column(db.String(64), nullable=False)
    value = db.Column(db.String(512), nullable=False)


class DepositBonusActivityStatistic(ModelBase):

    class BusinessType(Enum):
        TRADE_BEFORE_START = 'TRADE_BEFORE_START'  # 活动开始前统计交易达标人数

    __table_args__ = (
        db.UniqueConstraint('activity_config_id', 'business_type', name='activity_config_id_business_uniq'),
    )

    activity_config_id = db.Column(db.Integer, nullable=False, comment='DepositBonusActivityConfig.id')
    business_type = db.Column(db.StringEnum(BusinessType), nullable=False, default=BusinessType.TRADE_BEFORE_START)
    user_bit_map = db.Column(db.MYSQL_MEDIUM_BLOB, nullable=False)
    last_update_time = db.Column(db.MYSQL_DATETIME_6, nullable=False)

    def get_user_ids(self) -> list:
        if not self.user_bit_map:
            return []
        bm = BitMap.deserialize(self.user_bit_map)
        if not bm:
            return []
        return bm


class DepositBonusActivityApplyUser(ModelBase):
    """充值福利活动报名用户"""
    __table_args__ = (
        db.UniqueConstraint('deposit_bonus_id', 'activity_id', 'user_id', name='activity_id_user_id'),
    )

    deposit_bonus_id = db.Column(db.Integer, nullable=False, comment='DepositBonusActivity.id')
    activity_id = db.Column(db.Integer, nullable=False, comment='DepositBonusActivityConfig.id')
    user_id = db.Column(db.Integer, nullable=False, index=True)


class DepositBonusActivityUserInfo(ModelBase):

    class Status(Enum):
        VALID = '有效'
        INVALID = '无效'

    class GiftStatus(Enum):
        NONE = '无需发放'
        CREATED = '待发放'
        FINISHED = '已发放'

    class Type(Enum):
        NEW = '新用户'
        OLD = '老用户'

    class RemarkType(Enum):
        ANTI_FRAUD = '羊毛得分规则剔除'
        SIMILAR_EMAIL = '相似邮箱规则剔除'
        FRAUD_TX_ID = '羊毛txid校验'
        ABNORMAL_USER = '清退用户或羊毛党，无法发奖'

    class InvalidType(Enum):
        NONE = 'none'
        HAS_BEEN_BANNED = 'has_been_banned'
        VIOLATION = 'violation'

    __table_args__ = (
        db.UniqueConstraint('deposit_bonus_id', 'activity_id', 'user_id', name='activity_id_user_id'),
    )

    report_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    deposit_bonus_id = db.Column(db.Integer, nullable=False, comment='DepositBonusActivity.id')
    activity_id = db.Column(db.Integer, nullable=False, comment='DepositBonusActivityConfig.id')
    user_id = db.Column(db.Integer, nullable=False, index=True)
    rank = db.Column(db.Integer, nullable=True)  # None: 没有排名
    deposit_at = db.Column(db.MYSQL_DATETIME_6, nullable=True, comment='达标时间/实际充值满足阈值的时间')
    type = db.Column(db.StringEnum(Type), nullable=False)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, comment='充值总数')
    net_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, comment='净充值总数')
    '''
        [
            {
                gift_type: ASSET,
                gift_asset: CET,
                gift_amount: 1000
            },
            {
                gift_type: COUPON,
                coupon_apply_id: 1,
                gift_amount: 1,  卡券固定只能发一张
            }
        ]
    '''
    gifts = db.Column(db.Text, nullable=True, default='')
    is_first_deposit = db.Column(db.Boolean, default=False, nullable=False, comment='在活动期间为首次链上充值用户')
    is_comeback = db.Column(db.Boolean, default=False, nullable=False, comment='在报名前注册或充值过，但是资产为0的用户数量')
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID)
    gift_status = db.Column(db.StringEnum(GiftStatus), nullable=False, default=GiftStatus.NONE)
    remark = db.Column(db.String(128), nullable=False, default='')

    def get_gift_assets(self) -> dict:
        """ 找出币种奖励数量 """
        ret = defaultdict(Decimal)
        for gift in self.get_gifts():
            if gift['gift_type'] == DepositBonusActivityConfig.GiftType.ASSET.name:
                ret[gift['gift_asset']] += Decimal(gift['gift_amount'])
        return ret

    def get_gift_coupons(self) -> dict:
        """ 找出卡券奖励数量 """
        ret = defaultdict(int)
        for gift in self.get_gifts():
            if gift['gift_type'] == DepositBonusActivityConfig.GiftType.COUPON.name:
                ret[gift['coupon_apply_id']] += int(gift['gift_amount'])
        return ret
    
    def get_gift_equities(self) -> dict:
        """ 找出权益奖励数量 """
        ret = defaultdict(int)
        for gift in self.get_gifts():
            if gift['gift_type'] == DepositBonusActivityConfig.GiftType.EQUITY.name:
                ret[gift['equity_id']] += int(gift['gift_amount'])
        return ret

    def get_gifts(self) -> list:
        if not self.gifts:
            return []
        return json.loads(self.gifts)

    @property
    def invalid_type(self) -> InvalidType:
        if self.status == self.Status.VALID:
            return self.InvalidType.NONE
        elif self.remark == self.RemarkType.FRAUD_TX_ID.value:
            return self.InvalidType.VIOLATION
        else:
            return self.InvalidType.HAS_BEEN_BANNED


class DepositBonusActivityUserGiftRow(ModelBase):

    class GiftType(Enum):
        ASSET = "币种"
        EQUITY = "权益"
        
    class Status(Enum):
        CREATED = '待发放'
        FINISHED = '已发放'

    user_info_id = db.Column(db.Integer, nullable=False, index=True)
    user_id = db.Column(db.Integer, nullable=False, index=True)
    gift_type = db.Column(db.StringEnum(GiftType), nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED)
    gift_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    gift_asset = db.Column(db.String(32))
    gift_biz_id = db.Column(db.Integer)

    @classmethod
    def settle_gifts(cls, user_info: DepositBonusActivityUserInfo):
        """结算奖励"""
        gift_equities = user_info.get_gift_equities()
        gift_assets = user_info.get_gift_assets()
        user_id = user_info.user_id
        rows = []
        for equity_id, amount in gift_equities.items():
            rows.append(DepositBonusActivityUserGiftRow(
                user_info_id=user_info.id,
                user_id=user_id,
                gift_type=cls.GiftType.EQUITY,
                gift_amount=amount,
                gift_biz_id=equity_id,
            ))
        for asset, amount in gift_assets.items():
            rows.append(DepositBonusActivityUserGiftRow(
                user_info_id=user_info.id,
                user_id=user_id,
                gift_type=cls.GiftType.ASSET,
                gift_amount=amount,
                gift_asset=asset,
            ))
        return rows


class NameBlockWords(ModelBase):
    """用户 用户名和账户名的屏蔽词"""

    class BlockType(Enum):
        USER_NAME = "user_name"  # 有多语言翻译
        ACCOUNT_NAME = "account_name"  # 无

    word = db.Column(db.String(128), nullable=False, index=True)
    block_type = db.Column(db.StringEnum(BlockType), nullable=False, default=BlockType.USER_NAME)
    updated_user_id = db.Column(db.Integer, nullable=False)


class LangNameBlockWords(ModelBase):
    word_id = db.Column(db.Integer, nullable=False)
    lang = db.Column(db.StringEnum(Language), nullable=False)
    lang_word = db.Column(db.String(128), nullable=False, index=True)
