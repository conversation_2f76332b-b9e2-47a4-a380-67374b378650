from enum import Enum

from flask_babel import gettext as _

from app.common.constants import Language
from app.common.onchain import Chain, OrderSide

from app.models import db
from app.models import ModelBase

AMOUNT_STRING_LEN = 78  # 数量存储string, 最长78位, 取值自最大的64位16进制数对应2进制数的长度: len(str(int('f' * 64, 16)))


class OnchainSetting(ModelBase):
    class Status(Enum):  # noqa
        VALID = 'valid'
        DELETED = 'deleted'

    key = db.Column(db.String(64), index=True, nullable=False)
    value = db.Column(db.String(2048), nullable=False)

    status = db.Column(db.StringEnum(Status), nullable=False, index=True, default=Status.VALID)


class OnchainToken(ModelBase):
    """链上交易Token"""

    __table_args__ = (
        db.UniqueConstraint('chain', 'contract', name='chain_contract_uniq'),
    )

    chain = db.Column(db.StringEnum(Chain), nullable=False)
    contract = db.Column(db.String(64), nullable=False)
    symbol = db.Column(db.String(64), nullable=False)
    name = db.Column(db.String(256), nullable=False)
    decimals = db.Column(db.Integer, nullable=False)  # 精度
    logo = db.Column(db.String(512))


class OnchainTokenBalance(ModelBase):
    """链上交易Token余额"""

    __table_args__ = (
        db.UniqueConstraint('token_id', 'user_id', name='token_id_user_id_uniq'),
    )

    token_id = db.Column(db.Integer, nullable=False)
    user_id = db.Column(db.Integer, nullable=False, index=True)
    available = db.Column(db.String(AMOUNT_STRING_LEN), nullable=False)
    frozen = db.Column(db.String(AMOUNT_STRING_LEN), nullable=False)


class OnchainTokenFavorite(ModelBase):
    """链上交易Token收藏"""

    USER_MAX_FAVORITES = 1000  # 限制用户最大收藏Token数

    class StatusType(Enum):
        PASSED = 'passed'
        DELETED = 'deleted'

    __table_args__ = (
        db.UniqueConstraint('user_id', 'token_id', name='user_id_token_id_uniq'),
    )

    user_id = db.Column(db.Integer, nullable=False)
    token_id = db.Column(db.Integer, nullable=False)
    status = db.Column(db.StringEnum(StatusType), nullable=False, default=StatusType.PASSED)


class OnchainTokenInfo(ModelBase):
    """链上交易Token信息"""

    token_id = db.Column(db.Integer, nullable=False, unique=True)
    top_pool = db.Column(db.String(64))  # 成交量最大的Pool
    top_pool_launch_time = db.Column(db.MYSQL_DATETIME_6)  # Top Pool发行时间
    top_pool_name = db.Column(db.String(512))  # Top Pool Name
    top_pool_liquidity = db.Column(db.String(AMOUNT_STRING_LEN))  # Top Pool流动性
    total_supply = db.Column(db.String(AMOUNT_STRING_LEN))  # 总供应量
    circulating_supply = db.Column(db.String(AMOUNT_STRING_LEN))  # 流通供应量
    website = db.Column(db.String(512))
    community = db.Column(db.MYSQL_JSON, default={})  # 社媒
    about = db.Column(db.TEXT)  # 简介
    holders_count = db.Column(db.Integer)  # 持币地址数
    top10_percentage = db.Column(db.MYSQL_DECIMAL_26_8)  # Top10地址持币百分比
    risk = db.Column(db.MYSQL_JSON, default={})  # 风险信息

    @property
    def community_url(self):
        to_url_func_map = {
            'discord': lambda x: x if x else None,
            'twitter': lambda x: f'https://X.com/{x}' if x else None,
            'telegram': lambda x: f'https://t.me/{x}' if x else None,
        }
        return {k: to_url_func_map.get(k, lambda x: x if x else None)(v) for k, v in self.community.items()}


class OnchainTokenAboutSource(ModelBase):
    """链上交易Token的About信息翻译源"""

    token_id = db.Column(db.Integer, nullable=False, index=True)
    lang = db.Column(db.StringEnum(Language), nullable=False)   # langid识别出的语言(目前限定在英语与简体中文)
    about = db.Column(db.TEXT, nullable=False)  # Token简介


class OnchainTokenAboutTranslation(ModelBase):
    """链上交易Token的About信息翻译结果"""

    __table_args__ = (
        db.UniqueConstraint('token_id', 'lang', name='token_id_lang_uniq'),
    )

    token_id = db.Column(db.Integer, nullable=False)
    source_id = db.Column(db.Integer, nullable=False)
    lang = db.Column(db.StringEnum(Language), nullable=False)
    about = db.Column(db.TEXT, nullable=False)  # Token简介翻译结果


class OnchainTokenBlocklist(ModelBase):
    """链上交易Token黑名单"""

    class BlockType(Enum):
        HOT = '禁止上榜'
        TRADE = '禁止交易'
        TRADE_AND_HOT = '禁止交易和上榜'

        @classmethod
        def can_not_trade_type(cls) -> set:
            return {cls.TRADE, cls.TRADE_AND_HOT}

        @classmethod
        def can_not_hot_type(cls) -> set:
            return {cls.HOT, cls.TRADE_AND_HOT}

    class NoticeType(Enum):
        SUPPORTED = _('当前资产可在CoinEx现货进行交易，暂不支持链上交易。')
        ABNORMAL = _('当前资产存在异常，暂时无法交易。')
        COMING_SOON = _('当前资产即将上线CoinEx现货交易，敬请关注官方公告。')
        RENAMING = _('当前资产即将进行置换或更名，现已暂停交易。')

    __table_args__ = (
        db.UniqueConstraint('chain', 'contract', name='chain_contract_uniq'),
    )

    chain = db.Column(db.StringEnum(Chain), nullable=False)
    contract = db.Column(db.String(64), nullable=False)
    symbol = db.Column(db.String(64), nullable=False)
    name = db.Column(db.String(256), nullable=False)
    block_type = db.Column(db.StringEnum(BlockType), nullable=False)
    notice_type = db.Column(db.StringEnum(NoticeType))
    spot_asset = db.Column(db.String(64))  # 如果notice_type设置为SUPPORTED, 这里必填对应现货信息
    reason = db.Column(db.String(512))
    admin_user_id = db.Column(db.Integer, nullable=False)


class OnchainOrder(ModelBase):
    """链上交易订单数据"""

    class Type(Enum):
        MARKET = 'market'  # 市价单
        LIMIT = 'limit'  # 计划单

    class Status(Enum):
        CREATED = 'created'         # 待生效
        PROCESSING = 'processing'   # 待上链：钱包侧创建订单成功
        TO_FINISH = 'to_finish'     # 待完成：钱包测到达终结态
        FINISHED = 'finished'       # 成交：交易上链并兑换成功
        FAILED = 'failed'           # 失败：1.交易上链并兑换失败 2.内部服务其他异常情况
        CANCELLED = 'cancelled'     # 取消：长时间不打包阻塞交易时自动取消

    class TxResult(Enum):
        SUCCESS = 'success'  # 交易上链并成功
        FAILED = 'failed'  # 交易上链并失败
        CANCELLED = 'cancelled'  # 交易未上链取消

    token_id = db.Column(db.Integer, nullable=False, index=True)
    user_id = db.Column(db.Integer, nullable=False, index=True)
    side = db.Column(db.StringEnum(OrderSide), nullable=False)
    type = db.Column(db.StringEnum(Type), nullable=False)
    want_amount = db.Column(db.String(AMOUNT_STRING_LEN), nullable=False)  # 预期得到数量, 用于计算实际滑点
    money_asset = db.Column(db.String(64), nullable=False)
    money_amount = db.Column(db.String(AMOUNT_STRING_LEN))
    money_asset_price = db.Column(db.String(AMOUNT_STRING_LEN), nullable=False)
    token_amount = db.Column(db.String(AMOUNT_STRING_LEN))
    fee = db.Column(db.MYSQL_DECIMAL_26_8)  # 平台收取的佣金
    gas_fee = db.Column(db.MYSQL_DECIMAL_26_8)  # 平台收取的Gas费, 并非链上实际的Gas费
    slippage_limit = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 滑点设置, 1➡️0.01, 25➡️0.25, 限制0-50
    slippage = db.Column(db.MYSQL_DECIMAL_26_8)  # 滑点设置, 1➡️0.01, 25➡️0.25, 实际计算结果, 无限制
    chain = db.Column(db.StringEnum(Chain), nullable=False)
    swap_order_id = db.Column(db.String(128), nullable=False, unique=True)  # swap订单ID
    tx_data = db.Column(db.TEXT, nullable=False)  # 兑换合约调用参数
    tx_id = db.Column(db.String(128))
    trans_id = db.Column(db.Integer)  # 资产流水ID
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED)
    tx_result = db.Column(db.StringEnum(TxResult))
    finished_at = db.Column(db.MYSQL_DATETIME_6)


class OnchainStopOrder(ModelBase):
    """链上交易计划市价单"""

    class Status(Enum):
        CREATED = 'created'
        ACTIVE = 'active'          # 已委托
        FAILED = 'failed'          # 异常触发失败
        CANCELLED = 'cancelled'    # 用户手动取消

    class TriggerType(Enum):
        PRICE = 'price'  # 价格
        MARKET_CAP = 'market_cap'     # 市值

    class Operator(Enum):
        LE = '<='
        GE = '>='

    class FailType(Enum):
        SITE_DISABLED = 'site_disabled'   # 全站关闭
        NOT_ENOUGH = 'not_enough'  # 资金不足
        FORBIDDEN = 'forbidden'     # 用户交易受限
        TOKEN_FORBIDDEN = 'token_forbidden'     # Token交易受限
        AMOUNT_TOO_SMALL = 'amount_too_small'   # 交易金额太小
        AMOUNT_TOO_BIG = 'amount_too_big'       # 交易金额太大
        QUOTE_EXPIRED = 'quote_expired'         # 价格波动太大，报价失效
        RECEIVE_TOO_SMALL = 'receive_too_small' # 触发后询价预计获得小于最少获得

    class CancelType(Enum):
        TIMEOUT = 'timeout'  # 超时
        USER = 'user'        # 用户手动取消

    token_id = db.Column(db.Integer, nullable=False, index=True)
    user_id = db.Column(db.Integer, nullable=False, index=True)
    side = db.Column(db.StringEnum(OrderSide), nullable=False)
    money_asset = db.Column(db.String(64), nullable=False)
    chain = db.Column(db.StringEnum(Chain), nullable=False)
    from_amount = db.Column(db.String(AMOUNT_STRING_LEN), nullable=False)
    want_amount = db.Column(db.String(AMOUNT_STRING_LEN), nullable=False)
    trigger_type = db.Column(db.StringEnum(TriggerType), nullable=False)
    trigger_amount = db.Column(db.String(AMOUNT_STRING_LEN), nullable=False)
    trigger_unit = db.Column(db.String(64), nullable=False)  # USDT/SOL/ETH/BNB/USD
    operator = db.Column(db.StringEnum(Operator), nullable=False)
    slippage_limit = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 滑点设置, 1➡️0.01, 25➡️0.25, 限制0-50
    expired_at = db.Column(db.MYSQL_DATETIME_6, nullable=False, index=True)
    effected_at = db.Column(db.MYSQL_DATETIME_6, nullable=True)
    order_id = db.Column(db.Integer)  # 创建Order的id
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED)
    fail_type = db.Column(db.StringEnum(FailType))
    cancel_type = db.Column(db.StringEnum(CancelType))


class OnchainOrderTransHistory(ModelBase):
    """链上交易订单资金流水"""

    class Status(Enum):
        CREATED = "预锁定"
        LOCKED = "已锁定"  # 已锁定
        FAILED = "已失败"  # 终止态

        TO_UNLOCK = "预解锁"  # 创建订单失败
        UNLOCKED = "已解锁"  # 终止态

        TO_UNLOCK_SUB = "预解冻扣款"  # 创建链上订单成功；解锁+扣款是原子操作
        TO_ADD = "待加资产"  # 待加资产
        FINISHED = "已完成"  # 已加资产（终止态）

    order_id = db.Column(db.Integer, unique=True, comment="订单ID")
    user_id = db.Column(db.Integer, nullable=False, comment="用户id")
    side = db.Column(db.StringEnum(OrderSide), nullable=False)
    money_asset = db.Column(db.String(64), nullable=False)
    money_amount = db.Column(db.String(AMOUNT_STRING_LEN))
    token_id = db.Column(db.Integer, nullable=False)
    token_amount = db.Column(db.String(AMOUNT_STRING_LEN))
    fee = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, comment="平台收取的佣金")
    gas_fee = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, comment="gas费")
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED, comment="流水状态")


class OnchainAssetLiability(ModelBase):
    """链上交易资产负债"""

    token_id = db.Column(db.Integer, nullable=False, index=True)
    # 链上资产
    wallet_balance = db.Column(db.String(AMOUNT_STRING_LEN), nullable=False)
    # 用户持仓
    user_balance = db.Column(db.String(AMOUNT_STRING_LEN), nullable=False)


class OnchainAssetToSpotHistory(ModelBase):
    """链上交易资产迁移现货资产记录"""

    class Status(Enum):
        CREATED = 'created'
        DEDUCTED = 'deducted'
        FINISHED = 'finished'

    __table_args__ = (
        db.UniqueConstraint('user_id', 'token_id', name='user_id_token_id_uniq'),
    )

    user_id = db.Column(db.Integer, nullable=False)
    token_id = db.Column(db.Integer, nullable=False)
    asset = db.Column(db.String(32), nullable=False)
    amount = db.Column(db.String(AMOUNT_STRING_LEN), nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED)
