from enum import Enum
from app.models import db
from app.models.base import M2MModelBase


class TranslationTaskMySQL(M2MModelBase):
    """MySQL 版本的翻译任务模型"""
    __tablename__ = 'translation_tasks'

    class Business(Enum):
        INSIGHT = 'insight'
        KLINE_ANALYSIS = 'kline_analysis'
        AIRDROP = 'airdrop'
        AIRDROP_QUESTION = 'airdrop_question'
        COIN_INFO = 'coin_info'
        ACADEMY = 'academy'
        INFORMATION = 'information'
        COIN_INFORMATION = 'coin_information'
        COIN_COMMENT = 'coin_comment'
        BLOG = 'blog'
        CUSTOMER_CHATBOT = 'customer_chatbot'
        VIDEO_SUBTITLE = 'video_subtitle'
        ONCHAIN = 'onchain'

    class Status(Enum):
        CREATED = 'created'
        PROCESSING = 'processing'
        RETRYING = 'retrying'
        FINISHED = 'finished'
        FAILED = 'failed'
    
    class ExecutionMode(Enum):
        SYNC = 'sync'
        ASYNC = 'async'

    id = db.Column(db.Integer, primary_key=True)
    business = db.Column(db.StringEnum(Business), nullable=False)
    business_id = db.Column(db.String(255))
    business_info = db.Column(db.String(255), default='')
    source = db.Column(db.String(255), nullable=False)
    target = db.Column(db.String(255), nullable=False)
    task_id = db.Column(db.String(255), nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.CREATED)
    input_tokens = db.Column(db.Integer, default=0)
    output_tokens = db.Column(db.Integer, default=0)
    content = db.Column(db.MYSQL_MEDIUM_TEXT, default='')
    deleted = db.Column(db.Boolean, default=False)
    execution_mode = db.Column(db.StringEnum(ExecutionMode))
    model_name = db.Column(db.String(255), default='')

    __table_args__ = (
        db.Index('idx_business_id', 'business_id'),
    )

    @classmethod
    def get_business_tasks(cls, business, business_id=None, business_info=None, task_id=None):
        """获取业务相关任务"""
        query = cls.query.filter(
            cls.deleted == False,  # noqa: E712
            cls.business == business
        )

        if business_id is not None:
            if isinstance(business_id, list):
                query = query.filter(cls.business_id.in_(business_id))
            else:
                query = query.filter(cls.business_id == business_id)

        if business_info is not None:
            query = query.filter(cls.business_info == business_info)
        
        if task_id is not None:
            query = query.filter(cls.task_id == task_id)

        tasks = query.order_by(cls.created_at.desc()).all()
        
        unique_tasks = {}
        for task in tasks:
            key = (task.business, task.business_id, task.business_info, task.target)
            unique_task = unique_tasks.get(key)
            if unique_task is None or task.created_at > unique_task.created_at:
                unique_tasks[key] = task

        return list(unique_tasks.values())

    def update_status(self, status, **kwargs):
        """更新任务状态"""
        self.status = status.value
        for key, value in kwargs.items():
            setattr(self, key, value)
        db.session.commit()