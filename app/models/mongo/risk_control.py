# -*- coding: utf-8 -*-
from datetime import <PERSON><PERSON><PERSON>

from sqlalchemy.ext.mutable import Mu<PERSON><PERSON><PERSON>

from app.models.risk_control import RiskUser
from app.utils import now
from app.models import db
from app.models.base import M2MModelBase


class UserPreventRiskControlConfigMySQL(M2MModelBase):
    """MySQL 版本的用户免风控配置模型"""
    __tablename__ = 'user_prevent_risk_control_configs'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, nullable=False, index=True)
    latest_risk_user_record_id = db.Column(db.Integer, nullable=False, index=True)  # 关联到risk user
    reason = db.Column(db.StringEnum(RiskUser.Reason), nullable=False)
    source = db.Column(db.String(64))  # RiskUser.source
    expired_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    extra = db.Column(db.MYSQL_JSON, default={})
    remark = db.Column(db.String(512))
    history = db.Column(MutableList.as_mutable(db.MYSQL_JSON), default=[])

    @classmethod
    def get_expired_at_options(cls):
        _mapping = {
            "无": 0,
            "12h": 12 * 3600,
            "24h": 24 * 3600,
            "7d": 7 * 24 * 3600,
            "15d": 15 * 24 * 3600,
            "30d": 30 * 24 * 3600,
            "90d": 90 * 24 * 3600,
        }
        return _mapping

    @classmethod
    def enabled_reasons(cls) -> list:
        return [
            RiskUser.Reason.IMMEDIATELY_WITHDRAWAL,
            RiskUser.Reason.NEW_USER_IMMEDIATELY_WITHDRAWAL,
            RiskUser.Reason.WITHDRAWAL_NO_ON_CHAIN_DEPOSIT,
            RiskUser.Reason.BUS_ONLINE_COIN_MARKET_VOLATILITY,
            RiskUser.Reason.BUS_ONLINE_COIN_DEPOSIT_ASSET_VOLATILITY,
            RiskUser.Reason.ACCUMULATED_ASSET_DEPOSIT,
            RiskUser.Reason.ACCUMULATED_ASSET_DEPOSIT_PROPORTION,
            RiskUser.Reason.USER_ACCUMULATED_ASSET_DEPOSIT_PROPORTION,
            RiskUser.Reason.ASSET_BALANCE_IN_DISABLED,

            RiskUser.Reason.ABNORMAL_PROFIT,
            RiskUser.Reason.PERIOD_ABNORMAL_PROFIT,
            RiskUser.Reason.WASH_DEAL,
            RiskUser.Reason.MARKET_VOLATILITY,
            RiskUser.Reason.PERPETUAL_MARKET_VOLATILITY,
            RiskUser.Reason.MARGIN_LIQUIDATION,
            RiskUser.Reason.PERPETUAL_LIQUIDATION,
            RiskUser.Reason.PLEDGE_LIQUIDATION_BEYOND_THRESHOLD,
            RiskUser.Reason.MARGIN_LOAN_FLAT_CHECK,
            RiskUser.Reason.INVESTMENT_BALANCE_CHECK,
            RiskUser.Reason.PERPETUAL_BALANCE_CHECK,
            RiskUser.Reason.RED_PACKET_CHECK,
            RiskUser.Reason.P2P_BALANCE_CHECK,
            RiskUser.Reason.PLEDGE_LOAN_FLAT_CHECK,
            RiskUser.Reason.ACCUMULATED_ASSET_WITHDRAWAL,
            RiskUser.Reason.VIABTC_TRANS_BEYOND_THRESHOLD,
            RiskUser.Reason.USER_ASSET_PENDING_ORDER_THRESHOLD
        ]

    @classmethod
    def check_ignore(cls, user_id: int, reason: RiskUser.Reason, source: str = None) -> bool:
        q = cls.query.filter(
            cls.user_id == user_id,
            cls.reason == reason
        )
        if reason in RiskUser.SEC_PERM_REASONS:
            q = q.filter(cls.source == source)
        r = q.first()
        if not r or r.expired_at < now():
            return False
        return True

    @classmethod
    def add_config(cls, user_id: int, reason: RiskUser.Reason,
                   seconds: int,
                   extra: dict | None,
                   risk_user_id: int,
                   source: str = None,
                   remark: str = ''):
        if reason not in UserPreventRiskControlConfigMySQL.enabled_reasons():
            return
        if seconds not in UserPreventRiskControlConfigMySQL.get_expired_at_options().values():
            return
        if seconds <= 0:
            return
        expired_at = now() + timedelta(seconds=seconds)
        q = cls.query.filter(
            cls.user_id == user_id,
            cls.reason == reason
        )
        if reason in RiskUser.SEC_PERM_REASONS:
            q = q.filter(cls.source == source)
        r = q.first()
        _ex = extra or {}
        history = r.history if r else []
        history.append({
            "risk_user_id": risk_user_id,
            "source": source,
            "created_at": now(),
            "seconds": seconds,
            "expired_at": int(expired_at.timestamp()),
        })
        if not r:
            new_config = cls(
                user_id=user_id,
                reason=reason,
                latest_risk_user_record_id=risk_user_id,
                expired_at=expired_at,
                extra=_ex,
                history=history,
                source=source,
                remark=remark,
            )
            db.session.add(new_config)
        else:
            r.source = source
            r.expired_at = expired_at
            r.extra = _ex
            r.history = history
            r.remark = remark
            r.latest_risk_user_record_id = risk_user_id
        db.session.commit()


class UserPreventRiskControlEventMySQL(M2MModelBase):
    """MySQL 版本的用户免风控记录模型"""
    __tablename__ = 'user_prevent_risk_control_events'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, nullable=False)
    reason = db.Column(db.StringEnum(RiskUser.Reason), nullable=False)
    extra = db.Column(db.MYSQL_JSON, default={})

    @classmethod
    def add_event(cls, user_id: int, reason: RiskUser.Reason, extra: dict | None = None):
        _ex = extra or {}
        new_event = cls(
            user_id=user_id,
            reason=reason,
            extra=_ex
        )
        db.session.add(new_event)
        db.session.commit()