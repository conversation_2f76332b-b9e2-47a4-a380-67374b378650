from datetime import datetime
from enum import Enum
from typing import List

from pydantic import BaseModel

from app.models.mongo import Status
from app.models.base import M2MModelBase, db


class FormModel(BaseModel):
    class FiledType(Enum):
        TEXT = "text"
        FILE = "file"
        QR_CODE = "qr_code"

    filed_type: str
    key: str
    name: str
    field_name: str
    status: str
    required: bool = True


class P2pPayChannelMySQL(M2MModelBase):
    """MySQL 版本的支付配置表"""

    __tablename__ = "p2p_pay_channel"

    class ActiveStatus(Enum):
        PENDING = "pending"   # 准备中
        ACTIVE = 'active'     # 有效
        INACTIVE = 'inactive'   # 失效

    class ConfigType(Enum):
        NORMAL = "normal"
        COUNTRY = "country"

    Form = FormModel

    name = db.Column(db.String(255), nullable=False, index=True)
    color = db.Column(db.String(255), nullable=False)
    is_need_name = db.Column(db.Boolean, default=False, nullable=False)
    form = db.Column(db.MYSQL_JSON(FormModel), nullable=False)
    config_type = db.Column(
        db.StringEnum(ConfigType),
        nullable=False,
        default=ConfigType.NORMAL,
    )
    rank = db.Column(db.Integer, default=0, nullable=False)
    active_status = db.Column(
        db.StringEnum(ActiveStatus),
        nullable=False,
        default=ActiveStatus.PENDING,
    )
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID)
    lang_data = db.Column(db.MYSQL_JSON, default={})
    lock = db.Column(db.Boolean, default=False)

    def to_dict(self, **kwargs):
        """
        重写父类的 to_dict 方法，对 form 字段进行额外处理
        """
        form_data = []
        if self.form:
            for form_item in self.form:
                # 如果是 FormModel 对象，调用其 model_dump 方法转换为字典
                if hasattr(form_item, 'model_dump'):
                    form_data.append(form_item.model_dump())
                else:
                    form_data.append(form_item)
        
        # 创建一个副本，避免修改原对象
        obj_copy = self.__class__(**{c.name: getattr(self, c.name) for c in self.__table__.columns})
        obj_copy.form = form_data
        obj_copy.id = self.mongo_id

        # 调用父类的 to_dict 方法
        return super(P2pPayChannelMySQL, obj_copy).to_dict(**kwargs)

    @classmethod
    def get_all_valid_channel(cls):
        """获取所有有效的支付渠道"""
        return cls.query.filter(
            cls.status == Status.VALID,
            cls.active_status == cls.ActiveStatus.ACTIVE,
        ).all()

    @classmethod
    def get_all_channel(cls):
        """获取所有支付渠道，包括已删除的"""
        return cls.query.all()


class P2pFiatPayChannelMySQL(M2MModelBase):
    """MySQL 版本的法币支付渠道配置表"""

    __tablename__ = "p2p_fiat_pay_channel"

    fiat = db.Column(db.String(255), nullable=False, unique=True)
    pay_channel_ids = db.Column(db.MYSQL_JSON, nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID)

    @classmethod
    def get_all_valid_fiat_pay_channel(cls):
        """获取所有有效的法币支付渠道"""
        return cls.query.filter(cls.status == Status.VALID).all()


class P2pCountryFiat(M2MModelBase):
    """MySQL 版本的地区法币对照表"""

    __tablename__ = "p2p_country_fiat"

    country_code = db.Column(db.String(255), nullable=False, index=True)
    fiat = db.Column(db.String(255), nullable=False, index=True)
    is_primary = db.Column(db.Boolean, default=True)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID)
    fiat_symbol = db.Column(db.String(32), nullable=False, comment="法币符号")  # 已废弃

    @classmethod
    def get_all_valid_fiat_country(cls):
        """获取所有有效的地区法币对照"""
        return cls.query.filter(cls.status == Status.VALID).all()

    @classmethod
    def get_all_valid_fiat(cls):
        """获取所有有效的法币"""
        valid_fiats = (
            cls.query.filter(cls.status == Status.VALID)
            .with_entities(cls.fiat)
            .distinct()
            .all()
        )
        return list(set(item[0] for item in valid_fiats))

    @classmethod
    def get_all_fiats(cls):
        """获取所有法币"""
        all_fiats = cls.query.with_entities(cls.fiat).distinct().all()
        return list(set(item[0] for item in all_fiats))



class FiatDataModel(BaseModel):
    fiat: str
    pay_channel_ids: List[str]
    status: str
    created_at: datetime


class P2pCountrySuggestPayChannelMySQL(M2MModelBase):
    """MySQL 版本的地区推荐支付渠道报表"""

    __tablename__ = "p2p_country_pay_channel"

    country_code = db.Column(db.String(255), nullable=False, index=True)
    pay_channel_ids = db.Column(db.MYSQL_JSON, default=[])
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID)
    fiat_data = db.Column(db.MYSQL_JSON(FiatDataModel), nullable=False)

    def _process_fiat_data(self, fiat_data_list):
        """
        处理 FiatDataModel 类型的列表，将其转换为字典列表
        """
        result = []
        if fiat_data_list:
            for fiat_data in fiat_data_list:
                # 如果是 FiatDataModel 对象，调用其 model_dump 方法转换为字典
                if hasattr(fiat_data, 'model_dump'):
                    result.append(fiat_data.model_dump())
                else:
                    result.append(fiat_data)
        return result

    def to_dict(self, **kwargs):
        """
        重写父类的 to_dict 方法，对 FiatDataModel 类型字段进行额外处理
        """
        # 处理 fiat_data
        processed_fiat_data = self._process_fiat_data(self.fiat_data)
        
        # 创建一个副本，避免修改原对象
        obj_copy = self.__class__(**{c.name: getattr(self, c.name) for c in self.__table__.columns})
        obj_copy.fiat_data = processed_fiat_data

        # 调用父类的 to_dict 方法
        return super(P2pCountrySuggestPayChannelMySQL, obj_copy).to_dict(**kwargs)

    @classmethod
    def get_all_valid_suggest_channel(cls):
        """获取所有有效的地区推荐支付渠道"""
        # 注意：这里的查询逻辑与MongoDB版本不同，因为MySQL不支持直接查询JSON字段内部的值
        # 我们只能先查询status为VALID的记录，然后在应用层过滤fiat_data中status为VALID的记录
        channels = cls.query.filter(cls.status == Status.VALID).all()
        result = []
        for channel in channels:
            valid_fiat_data = False
            for fiat_data in channel.fiat_data:
                if fiat_data.status == Status.VALID.name:
                    valid_fiat_data = True
                    break
            if valid_fiat_data:
                result.append(channel)
        return result


class UserPayFormModel(BaseModel):
    key: str
    value: str
    file_url: str = ""


class UserPayChannelMySQL(M2MModelBase):
    """MySQL 版本的用户支付渠道配置表"""

    __tablename__ = "p2p_user_pay_channel"

    user_id = db.Column(db.Integer, nullable=False, index=True)
    pay_channel_id = db.Column(db.String(255), nullable=False, index=True)
    pay_form = db.Column(db.MYSQL_JSON(UserPayFormModel), default=[])
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID)
    alias = db.Column(db.String(512))
    remark = db.Column(db.String(512))

    def to_dict(self, **kwargs):
        """
        重写父类的 to_dict 方法，对 form 字段进行额外处理
        """
        form_data = []
        if self.pay_form:
            for form_item in self.pay_form:
                # 如果是 FormModel 对象，调用其 model_dump 方法转换为字典
                if hasattr(form_item, 'model_dump'):
                    form_data.append(form_item.model_dump())
                else:
                    form_data.append(form_item)
        
        # 创建一个副本，避免修改原对象
        obj_copy = self.__class__(**{c.name: getattr(self, c.name) for c in self.__table__.columns})
        obj_copy.pay_form = form_data
        # 导出时避免使用内部的 id
        obj_copy.id = self.mongo_id
        
        # 调用父类的 to_dict 方法
        return super(UserPayChannelMySQL, obj_copy).to_dict(**kwargs)

    @classmethod
    def get_user_all_channel(cls, user_id: int):
        """获取用户所有支付渠道"""
        return (
            cls.query.filter(cls.user_id == user_id, cls.status == Status.VALID)
            .order_by(cls.id.desc())
            .all()
        )

    @classmethod
    def get_by_id(cls, _id: str):
        """通过ID获取支付渠道"""
        return cls.query.filter(cls.mongo_id == _id).first()

    @classmethod
    def get_by_ids(cls, _ids: list[str]):
        """通过ID列表获取支付渠道"""
        return cls.query.filter(
            cls.mongo_id.in_(_ids), cls.status == Status.VALID
        ).all()

    @classmethod
    def get_user_channel_id(cls, user_id, _id: str):
        """获取用户特定支付渠道"""
        return cls.query.filter(cls.mongo_id == _id, cls.user_id == user_id).first()


class P2pUserFeedbackChannelMySQL(M2MModelBase):
    """MySQL 版本的支付渠道反馈表"""

    __tablename__ = "p2p_user_feedback_channel"

    user_id = db.Column(db.Integer, nullable=False, index=True)
    channel_name = db.Column(db.String(255), nullable=False)
    fiat_name = db.Column(db.String(255), nullable=False)
    remark = db.Column(db.Text)

