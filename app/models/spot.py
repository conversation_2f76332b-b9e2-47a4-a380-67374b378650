# -*- coding: utf-8 -*-

from __future__ import annotations
from datetime import datetime, date
from decimal import Decimal
from enum import Enum
from typing import List, Optional

from ..common import Currency, Language
from ..utils import now
from .base import db, ModelBase
from .user import User


class Market(db.Model):
    class Status(Enum):
        OFFLINE = '下架'  # audit
        PENDING = '待上架'  # pass
        BIDDING = '竞价中'
        COUNTING_DOWN = '倒计时中'
        ONLINE = '上架'  # is_open
        SUSPENDED = '停牌'  # suspension

    class Mode(Enum):
        NORMAL = '普通'
        BIDDING = '竞价'
        COUNTDOWN = '倒计时'

    class TradingArea(Enum):
        USDT = 'USDT'
        USDC = 'USDC'
        BTC = 'BTC'
        BCH = 'BCH'
        ETH = 'ETH'
        CET = 'CET'
        DEFAULT = USDT

        @classmethod
        def quotes(cls) -> List[str]:
            return [v.value for v in cls]

    id = db.Column(db.Integer, primary_key=True)
    created_at = db.Column(db.MYSQL_DATETIME_6, default=datetime.utcnow)
    updated_at = db.Column(db.MYSQL_DATETIME_6,
                           default=datetime.utcnow,
                           onupdate=datetime.utcnow)

    name = db.Column(db.String(64), nullable=False, unique=True)
    trading_area = db.Column(db.Enum(TradingArea), nullable=False, index=True)
    mode = db.Column(db.Enum(Mode), nullable=False, default=Mode.NORMAL,
                     index=True)
    status = db.Column(db.Enum(Status), nullable=False, default=Status.OFFLINE,
                       index=True)

    default_depth = db.Column(db.String(32), nullable=False)
    depths = db.Column(db.String(256), nullable=False)

    base_asset = db.Column(db.String(32), nullable=False, index=True)  # sell
    base_asset_precision = db.Column(db.Integer, nullable=False, default=0)
    quote_asset = db.Column(db.String(32), nullable=False, index=True)  # buy
    quote_asset_precision = db.Column(db.Integer, nullable=False, default=0)

    maker_fee_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    taker_fee_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    max_price_deviation = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)

    opening_price_asset = db.Column(db.String(32), nullable=False,
                                    default=Currency.USD.name)
    opening_price = db.Column(db.MYSQL_DECIMAL_PRICE, nullable=False)

    started_at = db.Column(db.MYSQL_DATETIME_6)
    ended_at = db.Column(db.MYSQL_DATETIME_6)
    bidding_matching_started_at = db.Column(db.MYSQL_DATETIME_6)
    bidding_ended_at = db.Column(db.MYSQL_DATETIME_6)
    countdown_ended_at = db.Column(db.MYSQL_DATETIME_6)

    trading_disabled = db.Column(db.Boolean, nullable=False, default=False)
    api_spot_trading_disabled = db.Column(db.Boolean, nullable=False, default=False)
    offline_visible = db.Column(db.Boolean, nullable=False, default=False)  # 市场下架后，是否可见

    def __repr__(self):
        return f'<{type(self).__name__} {self.name!r} id={self.id}>'

    def _row_to_dict_hook_(self, dict_: dict):
        dict_.update(
            min_order_amount=self.min_order_amount
        )

    @classmethod
    def get_by_name(cls, name: str) -> Optional[Market]:
        return cls.query.filter(cls.name == name).first()

    @property
    def min_order_amount(self) -> Decimal:
        from ..assets import try_get_asset_config
        from ..business import PriceManager

        if (conf := try_get_asset_config(self.base_asset)) is None:
            return Decimal()

        if (amount := conf.min_order_amount) <= 0:
            if (price_usd := PriceManager.convert_price(
                    self.opening_price_asset, self.opening_price,
                    Currency.USD)) <= 0:
                return Decimal()
            conf.min_order_amount = amount \
                = PriceManager.price_to_min_order_amount(price_usd)
        return amount

    @classmethod
    def check_order_permission(cls,
                               tradig_disabled: bool,
                               market_mode: Mode,
                               market_status: Status,
                               user_type: User.UserType,
                               countdown_ended_at: datetime,
                               *,
                               is_api: bool = False):
        if tradig_disabled:
            return False
        if market_mode is cls.Mode.NORMAL:
            return market_status is cls.Status.ONLINE
        elif market_mode is cls.Mode.BIDDING:
            return market_status in {cls.Status.BIDDING, cls.Status.ONLINE}
        elif market_mode is cls.Mode.COUNTDOWN:
            if market_status is cls.Status.COUNTING_DOWN:
                return user_type is User.UserType.INTERNAL_MAKER
            elif market_status is cls.Status.ONLINE:
                if (now() - countdown_ended_at).total_seconds() < 1800:
                    return (user_type is User.UserType.INTERNAL_MAKER
                            or not is_api)
                return True
            return False
        return False

    @classmethod
    def check_precision(cls, base_asset_precision: int, quote_asset_precision: int) -> bool:
        return (base_asset_precision <= 8 and quote_asset_precision <= 12
                and base_asset_precision + quote_asset_precision <= 18)

    @classmethod
    def to_online_statuses(cls) -> set:
        return {cls.Status.BIDDING, cls.Status.COUNTING_DOWN}

    def market_online_time(self):
        match self.status:
            case self.Status.BIDDING:
                return self.bidding_ended_at
            case self.Status.COUNTING_DOWN:
                return self.countdown_ended_at
            case _:
                return self.started_at


class MarketOfflineContent(ModelBase):
    """ 市场下架可见-下架文案 """

    __table_args__ = (
        db.UniqueConstraint("market", "market_type", "lang", name="market_type_lang_unique"),
    )

    class MarketType(Enum):
        SPOT = '现货'
        PERPETUAL = '合约'

    market = db.Column(db.String(64), nullable=False)
    market_type = db.Column(db.StringEnum(MarketType), nullable=False)
    lang = db.Column(db.StringEnum(Language), nullable=False)
    content = db.Column(db.Text, nullable=False, default='')


class SystemAssetLiability(ModelBase):

    EXTRA_FIELDS = ("payment_hedging_amount", )

    asset = db.Column(db.String(32), nullable=False, index=True)

    # 平台净资产
    cold_wallet = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    hot_wallet = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    deposit_wallet = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    onchain_wallet = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=Decimal())

    staking_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False) # 链上质押数量
    staking_pending_reward = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False) # 链上质押待提取收益
    # 借贷未还
    margin_unflat = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    credit_unflat = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    pledge_unflat = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    pre_asset_issue = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    sys_total = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)

    # 平台负债
    spot = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    margin = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    pledge = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    investment = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    perpetual = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    perpetual_insurance = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    margin_insurance = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    sys_debt = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)

    except_deposit = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    kyt_frozen = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    processing_deposit = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    processing_withdrawal = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    processing_local_transfer = db.Column(db.MYSQL_DECIMAL_26_8,
                                          nullable=False)
    ungrabbed_red_packet = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)

    sys_income = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)

    extra_data = db.Column(db.MYSQL_JSON, nullable=False, default={})  # 额外字段，后续新增字段都存这里

    remark = db.Column(db.String(256), nullable=False, default='')

    @classmethod
    def build_extra_data_dict(cls, extra_data: dict) -> dict:
        res = {}
        extra_data = extra_data or {}  # 老数据是null，填充为0
        for field in cls.EXTRA_FIELDS:
            res[field] = Decimal(extra_data.get(field, 0))
        return res


class AssetAutoRewardHistory(ModelBase):
    """TODO: 已废弃, 待删除"""

    class Status(Enum):
        CREATED = "created"
        ADDED = "added"
        FINISHED = "finished"

    asset = db.Column(db.String(32), nullable=False)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False)


class WalletAssetRewardHistory(ModelBase):
    """
        钱包利息币收入/钱包质押收入
        每日结算，汇总至收支结算流程中,
        最后插入一条asset = '' 的数据表示任务执行完成
    """

    __table_args__ = (
        db.UniqueConstraint('report_date', 'asset',
                            name='report_date_asset_unq'),
    )

    PLACEHOLDER_ASSET = ''

    report_date = db.Column(db.Date, nullable=False, index=True)
    asset = db.Column(db.String(32), nullable=False)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)

    @classmethod
    def gen_finish_record(cls, report_date: date):
        return cls(
            report_date=report_date,
            asset=cls.PLACEHOLDER_ASSET,
            amount=Decimal()
        )


class LoanStatistic(ModelBase):
    """
    借贷统计
    用于存储杠杆借币和质押借币的统计数据
    """
    
    class LoanType(Enum):
        ALL = "ALL"           # 全部
        MARGIN = "杠杆"       # 杠杆借币
        PLEDGE = "借贷"       # 质押借币
    
    # 币种，ALL表示所有币种
    asset = db.Column(db.String(32), nullable=False, index=True)
    
    # 借贷类型
    loan_type = db.Column(db.StringEnum(LoanType), nullable=False)
    
    # 借币人数
    borrower_count = db.Column(db.Integer, nullable=False, default=0)
    
    # 借币市值(USD)
    borrowed_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    
    # 抵押市值(USD)
    collateral_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    
    # 有效抵押市值(USD) - 实际抵押市值 * 折价率
    effective_collateral_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=True)
    
    # 最近7天利息(USD)
    interest_7d_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    
    # 最近30天利息(USD)
    interest_30d_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    
    # 最近90天利息(USD)
    interest_90d_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    
    # 最近180天利息(USD)
    interest_180d_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    
    # 最近365天利息(USD)
    interest_365d_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    
    # 统计日期
    report_date = db.Column(db.Date, nullable=False, index=True)
    
    
    
    @classmethod
    def get_latest_statistics(cls, loan_type: str = None, asset: str = None) -> List['LoanStatistic']:
        """
        获取最新的统计数据
        """
        query = cls.query
        
        if loan_type:
            query = query.filter(cls.loan_type == loan_type)
        if asset:
            query = query.filter(cls.asset == asset)
            
        return query.order_by(cls.report_date.desc()).all()
    
    @classmethod
    def get_by_asset_and_type(cls, asset: str, loan_type: str, report_date: date) -> Optional['LoanStatistic']:
        """
        根据币种、类型和日期获取统计记录
        """
        return cls.query.filter(
            cls.asset == asset,
            cls.loan_type == loan_type,
            cls.report_date == report_date
        ).first()
    
    def get_interest_by_days(self, days: int) -> Decimal:
        """
        根据天数获取对应的利息
        """
        interest_map = {
            7: self.interest_7d_usd,
            30: self.interest_30d_usd,
            90: self.interest_90d_usd,
            180: self.interest_180d_usd,
            365: self.interest_365d_usd
        }
        return interest_map.get(days, Decimal())
    