from enum import Enum

from pyroaring import BitMap

from app.common import ReportPlatform
from app.models import ModelBase, db


class WeeklyLossOfTradeUsersReport(ModelBase):
    """交易、合约流失用户周报"""

    class Business(Enum):
        TRADE_LOSS = '交易流失'
        PERPETUAL_LOSS = '合约流失'

    class TimeRange(Enum):
        LAST_30_DAYS = '最近三十天'
        LAST_90_DAYS = '最近九十天'
        LAST_180_DAYS = '最近一百八十天'
        LAST_365_DAYS = '最近三百六十五天'

    __table_args__ = (
        db.UniqueConstraint('report_date', 'business', 'time_range', name='report_business_range'),
    )
    report_date = db.Column(db.Date, nullable=False)
    business = db.Column(db.StringEnum(Business), nullable=False)
    time_range = db.Column(db.StringEnum(TimeRange), nullable=False)
    loss_of_users = db.Column(db.MYSQL_MEDIUM_BLOB, nullable=False, default=b'')  # bitmap
    loss_of_users_count = db.Column(db.Integer, nullable=False, default=0, comment='交易流失总人数')
    trade_users_loss_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0, comment='交易流失率')
    num_of_returns = db.Column(db.Integer, nullable=False, default=0, comment='上周回流人数')
    loss_of_users_increase_count = db.Column(db.Integer, nullable=False, default=0, comment='本周交易新增流失人数')

    def get_loss_of_users_ids(self):
        if not self.loss_of_users:
            return []
        bm = BitMap.deserialize(self.loss_of_users)
        if not bm:
            return []
        return list(bm)

    @classmethod
    def bm_serialize(cls, values: set):
        bm = BitMap()
        bm.update(values)
        return bm.serialize()


class WeeklyPublicityShortLinkReport(ModelBase):
    __table_args__ = (
        db.UniqueConstraint('period', 'short_link_id', 'report_date',
                            name="period_short_link_id_report_date_unique"),
    )

    class Period(Enum):
        HISTORY = '历史周期'
        CURRENT = '当前周期'  # 即以周期内增量为基础计算

    report_date = db.Column(db.Date, nullable=False)
    period = db.Column(db.StringEnum(Period), nullable=False)
    short_link_id = db.Column(db.Integer, nullable=False, index=True)
    register_user_count = db.Column(db.Integer, nullable=False, default=0)
    active_user_count = db.Column(db.Integer, nullable=False, default=0)
    asset_user_count = db.Column(db.Integer, nullable=False, default=0)
    spot_user_count = db.Column(db.Integer, nullable=False, default=0)
    perpetual_user_count = db.Column(db.Integer, nullable=False, default=0)
    trade_user_count = db.Column(db.Integer, nullable=False, default=0)
    exchange_user_count = db.Column(db.Integer, nullable=False, default=0)
    deposit_user_count = db.Column(db.Integer, nullable=False, default=0)  # 链上充值用户
    trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    trade_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    retained_count_1d = db.Column(db.Integer, nullable=False, default=0)
    retained_count_7d = db.Column(db.Integer, nullable=False, default=0)
    retained_count_30d = db.Column(db.Integer, nullable=False, default=0)
    visit_count = db.Column(db.Integer, nullable=False, default=0)
    ip_count = db.Column(db.Integer, nullable=False, default=0)

    country = db.Column(db.String(32), nullable=False, comment='国家', index=True)
    language_area = db.Column(db.String(32), nullable=False, comment='语区', index=True)
    category_id = db.Column(db.Integer, nullable=False, comment='渠道平台', index=True)
    business_segment = db.Column(db.String(128), nullable=False, comment='业务板块', index=True)
    publicity_channel_id = db.Column(db.Integer, nullable=False, comment='细分渠道', index=True)


class WeeklyPublicityChannelReport(ModelBase):
    __table_args__ = (
        db.UniqueConstraint('period', 'publicity_channel_id', 'report_date',
                            name="period_publicity_channel_id_report_date_unique"),
    )

    class Period(Enum):
        HISTORY = '历史周期'
        CURRENT = '当前周期'  # 即以周期内增量为基础计算

    report_date = db.Column(db.Date, nullable=False)
    period = db.Column(db.StringEnum(Period), nullable=False)
    publicity_channel_id = db.Column(db.Integer, nullable=False)
    register_user_count = db.Column(db.Integer, nullable=False, default=0)
    active_user_count = db.Column(db.Integer, nullable=False, default=0)
    asset_user_count = db.Column(db.Integer, nullable=False, default=0)
    spot_user_count = db.Column(db.Integer, nullable=False, default=0)
    perpetual_user_count = db.Column(db.Integer, nullable=False, default=0)
    trade_user_count = db.Column(db.Integer, nullable=False, default=0)
    exchange_user_count = db.Column(db.Integer, nullable=False, default=0)
    deposit_user_count = db.Column(db.Integer, nullable=False, default=0)   # 链上充值用户
    trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    trade_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    retained_count_1d = db.Column(db.Integer, nullable=False, default=0)
    retained_count_7d = db.Column(db.Integer, nullable=False, default=0)
    retained_count_30d = db.Column(db.Integer, nullable=False, default=0)
    visit_count = db.Column(db.Integer, nullable=False, default=0)
    ip_count = db.Column(db.Integer, nullable=False, default=0)


class WeeklyAppPublicityChannelReport(ModelBase):

    class Period(Enum):
        HISTORY = '历史周期'
        CURRENT = '当前周期'  # 即以周期内增量为基础计算

    report_date = db.Column(db.Date, nullable=False, index=True)
    period = db.Column(db.StringEnum(Period), nullable=False, index=True)
    channel = db.Column(db.String(256), nullable=True, index=True, comment="af渠道 + 其他")
    country_code = db.Column(db.String(3), index=True, comment="国家")
    language = db.Column(db.String(32), nullable=False, index=True, comment="语言")
    register_user_count = db.Column(db.Integer, nullable=False, default=0)
    active_user_count = db.Column(db.Integer, nullable=False, default=0)
    asset_user_count = db.Column(db.Integer, nullable=False, default=0)
    spot_user_count = db.Column(db.Integer, nullable=False, default=0)
    perpetual_user_count = db.Column(db.Integer, nullable=False, default=0)
    trade_user_count = db.Column(db.Integer, nullable=False, default=0)
    exchange_user_count = db.Column(db.Integer, nullable=False, default=0)
    deposit_user_count = db.Column(db.Integer, nullable=False, default=0)   # 链上充值用户
    trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    trade_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    retained_count_1d = db.Column(db.Integer, nullable=False, default=0)
    retained_count_7d = db.Column(db.Integer, nullable=False, default=0)
    retained_count_30d = db.Column(db.Integer, nullable=False, default=0)
    platform = db.Column(db.StringEnum(ReportPlatform), nullable=False, comment="报告平台", index=True)



class WeeklyPublicityCountryReport(ModelBase):
    __table_args__ = (
        db.UniqueConstraint('period', 'country', 'report_date',
                            name="period_country_report_date_unique"),
    )

    class Period(Enum):
        HISTORY = '历史周期'
        CURRENT = '当前周期'  # 即以周期内增量为基础计算

    report_date = db.Column(db.Date, nullable=False)
    period = db.Column(db.StringEnum(Period), nullable=False)
    country = db.Column(db.String(32), nullable=False)
    register_user_count = db.Column(db.Integer, nullable=False, default=0)
    active_user_count = db.Column(db.Integer, nullable=False, default=0)
    asset_user_count = db.Column(db.Integer, nullable=False, default=0)
    spot_user_count = db.Column(db.Integer, nullable=False, default=0)
    perpetual_user_count = db.Column(db.Integer, nullable=False, default=0)
    trade_user_count = db.Column(db.Integer, nullable=False, default=0)
    exchange_user_count = db.Column(db.Integer, nullable=False, default=0)
    deposit_user_count = db.Column(db.Integer, nullable=False, default=0)
    trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    trade_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    retained_count_1d = db.Column(db.Integer, nullable=False, default=0)
    retained_count_7d = db.Column(db.Integer, nullable=False, default=0)
    retained_count_30d = db.Column(db.Integer, nullable=False, default=0)
    visit_count = db.Column(db.Integer, nullable=False, default=0)
    ip_count = db.Column(db.Integer, nullable=False, default=0)


class WeeklyPublicityLanguageAreaReport(ModelBase):
    __table_args__ = (
        db.UniqueConstraint('period', 'language_area', 'report_date',
                            name="period_language_area_report_date_unique"),
    )

    class Period(Enum):
        HISTORY = '历史周期'
        CURRENT = '当前周期'  # 即以周期内增量为基础计算

    report_date = db.Column(db.Date, nullable=False)
    period = db.Column(db.StringEnum(Period), nullable=False)
    language_area = db.Column(db.String(32), nullable=False)
    register_user_count = db.Column(db.Integer, nullable=False, default=0)
    active_user_count = db.Column(db.Integer, nullable=False, default=0)
    asset_user_count = db.Column(db.Integer, nullable=False, default=0)
    spot_user_count = db.Column(db.Integer, nullable=False, default=0)
    perpetual_user_count = db.Column(db.Integer, nullable=False, default=0)
    trade_user_count = db.Column(db.Integer, nullable=False, default=0)
    exchange_user_count = db.Column(db.Integer, nullable=False, default=0)
    deposit_user_count = db.Column(db.Integer, nullable=False, default=0)
    trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    trade_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    retained_count_1d = db.Column(db.Integer, nullable=False, default=0)
    retained_count_7d = db.Column(db.Integer, nullable=False, default=0)
    retained_count_30d = db.Column(db.Integer, nullable=False, default=0)
    visit_count = db.Column(db.Integer, nullable=False, default=0)
    ip_count = db.Column(db.Integer, nullable=False, default=0)


class WeeklyPublicityBusinessSegmentReport(ModelBase):
    __table_args__ = (
        db.UniqueConstraint('period', 'business_segment', 'report_date',
                            name="period_business_segment_report_date_unique"),
    )

    class Period(Enum):
        HISTORY = '历史周期'
        CURRENT = '当前周期'  # 即以周期内增量为基础计算

    report_date = db.Column(db.Date, nullable=False)
    period = db.Column(db.StringEnum(Period), nullable=False)
    business_segment = db.Column(db.String(128), nullable=False)
    register_user_count = db.Column(db.Integer, nullable=False, default=0)
    active_user_count = db.Column(db.Integer, nullable=False, default=0)
    asset_user_count = db.Column(db.Integer, nullable=False, default=0)
    spot_user_count = db.Column(db.Integer, nullable=False, default=0)
    perpetual_user_count = db.Column(db.Integer, nullable=False, default=0)
    trade_user_count = db.Column(db.Integer, nullable=False, default=0)
    exchange_user_count = db.Column(db.Integer, nullable=False, default=0)
    deposit_user_count = db.Column(db.Integer, nullable=False, default=0)
    trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    trade_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    retained_count_1d = db.Column(db.Integer, nullable=False, default=0)
    retained_count_7d = db.Column(db.Integer, nullable=False, default=0)
    retained_count_30d = db.Column(db.Integer, nullable=False, default=0)
    visit_count = db.Column(db.Integer, nullable=False, default=0)
    ip_count = db.Column(db.Integer, nullable=False, default=0)


class WeeklyPublicityCategoryReport(ModelBase):
    __table_args__ = (
        db.UniqueConstraint('period', 'publicity_category_id', 'report_date',
                            name="period_publicity_category_id_report_date_unique"),
    )

    class Period(Enum):
        HISTORY = '历史周期'
        CURRENT = '当前周期'  # 即以周期内增量为基础计算

    report_date = db.Column(db.Date, nullable=False)
    period = db.Column(db.StringEnum(Period), nullable=False)
    publicity_category_id = db.Column(db.Integer, nullable=False)
    register_user_count = db.Column(db.Integer, nullable=False, default=0)
    active_user_count = db.Column(db.Integer, nullable=False, default=0)
    asset_user_count = db.Column(db.Integer, nullable=False, default=0)
    spot_user_count = db.Column(db.Integer, nullable=False, default=0)
    perpetual_user_count = db.Column(db.Integer, nullable=False, default=0)
    trade_user_count = db.Column(db.Integer, nullable=False, default=0)
    exchange_user_count = db.Column(db.Integer, nullable=False, default=0)
    deposit_user_count = db.Column(db.Integer, nullable=False, default=0)
    trade_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    trade_fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    retained_count_1d = db.Column(db.Integer, nullable=False, default=0)
    retained_count_7d = db.Column(db.Integer, nullable=False, default=0)
    retained_count_30d = db.Column(db.Integer, nullable=False, default=0)
    visit_count = db.Column(db.Integer, nullable=False, default=0)
    ip_count = db.Column(db.Integer, nullable=False, default=0)


class WeeklyP2pTradeReport(ModelBase):
    """P2P交易周报"""
    class KeyType(Enum):
        ALL = 'all'
        ASSET = 'asset'
        FIAT = 'fiat'

    report_date = db.Column(db.Date, nullable=False, index=True)
    key_type = db.Column(db.StringEnum(KeyType), nullable=False)
    key = db.Column(db.String(64), index=True)  # ALL 为 null  ASSET 为 具体的币种  FIAT 为 具体法币
    put_user_count = db.Column(db.Integer, nullable=False, default=0, comment='下单人数')
    deal_user_count = db.Column(db.Integer, nullable=False, default=0, comment='成交人数')
    new_user_count = db.Column(db.Integer, nullable=False, default=0, comment='下单新用户数')
    active_merchant_count = db.Column(db.Integer, nullable=False, default=0, comment='活跃商家数')
    deal_merchant_count = db.Column(db.Integer, nullable=False, default=0, comment='成交商家数')
    put_order_count = db.Column(db.Integer, nullable=False, default=0, comment='下单笔数')
    accept_order_count = db.Column(db.Integer, nullable=False, default=0, comment='接单笔数')
    deal_order_count = db.Column(db.Integer, nullable=False, default=0, comment='成交笔数')
    appeal_order_count = db.Column(db.Integer, nullable=False, default=0, comment='申诉笔数')
    deal_volume_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0, comment='成交市值USD')
    deal_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0, comment='成交数量')


class WeeklyMerchantReport(ModelBase):
    """p2p商家周报"""
    report_date = db.Column(db.Date, nullable=False, unique=True)
    merchant_count = db.Column(db.Integer, nullable=False, default=0, comment='商家总数量')
    new_merchant_count = db.Column(db.Integer, nullable=False, default=0, comment='新增商家数量')
    active_merchant_count = db.Column(db.Integer, nullable=False, default=0, comment='活跃商家数量')
    deal_merchant_count = db.Column(db.Integer, nullable=False, default=0, comment='成交商家数量')
    fiat_amount = db.Column(db.Integer, nullable=False, default=0, comment='法币数量')
    adv_count = db.Column(db.Integer, nullable=False, default=0, comment='广告数量')
    pay_channel_count = db.Column(db.Integer, nullable=False, default=0, comment='支付方式数量')


class WeeklyUserReport(ModelBase):
    report_date = db.Column(db.Date, nullable=False, unique=True)

    total_user = db.Column(db.Integer, nullable=False, default=0)

    user_bind_email = db.Column(db.Integer, nullable=False, default=0)
    user_email_verified = db.Column(db.Integer, nullable=False, default=0)
    user_bind_mobile = db.Column(db.Integer, nullable=False, default=0)
    user_bind_totp = db.Column(db.Integer, nullable=False, default=0)
    user_bind_2fa = db.Column(db.Integer, nullable=False, default=0)
    user_trade_password = db.Column(db.Integer, nullable=False, default=0)

    increase_user = db.Column(db.Integer, nullable=False, default=0)
    increase_user_bind_2fa = db.Column(db.Integer, nullable=False, default=0)
    increase_sub_user = db.Column(db.Integer, nullable=False, default=0)
    increase_cet_user = db.Column(db.Integer, nullable=False, default=0)

    total_refer_user = db.Column(db.Integer, nullable=False, default=0)
    increase_refer_user = db.Column(db.Integer, nullable=False, default=0)
    increase_trade_user = db.Column(db.Integer, nullable=False, default=0)

    sign_in_user = db.Column(db.Integer, nullable=False, default=0)
    deposit_user = db.Column(db.Integer, nullable=False, default=0)
    withdraw_user = db.Column(db.Integer, nullable=False, default=0)
    local_transfer_user = db.Column(db.Integer, nullable=False, default=0)
    trade_user = db.Column(db.Integer, nullable=False, default=0)
    active_user = db.Column(db.Integer, nullable=False, default=0)

    active_trade_user = db.Column(db.Integer, nullable=False, default=0)
    active_spot_user = db.Column(db.Integer, nullable=False, default=0)
    active_margin_user = db.Column(db.Integer, nullable=False, default=0)
    active_option_user = db.Column(db.Integer, nullable=False, default=0)
    active_future_user = db.Column(db.Integer, nullable=False, default=0)
    active_perpetual_user = db.Column(db.Integer, nullable=False, default=0)
    asset_user = db.Column(db.Integer, nullable=False, default=0)

    total_kyc = db.Column(db.Integer, nullable=False, default=0)
    increase_kyc = db.Column(db.Integer, nullable=False, default=0)
    amm_user = db.Column(db.Integer, nullable=False, default=0)
    exchange_user = db.Column(db.Integer, nullable=False, default=0)  # 兑换用户数
    api_user = db.Column(db.Integer, nullable=False, default=0)  # api成交用户数
    # 环比值
    total_user_ring_ratio = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    deposit_user_ring_ratio = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    active_user_ring_ratio = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    active_trade_user_ring_ratio = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)


class WeeklyBusinessAmbassadorTotalReferralReport(ModelBase):
    """商务大使周报"""
    report_date = db.Column(db.Date, nullable=False)

    bus_user_count = db.Column(db.Integer, nullable=False, default=0)  # 商务数量
    bus_amb_count = db.Column(db.Integer, nullable=False, default=0)  # 商务大使数量
    new_bus_amb_count = db.Column(db.Integer, nullable=False, default=0)  # 新增商务大使数量
    refer_count = db.Column(db.Integer, nullable=False, default=0)  # 大使邀请总用户数
    new_refer_count = db.Column(db.Integer, nullable=False, default=0)  # 大使邀请新用户数
    deal_count = db.Column(db.Integer, nullable=False, default=0)  # 大使邀请交易用户数
    new_deal_count = db.Column(db.Integer, nullable=False, default=0)  # 大使邀请新增交易用户数

    deal_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 交易总金额
    deal_spot_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 现货交易额
    deal_perpetual_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 合约交易额
    fee_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 手续费
    fee_spot_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 现货手续费
    fee_perpetual_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 合约手续费

    refer_amb_count = db.Column(db.Integer, nullable=False, default=0)  # 收到返佣大使数
    refer_amb_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 大使返佣金额
    refer_agent_count = db.Column(db.Integer, nullable=False, default=0)  # 收到返佣代理数
    refer_agent_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 代理返佣金额
    average_refer_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # 大使平均返佣比例 = 大使返佣金额 / 大使邀请用户手续费

    refer_bus_count = db.Column(db.Integer, nullable=False, default=0)  # 收到返佣商务
    refer_bus_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 商务返佣金额

    invitee_percent = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # refer注册人数占比
    trade_percent = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # refer交易人数占比
    new_trade_percent = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)  # 新增refer交易人数占比
    refer_user_balance_usd = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)  # refer总人数的总资产
    refer_active_user_count = db.Column(db.Integer, nullable=False, default=0)  # refer总人数里在该周期内有活跃的用户
    team_id = db.Column(db.Integer, nullable=False, index=True, default=0)    # 团队ID(统计全部时设为0)
