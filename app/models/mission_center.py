from datetime import datetime
from enum import Enum
from functools import cached_property
import json
from typing import Set

from dateutil.tz import UTC
from flask_babel import gettext as _
from pyroaring import BitMap

from app.models import ModelBase, db
from app.common.constants import BusinessParty


class SceneType(Enum):
    NEWBIE = "新手任务"
    ROUTINE = "老用户任务"


class LogicTemplate(Enum):
    # key + op

    # NEWBIE
    CHANNEL_ID_EQ = 'channel_id_eq'
    REFERER_ID_IN = 'referer_id_in'
    REGISTRATION_AREA_IN = 'registration_area_in'
    REGISTERED_AT_GE = 'registered_at_ge'


class MissionScene(ModelBase):
    """(自定义)任务场景"""
    name = db.Column(db.String(64), nullable=False)
    created_by = db.Column(db.Integer, nullable=False)
    remark = db.Column(db.String(128))
    scene_type = db.Column(db.StringEnum(SceneType), nullable=False, default=SceneType.ROUTINE)
    

class MissionPlan(ModelBase):
    """任务计划"""

    class Status(Enum):
        DRAFT = "待提交"  # 待提交审核
        PENDING = "待审核"  # 待审核
        PASSED = "待生效"  # 已审核
        REJECTED = "审核未通过"  # 被拒绝
        EFFECTIVE = "已生效"  # 生效中
        STOPPED = "已停止"  # 已停止
        FINISHED = "已结束"  # 已结束
        DELETED = "已删除"  # 已删除

    class StatisticsStatus(Enum):
        CREATED = "created"  # 已创建
        PROCESSING = "processing"  # 处理中
        COMPLETED = "completed"  # 已完成

    # 计划名称
    name = db.Column(db.String(128), nullable=False)
    # 场景类型
    scene_type = db.Column(db.StringEnum(SceneType), nullable=False)
    # 场景ID
    scene_id = db.Column(db.Integer, nullable=True)
    # 业务方
    business_party = db.Column(
        db.StringEnum(BusinessParty), nullable=False, default=BusinessParty.OTHERS
    )
    # 计划开始时间
    start_at = db.Column(db.MYSQL_DATETIME_6, nullable=False, index=True)
    finished_at = db.Column(db.MYSQL_DATETIME_6)
    # 计划结束时间
    end_at = db.Column(db.MYSQL_DATETIME_6)
    # 状态
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.DRAFT)
    # 推送总数量
    total = db.Column(db.Integer, nullable=False, default=0)
    # 优先级 (推送)
    priority = db.Column(db.Integer, nullable=False, default=0)
    # 推送内容 {lang: {title: "", content: ""}}
    deliver_content = db.Column(db.JSON)
    # 拒绝原因
    rejected_reason = db.Column(db.TEXT)
    # 创建人
    created_by = db.Column(db.Integer, nullable=False)
    # 审核人员
    auditor_by = db.Column(db.Integer)
    # 统计状态
    statistics_status = db.Column(db.StringEnum(StatisticsStatus), nullable=False, default=StatisticsStatus.CREATED)


class MissionPlanUserGroup(ModelBase):
    ONCE_MAX_DISTRIBUTE_COUNT = 100000
    
    class GroupType(Enum):
        USER_TAG = "user_tag"
        LOGIC = "logic"

    plan_id = db.Column(db.Integer, nullable=False, index=True)
    # 场景类型
    scene_type = db.Column(db.StringEnum(SceneType), nullable=False)
    # 分组类型
    group_type = db.Column(db.StringEnum(GroupType), nullable=False, default=GroupType.USER_TAG)
    # {"CHANNEL_ID": ""}
    logic_params = db.Column(db.JSON)
    logic_template = db.Column(db.StringEnum(LogicTemplate), nullable=True)
    # group_ids 用户画像id [1, 2, 3]
    group_ids = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False, default='')
    # 白名单是否开启
    whitelist_enabled = db.Column(db.Boolean, nullable=False, default=False)
    # 白名单用户ID
    whitelist_user_ids = db.Column(db.MYSQL_MEDIUM_BLOB, nullable=False, default=b'')
    # 已选择用户ID
    selected_users = db.Column(db.MYSQL_MEDIUM_BLOB, nullable=False, default=b'')
    # 已分发用户ID
    distributed_users = db.Column(db.MYSQL_MEDIUM_BLOB, nullable=False, default=b'')
    # 发放失败用户, 需要保存当时失败的原因和信息 {"user_id": "1", "fail_data": {"reason": "1", "device_id": "1"}} 
    failed_users = db.Column(db.JSON)
    registered_at = db.Column(db.MYSQL_DATETIME_6)
    
    @property
    def is_user_tag(self):
        """检查是否是用户画像分组（非逻辑分组）"""
        return self.scene_type == SceneType.ROUTINE and self.group_type == self.GroupType.USER_TAG

    @property
    def check_logic_params(self):
        logic_params = self.logic_params
        if self.registered_at:
            logic_params[LogicTemplate.REGISTERED_AT_GE.name] = self.registered_at
        return logic_params

    def update_selected_users(self):
        from app.business.push_statistic import MissionPlanUserGroupUserParser
        if not self.is_user_tag:
            return
        users, push_users = MissionPlanUserGroupUserParser(self).parse()
        updated_user_ids = self.set_selected_users(users)
        return updated_user_ids
    
    def get_new_selected_users(self):
        """获取新增用户ID"""
        current_selected_users = self.cached_selected_users
        distributed_users = self.get_distributed_users()
        return set(current_selected_users) - set(distributed_users)
        
    def set_selected_users(self, user_ids: list):
        from app.business.user import UserRepository

        from app.caches.user import SubMainUserCache
        sub_main_mapper = SubMainUserCache.get_sub_main_mapper()
        if not self.is_user_tag:
            return
        risk_users = UserRepository.get_abnormal_users()
        # 移除风控用户和子账号
        selected_user_ids = set(user_ids) - risk_users - set(sub_main_mapper.keys())
        self.selected_users = BitMap(selected_user_ids).serialize()
        return selected_user_ids
        
    @cached_property
    def cached_selected_users(self):
        """缓存已选择用户ID 主要用于admin展示 不参与分发"""
        if not self.selected_users:
            return []
        return BitMap.deserialize(self.selected_users)
    
    def get_distributed_users(self):
        if not self.distributed_users:
            return []
        return list(BitMap.deserialize(self.distributed_users))

    def get_groups(self):
        if not self.group_ids:
            return []
        return json.loads(self.group_ids)
    
    @cached_property
    def cached_whitelist_user_ids(self):
        """缓存白名单用户ID, 用户分发时需要排除"""
        if not self.whitelist_user_ids:
            return []
        return list(BitMap.deserialize(self.whitelist_user_ids))
    
    def update_distributed_users(self, user_ids: Set[int]):
        if not self.is_user_tag:
            return
        current_distributed_users = self.get_distributed_users()
        new_distributed_users = set(user_ids) | set(current_distributed_users)
        self.distributed_users = BitMap(list(new_distributed_users)).serialize()
        return new_distributed_users

    def get_next_distribute_users(self) -> Set[int]:
        selected_user_ids = self.update_selected_users()
        if not selected_user_ids:
            return set()
        distributed_users = self.get_distributed_users()
        next_distributed_users = set(selected_user_ids) - set(distributed_users) - set(self.cached_whitelist_user_ids)
        return set(list(next_distributed_users)[:self.ONCE_MAX_DISTRIBUTE_COUNT])
    
    def update_failed_users(self, add_failed_users: list):
        if not self.failed_users:
            self.failed_users = add_failed_users
        else:
            self.failed_users.extend(add_failed_users)
        return self.failed_users

    def get_failed_users_mapper(self):
        if not self.failed_users:
            return {}
        return {i['user_id']: i for i in self.failed_users}


class MissionCondition(Enum):
    DEPOSIT_AMOUNT = _("入金")
    SPOT_AMOUNT = _("币币交易")
    PERPETUAL_AMOUNT = _("合约交易")
    COPY_TRADING_ONCE = _("跟单交易")
    DEMO_TRADING_ONCE = _("模拟交易")
    # todo 测试使用
    INVITE_NEWBIE_DEPOSIT_COUNT = "裂变任务占位1（请忽略）"
    INVITE_NEWBIE_TRADED_AMOUNT = "裂变任务占位2（请忽略）"
    INVITE_NEWBIE_TRADED_COUNT = "裂变任务占位3（请忽略）"


class Mission(ModelBase):
    """任务"""

    # 计划ID（冗余）
    plan_id = db.Column(db.Integer, nullable=False, index=True)
    sequence = db.Column(db.Integer, nullable=False)
    # 任务期限(日)
    deadline_days = db.Column(db.Integer, nullable=False)
    # 权益ID
    equity_id = db.Column(db.Integer, index=True)
    mission_condition = db.Column(db.StringEnum(MissionCondition), nullable=False)
    # {SPOT_AMOUNT: "100", "asset": "USDT"}
    logic_params = db.Column(db.JSON, nullable=False)


class UserMission(ModelBase):
    """用户任务"""

    __table_args__ = (
        db.UniqueConstraint('mission_id', 'user_id', name='uq_user_mission_idx'),
        db.Index('ix_plan_status_user_idx', 'plan_id', 'status', 'user_id')
    )

    MIN_UTC_DATETIME = datetime.min.replace(tzinfo=UTC)
    MAX_UTC_DATETIME = datetime.max.replace(tzinfo=UTC)

    class Status(Enum):
        PENDING = '进行中'
        FINISHED = '已完成'
        SETTLING = '结算中'
        EXPIRED = '已过期'
        FAILED = '已失败'

    class FailReason(Enum):
        RISK_USER = '用户异常'  # 风控用户
        SETTLING_ERROR = "结算异常"

    id = db.Column(db.BigInteger, primary_key=True)
    mission_id = db.Column(db.Integer, nullable=False, index=True)
    user_id = db.Column(db.Integer, nullable=False, index=True)
    plan_id = db.Column(db.Integer, nullable=False, index=True)
    scene_type = db.Column(db.StringEnum(SceneType), nullable=False)
    # 冗余字段
    mission_condition = db.Column(db.StringEnum(MissionCondition), nullable=False)
    progress = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    # 最后一次更新时间
    last_updated_at = db.Column(db.MYSQL_DATETIME_6)
    completed_at = db.Column(db.MYSQL_DATETIME_6)
    # 实际生效时间(任务被监控的时间)
    used_at = db.Column(db.MYSQL_DATETIME_6, index=True, default=MIN_UTC_DATETIME)
    expired_at = db.Column(db.MYSQL_DATETIME_6, nullable=False, index=True, default=MAX_UTC_DATETIME)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.PENDING)
    fail_reason = db.Column(db.StringEnum(FailReason))
    event_data = db.Column(db.JSON)


class DailyMissionStatistics(ModelBase):
    __table_args__ = (
        db.UniqueConstraint('report_date', 'plan_id', 'mission_id', name='uq_date_plan_mission_idx'),
    )
    report_date = db.Column(db.Date, nullable=False, index=True)
    plan_id = db.Column(db.Integer, nullable=False, index=True)
    mission_id = db.Column(db.Integer, nullable=False)
    # 推送用户数
    delivery_count = db.Column(db.Integer, nullable=False, default=0)
    # 任务完成人数
    completion_count = db.Column(db.Integer, nullable=False, default=0)
    # 奖励完成任务
    finished_count = db.Column(db.Integer, nullable=False, default=0)
    # 应发奖励价值
    real_reward_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
    # 应发奖励价值
    reward_amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False, default=0)
