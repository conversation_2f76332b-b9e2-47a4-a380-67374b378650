# -*- coding: utf-8 -*-
import datetime
from decimal import Decimal
from enum import Enum, IntEnum
from typing import List

from pydantic import BaseModel, AwareDatetime

from .base import db, ModelBase
from ..common import Language, BalanceBusiness
from ..utils import now


class SiteSetting(ModelBase):

    class Status(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    key = db.Column(db.String(64), index=True, nullable=False)
    value = db.Column(db.String(2048), nullable=False)

    status = db.Column(db.Enum(Status), nullable=False, index=True,
                       default=Status.VALID)


class CountrySetting(ModelBase):

    class Status(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    code = db.Column(db.String(3), index=True, nullable=False)
    key = db.Column(db.String(64), index=True, nullable=False)
    value = db.Column(db.String(2048), nullable=False)

    status = db.Column(db.Enum(Status), nullable=False, index=True,
                       default=Status.VALID)

    __table_args__ = (
        db.UniqueConstraint('code', 'key', name='code_key_uniq'),
    )


class LocalAreasBlock(ModelBase):
    AVAILABLE_LANGS = (Language.EN_US, Language.ZH_HANS_CN, Language.ZH_HANT_HK)

    __table_args__ = (
        db.UniqueConstraint('country_code', 'state_code', 'type',
                            name='country_code_state_code_type_uniq'),
    )

    class Status(Enum):
        VALID = '生效中'
        DELETED = '已失效'

    class Type(Enum):
        NO_ACCESS = '禁止访问'
        REMINDED_ONLY = '仅提醒'
        WITHDRAWAL_ONLY = '仅提现'

    country_code = db.Column(db.String(2), index=True, nullable=False)  # iso2_code
    state_code = db.Column(db.String(10), nullable=False)  # 行政区code
    remark = db.Column(db.String(256), default='')
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID)
    type = db.Column(db.StringEnum(Type), nullable=False, default=Type.NO_ACCESS)


class LocalAreasBlockContent(ModelBase):
    local_areas_block_id = db.Column(db.Integer, db.ForeignKey('local_areas_block.id'),
                           nullable=False)
    lang = db.Column(db.StringEnum(Language), nullable=False)
    title = db.Column(db.String(256), nullable=False, default='')


class BusinessSetting(ModelBase):
    """业务相关配置"""
    class Status(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    key = db.Column(db.String(64), index=True, nullable=False)
    value = db.Column(db.String(2048), nullable=False)

    status = db.Column(db.Enum(Status), nullable=False, index=True,
                       default=Status.VALID)


class BalanceUpdateBusiness(ModelBase):

    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    asset = db.Column(db.String(32), nullable=False)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, default=0)

    @classmethod
    def new_id(cls, user_id: int, asset: str, amount: Decimal):
        obj = cls(
            user_id=user_id,
            asset=asset,
            amount=amount
        )
        db.session.add(obj)
        db.session.commit()
        return obj.id


class RiskControlSetting(ModelBase):
    """风控参数配置"""
    class Status(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    key = db.Column(db.String(64), index=True, nullable=False)
    value = db.Column(db.String(2048), nullable=False)

    status = db.Column(db.Enum(Status), nullable=False, index=True,
                       default=Status.VALID)


class WithdrawalFuseSetting(ModelBase):
    class Status(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    key = db.Column(db.String(128), nullable=False)
    value = db.Column(db.String(2048), nullable=False)
    valid_from = db.Column(db.MYSQL_DATETIME_6)
    valid_till = db.Column(db.MYSQL_DATETIME_6)

    status = db.Column(db.Enum(Status), nullable=False, default=Status.VALID)


class DepositFuseSetting(ModelBase):
    class Status(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    key = db.Column(db.String(128), nullable=False)
    value = db.Column(db.String(2048), nullable=False)
    valid_from = db.Column(db.MYSQL_DATETIME_6)
    valid_till = db.Column(db.MYSQL_DATETIME_6)

    status = db.Column(db.Enum(Status), nullable=False, default=Status.VALID)


class AppEntranceList(ModelBase):

    # 已废弃，使用新版本的快捷入口
    """
    APP 1.9.2
    添加QuickEntranceList, 兼容之前版本
    """

    class Status(Enum):
        OPEN = '开放'
        CLOSE = '关闭'

    api_name = db.Column(db.String(32), nullable=False)
    api_field = db.Column(db.String(32), nullable=False, unique=True)
    sort_id = db.Column(db.SmallInteger, nullable=False, index=True)
    status = db.Column(db.Enum(Status), default=Status.OPEN)


class AppJumpList(ModelBase):
    class JumpType(Enum):
        NATIVE = '原生'
        URL = 'URL'

    """
    APP 跳转管理
    """
    remark = db.Column(db.String(128), nullable=False, default='')
    jump_type = db.Column(db.Enum(JumpType), nullable=False)
    jump_data = db.Column(db.String(256), nullable=False)


class AppQuickEntranceList(ModelBase):

    AVAILABLE_LANGS = (Language.EN_US, Language.ZH_HANS_CN,
                       Language.ZH_HANT_HK, Language.JA_JP,
                       Language.KO_KP, Language.RU_KZ, Language.ES_ES,
                       Language.ID_ID, Language.FA_IR, Language.TR_TR,
                       Language.VI_VN, Language.AR_AE, Language.FR_FR,
                       Language.DE_DE, Language.PT_PT, Language.TH_TH,
                       Language.PL_PL, Language.IT_IT
                       )

    class JumpType(Enum):
        NATIVE = '原生'
        URL = 'URL'

    class Status(Enum):
        OPEN = '开放'
        CLOSE = '关闭'

    class EntranceType(Enum):
        FREQUENTLY = "常用功能"
        DEAL = '交易'
        FINANCE = '理财'
        ACTIVATE = '活动'
        OTHER = '其他'
        PLATFORM = "平台特色"

        ASSET = '资产'
        SUPPORT = '支持'
        SERVICE = '服务'

    class Version(IntEnum):
        VERSION_1 = 1
        VERSION_2 = 2
        VERSION_3 = 3

    @classmethod
    def get_support_entrance_type(cls, version: Version) -> List[EntranceType]:
        match version:
            case cls.Version.VERSION_3:
                return [
                    cls.EntranceType.FREQUENTLY,
                    cls.EntranceType.DEAL,
                    cls.EntranceType.ASSET,
                    cls.EntranceType.FINANCE,
                    cls.EntranceType.ACTIVATE,
                    cls.EntranceType.SERVICE,
                    cls.EntranceType.SUPPORT,
                    cls.EntranceType.OTHER,
                ]
            case cls.Version.VERSION_2:
                return [
                    cls.EntranceType.FREQUENTLY,
                    cls.EntranceType.DEAL,
                    cls.EntranceType.FINANCE,
                    cls.EntranceType.ACTIVATE,
                    cls.EntranceType.OTHER,
                    cls.EntranceType.PLATFORM
                ]
            case _:
                return list(cls.EntranceType)

    class Platform(Enum):
        ANDROID = '安卓'
        IOS = "iOS"
        ANDROID_AND_IOS = '安卓+iOS'

    display_name = db.Column(db.String(64), nullable=False)
    api_field = db.Column(db.String(32), nullable=False, unique=True)
    jump_type = db.Column(db.Enum(JumpType), nullable=False)
    jump_id = db.Column(db.Integer, db.ForeignKey('app_jump_list.id'))
    sort_id = db.Column(db.SmallInteger, nullable=False, index=True)
    light_pic_id = db.Column(db.Integer, db.ForeignKey('file.id'), nullable=False)
    dark_pic_id = db.Column(db.Integer, db.ForeignKey('file.id'), nullable=False)
    platform = db.Column(db.StringEnum(Platform), nullable=False)

    status = db.Column(db.Enum(Status), default=Status.OPEN, nullable=False)
    # 旧版版本号为1，目前新版版本号为2
    version = db.Column(db.Integer, default=2, nullable=False)
    entrance_type = db.Column(db.StringEnum(EntranceType), default=EntranceType.ACTIVATE, nullable=False)
    is_default = db.Column(db.Boolean, nullable=False, default=False)
    is_first = db.Column(db.Boolean, default=False)

    @property
    def platform_list(self) -> List[str]:
        if self.platform == self.Platform.ANDROID:
            return ["ANDROID"]
        elif self.platform == self.Platform.IOS:
            return ["IOS"]
        elif self.platform == self.Platform.ANDROID_AND_IOS:
            return ["ANDROID", "IOS"]
        else:
            return []


class AppQuickEntranceTrans(ModelBase):

    quick_entrance_id = db.Column(db.Integer, db.ForeignKey('app_quick_entrance_list.id'), nullable=False)
    lang = db.Column(db.StringEnum(Language), nullable=False)
    name = db.Column(db.String(32), nullable=False)


class AppActivate(ModelBase):

    AVAILABLE_LANGS = (Language.EN_US, Language.ZH_HANS_CN,
                       Language.ZH_HANT_HK, Language.JA_JP,
                       Language.KO_KP, Language.RU_KZ, Language.ES_ES,
                       Language.ID_ID, Language.FA_IR, Language.TR_TR,
                       Language.VI_VN, Language.AR_AE, Language.FR_FR,
                       Language.DE_DE, Language.PT_PT, Language.TH_TH,
                       Language.PL_PL, Language.IT_IT
                       )

    class ActivateType(Enum):
        publicity = "活动宣传"
        introduction = "功能介绍"

    class Status(Enum):
        VALID = 'valid'
        DELETE = 'delete'

    class Platform(Enum):
        ANDROID = '安卓'
        IOS = "iOS"
        ANDROID_AND_IOS = '安卓+iOS'

    display_name = db.Column(db.Text, nullable=False)
    jump_type = db.Column(db.StringEnum(AppJumpList.JumpType), nullable=True)
    jump_id = db.Column(db.Integer, db.ForeignKey('app_jump_list.id'), nullable=True)
    light_pic_id = db.Column(db.Integer, db.ForeignKey('file.id'), nullable=False)
    dark_pic_id = db.Column(db.Integer, db.ForeignKey('file.id'), nullable=False)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    begin_at = db.Column(db.MYSQL_DATETIME_6, nullable=False, index=True)
    end_at = db.Column(db.MYSQL_DATETIME_6, nullable=False, index=True)
    sort_id = db.Column(db.SmallInteger, nullable=False, index=True)
    status = db.Column(db.StringEnum(Status), default=Status.VALID, index=True)
    platform = db.Column(db.StringEnum(Platform), nullable=False)

    activate_type = db.Column(db.StringEnum(ActivateType), nullable=False)

    @property
    def platform_list(self) -> List[str]:
        if self.platform == self.Platform.ANDROID:
            return ["ANDROID"]
        elif self.platform == self.Platform.IOS:
            return ["IOS"]
        elif self.platform == self.Platform.ANDROID_AND_IOS:
            return ["ANDROID", "IOS"]
        else:
            return []


class AppActivateContent(ModelBase):
    activate_id = db.Column(db.Integer, db.ForeignKey('app_activate.id'),
                                nullable=False)
    lang = db.Column(db.StringEnum(Language), nullable=False)
    title = db.Column(db.Text, nullable=False, default='')
    sub_title = db.Column(db.Text, nullable=False, default='')


def get_file_url(file_id):
    if not file_id:
        return ''
    from app.models import File
    return File.query.get(file_id).static_url


class NewAppActivate(ModelBase):
    AVAILABLE_LANGS = (Language.EN_US, Language.ZH_HANS_CN,
                       Language.ZH_HANT_HK, Language.JA_JP,
                       Language.KO_KP, Language.RU_KZ, Language.ES_ES,
                       Language.ID_ID, Language.FA_IR, Language.TR_TR,
                       Language.VI_VN, Language.AR_AE, Language.FR_FR,
                       Language.DE_DE, Language.PT_PT, Language.TH_TH,
                       Language.PL_PL, Language.IT_IT
                       )

    class Status(Enum):
        VALID = 'valid'
        DELETE = 'delete'

    class Platform(Enum):
        ANDROID = '安卓'
        IOS = "iOS"
        ANDROID_AND_IOS = '安卓+iOS'

    class ContentStyle(Enum):
        ARTICLE = '图文结合'     # 图文结合
        IMAGE = '纯图片'     # 纯图片

    class ActivateType(Enum):
        publicity = "活动宣传"
        introduction = "功能介绍"

    class Version(IntEnum):
        VERSION_2 = 2
        VERSION_3 = 3

    display_name = db.Column(db.Text, nullable=False)
    platform = db.Column(db.StringEnum(Platform), nullable=False)
    begin_at = db.Column(db.MYSQL_DATETIME_6, nullable=False, index=True)
    end_at = db.Column(db.MYSQL_DATETIME_6, nullable=False, index=True)
    jump_type = db.Column(db.StringEnum(AppJumpList.JumpType), nullable=True)
    jump_id = db.Column(db.Integer, db.ForeignKey('app_jump_list.id'), nullable=True)

    content_style = db.Column(db.StringEnum(ContentStyle), nullable=False, default=ContentStyle.ARTICLE)
    # LTR 图文结合(因为多语言共用一张图片
    light_pic_id = db.Column(db.Integer, db.ForeignKey('file.id'), nullable=True)
    dark_pic_id = db.Column(db.Integer, db.ForeignKey('file.id'), nullable=True)
    # RTL(波斯语和阿拉伯语
    rtl_light_pic_id = db.Column(db.Integer, db.ForeignKey('file.id'), nullable=True)
    rtl_dark_pic_id = db.Column(db.Integer, db.ForeignKey('file.id'), nullable=True)

    sort_id = db.Column(db.SmallInteger, nullable=False, index=True)
    activate_type = db.Column(db.StringEnum(ActivateType), nullable=False, default=ActivateType.introduction)
    status = db.Column(db.StringEnum(Status), default=Status.VALID, index=True)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    # version=1是AppActivate数据
    version = db.Column(db.Integer, default=2)

    @property
    def light_pic_url(self):
        return get_file_url(self.light_pic_id)

    @property
    def dark_pic_url(self):
        return get_file_url(self.dark_pic_id)

    @property
    def rtl_light_pic_url(self):
        return get_file_url(self.rtl_light_pic_id)

    @property
    def rtl_dark_pic_url(self):
        return get_file_url(self.rtl_dark_pic_id)

    @property
    def platform_list(self) -> List[str]:
        if self.platform == self.Platform.ANDROID:
            return ["ANDROID"]
        elif self.platform == self.Platform.IOS:
            return ["IOS"]
        elif self.platform == self.Platform.ANDROID_AND_IOS:
            return ["ANDROID", "IOS"]
        else:
            return []


class NewAppActivateContent(ModelBase):
    activate_id = db.Column(db.Integer, db.ForeignKey('new_app_activate.id'), nullable=False)
    lang = db.Column(db.StringEnum(Language), nullable=False)
    title = db.Column(db.Text, nullable=False, default='')
    sub_title = db.Column(db.Text, nullable=False, default='')
    light_pic_id = db.Column(db.Integer, db.ForeignKey('file.id'), nullable=True)
    dark_pic_id = db.Column(db.Integer, db.ForeignKey('file.id'), nullable=True)

    @property
    def light_pic_url(self):
        return get_file_url(self.light_pic_id)

    @property
    def dark_pic_url(self):
        return get_file_url(self.dark_pic_id)


class AppVersion(ModelBase):

    AVAILABLE_LANGS = (Language.EN_US, Language.ZH_HANS_CN,
                       Language.ZH_HANT_HK, Language.JA_JP,
                       Language.RU_KZ, Language.KO_KP,
                       Language.ID_ID, Language.ES_ES,
                       Language.FA_IR, Language.TR_TR,
                       Language.VI_VN, Language.AR_AE,
                       Language.FR_FR, Language.PT_PT,
                       Language.DE_DE, Language.TH_TH,
                       Language.PL_PL, Language.IT_IT
                       )

    class Platform(Enum):
        IOS = 'iOS'
        ANDROID = 'Android'
        IOS_APPSTORE = 'iOS_appstore'
        ANDROID_GOOGLE_PLAY = 'Android_GooglePlay'
        ANDROID_SAMSUNG_GALAXY_STORE = "Android_SamsungGalaxyStore"
        # 原先的APP也被下架了，重新添加两个字段区分上架
        IOS_LITE = 'iOSLite'
        IOS_LITE_APP_STORE = 'iOSLite_appstore'

    class UpdateType(Enum):
        FORCE = 'force'
        NOTICE = 'notice'
        NONE = 'none'
        ONLY_ONCE = 'only_once'

    class Status(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    platform = db.Column(db.StringEnum(Platform), nullable=False)
    version = db.Column(db.String(32), nullable=False)
    build = db.Column(db.Integer, nullable=False)
    update_type = db.Column(db.StringEnum(UpdateType), nullable=False,
                            default=UpdateType.NONE)
    file_id = db.Column(db.Integer, db.ForeignKey('file.id'))
    file_md5 = db.Column(db.String(32))
    file_url = db.Column(db.String(128), nullable=False, default='')
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID)
    remark = db.Column(db.Text, nullable=False, default='')

    file = db.relationship('File', foreign_keys=[file_id])


class AppVersionLangDetails(db.Model):

    id = db.Column(db.Integer, primary_key=True)
    owner_id = db.Column(db.Integer, db.ForeignKey(AppVersion.id),
                         nullable=False)
    lang = db.Column(db.StringEnum(Language), nullable=False)
    desc = db.Column(db.Text, nullable=False)

    owner = db.relationship(
        AppVersion, backref=db.backref('lang_details', lazy='dynamic'))


class SystemAlertConfig(ModelBase):
    class MethodType(Enum):
        WEWORK = 'wework'

    alert_method = db.Column(db.Enum(MethodType), nullable=False)
    alert_type = db.Column(db.String(64), nullable=False)
    config_data = db.Column(db.Text, nullable=False)


class VerifyChannel(ModelBase):

    __table_args__ = (
        db.Index("idx_content_channel_type", "content", "channel_type"),
    )

    class ChannelType(Enum):
        DOMAIN = "域名"
        EMAIL = '邮箱'
        WECHAT = '微信'
        TELEGRAM = 'Telegram'
        X = 'X'
        INSTAGRAM = 'Instagram'
        FACEBOOK = 'Facebook'
        WHATSAPP = 'Whatsapp'
        DISCORD = 'Discord'
        MEDIUM = 'Medium'
        YOUTUBE = 'Youtube'
        REDDIT = 'Reddit'
        OTHERS = '其它'

    Special_Community = (ChannelType.TELEGRAM, ChannelType.X, ChannelType.FACEBOOK, ChannelType.INSTAGRAM)

    name = db.Column(db.String(64), nullable=False)
    channel_type = db.Column(db.StringEnum(ChannelType), nullable=False)
    content = db.Column(db.String(256), nullable=False)
    edit_by = db.Column(db.Integer, db.ForeignKey('user.id'))


class TempMaintain(ModelBase):

    AVAILABLE_LANGS = (
        Language.EN_US,
        Language.ZH_HANS_CN,
        Language.ZH_HANT_HK,
        Language.JA_JP,
        Language.RU_KZ,
        Language.ES_ES,
        Language.FA_IR,
        Language.KO_KP,
        Language.ID_ID,
        Language.TR_TR,
        Language.VI_VN,
        Language.AR_AE,
        Language.FR_FR,
        Language.DE_DE,
        Language.PT_PT,
        Language.TH_TH,
        Language.PL_PL,
        Language.IT_IT
    )

    class Status(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    class MaintainScope(Enum):
        SPOT = '币币'
        PERPETUAL = '合约'
        ALL = '币币+合约'
        ALL_SITE = '全局'

    class ActiveStatus(Enum):
        CREATED = "未开始"
        STARTED = "维护中"
        PROTECTING = '保护期中'
        FINISHED = "已结束"


    scope = db.Column(db.StringEnum(MaintainScope), nullable=False)
    started_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    ended_at = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    remark = db.Column(db.String(128), default='')
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID)
    jump_page_enabled = db.Column(db.Boolean, nullable=False, default=False)
    url = db.Column(db.String(128), nullable=False, default='')
    protect_duration = db.Column(db.Integer, nullable=True)   # 保护期分钟数
    protect_duration_updated = db.Column(db.Boolean, default=False, nullable=False)   # 是否已调用过关闭保护期接口
    notification_ids = db.Column(db.String(32), nullable=True)
    user = db.relationship(
        'User',
        backref=db.backref('temp_maintain', lazy='dynamic'),
        foreign_keys=user_id)

    @property
    def active_status(self):
        now_ = now()
        if now_ < self.started_at:
            return self.ActiveStatus.CREATED
        if now_ < self.ended_at and self.status == self.Status.VALID:
            return self.ActiveStatus.STARTED
        if self.protect_duration and self.ended_at + datetime.timedelta(minutes=self.protect_duration) >= now_:
            return self.ActiveStatus.PROTECTING
        return self.ActiveStatus.FINISHED


class TempMaintainContent(ModelBase):

    temp_maintain_id = db.Column(db.Integer, db.ForeignKey('temp_maintain.id'))
    lang = db.Column(db.StringEnum(Language), nullable=False)
    title = db.Column(db.Text, nullable=False)
    content = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False)  # 兼容 app 旧版本保留
    new_content = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False, default='')


class MarketMaintain(ModelBase):
    """单市场停服维护"""

    class Status(Enum):
        VALID = '生效中'
        DELETED = '删除'

    class MaintainStatus(Enum):
        ON_SERVICE = 0   # 正常状态
        OUT_OF_SERVICE = 1   # 停服维护
        PROTECT_DURATION = 2   # 保护期

    market = db.Column(db.String(32), nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID)
    maintain_status = db.Column(db.StringEnum(MaintainStatus), nullable=False)
    maintain_expect_end_at = db.Column(db.MYSQL_DATETIME_6, nullable=True)   # 停服或保护期的预计恢复时间（仅用作提示）
    tip_bar_id = db.Column(db.Integer, db.ForeignKey('tip_bar.id'), nullable=True)


class SystemAccountSnapshotHistory(ModelBase):
    """snapshot for income"""

    class SystemType(Enum):
        SPOT = 'spot'
        PERPETUAL = 'perpetual'

    report_date = db.Column(db.Date, nullable=False)
    system_type = db.Column(db.StringEnum(SystemType), nullable=False)
    assets_data = db.Column(db.MYSQL_MEDIUM_TEXT, nullable=False)


class RealTimeIncomeHistory(ModelBase):

    class StatusType(Enum):
        CREATED = "created"
        FINISHED = "finished"

    business_type = db.Column(db.StringEnum(BalanceBusiness), nullable=False, index=True)

    asset = db.Column(db.String(128), nullable=False)
    # 收入为+，支出为-
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    status = db.Column(db.StringEnum(StatusType), nullable=False)

    # 外部id，用于同步任务标识
    extra_id = db.Column(db.Integer, index=True)


class KlineBoostSetting(ModelBase):

    class BusinessType(Enum):
        SPOT = "spot"
        PERPETUAL = "perpetual"
        PERPETUAL_POSITION = "perpetual_position"

    business_type = db.Column(db.StringEnum(BusinessType), nullable=False)
    start_multiple = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    start_day = db.Column(db.Date, nullable=False)
    end_multiple = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    increase_rate = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)


class PriceVerifySetting(ModelBase):

    class Status(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    # 冗余字段，在detail中也会存
    key = db.Column(db.String(128), index=True, nullable=False)
    value = db.Column(db.JSON, nullable=False)
    status = db.Column(db.StringEnum(Status), nullable=False, default=Status.VALID)
    remark = db.Column(db.String(512), nullable=False)


class SpecialAccountBalanceUpdate(ModelBase):
    # 特殊账户资产变更，比如收支账户,暂时用于资产改名0账户资产处理

    class Status(Enum):
        CREATED = 'created'
        FINISHED = 'finished'
        FAILED = 'failed'

    # 还是使用 balance_update_business_id的id

    balance_update_business_id = db.Column(db.Integer, unique=True, nullable=False)
    user_id = db.Column(db.Integer, index=True, nullable=False)
    asset = db.Column(db.String(32), nullable=False)
    amount = db.Column(db.MYSQL_DECIMAL_26_8, nullable=False)
    status = db.Column(db.Enum(Status), nullable=False, default=Status.CREATED)


class AssetChangeEvent(ModelBase):
    """
    资产置换事件

    一般换币逻辑是旧币from_asset改名为to_asset
    这个事件在执行资产置换操作时候触发
    由定时任务去检查新币from_asset市场是否上线，上线之后，设置结束时间为min(开始时间+1天, 新市场上线时间+1天），防止充提风控触发
    """

    class Status(Enum):
        VALID = 'valid'
        DELETED = 'deleted'

    from_asset = db.Column(db.String(32), index=True, nullable=False)
    to_asset = db.Column(db.String(32), index=True, nullable=False)
    snap_price = db.Column(db.MYSQL_DECIMAL_PRICE_NEW, nullable=False)  # 创建置换事件时的from_asset价格
    start = db.Column(db.MYSQL_DATETIME_6, nullable=False)
    end = db.Column(db.MYSQL_DATETIME_6, nullable=True)
    rate = db.Column(db.MYSQL_DECIMAL_26_8, default=Decimal('1'), nullable=False)
    remark = db.Column(db.String(512), nullable=False, default="")
    status = db.Column(db.StringEnum(Status), default=Status.VALID)

    class PModel(BaseModel):
        from_asset: str
        to_asset: str
        start: AwareDatetime
        end: AwareDatetime | None
        rate: Decimal
        from_asset_market: bool = False

    def to_model(self) -> PModel | None:
        if self.status == self.Status.VALID:
            return self.PModel(
                from_asset=self.from_asset,
                to_asset=self.to_asset,
                start=self.start,
                end=self.end,
                rate=self.rate,
            )
        return None



