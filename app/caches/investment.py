# -*- coding: utf-8 -*-
from collections import defaultdict
from datetime import timedelta
from decimal import Decimal
from enum import Enum
import json
from sqlalchemy import func

from app.models.investment import AssetInvestmentConfig, UserDayInterestDetail, UserDayInterestHistory
from app.utils import now, amount_to_str, quantize_amount, today
from app.models.activity import Coupon, CouponDailyBalanceHistory
from app.utils.parser import JsonEncoder
from .base import HashCache
from ..models.equity_center import UserInvestIncreaseEquity, UserDailyIncEquityHistory


class Investment7DaysEARCache(HashCache):
    """七日年化"""

    def __init__(self):
        super().__init__(None)

    def reload(self):
        data = self.get_data()
        if data:
            self.save({asset: amount_to_str(value, 6) for asset, value in data.items()})

    @classmethod
    def get_data(cls):
        from app.models.daily import DailyInvestmentReport

        start = now() - timedelta(days=8)
        query = DailyInvestmentReport.query.filter(
            DailyInvestmentReport.report_date > start,
        )
        open_asset = AssetInvestmentConfig.get_valid_assets()
        result = defaultdict(lambda: defaultdict(lambda: dict(day_rate=Decimal(), interest_amount=Decimal())))
        for record in query:
            result[record.asset][record.report_date]["day_rate"] = record.day_rate
            result[record.asset][record.report_date]["interest_amount"] = record.investment_interest_amount
        final_result = dict()
        for asset, _d in result.items():
            if asset not in open_asset:
                continue
            total_interest_amount = Decimal()
            total_amount = Decimal()
            for _, _d1 in _d.items():
                day_rate = _d1["day_rate"]
                total_interest_amount += _d1["interest_amount"]
                if day_rate > Decimal():
                    total_amount += _d1["interest_amount"] / _d1["day_rate"]
            if total_amount > Decimal():
                final_result[asset] = quantize_amount(total_interest_amount * 365 / total_amount, 6)
            else:
                final_result[asset] = Decimal()

        return final_result

    def read_data(self):
        result = self.hgetall()
        return {asset: Decimal(rate) for asset, rate in result.items()}

    def get_rate(self, asset: str, default: Decimal = None) -> Decimal | None:
        if not (rate := self.hget(asset)):
            return default
        return Decimal(rate)


class InvestmentYesterdayARRCache(HashCache):
    """昨日年化"""

    def __init__(self):
        super().__init__(None)

    def reload(self):
        data = self.get_data()
        self.save(data)

    @classmethod
    def get_data(cls):
        from app.models.daily import DailyInvestmentReport

        yesterday = now().date() - timedelta(days=1)
        q = DailyInvestmentReport.query.filter(DailyInvestmentReport.report_date == yesterday).all()
        return {v.asset: amount_to_str(v.day_rate * 365, 6) for v in q}

    def read_data(self):
        result = self.hgetall()
        return {asset: Decimal(rate) for asset, rate in result.items()}


class InvestmentIncTotalIncomeCache(HashCache):
    """用户理财加息-总收益"""

    # todo 保留一段时间，后续删除

    def __init__(self, user_id: int):
        super().__init__(str(user_id))

    def get_user_income(self) -> dict:
        data = self.hgetall()
        return {k: Decimal(v) for k, v in data.items()}

    @classmethod
    def reload(cls):
        rows = (
            CouponDailyBalanceHistory.query.filter(
                CouponDailyBalanceHistory.coupon_type == Coupon.CouponType.INVESTMENT_INCREASE_RATE,
            )
            .group_by(
                CouponDailyBalanceHistory.user_id,
                CouponDailyBalanceHistory.asset,
            )
            .with_entities(
                CouponDailyBalanceHistory.user_id,
                CouponDailyBalanceHistory.asset,
                func.sum(CouponDailyBalanceHistory.amount).label("total_amount"),
            )
            .all()
        )

        user_asset_income_map = defaultdict(lambda: defaultdict(Decimal))
        for r in rows:
            user_asset_income_map[r.user_id][r.asset] = r.total_amount

        for user_id, asset_income_map in user_asset_income_map.items():
            _cache = cls(user_id)
            _data = {str(k): amount_to_str(v) for k, v in asset_income_map.items()}
            _cache.save(_data)


class InvIncEquityYesIncomeCache(HashCache):
    """用户理财加息-权益昨日收益"""

    def __init__(self, user_id: int):
        super().__init__(str(user_id))

    def get_user_income(self) -> dict:
        data = self.hgetall()
        return {k: Decimal(v) for k, v in data.items()}

    @classmethod
    def reload(cls):
        model = UserDailyIncEquityHistory
        rows = model.query.filter(
            model.report_date == today() - timedelta(days=1),
            model.status == model.Status.FINISHED,
        ).all()

        user_asset_income_map = defaultdict(lambda: defaultdict(Decimal))
        for r in rows:
            user_asset_income_map[r.user_id][r.asset] = r.interest_amount

        for user_id, asset_income_map in user_asset_income_map.items():
            _cache = cls(user_id)
            _data = {str(k): amount_to_str(v) for k, v in asset_income_map.items()}
            _cache.save(_data)


class InvIncEquityTotalIncomeCache(HashCache):
    """用户理财加息-权益总收益"""

    def __init__(self, user_id: int):
        super().__init__(str(user_id))

    def get_user_income(self) -> dict:
        data = self.hgetall()
        return {k: Decimal(v) for k, v in data.items()}

    @classmethod
    def reload(cls):
        model = UserInvestIncreaseEquity
        rows = (
            model.query.filter(
                model.increase_amount > 0,
            )
            .group_by(
                model.user_id,
                model.investment_asset,
            )
            .with_entities(
                model.user_id,
                model.investment_asset,
                func.sum(model.increase_amount).label("total_amount"),
            )
        )

        user_asset_income_map = defaultdict(lambda: defaultdict(Decimal))
        for r in rows:
            if not r.investment_asset:
                continue
            user_asset_income_map[r.user_id][r.investment_asset] = r.total_amount

        for user_id, asset_income_map in user_asset_income_map.items():
            _cache = cls(user_id)
            _data = {str(k): amount_to_str(v) for k, v in asset_income_map.items()}
            _cache.save(_data)


class InvestmentBalanceRankCache(HashCache):
    """理财余额排名"""

    LIMIT = 1000
    TTL = 86400

    def __init__(self, asset: str):
        super().__init__(asset)

    def get_rank(self) -> dict:
        return self.hgetall()

    @classmethod
    def reload(cls):
        from app.business.investment import InvestmentDataProc

        dt = now()
        asset_user_balance = InvestmentDataProc.get_dt_asset_user_snap_map(dt)
        for asset, user_balance in asset_user_balance.items():
            rank_user_balance = sorted(user_balance.items(), key=lambda x: x[1], reverse=True)[: cls.LIMIT]
            _cache = cls(asset)
            _cache.save({str(user_id): amount_to_str(balance, 8) for user_id, balance in rank_user_balance})
            _cache.expire(cls.TTL)


class InterestRankCache(HashCache):
    """理财利息排名"""

    LIMIT = 100
    TTL = 86400

    class Interval(Enum):
        day_7 = 7
        day_30 = 30

    def __init__(self, asset: str, interval: Interval):
        super().__init__(f"{asset}:{interval.name}")

    def get_rank(self) -> dict:
        data = self.hgetall()
        return {user_id: json.loads(v) for user_id, v in data.items()}

    @classmethod
    def reload(cls):
        for interval in cls.Interval:
            end = today() - timedelta(days=1)
            start = end - timedelta(days=interval.value)
            asset_user_interest_map = cls.get_asset_user_interest_map(start, end)
            user_ids = set()
            all_asset_rank_map = defaultdict(lambda: defaultdict(list))
            for asset, user_interest in asset_user_interest_map.items():
                rank_user_interest = sorted(user_interest.items(), key=lambda x: x[1], reverse=True)[: cls.LIMIT]
                all_asset_rank_map[asset] = rank_user_interest
                user_ids.update(user_id for user_id, _ in rank_user_interest)

            user_interest_detail_map = cls.get_users_detail_map(user_ids, start, end)

            # 给asset_rank_map赋值
            for asset, user_interest in all_asset_rank_map.items():
                rank_map = {}
                for user_id, interest in user_interest:
                    rank_map[user_id] = json.dumps(
                        dict(
                            total_amount=interest,
                            **user_interest_detail_map[user_id][asset],
                        ),
                        cls=JsonEncoder,
                    )
                _cache = cls(asset, interval)
                _cache.save(rank_map)
                _cache.expire(cls.TTL)

    @classmethod
    def get_asset_user_interest_map(cls, start, end):
        model = UserDayInterestHistory
        sum_query = (
            model.query.filter(
                model.report_date >= start,
                model.report_date <= end,
                model.status == model.Status.SUCCESS,
            )
            .group_by(model.user_id, model.asset)
            .with_entities(
                func.sum(model.interest_amount).label("total_amount"),
                model.user_id,
                model.asset,
            )
            .all()
        )

        asset_user_interest_map = defaultdict(lambda: defaultdict(Decimal))
        for row in sum_query:
            asset_user_interest_map[row.asset][row.user_id] = row.total_amount
        return asset_user_interest_map

    @classmethod
    def get_users_detail_map(cls, user_ids, start, end):
        # 获取收益详情
        d_model = UserDayInterestDetail
        rows = (
            d_model.query.filter(
                d_model.user_id.in_(user_ids),
                d_model.report_date >= start,
                d_model.report_date <= end,
            )
            .group_by(
                d_model.user_id,
                d_model.interest_type,
                d_model.asset,
            )
            .with_entities(
                d_model.user_id,
                d_model.asset,
                d_model.interest_type,
                func.sum(d_model.interest_amount).label("total_amount"),
            )
            .all()
        )
        user_interest_detail_map = defaultdict(lambda: defaultdict(lambda: defaultdict(Decimal)))
        for row in rows:
            user_interest_detail_map[row.user_id][row.asset][row.interest_type.name] = row.total_amount
        return user_interest_detail_map


class InvestmentConfigCache(HashCache):
    """理财配置"""

    model = AssetInvestmentConfig

    def __init__(self):
        super().__init__(None)

    @classmethod
    def reload(cls):
        configs = cls.model.query.filter(cls.model.status == cls.model.StatusType.OPEN)
        config_map = {config.asset: json.dumps(config.to_dict(enum_to_name=True), cls=JsonEncoder) for config in configs}
        cls().save(config_map)

    def get_asset_config(self, asset: str) -> dict:
        data = self.hget(asset)
        return json.loads(data) if data else {}

    def get_all_config(self) -> dict:
        data = self.hgetall()
        return {asset: json.loads(v) for asset, v in data.items()}

    def get_assets(self) -> list:
        return self.hkeys()

    def get_asset_show_rate(self, asset: str, is_sub: bool = False) -> Decimal:
        config = self.get_asset_config(asset)
        rule_map = config.get("rule_map") or {}
        ladder_rule = rule_map.get(self.model.ConfigType.LADDER.name, {})
        fixed_rule = rule_map.get(self.model.ConfigType.FIXED.name, {})
        base_rate = Decimal(config.get("base_rate", 0))
        if fixed_rule:
            base_rate += Decimal(fixed_rule.get("rate", 0))
        if ladder_rule and not is_sub:
            base_rate += Decimal(ladder_rule.get("rate", 0))
        return base_rate