# -*- coding: utf-8 -*-
from app.caches.base import Set<PERSON>ache
from app.utils import batch_iter


class EquityNoticeSendCache(SetCache):
    """ 权益触达通知发送缓存 """

    def __init__(self, notice_scene: str, notice_type: str):
        super().__init__(f"{notice_scene}:{notice_type}")

    def add_user_equity_ids(self, user_equity_ids: set[int]):
        for ch_ids in batch_iter(user_equity_ids, 5000):
            self.sadd(*[str(i) for i in ch_ids])

    def del_user_equity_ids(self, user_equity_ids: set[int]):
        for ch_ids in batch_iter(user_equity_ids, 5000):
            self.srem(*[str(i) for i in ch_ids])

    def get_sent_user_equity_ids(self) -> set[int]:
        return {int(i) for i in self.read()}
