import json
from collections import defaultdict
from datetime import timed<PERSON><PERSON>
from decimal import Decimal, ROUND_UP
from typing import Any

from flask import current_app
from sqlalchemy import func

from app import Language
from app.caches import HashCache, StringCache
from app.common import Currency, P2P_ASSET_SORT
from app.models import P2pOrder, P2pMerActFairPrice, db
from app.models.mongo.p2p.config import P2pFiat, P2pFiatMarket, P2pAssetConfig, DEFAULT_ASSET_USDT
from app.models.mongo.p2p.advertising import P2pAdvertisingMySQL
from app.models.mongo.p2p.mer_act import P2pMerAct as P2pMerAct
from app.models.mongo.p2p.pay_channel import P2pPayChannelMySQL, Status, P2pFiatPayChannelMySQL, \
    P2pCountryFiat, P2pCountrySuggestPayChannelMySQL, UserPayChannelMySQL

from enum import Enum

from app.caches import SetCache
from app.models.p2p import P2pOrderFile
from app.utils import quantize_amount_non_zero, current_timestamp, today_datetime, amount_to_str, \
    AWSBucketPublic, quantize_amount
from app.utils.iterable import batch_iter
from app.utils.parser import JsonEncoder


class UniqueOptionCache(HashCache):
    """业务防重缓存"""

    TTL = 3600 * 24 * 7  # 7 days

    class OptionType(Enum):
        P2p_LOCK_STOCK = "p2p_lock_stock"
        P2p_UNLOCK_STOCK = "p2p_unlock_stock"
        P2p_REDUCE_STOCK = "p2p_reduce_stock"
        P2p_CLOSE_SHOP = "p2p_close_shop"
        P2p_OPEN_SHOP = "p2p_open_shop"

    def __init__(self):
        super().__init__(None)

    @classmethod
    def _splice_key(cls, unique_id: int, unique_type, option: OptionType):
        return f"{unique_id}:{unique_type}:{option.value}"

    def is_unique(self, unique_id: int, unique_type: str, option: OptionType) -> bool:
        return self.hexists(self._splice_key(unique_id, unique_type, option))

    def add_option(self, unique_id: int, unique_type: str, option: OptionType):
        expired_ts = current_timestamp(to_int=True) + self.TTL
        self.hset(self._splice_key(unique_id, unique_type, option), str(expired_ts))

    @classmethod
    def delete_expired_keys(cls):
        cache = cls()
        current_ts = current_timestamp(to_int=True)
        values = cache.hgetall()
        del_keys = [k for k, v in values.items() if int(v) < current_ts]
        if del_keys:
            cache.hdel(*del_keys)


class PayChannelCache(HashCache):
    def __init__(self):
        super().__init__(None)

    @classmethod
    def reload(cls):
        """ 更新所有支付渠道缓存 """
        channels = P2pPayChannelMySQL.get_all_channel()
        channels_map = {str(i.mongo_id): i.to_dict(enum_to_name=True) for i in channels}
        cls().save({k: json.dumps(v, cls=JsonEncoder) for k, v in channels_map.items()})
        return channels_map

    @classmethod
    def save_one(cls, data: P2pPayChannelMySQL):
        cls().hset(str(data.mongo_id), json.dumps(data.to_dict(enum_to_name=True), cls=JsonEncoder))

    @classmethod
    def get_many(cls, channel_ids: list, lang: Language = None, inactive=False) -> dict:
        channel_ids = list(map(str, channel_ids))
        """ 获取多个支付渠道 """
        channel_map = {k: json.loads(v) for k, v in cls().hmget_with_keys(channel_ids)}
        if lang:
            channel_map = {k: cls.format_lang(v, lang) for k, v in channel_map.items()}
        if not inactive:
            channel_map = cls.filter_active(channel_map)
        return channel_map

    @classmethod
    def get_all(cls, lang=None, inactive=False):
        channel_map = {k: json.loads(v) for k, v in cls().hgetall().items()}
        if lang:
            channel_map = {k: cls.format_lang(v, lang) for k, v in channel_map.items()}
        if not inactive:
            channel_map = cls.filter_active(channel_map)
        return channel_map

    @staticmethod
    def filter_active(data):
        return {
            k: v for k, v in data.items() if
            v["active_status"] == P2pPayChannelMySQL.ActiveStatus.ACTIVE.name and v["status"] == Status.VALID.name
        }

    @classmethod
    def get_one(cls, channel_id: str, lang: Language = None) -> dict:
        if val := cls().hget(str(channel_id)):
            data = json.loads(val)
            if lang:
                data = cls.format_lang(data, lang)
            return data
        return {}

    @classmethod
    def format_lang(cls, data, lang):
        all_lang_data = data.pop("lang_data", {})
        lang_data = all_lang_data.get(lang.name) or all_lang_data.get(Language.EN_US.name)
        if not lang_data:
            return {}
        data["name"] = lang_data["name"]
        data["name_field"] = lang_data["name_field"]
        data["form"] = cls.format_form(data["form"], lang_data["form_map"])
        return data

    @staticmethod
    def format_form(form: list, lang_form: dict) -> list:
        from app.api.common.request import is_old_app_request
        from app.models.mongo.p2p import FormModel
        ret = []
        for i in form:
            if not i['status'] or i["status"].upper() != Status.VALID.name:
                continue
            filed_type = i.get("filed_type", "TEXT")
            if filed_type == FormModel.FiledType.QR_CODE.name and is_old_app_request(4010, 102):
                continue
            item = {
                "key": i["key"],
                "field_name": lang_form.get(i["key"]),
                "filed_type": filed_type,
                "required": i.get("required", True)
            }
            ret.append(item)
        return ret


class FiatPayChannelCache(HashCache):
    def __init__(self):
        super().__init__(None)

    @classmethod
    def reload(cls):
        data = P2pFiatPayChannelMySQL.get_all_valid_fiat_pay_channel()
        active_pay_channels = P2pPayChannelMySQL.get_all_valid_channel()
        active_pay_ids = [p.mongo_id for p in active_pay_channels]
        fiat_channel_map = {
            i.fiat: json.dumps(list(set(i.pay_channel_ids) & set(active_pay_ids))) for i in data
        }
        cls().save(fiat_channel_map)

    def read_one_fiat(self, fiat: str) -> list[str]:
        value = self.hget(fiat)
        if not value:
            return []
        return json.loads(self.hget(fiat))

    def get_all(self):
        return {i: json.loads(v) for i, v in self.hgetall().items()}


class CountryFiatCache(HashCache):
    def __init__(self):
        super().__init__(None)

    @classmethod
    def reload(cls):
        # 1个国家可能对应多个法币, 返回产品要求的主要法币
        data = P2pCountryFiat.get_all_valid_fiat_country()
        country_fiat_map = {i.country_code: i.fiat for i in data if i.is_primary is not False}
        cls().save(country_fiat_map)

    def read_one_country(self, country: str) -> str:
        return self.hget(country)


class P2pFiatCfgCache(HashCache):
    # 所有法币
    def __init__(self):
        super().__init__(None)

    @classmethod
    def reload(cls):
        rows = P2pFiat.get_all_data()
        data = {i.fiat: json.dumps(i.to_dict(enum_to_name=True), cls=JsonEncoder) for i in rows}
        cls().save(data)
        return data
        
    def get_all_valid_fiats(self) -> list[str]:
        data = [json.loads(i) for i in self.hval()]
        fiats = [i['fiat'] for i in data if i['status'] == Status.VALID.name]
        return fiats
    
    def get_all_fiats(self) -> list[str]:
        data = [json.loads(i) for i in self.hval()]
        fiats = [i['fiat'] for i in data]
        return fiats


class P2pUserLastFiatCache(HashCache):
    # 用户最后一次交易的法币缓存
    def __init__(self, user_id: int):
        super().__init__(str(user_id))

    def set_fiat(self, asset: str, fiat: str):
        """设置用户最后一次交易的法币"""
        self.hset(asset, fiat)

    def read_fiat(self, asset: str) -> str:
        """读取用户最后一次的法币"""
        value = self.hget(asset)
        return value if value else Currency.USD.name

    def read_all(self, asset: str) -> str:
        """读取用户最后一次的法币"""
        value = self.hget(asset)
        return value


class CountrySuggestPayChannelCache(HashCache):

    def __init__(self):
        super().__init__(None)

    @classmethod
    def reload(cls):
        data = P2pCountrySuggestPayChannelMySQL.get_all_valid_suggest_channel()
        country_channel_map = defaultdict(dict)
        for i in data:
            for fiat_data in i.fiat_data:
                if fiat_data.status != Status.VALID.name:
                    continue
                country_channel_map[i.country_code].update({
                    fiat_data.fiat: fiat_data.pay_channel_ids
                })
        cls().save({k: json.dumps(v) for k, v in country_channel_map.items()})

    def read_one_country(self, country_code: str) -> dict[str, list]:
        value = self.hget(country_code)
        return json.loads(value) if value else {}


class P2pFiatMarketCache(HashCache):
    # 法币市场配置缓存
    SLIDE_EXCHANGE_RATE = 0.05  # 滑点汇率容错比例
    model = P2pFiatMarket

    def __init__(self, asset: str):
        self.asset = asset
        super().__init__(asset)
        
    @classmethod
    def refresh_all(cls):
        assets = [i.asset for i in P2pAssetConfig.get_all_valid_data()]
        for asset in assets:
            cls(asset).reload()

    def reload(self):
        rows = self.model.query.filter(
            self.model.asset == self.asset,
            self.model.status == Status.VALID
        ).all()
        save_data = {}
        for row in rows:
            save_data[row.fiat] = json.dumps(self.format_item(row), cls=JsonEncoder)
        self.save(save_data)
        return save_data
        
    def refresh_one_fiat(self, fiat: str):
        row = self.model.query.filter(
            self.model.fiat == fiat,
            self.model.asset == self.asset,
            self.model.status == Status.VALID
        ).first()
        if row:
            self.hset(fiat, json.dumps(self.format_item(row), cls=JsonEncoder))
        else:
            self.hdel(fiat)
        
    @classmethod
    def format_item(cls, row: P2pFiatMarket):
        item = row.to_dict(enum_to_name=True)
        fair_price = row.fair_price()
        item["fair_price"] = fair_price
        rule = row.rule
        item["min_price"] = fair_price * (1 - Decimal(rule.left))
        item["max_price"] = fair_price * (1 + Decimal(rule.right))
        return item

    @classmethod
    def format_limit_amount(cls, amount: Decimal):
        """限额数据格式化，最高位向上取整 20.02 => 30"""
        return quantize_amount_non_zero(amount, 1, ROUND_UP)

    def get_all(self):
        data = self.hgetall()
        if not data:
            data = self.reload()
        return {
            k: json.loads(v) for k, v in data.items()
        }

    def get_country_visible_markets(self, country_code: str = None):
        # 排除不可见的法币
        data = self.get_all()
        ret = {}
        for fiat, item in data.items():
            visible_countries = item.get("visible_countries")
            if not visible_countries or country_code in visible_countries:
                ret[fiat] = item
        return ret

    def get_fiats(self):
        value = self.hkeys()
        if not value:
            data = self.reload()
            value = list(data.keys())
        return list(value)

    def get_country_visible_fiats(self, country_code: str = None):
        return list(self.get_country_visible_markets(country_code).keys())

    def get_fiat_info(self, fiat: str) -> dict[str, Any]:
        value = self.hget(fiat)
        return json.loads(value) if value else {}


class P2pAssetConfigCache(HashCache):

    def __init__(self):
        super().__init__(None)

    @classmethod
    def reload(cls):
        data = P2pAssetConfig.query.all()
        asset_config_map = {i.asset: json.dumps(i.to_dict(enum_to_name=True), cls=JsonEncoder) for i in data}
        cls().save(asset_config_map)
        return asset_config_map

    @classmethod
    def get_asset_info(cls, asset: str) -> dict[str, Any]:
        value = cls().hget(asset)
        return json.loads(value) if value else {}

    @classmethod
    def get_many(cls, assets=None):
        if assets:
            values = dict(cls().hmget_with_keys(assets))
        else:
            values = cls().hgetall()
        return {k: json.loads(v) for k, v in values.items()}

    @classmethod
    def get_active_assets(cls):
        data = cls().get_many()
        return [k for k, v in data.items() if v["status"] == Status.VALID.name]

    @classmethod
    def get_active_asset_names(cls):
        assets = sorted(cls().get_active_assets(), key=lambda x: P2P_ASSET_SORT.index(x) if x in P2P_ASSET_SORT else float('inf'))
        return assets
    
    @classmethod
    def get_assets(cls):
        value = cls().hgetall()
        if not value:
            return []
        return list(value.keys())


class AdvertisingBookCache(HashCache):

    def __init__(self, asset: str):
        super().__init__(asset)

    @classmethod
    def reload(cls):
        # 使用SQLAlchemy进行分组查询
        query = db.session.query(
            P2pAdvertisingMySQL.quote,
            P2pAdvertisingMySQL.base,
            P2pAdvertisingMySQL.price,
            P2pAdvertisingMySQL.adv_type,
            func.sum(P2pAdvertisingMySQL.stocks_quantity).label("amount"),
            func.sum(P2pAdvertisingMySQL.lock_stocks_quantity).label("lock_amount"),
            func.count(P2pAdvertisingMySQL.id).label("count")
        ).filter(
            P2pAdvertisingMySQL.status == P2pAdvertisingMySQL.Status.ONLINE
        ).group_by(
            P2pAdvertisingMySQL.quote,
            P2pAdvertisingMySQL.base,
            P2pAdvertisingMySQL.price,
            P2pAdvertisingMySQL.adv_type
        ).all()
        
        asset_mapper = defaultdict(lambda: defaultdict(list))
        for row in query:
            base, quote = row.base, row.quote
            data = {
                "price": row.price,
                "adv_type": row.adv_type,
                "amount": row.amount - row.lock_amount,
                "count": row.count
            }
            asset_mapper[base][quote].append(data)

        for k, fiat_mapper in asset_mapper.items():
            cls(k).save({f: json.dumps(v, cls=JsonEncoder) for f, v in fiat_mapper.items()})

    def get_book_list(self, fiat: str):
        value = self.hget(fiat)
        if not value:
            return []
        return json.loads(value)


class P2pLastSummaryTimeCache(StringCache):
    """已弃用"""

    def __init__(self):
        super().__init__(None)

    def set_time(self, time: float):
        self.save(str(time))

    def get_last_trace_time(self) -> float:
        value = self.get()
        return float(value) if value else 0


class P2pLastSummaryIdCache(StringCache):

    def __init__(self):
        super().__init__(None)

    def set_last_id(self, last_id: int):
        self.save(str(last_id))

    def get_last_id(self) -> int:
        value = self.get()
        return int(value) if value else 0


class P2pNoticeCache(SetCache):
    """p2p已发放消息缓存"""

    def __init__(self, biz_type: str):
        super().__init__(biz_type)

    def has_biz_id(self, biz_id: str) -> bool:
        return self.sismember(biz_id)

    def add_biz_id(self, biz_id: str):
        self.sadd(biz_id)


class UserUnreadComplaintCache(SetCache):
    """用户申诉锚点缓存"""
    ttl = 86400 * 30

    def __init__(self, user_id):
        super().__init__(user_id)

    def add_complaint_id(self, complaint_id):
        self.sadd(complaint_id)
        self.expire(self.ttl)

    def delete_complaint_id(self, complaint_id):
        values = self.smembers()
        complaint_id = str(complaint_id)
        if values == set(complaint_id):
            self.delete()
        else:
            self.srem(complaint_id)

    def has_unread(self):
        return bool(self.smembers())

    def get_unread_complaint_set(self):
        return {int(i) for i in self.smembers()}

    def reload(self):
        pass


class P2pSellRiskReqRecord(StringCache):
    limit = 7200

    def __init__(self, row_id):
        super().__init__(f"{row_id}")

    def add_record(self):
        self.set("", ex=self.limit)


class P2pAdvPriceSnapshotCache(HashCache):
    """p2p 广告价格快照数据（仅用来支持活动, 活动结束删除）"""

    def __init__(self):
        super().__init__(None)


class P2pMerActCache(StringCache):
    model = P2pMerAct

    def __init__(self, act_id: int):
        super().__init__(str(act_id))

    @classmethod
    def _uni(cls, row):
        return row.fiat, row.asset

    @classmethod
    def reload(cls):

        statuses = [cls.model.Status.ONLINE, cls.model.Status.OFFLINE, cls.model.Status.EARLY_OFFLINE]
        rows = cls.model.query.filter(
            cls.model.status.in_(statuses)
        ).all()
        exclude_fields = [
            "id", "apply_count", "buy_reward_count", "sell_reward_count", "updated_at", "created_at"
        ]
        ts_fields = [
            "start_at", "end_at", "apply_start_at", "apply_end_at"
        ]

        f_model = P2pMerActFairPrice
        fair_map = {
            cls._uni(i): i for i in f_model.query.all()
        }

        for row in rows:
            fair: P2pMerActFairPrice = fair_map.get(cls._uni(row))
            if not fair:
                current_app.logger.error(f"p2p mer act market {cls._uni(row)} not found")
                continue
            data = row.to_dict(enum_to_name=True)
            for field in exclude_fields:
                data.pop(field, "")
            for ts_field in ts_fields:
                data[ts_field] = int(data[ts_field].timestamp())
            data["min_limit"], data["max_limit"] = cls.format_act_min_max(row)
            data["has_buy"] = bool(row.buy_reward)
            data["has_sell"] = bool(row.sell_reward)
            data["fair_price"] = fair.display_price()
            data["all_reward"] = row.get_all_reward()
            data["completion_rate_list"] = [
                {
                    "rank_amount": i.rank_amount,
                    "rank_max": Decimal(i.rank_max) * 100,
                    "rank_min": Decimal(i.rank_min) * 100,
                }
                for i in row.completion_rate_list
            ]
            data["rank_rate_list"] = [
                {
                    "rank_amount": Decimal(i.rank_amount) * 100,
                    "rank_max": i.rank_max,
                    "rank_min": i.rank_min,
                }
                for i in row.rank_rate_list
            ]
            data["price_range"] = {k: quantize_amount(v, 2) for k, v in fair.get_rule_price().items()}

            cls(row.act_id).set(json.dumps(data, cls=JsonEncoder))

    @classmethod
    def format_act_min_max(cls, act):
        from app.business.p2p.utils import P2pUtils

        """
        配置0，不显示，即活动没有广告单限额
        仅配置最小值，限额为最小值~默认配置最大值
        仅最大值，限额为默认配置最小值~最大值
        """
        system_min, system_max = P2pUtils.get_base_default_adv_limit(act.asset, act.fiat)  # 单位 base
        min_, max_ = act.min_limit, act.max_limit
        if act.min_limit and not act.max_limit:
            max_ = system_max
        elif act.max_limit and not act.min_limit:
            min_ = system_min
        return min_, max_

    def read_by_lang(self, lang: Language):
        value = self.get()
        if not value:
            return {}
        data = json.loads(value)
        lang_map = data.pop("lang_map", {})
        image_key = data.get("image_key")
        data["file_url"] = AWSBucketPublic.get_file_url(image_key) if image_key else ""
        lang_data = lang_map.get(lang.name) or lang_map.get(Language.DEFAULT.name)
        if lang_data:
            data.update(lang_data)
        return data


class P2pSiteFairPriceCache(HashCache):
    """最近3天所有p2p订单交易区的平均价格"""
    DAYS = 3
    ORDER_LIMIT = 100
    SPECIAL_FIAT_LIMIT = {"MLC": 10}

    def __init__(self):
        super().__init__(None)

    @classmethod
    def reload(cls):
        from app.business.prices import PriceManager

        usdt = DEFAULT_ASSET_USDT
        o_model = P2pOrder
        orders = o_model.query.filter(
            o_model.created_at >= today_datetime() - timedelta(days=cls.DAYS),
            o_model.status == o_model.Status.FINISHED,
        ).all()
        fiat_orders_map = defaultdict(list)
        for order in orders:
            # 将其他交易区的币种转换为 usd
            price = order.price
            if order.base != usdt:
                asset_usd = PriceManager.asset_to_usd(order.base)
                if not asset_usd:
                    continue
                price = order.price / asset_usd
            fiat_orders_map[order.quote].append(price)

        data = {}
        # 获取所有法币的精度
        decimals_map = P2pFiat.get_all_precision()
            
        for fiat, prices in fiat_orders_map.items():
            limit = cls.SPECIAL_FIAT_LIMIT.get(fiat, cls.ORDER_LIMIT)
            if (length := len(prices)) >= limit:
                avg_price = sum(prices) / Decimal(length)
                data[fiat] = amount_to_str(avg_price, decimals_map.get(fiat, 4))
        cls().save(data)
        return data

    def read_fiat_price(self, fiat: str) -> Decimal:
        value = self.hget(fiat)
        return Decimal(value or 0)


class P2pMarginGraceEmailCache(SetCache):
    """商家保证金限期提醒邮件缓存"""

    expired_ttl = 86400

    def __init__(self):
        stamp = str(int(today_datetime().timestamp()))
        super().__init__(stamp)

    def add_user_id(self, user_id: int):
        self.sadd(str(user_id))
        self.expire(self.expired_ttl)

    def has_user_id(self, user_id: int) -> bool:
        return self.sismember(str(user_id))


class P2pTPlusNNotifyCache(HashCache):

    def __init__(self):
        super().__init__(None)

    def has_user_change(self, user_id: int) -> bool:
        return bool(self.hget(str(user_id)))

    def del_user_notify(self, user_id: int):
        self.hdel(str(user_id))


class P2pOrderCertFileCache(HashCache):
    """近20条同渠道付款证明的订单"""
    
    model = P2pOrder
    f_model = P2pOrderFile
    ORDER_LIMIT = 50 * 10000
    FILE_LIMIT = 20
    TTL = 86400 * 30
    
    def __init__(self):
        super().__init__(None)
        
    @classmethod
    def reload(cls):
        # 取最近50w条订单
        
        num = 0
        batch = 5 * 10000
        all_orders = []
        tmp_id = 0
        while True:
            query = cls.model.query
            if tmp_id:
                query = query.filter(cls.model.id < tmp_id)
            batch_orders = query.with_entities(
                cls.model.id,
                cls.model.order_id,
                cls.model.created_at,
                cls.model.user_pay_channel_id,
                cls.model.cert_file_ids,
                cls.model.status,
            ).order_by(cls.model.id.desc()).limit(batch).all()
            if not batch_orders:
                break
            all_orders.extend(batch_orders)
            num += len(batch_orders)
            tmp_id = batch_orders[-1].id
            if num >= cls.ORDER_LIMIT:
                break
            
        user_channel_ids = set([i.user_pay_channel_id for i in all_orders])
        
        u_model = UserPayChannelMySQL
        user_pay_map = {}
        for user_c_ids in batch_iter(user_channel_ids, 10000):
            pay_rows = u_model.query.filter(u_model.mongo_id.in_(user_c_ids)).with_entities(
                u_model.mongo_id,
                u_model.pay_channel_id,
            ).all()
            user_pay_map.update({i.mongo_id: i.pay_channel_id for i in pay_rows})
        
        pay_order_map = defaultdict(list)
        for order in all_orders:
            if order.status != cls.model.Status.FINISHED:
                continue
            if not (pay_channel_id := user_pay_map.get(order.user_pay_channel_id)):
                continue
            _list = pay_order_map[pay_channel_id]
            if len(_list) >= cls.FILE_LIMIT:
                continue
            if order.cert_file_ids and (f_ids := json.loads(order.cert_file_ids)):
                _list.append(
                    dict(
                        id=order.id,
                        order_id=order.order_id,
                        created_at=order.created_at,
                        cert_file_ids=f_ids,
                    )
                )
                
        save_data = {k: json.dumps(v, cls=JsonEncoder) for k, v in pay_order_map.items() if v}
        cls().save(save_data)
        cls().expire(cls.TTL)
        return all_orders

    def get_channel_orders(self, pay_channel_id: str) -> list[str]:
        value = self.hget(pay_channel_id)
        if not value:
            return []
        return json.loads(value)