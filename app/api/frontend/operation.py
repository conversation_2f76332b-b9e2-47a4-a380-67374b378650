# -*- coding: utf-8 -*-
import json
from enum import Enum

from flask import g
from flask_babel import gettext, force_locale
from webargs import fields

from ..common.fields import EnumField
from ..common.request import is_old_app_request
from ...business.p2p.utils import P2pUtils
from ..common import (
    Resource, Namespace, respond_with_code, json_string_success, get_request_platform, get_request_user, response_replace_host_url)
from ... import Language
from ...business import mem_cached, cached, get_request_language
from ...business.mission_center.mission import MissionBiz
from ...business.operational_market import OperationalMarketBusiness
from ...caches.operation import NewerGuideCache, BlogDisplayCache, \
    CurrentPortraitHashCache, NotificationBarCache, PageInsetCache, \
    CharityBannerCache, CharityActivitiesCache, MarketBannerCache, MarketBannerCodeCache, \
    AssetCirculationHistoryCache, CharityVideoCache, CharityFootprintCache, InsetConfigCache, \
    PerpetualActivityCache, TipBar<PERSON><PERSON>, D<PERSON><PERSON>allingCache, P2pActivityBannerCache, SNSConfCache, \
    AssetMaintainConfigCache
from ...exceptions import InvalidArgument
from ...models import (
    LoginPageMarket,
    NewerGuide,
    NotificationBar,
    CharityDonationData,
    Blog,
    Portrait,
    CharityFootprint,
    CharityCategory,
    db,
    PageInset, TipBar, CharityFootprintCategory, CharityFootprintContent, NewInsetConfigContent, DynamicFalling,
    P2pActivity, User, P2pUser, FooterConfig, NewInsetConfig, AssetMaintain, )

ns = Namespace('Operation')


@ns.route('/login-page-market')
@respond_with_code
class LoginPageMarketResource(Resource):

    @classmethod
    @cached(60)
    def get(cls):
        """登录页市场"""
        return OperationalMarketBusiness.list_all_open_records(LoginPageMarket)


@ns.route('/newer-guide')
@respond_with_code
class NewerGuideResource(Resource):

    @classmethod
    def get(cls):
        """首页-新手指引"""
        lang = Language(lang) if (lang := g.get('lang')) else Language.DEFAULT
        if lang not in NewerGuide.AVAILABLE_LANGS:
            lang = Language.DEFAULT
        ret = cls._get_res(lang.name)
        return ret

    @classmethod
    @mem_cached(300)
    def _get_res(cls, lang):
        cache = NewerGuideCache(lang)
        if data := cache.read():
            return json_string_success(data)
        return {}


@ns.route('/blog-display')
@respond_with_code
class BlogDisplayResource(Resource):

    @classmethod
    def get(cls):
        """首页博客"""
        lang = Language(lang) if (lang := g.get('lang')) else Language.DEFAULT
        if lang not in Blog.AVAILABLE_LANGS:
            return []
        ret = cls._get_res(lang.name)
        return ret

    @classmethod
    @mem_cached(300)
    def _get_res(cls, lang):
        cache = BlogDisplayCache(lang)
        if data := cache.read():
            return json_string_success(data)
        return {}


@ns.route('/theme')
@respond_with_code
class ThemeResource(Resource):
    model = Portrait
    """主题：是节日头像需求催生的类，准备以后保存所有装饰信息"""

    @classmethod
    def get(cls):
        default_dict = {
            "portrait": "",
            "night_url": "",
            "day_border_url": "",
            "night_border_url": "",
        }
        cache_dict = CurrentPortraitHashCache().read()
        if cache_dict:
            if cls.model.Platform.ALL.name in cache_dict:
                portrait_data = cache_dict[cls.model.Platform.ALL.name]
            else:
                platform = cls.model.Platform.APP if get_request_platform().is_mobile() else cls.model.Platform.WEB
                portrait_data = cache_dict.get(platform.name, default_dict)
        else:
            portrait_data = default_dict
        portrait = portrait_data.pop("file_url", "")
        return {
            **portrait_data,
            'portrait': portrait  # 兼容老版本服务
        }


@ns.route('/notification-bars')
@respond_with_code
class NotificationBarsResource(Resource):
    model = NotificationBar

    @classmethod
    @response_replace_host_url
    def get(cls):
        model = cls.model
        platform = (model.Platform.APP if get_request_platform().is_mobile() else model.Platform.WEB)
        lang = Language(lang) if (lang := g.get('lang')) else Language.DEFAULT
        if data := NotificationBarCache(platform, lang).read():
            return json_string_success(data)
        return []


@ns.route('/tip-bars')
@respond_with_code
class TipBarResource(Resource):

    @classmethod
    @response_replace_host_url
    def get(cls):
        if is_old_app_request(0, 83):
            # IOS旧版本存在兼容问题，83之前返回[]
            return []
        platform = (TipBar.Platform.APP if get_request_platform().is_mobile()
                    else TipBar.Platform.WEB)
        lang = Language(lang) if (lang := g.get('lang')) else Language.DEFAULT
        if lang not in TipBar.AVAILABLE_LANGS:
            lang = Language.DEFAULT
        user_id = user.id if (user := get_request_user()) else None
        result = TipBarCache(platform, lang).filter_by_user(user_id)
        return result


@ns.route('/asset-maintain')
@respond_with_code
class AssetMaintainResource(Resource):

    @classmethod
    @response_replace_host_url
    def get(cls):
        platform = (AssetMaintain.Platform.APP if get_request_platform().is_mobile()
                    else AssetMaintain.Platform.WEB)
        maintain_types = cls._get_maintain_type_trans()
        if data := AssetMaintainConfigCache(platform).read():
            data = json.loads(data)
            if is_old_app_request(0, 83):
                for row in data:  # IOS 83版本不返回STRATEGY_ASSET_LIST
                    row['tag_pages'] = [i for i in row['tag_pages'] if i != 'STRATEGY_ASSET_LIST']
            return dict(data=data, maintain_types=maintain_types)
        return dict(data=[], maintain_types=maintain_types)

    @classmethod
    def _get_maintain_type_trans(cls) -> dict:
        lang = Language(lang) if (lang := g.get('lang')) else Language.DEFAULT
        if lang not in TipBar.AVAILABLE_LANGS:
            lang = Language.DEFAULT
        with force_locale(lang.value):
            return {
                AssetMaintain.MaintainType.ST.name: gettext(
                    "请注意：该资产符合[r]CoinEx ST规则[/r]，已进入观察期，可能存在下架风险，请注意防范风险"),
                AssetMaintain.MaintainType.OFFLINE.name: gettext(
                    "请注意：CoinEx已启动该资产的下架流程，[r]请参考公告了解详情[/r] >>"),
                AssetMaintain.MaintainType.MAINTAIN.name: gettext(
                    "请注意：CoinEx已启动该资产的维护流程，[r]请参考公告了解详情[/r] >>"),
                AssetMaintain.MaintainType.SHORT_TERM_RISK.name: gettext(
                    "风险提示：该资产近期存在负面新闻或价格异常波动，请谨慎评估投资风险。"),
            }


@ns.route('/charity/banner')
@respond_with_code
class CharityBannerResource(Resource):

    @classmethod
    @mem_cached(180)
    def get_banner_cache_data(cls, lang: str):
        return CharityBannerCache(Language[lang]).read()

    @classmethod
    def get(cls):
        lang = get_request_language()
        if lang not in [Language.ZH_HANS_CN, Language.EN_US]:
            lang = Language.DEFAULT
        cache_data = cls.get_banner_cache_data(lang.name)
        return json_string_success(str(cache_data))


@ns.route('/charity/donation')
@respond_with_code
class CharityDonationResource(Resource):

    @classmethod
    @mem_cached(180)
    def get(cls):
        return {
            k.name.lower(): v for k, v in CharityDonationData.query.with_entities(
                CharityDonationData.data_type,
                CharityDonationData.data_value
            ).all()
        }


@ns.route('/charity/activity')
@respond_with_code
class CharityActivityResource(Resource):

    @classmethod
    @mem_cached(180)
    def get_activity_cache_data(cls, lang: str):
        cache_data = CharityActivitiesCache(Language[lang]).read()
        if not cache_data:
            return []
        return cache_data

    @classmethod
    def get(cls):
        lang = get_request_language()
        if lang not in [Language.EN_US, Language.ZH_HANS_CN]:
            lang = Language.DEFAULT
        cache_data = cls.get_activity_cache_data(lang.name)
        return json_string_success(str(cache_data))


@ns.route('/charity/video')
@respond_with_code
class CharityVideoResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        category_id=fields.Integer(),
    ))
    def get(cls, **kwargs):
        lang = get_request_language()
        if lang not in [Language.ZH_HANS_CN, Language.EN_US]:
            lang = Language.DEFAULT
        cache_data = cls.get_data(lang.name, kwargs.get('category_id'))
        return json_string_success(str(cache_data))

    @classmethod
    @mem_cached(180)
    def get_data(cls, lang: str, category_id: int):
        data = CharityVideoCache(Language[lang]).read()
        if not category_id:
            return data

        data = json.loads(data)
        ret = [e for e in data if e['category_id'] == category_id]
        return ret


@ns.route('/charity/category')
@respond_with_code
class CharityCategoryResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        type=EnumField(CharityCategory.Type, missing=CharityCategory.Type.FOOTPRINT),
    ))
    def get(cls, **kwargs):
        _lang = get_request_language()
        _lang = _lang if _lang in [i for i in CharityCategory.AVAILABLE_LANGS] else Language.EN_US
        if kwargs['type'] == CharityCategory.Type.FOOTPRINT:
            return cls.foot_print_res(_lang)
        elif kwargs['type'] == CharityCategory.Type.ACTIVITY_VIDEO:
            return cls.activity_video_res(_lang)
        else:
            raise InvalidArgument

    @classmethod
    def activity_video_res(cls, _lang):
        model = CharityCategory

        records = model.query.filter(
            model.status == model.Status.VALID,
            model.lang == _lang,
            model.type == CharityCategory.Type.ACTIVITY_VIDEO,
        ).order_by(
            model.sort_id.desc()
        ).all()
        items = [
            dict(
                name=item.name,
                id=item.id,
            )
            for item in records]
        return dict(
            items=items,
        )

    @classmethod
    def foot_print_res(cls, _lang):
        model = CharityFootprintCategory
        name_field = getattr(model, 'en_name') if _lang == Language.EN_US else getattr(model, 'cn_name')
        records = model.query.filter(
            model.status == model.Status.VALID,
        ).with_entities(
            model.id,
            name_field
        ).order_by(
            model.sort_id.desc()
        ).all()
        items = [
            dict(
                name=item[1],
                id=item[0],
            )
            for item in records
        ]
        return dict(items=items)


@ns.route('/charity/footprint')
@respond_with_code
class CharityFootprintResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        category_id=fields.Integer(),
    ))
    def get(cls, **kwargs):
        lang = get_request_language()
        if lang not in [Language.ZH_HANS_CN, Language.EN_US]:
            lang = Language.DEFAULT
        cache_data = cls.get_data(lang.name, kwargs.get('category_id'))
        return json_string_success(str(cache_data))

    @classmethod
    @mem_cached(180)
    def get_data(cls, lang: str, category_id: int):
        data = CharityFootprintCache(Language[lang]).read()
        if not category_id:
            return data

        data = json.loads(data)
        ret = [e for e in data if e['category_id'] == category_id]
        return ret


@ns.route('/charity/footprint/<int:id_>')
@respond_with_code
class CharityFootprintDetailResource(Resource):

    @classmethod
    def get(cls, id_):
        lang = get_request_language()
        if lang not in [Language.ZH_HANS_CN, Language.EN_US]:
            lang = Language.DEFAULT
        row = CharityFootprint.query.get(id_)
        if not row:
            raise InvalidArgument
        row.read_count += 1
        db.session.commit()
        content = CharityFootprintContent.query.filter(
            CharityFootprintContent.footprint_id == id_,
            CharityFootprintContent.lang == lang
        ).first()
        if not content:
            raise InvalidArgument

        result = dict(
            id=row.id,
            seo_title=content.seo_title,
            seo_url_keyword=content.seo_url_keyword,
            title=content.title,
            content=content.content,
            blog_id=content.blog_id,
            created_at=row.created_at,
            cover=content.cover_url,
            abstract=content.abstract,
        )
        return result


@ns.route('/market-banner')
@respond_with_code
class MarketBannerResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            code=fields.String(required=False),
        )
    )
    def get(cls, **kwargs):
        lang = Language(lang) if (lang := g.get('lang')) else Language.DEFAULT
        if code := kwargs.get("code"):
            code_cache = MarketBannerCodeCache(lang)
            if data := code_cache.hget(code):
                return json_string_success(data)

        # 返回语言默认的营销banner
        cache = MarketBannerCache(lang)
        if data := cache.read():
            return json_string_success(data)

        return dict(
            banner_id=0,
            url="",
            img_src="",
            refer_code="",
        )


@ns.route('/page-inset')
@respond_with_code
class PageImageResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        page=EnumField(PageInset.Page, required=True),
    ))
    def get(cls, **kwargs):
        """数据已经整合到任务中心页面"""
        page = kwargs["page"]
        cache = PageInsetCache(page)
        lang = Language(lang) if (lang := g.get('lang')) else Language.DEFAULT
        img_src = cache.hget(lang.name) or ""
        return {
            "img_src": img_src,
        }


@ns.route('/assets/circulation-history')
@respond_with_code
class AssetCirculationHistoryResource(Resource):

    @classmethod
    def get(cls):
        cache = AssetCirculationHistoryCache()
        ret = []
        for timestamp, circulation_usd in cache.read().items():
            ret.append([int(timestamp), circulation_usd])

        ret.sort(key=lambda e: e[0])
        return ret


@ns.route('/sns-conf')
@respond_with_code
class SNSConfResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        business=EnumField(FooterConfig.Business, missing=FooterConfig.Business.SNS_SITE),
    ))
    def get(cls, **kwargs):
        """获取 SNS 配置信息"""
        lang = Language(lang) if (lang := g.get('lang')) else Language.DEFAULT
        result = cls.get_sns_conf(kwargs['business'], lang)
        return result

    @classmethod
    @mem_cached(120)
    def get_sns_conf(cls, business, lang):
        cache = SNSConfCache(business, lang)
        res = cache.read()
        if res:
            return json_string_success(res)
        return []


@ns.route('/insets')
@respond_with_code
class InsetsResource(Resource):

    @classmethod
    def get(cls):
        lang = Language(lang) if (lang := g.get('lang')) else Language.DEFAULT
        return cls._get_res(lang)

    @classmethod
    @mem_cached(300)
    def _get_res(cls, lang):
        cache = InsetConfigCache()
        res = []
        if data := cache.read():
            json_data = json.loads(data)
            inset_ids = [i['id'] for i in json_data]
            # 按目前的业务逻辑实际上只能存在一个
            content_query = NewInsetConfigContent.query.filter(
                NewInsetConfigContent.inset_config_id.in_(inset_ids),
                NewInsetConfigContent.lang == lang
            ).all()
            content_map = {i.inset_config_id: i for i in content_query}
            for row in json_data:
                if content := content_map.get(row['id']):
                    if not content.light_pic_url:
                        continue
                    row['title'] = content.title
                    row['sub_title'] = content.sub_title
                    row['button_text'] = content.button_text
                    row['light_pic_url'] = content.light_pic_url
                    row['dark_pic_url'] = content.dark_pic_url
                    row['content_url'] = content.url
                    row['dark_background_color'] = content.dark_background_color
                    row['light_background_color'] = content.light_background_color
                    if row['content_style'] == NewInsetConfig.ContentStyle.ACTIVITY.name:
                        row['dark_title_color'] = content.dark_title_color
                        row['light_title_color'] = content.light_title_color
                        row['dark_subtitle_color'] = content.dark_subtitle_color
                        row['light_subtitle_color'] = content.light_subtitle_color
                    res.append(row)
            return res
        return res


@ns.route('/perpetual-activity')
@respond_with_code
class PerpetualActivity(Resource):

    @classmethod
    def get(cls):
        """合约交易页活动列表"""
        cache = PerpetualActivityCache()
        lang = get_request_language()
        return cache.get_contents(lang.name)


@ns.route('/dynamic-falling')
@respond_with_code
class DynamicFallingResource(Resource):
    model = DynamicFalling

    @classmethod
    def get(cls):
        req_platform = get_request_platform()
        if req_platform.is_ios():
            platform = cls.model.Platform.IOS
        elif req_platform.is_android():
            platform = cls.model.Platform.ANDROID
        elif req_platform.is_web():
            platform = cls.model.Platform.WEB
        else:
            platform = None

        if not platform:
            return {}
        if data := DynamicFallingCache(platform).read():
            return json_string_success(data)
        return {}


@ns.route("/p2p/activity")
@respond_with_code
class P2pActivityResource(Resource):

    @classmethod
    def check_user_is_merchant(cls, user: User):
        if not user:
            return False
        p2p_user = P2pUser.query.filter(
            P2pUser.user_id == user.id
        ).first()
        if not p2p_user:
            return False
        return p2p_user.is_merchant

    @classmethod
    def _get_user_show_fiats(cls, show_type: P2pActivity.ShowType, fiat: str, user: User):
        if show_type == P2pActivity.ShowType.USER:
            return [fiat]
        if not user:
            return []
        return P2pUtils.get_merchant_adv_fiats(user.id)

    @classmethod
    def get_activity_data(
            cls,
            show_type: P2pActivity.ShowType,
            lang: Language,
            platform: P2pActivity.Platform,
            is_merchant: bool,
            fiats: set[str]
    ):
        cache = P2pActivityBannerCache()
        lang_data = cache.get_lang_banner(lang.value)
        filter_data = []
        for data in lang_data:
            if data['platform'] != P2pActivity.Platform.ALL.name and data['platform'] != platform.name:
                continue
            # 商家banner 仅仅展示活动推广内容
            if show_type == P2pActivity.ShowType.MERCHANT and \
                    data['content_type'] != P2pActivity.ContentType.ACTIVITY.name:
                continue
            # 仅展示给商家 只能展示在商家后台 并且身份只能是商家
            if data['display_to_merchant'] and (not is_merchant or show_type != P2pActivity.ShowType.MERCHANT):
                continue
            if data['fiats'] and fiats and not (set(data['fiats']) & set(fiats)):
                continue
            if platform == P2pActivity.Platform.WEB and not (data['daylight_file_url'] or data['night_file_url']):
                continue
            filter_data.append(data)
        filter_data.sort(key=lambda x: x['sort_id'], reverse=True)
        return filter_data

    @classmethod
    @ns.use_kwargs(dict(
        show_type=EnumField(P2pActivity.ShowType, missing=P2pActivity.ShowType.USER),
        fiat=fields.String
    ))
    def get(cls, **kwargs):
        show_type = kwargs.get("show_type", P2pActivity.ShowType.USER)
        fiat = kwargs.get("fiat")
        if fiat and fiat not in P2pUtils.get_all_valid_fiats():
            raise InvalidArgument
        if show_type == P2pActivity.ShowType.USER and not fiat:
            raise InvalidArgument
        platform = (P2pActivity.Platform.APP if get_request_platform().is_mobile()
                    else P2pActivity.Platform.WEB)
        lang = Language(lang) if (lang := g.get('lang')) else Language.DEFAULT
        user = get_request_user()
        return cls.get_activity_data(
            show_type=kwargs['show_type'],
            lang=lang,
            platform=platform,
            is_merchant=cls.check_user_is_merchant(user),
            fiats=cls._get_user_show_fiats(show_type, fiat, user),
        )


@ns.route("/auth/page-config")
@respond_with_code
class AuthPageConfigResource(Resource):
    class ContentType(Enum):
        PAGE_INSET = "page_inset"  # 首页插画
        REWARD_CENTER = "reward_center"  # 奖励中心

    @classmethod
    @ns.use_kwargs(dict(
        channel=fields.String,
        refer_code=fields.String,
        device_id=fields.String,
        page=EnumField(PageInset.Page),
    ))
    def get(cls, **kwargs):
        """
        获取登录注册页面展示数据
        """
        lang = Language(user_lang) if (user_lang := g.get('lang')) else Language.DEFAULT
        content_data = MissionBiz.get_mission_content(kwargs, user_lang, g.get('user_tz_offset'))
        page = kwargs.get("page") or PageInset.Page.REGISTER
        if content_data and page == PageInset.Page.REGISTER:
            return dict(
                content_type=cls.ContentType.REWARD_CENTER.name,
                data=content_data
            )
        if page:
            cache = PageInsetCache(page)
            img_src = cache.hget(lang.name) or ""
            content_data = {"img_src": img_src}
            return dict(
                content_type=cls.ContentType.PAGE_INSET.name,
                data=content_data
            )
        return {}

