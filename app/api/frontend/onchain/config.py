from app.business.onchain.base import OnchainSettings

from app.common.onchain import Chain
from app.common.onchain import CHAIN_MONEY_MAPPING

from app.api.common import Resource
from app.api.common import Namespace
from app.api.common import respond_with_code
from app.api.common import get_request_user

from app.business.user import UserSettings
from app.business.site import SiteSettings

from app.models.user import User
from app.models.user import SubAccount

ns = Namespace('Config')


@ns.route('')
@respond_with_code
class OnchainConfigResource(Resource):

    @classmethod
    def get_user_enable(cls, user: User) -> bool:
        if not user:
            return True
        if UserSettings(user.id).onchain_trading_disabled_by_admin:
            return False
        sub_account = SubAccount.query.filter(
            SubAccount.user_id == user.id
        ).first()
        if not sub_account:
            return True
        return not UserSettings(sub_account.main_user_id).onchain_trading_disabled_by_admin

    @classmethod
    def get(cls):
        return dict(
            onchain_enable=SiteSettings.onchain_trading_enabled,
            user_enable=cls.get_user_enable(get_request_user()),
        )


@ns.route('/chain')
@respond_with_code
class OnchainConfigChainResource(Resource):

    @classmethod
    def get(cls):
        res = {}
        for chain, asset_monies in CHAIN_MONEY_MAPPING.items():
            res[chain.get_display_name()] = {
                'full_name': chain.get_display_full_name(),
                'monies': [{
                    'asset': asset,
                    'name': data['name'],
                    'decimals': data['decimals'],
                    'max_trade_amount': cls.get_money_asset_trade_limit(chain, asset),
                    'is_native_token': data.get('is_native_token', False),
                } for asset, data in asset_monies.items()]
            }
        return res

    @classmethod
    def get_money_asset_trade_limit(cls, chain: Chain, money_asset: str):
        return OnchainSettings.get_quantity_per_order_limit_value(chain, money_asset)
