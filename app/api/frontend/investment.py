# -*- coding: utf-8 -*-
from collections import defaultdict
from decimal import Decimal
from datetime import timed<PERSON><PERSON>
from enum import Enum

from flask import g
from flask_babel import gettext as _

from app.api.common.request import get_request_user
from app.business.equity_center.inv_increase import IncreaseEquityHelper, IncreaseEquityHourlyProc
from app.business.prices import PriceManager
from app.caches.investment import Investment7DaysEARCache, InvestmentConfigCache
from app.exceptions.basic import InvalidArgument
from app.models.equity_center import UserDailyIncEquityHistory
from app.models.investment import UserDayInterestHistory
from app.models.staking import StakingHistory
from app.utils.amount import amount_to_str, quantize_amount
from app.utils.date_ import date_to_datetime
from ..common import Namespace, Resource, respond_with_code, require_login, success, require_ip_not_only_withdrawal
from ..common.fields import mm_fields, PageField, LimitField, AssetField, EnumField, PositiveDecimalField
from ...business import SPOT_ACCOUNT_ID, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Asset<PERSON>omparator
from ...business.clients.biz_monitor import biz_monitor
from ...business.investment import BalanceTransferOperation, InvestmentHourlyProc, InvestmentUserAccountsHelper
from app.assets.asset import get_asset_config
from ...business.user import require_user_kyc, require_user_not_only_withdrawal
from ...common.events import BalanceEvent, IncreaseEvent
from ...exceptions import TwoFactorAuthenticationRequired
from ...models import db, InvestmentTransferHistory, AssetInvestmentConfig, DailyInvestmentReport
from ...utils import today
from ...utils.helper import Struct


url_prefix = "/invest"
ns = Namespace("Investment")


@ns.route("/accounts")
@respond_with_code
class AccountListResource(Resource):
    @classmethod
    @require_login
    def get(cls):
        user = g.user
        result = InvestmentUserAccountsHelper(user.id, user.is_sub_account).get_accounts_info()
        return sorted(list(result.values()), key=lambda x: AssetComparator(x["coin_type"]))


# !!!已废弃，使用 /history 接口替代
@ns.route("/accounts/history")
@respond_with_code
class AccountHistoryResource(Resource):
    model = InvestmentTransferHistory

    @classmethod
    @require_login
    @ns.use_kwargs(
        dict(
            page=PageField(),
            limit=LimitField(),
            coin_type=AssetField(),
            opt=EnumField(model.OptType, enum_by_value=True, missing=model.OptType.INTEREST),
        )
    )
    def get(cls, **kwargs):
        args = Struct(**kwargs)
        user = g.user
        helper = InvestmentUserAccountsHelper(user.id, user.is_sub_account)
        if args.opt == cls.model.OptType.INTEREST:
            result = helper.get_interest_history(
                args.page,
                args.limit,
                args.coin_type,
            )
        else:
            result = helper.get_trans_history(
                args.page,
                args.limit,
                args.coin_type,
                args.opt,
            )
        return result


@ns.route("/transfer")
class InvestmentTransferResource(Resource):
    @classmethod
    @require_login
    @ns.use_kwargs(dict(coin_type=AssetField()))
    @respond_with_code
    def get(cls, **kwargs):
        args = Struct(**kwargs)
        investment_amount = BalanceTransferOperation(
            g.user, SPOT_ACCOUNT_ID, AssetInvestmentConfig.ACCOUNT_ID, args.coin_type, Decimal()
        ).get_user_real_invest_balance()
        cfg = get_asset_config(args.coin_type)
        return dict(
            coin_type=args.coin_type,
            limit_amount=investment_amount,
            min_amount=cfg.min_order_amount,
        )

    @classmethod
    @require_login
    @ns.use_kwargs(
        dict(
            from_account=mm_fields.Integer(required=True, validate=lambda x: x in (SPOT_ACCOUNT_ID, AssetInvestmentConfig.ACCOUNT_ID)),
            to_account=mm_fields.Integer(required=True, validate=lambda x: x in (SPOT_ACCOUNT_ID, AssetInvestmentConfig.ACCOUNT_ID)),
            coin_type=AssetField(required=True),
            amount=PositiveDecimalField(required=True),
        )
    )
    def post(cls, **kwargs):
        params = Struct(**kwargs)
        if not g.auth_user.has_2fa:
            raise TwoFactorAuthenticationRequired
        if params.from_account == SPOT_ACCOUNT_ID:
            require_ip_not_only_withdrawal()
            require_user_not_only_withdrawal(g.user)
            require_user_kyc(g.user)

        with CacheLock(LockKeys.investment_balance_transfer(g.user.id)):
            db.session.rollback()
            operation = BalanceTransferOperation(
                user=g.user, transfer_from=params.from_account, transfer_to=params.to_account, asset=params.coin_type, amount=params.amount
            )
            operation.transfer()

        cls.report_biz_event(params.from_account, g.user.id)

        return success(message=_("划转成功"))

    @staticmethod
    def report_biz_event(from_account, user_id):
        if from_account == 0:
            transfer_count_event = BalanceEvent.SPOT_TO_INVESTMENT
            biz_monitor.increase_uniq_counter(IncreaseEvent.SPOT_TO_INVESTMENT_USER_COUNT, value=[user_id], with_source=True)
        else:
            transfer_count_event = BalanceEvent.INVESTMENT_TO_SPOT
        biz_monitor.increase_counter(transfer_count_event)


@ns.route("/day_rate/history")
@respond_with_code
class DayRateHistoryResource(Resource):
    @classmethod
    @ns.use_kwargs(dict(asset=AssetField(required=True), time_type=EnumField(enum=["30d", "90d", "180d", "365d"], required=True)))
    def get(cls, **kwargs):
        """查看用户理财历史收益率"""

        asset = kwargs["asset"]
        time_type = kwargs["time_type"]

        _today = today()

        if time_type == "7d":
            start_time, end_time = _today - timedelta(days=7), _today
        elif time_type == "30d":
            start_time, end_time = _today - timedelta(days=30), _today
        elif time_type == "90d":
            start_time, end_time = _today - timedelta(days=90), _today
        elif time_type == "180d":
            start_time, end_time = _today - timedelta(days=180), _today
        elif time_type == "365d":
            start_time, end_time = _today - timedelta(days=365), _today

        result = (
            DailyInvestmentReport.query.filter(
                DailyInvestmentReport.asset == asset,
                DailyInvestmentReport.report_date >= start_time,
                DailyInvestmentReport.report_date < end_time,
            )
            .order_by(DailyInvestmentReport.report_date.asc())
            .with_entities(
                DailyInvestmentReport.report_date,
                DailyInvestmentReport.day_rate,
                DailyInvestmentReport.base_rate,
                DailyInvestmentReport.seven_day_rate,
            )
            .all()
        )

        ret = []
        for i in result:
            day_rate = quantize_amount(i.base_rate or i.day_rate * 365, 6)
            ret.append([i.report_date, day_rate, i.seven_day_rate])

        return ret


# !!!已废弃，兼容旧版app保留
@ns.route("/summary")
@respond_with_code
class InvestmentSummaryResource(Resource):
    @classmethod
    def get(cls):
        """
        理财专题页
        """
        today_ = today()
        day_7_rate_map = Investment7DaysEARCache().read_data()
        rate_map = cls.get_asset_show_rates()
        model = DailyInvestmentReport
        last_record = model.query.order_by(model.report_date.desc()).first()
        if not last_record:
            return {}
        last_report_date = last_record.report_date

        assets = InvestmentConfigCache().get_assets()

        result = (
            model.query.filter(
                model.report_date >= today_ - timedelta(days=30),
                model.report_date < today_,
                model.asset.in_(assets),
            )
            .order_by(model.report_date.asc())
            .with_entities(model.asset, model.report_date, model.seven_day_rate, model.investment_interest_usd, model.usd)
            .all()
        )

        # 以下代码已废弃，兼容旧版app保留，其实只需返回 day_rate
        asset_usd_list, asset_rate_list, asset_item_list_map = [], [], defaultdict(list)
        seven_days_before = last_report_date - timedelta(days=7)
        all_assets = set()
        seven_day_interest_usd = seven_day_total_usd = 0
        for item in result:
            asset_item_list_map[item.asset].append(item)
            if item.report_date == last_report_date:
                asset_rate_list.append([item.asset, item.seven_day_rate])
                asset_usd_list.append([item.asset, item.usd])
            if item.report_date >= seven_days_before:
                seven_day_interest_usd += item.investment_interest_usd
                seven_day_total_usd += item.usd
            all_assets.add(item.asset)
        total_usd = sum(i[1] for i in asset_usd_list)
        asset_proportion_list = []
        asset_usd_list.sort(key=lambda x: x[1], reverse=True)
        if total_usd > 0:
            for asset, usd in asset_usd_list[:5]:
                asset_proportion_list.append([asset, amount_to_str(usd / total_usd, 4)])
        asset_rate_list.sort(key=lambda x: x[1], reverse=True)
        asset_rate_list = asset_rate_list[:10]
        assets = [i[0] for i in asset_rate_list]
        items = []
        for asset in assets:
            items.append(
                dict(
                    asset=asset,
                    day_rate=rate_map.get(asset, 0),
                    seven_day_rate=day_7_rate_map.get(asset, 0),
                    history_rates=[[i.report_date, i.seven_day_rate] for i in asset_item_list_map[asset]],
                )
            )

        average_interest_rate = 0
        if seven_day_total_usd > 0 and len(all_assets) > 0:
            average_interest_rate = quantize_amount(seven_day_interest_usd * 365 / seven_day_total_usd, 4)

        return dict(assets=items, average_rate=average_interest_rate, asset_proportion=asset_proportion_list, asset_count=len(all_assets))

    @classmethod
    def get_asset_show_rates(cls) -> dict:
        # 旧版本app使用，不展示阶梯利率
        data = InvestmentConfigCache().get_all_config()
        ret = {}
        for asset, config in data.items():
            rule_map = config.get("rule_map") or {}
            fixed_rate = rule_map.get(AssetInvestmentConfig.ConfigType.FIXED.name, {}).get("rate", 0)
            ret[asset] = Decimal(config.get("base_rate", 0)) + Decimal(fixed_rate)
        return ret


class InvestmentSummaryBase(Resource):
    @classmethod
    def get_assets_summary_info(cls, asset: str = None) -> list:
        user = get_request_user()
        if user:
            is_sub_account = user.is_sub_account
            user_eq_info = IncreaseEquityHelper.get_user_show_inc_info(user.id)
        else:
            is_sub_account = False
            user_eq_info = {}
        asset_configs = InvestmentConfigCache().get_all_config()
        if asset:
            if asset not in asset_configs:
                raise InvalidArgument(message="invalid asset")
            asset_configs = {asset: asset_configs[asset]}
        ret = []

        for asset, config in asset_configs.items():
            rule_map = config.get("rule_map") or {}
            fixed_rate = Decimal(rule_map.get(AssetInvestmentConfig.ConfigType.FIXED.name, {}).get("rate", 0))
            ladder_rule = rule_map.get(AssetInvestmentConfig.ConfigType.LADDER.name, {})
            show_ladder_rule = AssetInvestmentConfig.ladder_rule_show(ladder_rule, is_sub_account)
            base_rate = Decimal(config.get("base_rate", 0))
            # 子账号不展示阶梯加息
            rate = base_rate + fixed_rate
            ret.append(
                dict(
                    asset=asset,
                    rate=rate,
                    min_amount=config["min_amount"],
                    ladder_rule=show_ladder_rule,
                    show_rate=(rate + Decimal(show_ladder_rule.get("rate", 0))) if show_ladder_rule else rate,
                    **IncreaseEquityHelper.unpack_user_asset_incr_info(asset, user_eq_info),
                )
            )

        # 将 default_asset 排在前面，后面按 asset 字符串排序
        default_asset = ["CET", "USDT", "USDC", "BTC", "ETH"]
        ret.sort(key=lambda item: (0, default_asset.index(item["asset"])) if item["asset"] in default_asset else (1, -item["show_rate"]))
        return ret


@ns.route("/summary/new")
@respond_with_code
class InvestmentNewSummaryResource(InvestmentSummaryBase):
    @classmethod
    @ns.use_kwargs(dict(asset=mm_fields.String()))
    def get(cls, **kwargs):
        return cls.get_assets_summary_info(kwargs.get("asset"))


@ns.route("/recommend")
@respond_with_code
class InvestmentRecommendResource(InvestmentSummaryBase):
    LIMIT = 3

    @classmethod
    def get(cls):
        base_assets = ["USDT", "BTC"]
        all_assets_summary = cls.get_assets_summary_info()
        asset_summary_map = {summary["asset"]: summary for summary in all_assets_summary}
        sorted_summary = sorted(all_assets_summary, key=lambda x: x["show_rate"], reverse=True)
        for summary in sorted_summary:
            if summary["asset"] not in base_assets:
                base_assets.append(summary["asset"])
                if len(base_assets) >= cls.LIMIT:
                    break
        ret = []
        for asset in base_assets:
            ret.append(asset_summary_map[asset])
        return ret


@ns.route("/history")
@respond_with_code
class InvestmentHistoryResource(Resource):
    fixed_limit = 20
    TRANS_MODEL = InvestmentTransferHistory

    class BusType(Enum):
        INVESTMENT = "理财"
        STAKING = "质押"
        ALL_INTEREST = "总利息"  # 实际查询 INTEREST 和 INC_EQUITY
        ALL = "全部"

    class DetailType(Enum):
        INVESTMENT = "investment"
        STAKING = "staking"
        INTEREST = "interest"
        INC_EQUITY = "inc_equity"

    _type_map = {
        TRANS_MODEL.OptType.IN: StakingHistory.Type.STAKE,
        TRANS_MODEL.OptType.OUT: StakingHistory.Type.UNSTAKE,
    }

    _investment_types = [TRANS_MODEL.OptType.IN.name, TRANS_MODEL.OptType.OUT.name]
    _interest_types = [TRANS_MODEL.OptType.INTEREST.name]
    _inc_equity_types = [DetailType.INC_EQUITY.name]
    _staking_types = [item.name for item in StakingHistory.Type]

    model_map = {
        DetailType.INVESTMENT: InvestmentTransferHistory,
        DetailType.INTEREST: UserDayInterestHistory,
        DetailType.STAKING: StakingHistory,
        DetailType.INC_EQUITY: UserDailyIncEquityHistory,
    }

    class Status:
        FINISHED = "FINISHED"
        PROCESSING = "PROCESSING"
        ALL = "ALL"
        
    @classmethod
    @require_login
    @ns.use_kwargs(
        dict(
            next_key=mm_fields.String,
            asset=AssetField,
            type=mm_fields.String,
            bus_type=EnumField(BusType, missing=BusType.ALL),
            limit=LimitField(missing=fixed_limit, max_limit=fixed_limit)
        )
    )
    def get(cls, **kwargs):
        """
        理财记录(支持查询理财和链上质押)
        """
        limit = kwargs["limit"]
        max_id_map = cls.parse_next_key(kwargs.get("next_key"))

        type_ = kwargs.get("type")
        all_types = cls._investment_types + cls._interest_types + cls._staking_types + cls._inc_equity_types
        if type_ and type_ not in all_types:
            raise InvalidArgument

        asset = kwargs.get("asset")
        bus_type = kwargs.get("bus_type")

        bus_config = cls._get_business_items(bus_type, asset, type_, max_id_map, limit)

        # 合并并排序所有项目
        items, total_count = cls._merge_and_sort_items(bus_config, limit)

        # 生成下一页key
        next_key = cls._generate_next_key(items, bus_config, total_count, limit)

        # 移除id字段
        for item in items:
            item.pop("id")

        return dict(
            data=items,
            next_key=next_key,
            limit=limit,
        )

    @classmethod
    def parse_next_key(cls, key: str):
        if not key:
            return {i: 0 for i in cls.DetailType}
        parts = key.split("-")
        if len(parts) != 4 or not all(p.isdigit() for p in parts):
            raise InvalidArgument
        parts = [int(i) for i in parts]
        return dict(zip(cls.DetailType, parts))

    @classmethod
    def get_data(cls, model, business, filters, limit):
        bus_filed_map = {
            cls.DetailType.INVESTMENT: "opt_type",
            cls.DetailType.INTEREST: "",  # _process_type函数内处理，返回 INTEREST
            cls.DetailType.STAKING: "type",
            cls.DetailType.INC_EQUITY: "",  # _process_type函数内处理
        }

        def _process_status(status):
            status_map = {
                cls.DetailType.INTEREST: cls.Status.FINISHED if status == UserDayInterestHistory.Status.SUCCESS else cls.Status.PROCESSING,
                cls.DetailType.STAKING: cls.Status.FINISHED if status == StakingHistory.Status.FINISHED else cls.Status.PROCESSING,
            }
            return status_map.get(business, cls.Status.FINISHED)

        def _process_type(row):
            if business == cls.DetailType.INTEREST:
                return InvestmentTransferHistory.OptType.INTEREST.name
            if bus_type := bus_filed_map.get(business):
                return getattr(row, bus_type).name
            else:
                return business.name

        query = model.query.filter(*filters).order_by(model.id.desc())
        next_limit = limit + 1  # 多查询一条，判断是否有下一页
        rows = query.limit(next_limit).all()
        has_next = len(rows) > limit
        items = []

        interest_amount_bus = {cls.DetailType.INTEREST, cls.DetailType.INC_EQUITY}

        for item in rows[: limit]:
            amount = item.amount if business not in interest_amount_bus else item.interest_amount
            time_at = item.created_at
            if business in [cls.DetailType.INTEREST, cls.DetailType.INC_EQUITY]:
                time_at = date_to_datetime(item.report_date + timedelta(days=1))
            items.append(
                dict(
                    id=item.id,
                    created_at=time_at,
                    asset=item.asset,
                    amount=amount,
                    type=_process_type(item),
                    business=business,
                    status=_process_status(item.status),
                )
            )
        return items, has_next

    @classmethod
    def _get_items(cls, model, business, max_id, asset, type_, limit):
        """通用方法获取数据项"""

        bus_valid_types = {
            cls.DetailType.INVESTMENT: cls._investment_types,
            cls.DetailType.INTEREST: cls._interest_types,
            cls.DetailType.STAKING: cls._staking_types,
            cls.DetailType.INC_EQUITY: cls._inc_equity_types,
        }

        if type_ and type_ not in bus_valid_types[business]:
            return [], 0

        filters = [model.user_id == g.user.id]

        # 添加状态过滤条件
        if business == cls.DetailType.INVESTMENT:
            filters.append(model.status == model.StatusType.SUCCESS)
            filters.append(model.opt_type != model.OptType.INTEREST)  # todo 利息记录迁移后删除
        elif business == cls.DetailType.INTEREST:
            filters.append(model.status == model.Status.SUCCESS)
        elif business == cls.DetailType.STAKING:
            filters.append(model.status != model.Status.FAILED)
        elif business == cls.DetailType.INC_EQUITY:
            filters.append(model.status == model.Status.FINISHED)

        if max_id:
            filters.append(model.id < max_id)
        if asset:
            filters.append(model.asset == asset)
        if type_:
            if business == cls.DetailType.INVESTMENT:
                filters.append(model.opt_type == model.OptType[type_])
            elif business == cls.DetailType.STAKING:
                filters.append(model.type == model.Type[type_])

        return cls.get_data(model, business, filters, limit)

    @classmethod
    def _get_business_items(cls, bus_type, asset, type_, max_id_map, limit):
        """根据业务类型获取对应的数据项"""


        bus_list_map = {
            cls.BusType.INVESTMENT: [cls.DetailType.INVESTMENT, cls.DetailType.INTEREST, cls.DetailType.INC_EQUITY],
            cls.BusType.STAKING: [cls.DetailType.STAKING],
            cls.BusType.ALL_INTEREST: [cls.DetailType.INTEREST, cls.DetailType.INC_EQUITY],
            cls.BusType.ALL: list(cls.DetailType),
        }

        data = []
        bus_list = bus_list_map[bus_type]
        for bus in bus_list:
            data.append(
                dict(
                    bus=bus,
                    items=cls._get_items(cls.model_map[bus], bus, max_id_map[bus], asset, type_, limit),
                    max_id=max_id_map[bus],
                )
            )
        return data

    @classmethod
    def _merge_and_sort_items(cls, bus_config_data, limit):
        """合并并排序所有项目"""
        all_items = []
        has_next_list = []

        for bus_data in bus_config_data:
            items, has_next = bus_data["items"]
            all_items.extend(items)
            has_next_list.append(has_next)

        # 按创建时间排序并限制数量
        sorted_items = sorted(all_items, key=lambda x: x["created_at"], reverse=True)[: limit]

        return sorted_items, any(has_next_list)

    @classmethod
    def _generate_next_key(cls, items, bus_config_data, has_next, limit):
        """生成下一页的key"""
        if len(items) < limit or not has_next:
            return None

        # 按业务类型分组并获取最大ID
        new_max_ids = {}
        for bus_data in bus_config_data:
            bus = bus_data["bus"]
            max_id = bus_data["max_id"]
            bus_items = [item for item in items if item["business"] == bus]
            new_max_ids[bus] = bus_items[-1]["id"] if bus_items else max_id

        # 按照 DetailType 组合返回 next_key
        ordered_max_ids = [new_max_ids.get(i, 0) for i in cls.DetailType]

        return "-".join([str(i) for i in ordered_max_ids])


@ns.route("/interest/preview")
@respond_with_code
class InterestPreviewResource(Resource):
    @classmethod
    @require_login
    @ns.use_kwargs(
        dict(
            asset=AssetField(required=True),
            exist_amount=PositiveDecimalField(required=True, allow_zero=True),
            add_amount=PositiveDecimalField(required=True, allow_zero=True),
        )
    )
    def get(cls, **kwargs):
        asset = kwargs["asset"]
        exist_amount = kwargs["exist_amount"]
        add_amount = kwargs["add_amount"]
        ret = dict(
            reward_asset=asset,
            daily_interest=Decimal(),
            inc_eq_interest=Decimal(),
        )
        if not add_amount:
            return ret

        site_interest = cls.calc_site_interest_diff(asset, exist_amount, add_amount)
        inc_eq_interest = cls.calc_inc_eq_interest_diff(asset, exist_amount, add_amount)
        ret["daily_interest"] = site_interest + inc_eq_interest
        ret["inc_eq_interest"] = inc_eq_interest
        return ret

    @classmethod
    def calc_site_interest_diff(cls, asset, exist_amount, add_amount):
        config = InvestmentConfigCache().get_asset_config(asset)
        is_sub_account = g.user.is_sub_account

        proc = InvestmentHourlyProc()
        base_rate = Decimal(config.get("base_rate", 0)) / proc.YEAR_HOURS
        rule_map = config.get("rule_map") or {}
        old_interest = 0
        if exist_amount > 0:
            old_data = proc.calculate_user_hourly_interest(exist_amount, base_rate, rule_map, is_sub_account)
            old_interest = sum(i.get("amount", Decimal()) for i in old_data.values())
        new_amount = exist_amount + add_amount
        new_data = proc.calculate_user_hourly_interest(new_amount, base_rate, rule_map, is_sub_account)
        new_interest = sum(i.get("amount", Decimal()) for i in new_data.values())
        diff_daily_interest = quantize_amount((new_interest - old_interest) * 24, 8)
        return diff_daily_interest

    @classmethod
    def calc_inc_eq_interest_diff(cls, asset, exist_amount, add_amount):
        user_eq = IncreaseEquityHelper.get_user_using_inc_info(g.user.id)
        if not user_eq:
            return Decimal()

        prices = PriceManager.assets_to_usd([asset, user_eq.principal_asset])
        proc = IncreaseEquityHourlyProc()
        old_interest = 0
        if exist_amount > 0:
            old_data = proc.calculate_user_hourly_interest(exist_amount, user_eq, prices)
            old_interest = old_data.get("amount", Decimal())

        new_amount = exist_amount + add_amount
        new_data = proc.calculate_user_hourly_interest(new_amount, user_eq, prices)
        new_interest = new_data.get("amount", Decimal())
        diff_daily_interest = quantize_amount((new_interest - old_interest) * 24, 8)
        return diff_daily_interest
