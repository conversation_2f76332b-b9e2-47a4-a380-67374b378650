# -*- coding: utf-8 -*-
from datetime import timed<PERSON><PERSON>
from decimal import Decimal
from typing import List, Dict, Callable
from enum import Enum

from flask import g
from flask_babel import gettext
from sqlalchemy import func
from marshmallow import fields as mm_fields

from app.api.common.request import is_old_app_request
from app.business.equity_center.inv_increase import IncreaseEquityHelper
from app.business.lock import CacheLock, LockKeys
from app.business.prices import PriceManager
from app.models.base import db
from app.models.equity_center import EquityType, UserEquity, UserCashbackEquity, UserAirdropEquity, UserInvestIncreaseEquity
from app.exceptions import InvalidArgument, InvIncreaseAssetUsed, AppUpgradeRequired
from app.api.common import (
    Resource,
    Namespace,
    respond_with_code,
    require_login,
)
from app.api.common.fields import LimitField, PageField, EnumField
from app.models.investment import AssetInvestmentConfig
from app.utils import group_by, now, amount_to_str, quantize_amount
from app.business.equity_center.cashback import CashbackSettleHelper


ns = Namespace("RewardCenter")
url_prefix = '/reward-center'


@ns.route("/rewards")
@respond_with_code
class RewardCenterRewardsResource(Resource):

    class FrontStatus(Enum):
        PENDING = "待激活"  # 理财加息券
        RECEIVED = "已到账"  # 空投
        USING = "使用中"  # 返现
        EXPIRED = "已过期"  # 返现
        FINISHED = "已使用"  # 返现

    FRONT_STATUS_REAL_STATUS_DICT = {
        FrontStatus.PENDING: UserEquity.Status.PENDING,
        FrontStatus.RECEIVED: UserEquity.Status.FINISHED,
        FrontStatus.USING: UserEquity.Status.USING,
        FrontStatus.EXPIRED: UserEquity.Status.EXPIRED,
        FrontStatus.FINISHED: UserEquity.Status.FINISHED,
    }

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            reward_type=EnumField(EquityType),
            status=EnumField(FrontStatus),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 任务中心-我的奖励 """
        user = g.user

        model = UserEquity
        q = model.query.filter(
            model.user_id == user.id,
        ).order_by(model.id.desc())
        if reward_type := kwargs.get("reward_type"):
            q = q.filter(model.type == reward_type)
        if front_status := kwargs.get("status"):
            real_status = cls.FRONT_STATUS_REAL_STATUS_DICT[front_status]
            if front_status == cls.FrontStatus.RECEIVED:
                # 已到账，只有空投类型
                q = q.filter(
                    model.type == EquityType.AIRDROP,
                    model.status == real_status,
                )
            elif front_status == cls.FrontStatus.PENDING:
                q = q.filter(
                    model.type == EquityType.INVEST_INCREASE,
                    model.status == real_status,
                )
            elif front_status in [
                cls.FrontStatus.USING,
                cls.FrontStatus.EXPIRED,
                cls.FrontStatus.FINISHED,
            ]:
                q = q.filter(
                    model.type.in_(EquityType.need_equity_id_types()),
                    model.status == real_status,
                )
        else:
            q = q.filter(
                model.status.in_(
                    [
                        model.Status.PENDING,
                        model.Status.USING,
                        model.Status.EXPIRED,
                        model.Status.FINISHED,
                    ]
                ),
            )

        page, limit = kwargs["page"], kwargs["limit"]
        pagination = q.paginate(page, limit, error_out=False)
        rows: List[model] = pagination.items

        if is_old_app_request(4010, 102):
            # 旧版本，且有理财加息奖励，提示升级
            for row in rows:
                if row.type == EquityType.INVEST_INCREASE:
                    raise AppUpgradeRequired

        items = cls.format_equity_rows(rows)

        return dict(
            has_next=pagination.has_next,
            curr_page=pagination.page,
            count=len(items),
            data=items,
            total=pagination.total,
            total_page=pagination.pages,
        )

    @classmethod
    def format_equity_rows(cls, rows: list[UserEquity]) -> list[Dict]:
        type_format_func_map: Dict[EquityType, Callable] = {
            EquityType.AIRDROP: cls.format_airdrop,
            EquityType.CASHBACK: cls.format_cashback,
            EquityType.INVEST_INCREASE: cls.format_invest_increase,
        }
        format_items = []
        for type_, items in group_by(lambda x: x.type, rows).items():
            if func_ := type_format_func_map.get(type_):
                format_items.extend(func_(items))
        org_ids_order = [i.id for i in rows]
        format_items.sort(key=lambda x: org_ids_order.index(x["reward_id"]))
        return format_items

    @classmethod
    def format_airdrop(cls, rows: list[UserEquity]) -> list[Dict]:
        user_eq_ids = [i.id for i in rows]
        air_rows = UserAirdropEquity.query.filter(
            UserAirdropEquity.user_equity_id.in_(user_eq_ids),
        ).all()
        air_map = {i.user_equity_id: i for i in air_rows}
        items = []
        for r in rows:
            air: UserAirdropEquity = air_map[r.id]
            item = {
                'reward_id': air.user_equity_id,
                'value_type': air.airdrop_asset,
                'value': air.airdrop_amount,
                'created_at': air.created_at,
                'expired_at': None,
                'reward_type': EquityType.AIRDROP.name,
                'status': cls.FrontStatus.RECEIVED.name,
                'extra': {
                    'cost_asset': air.value_asset,
                    'cost_amount': amount_to_str(air.value_amount, 2),
                },
            }
            items.append(item)
        return items

    @classmethod
    def format_invest_increase(cls, rows: list[UserEquity]) -> list[Dict]:
        user_eq_ids = [i.id for i in rows]
        invest_rows = UserInvestIncreaseEquity.query.filter(
            UserInvestIncreaseEquity.user_equity_id.in_(user_eq_ids),
        ).all()
        invest_map = {i.user_equity_id: i for i in invest_rows}
        front_status_map = {v: k for k, v in cls.FRONT_STATUS_REAL_STATUS_DICT.items()}
        items = []
        for r in rows:
            status = r.status
            status_map = IncreaseEquityHelper.END_STATUS_MAP
            if r.status in status_map.keys() and r.finished_at <= now():
                status = status_map[r.status]
            invest: UserInvestIncreaseEquity = invest_map[r.id]
            fields = [
                "active_at",
                "assets",
                "increase_rate",
                "principal_asset",
                "principal_amount_limit",
                "usable_days",
                "investment_asset",
                "finished_at",
            ]
            extra = {field: getattr(invest, field, "") for field in fields}
            extra["business_type"] = r.business_type.name
            item = {
                "reward_id": invest.user_equity_id,
                "created_at": r.created_at,
                "expired_at": r.finished_at,
                "reward_type": EquityType.INVEST_INCREASE.name,
                "status": front_status_map[status].name,
                "extra": extra,
            }
            item["value"], item["value_type"] = IncreaseEquityHelper.format_value_and_type(invest.increase_rate)
            items.append(item)
        return items

    @classmethod
    def format_progress(cls, current: Decimal, total: Decimal, unit: str):
        progress = dict(
            total=quantize_amount(total, 2),
            current=current,
            progress=quantize_amount(current / total * 100, 2),
            unit=unit
        )
        return progress

    @classmethod
    def format_cashback(cls, rows: list[UserEquity]) -> list[Dict]:
        user_eq_ids = [i.id for i in rows]
        cb_rows = UserCashbackEquity.query.filter(
            UserCashbackEquity.user_equity_id.in_(user_eq_ids),
        ).all()
        cb_map = {i.user_equity_id: i for i in cb_rows}

        items = []
        for r in rows:
            cb: UserCashbackEquity = cb_map[r.id]
            front_status = cb.status
            if cb.status == UserCashbackEquity.Status.USING and cb.end_time <= now():
                front_status = UserCashbackEquity.Status.FINISHED if cb.cashback_amount > 0 else UserCashbackEquity.Status.EXPIRED
            item = {
                'reward_id': cb.user_equity_id,
                'value_type': cb.cost_asset,
                'value': cb.cost_amount,
                'created_at': cb.start_time,
                'expired_at': cb.end_time,
                'reward_type': EquityType.CASHBACK.name,
                'status': front_status.name,
                'extra': {
                    'remain_asset': cb.cost_asset,
                    'remain_amount': cb.remain_cost_amount,
                    'cashback_asset': cb.cashback_asset,
                    'cashback_scope': cb.cashback_scope.name,
                    'cashback_ratio': quantize_amount(cb.cashback_ratio * Decimal(100), 0),
                    'progress': cls.format_progress(cb.used_cost_amount, cb.cost_amount, cb.cost_asset)
                },
            }
            items.append(item)
        return items


@ns.route("/reward-detail")
@respond_with_code
class RewardCenterRewardDetailResource(Resource):

    class CashbackScopeDesc(Enum):
        # 返现范围
        SPOT = gettext("币币市场")
        PERPETUAL = gettext("合约市场")
        ALL = gettext("所有市场")

    @classmethod
    def format_reward_desc(cls, reward_info: dict) -> str:
        if reward_info['reward_type'] == EquityType.CASHBACK.name:
            cashback_scope = reward_info['extra']['cashback_scope']

            reward_desc = gettext(
                "1. 返现范围：%(cashback_scope)s；\n"
                "2. 返现比例：根据实际手续费金额，按%(cashback_ratio)s%%比例返还；\n"
                "3. 返现时间：符合使用条件的交易订单将在1-3小时内返还手续费；\n"
                "4. 返还形式：以 %(cashback_asset)s 形式发放到现货账户；\n"
                "5. 注意事项：\n"
                "（1）返现额度用完或返现权益到期后，无法继续使用该返现奖励；\n"
                "（2）如获得多张返现奖励，系统会按一定顺序依次消耗额度；\n"
                "（3）手续费返现奖励适用于VIP等级、CET费率折扣；\n"
                "（4）手续费返现奖励不适用于AMM市场。\n"
                "6. CoinEx保留手续费返现奖励相关规则的最终解释权。\n",
                cashback_scope=gettext(cls.CashbackScopeDesc[cashback_scope].value),
                cashback_asset=reward_info['extra']['cashback_asset'],
                cashback_ratio=quantize_amount(reward_info['extra']['cashback_ratio'], 0),
            )
        elif reward_info['reward_type'] == EquityType.AIRDROP.name:
            reward_desc = gettext("空投奖励已发放到现货账户，可划转或交易。请前往现货账户查看。")
        elif reward_info["reward_type"] == EquityType.INVEST_INCREASE.name:
            reward_desc = gettext(
                "1. 使用方式：加息权益需在有效期内手动激活，激活后方可生效。\n"
                "2. 生效时间：激活后，加息权益将于下一次计息结算生效。\n"
                "3. 注意事项：\n"
                "（1）加息权益不可叠加使用，同一时间仅可激活一个加息权益。\n"
                "（2）每次加息权益仅对应一个币种，激活后不可更改，请按需选择。\n"
                "（3）当前加息权益到期后，如需继续享受加息，请手动激活下一次有效权益。\n"
                "4. CoinEx保留活期理财加息权益相关规则的最终解释权。"
            )
        else:
            reward_desc = ""
        return reward_desc

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            reward_id=mm_fields.Integer,
        )
    )
    def get(cls, **kwargs):
        """ 任务中心-我的奖励详情 """
        user = g.user

        user_eq_id = kwargs['reward_id']
        eq: UserEquity = UserEquity.query.filter(
            UserEquity.user_id == user.id,
            UserEquity.id == user_eq_id
        ).first()
        if not eq:
            raise InvalidArgument

        detail = RewardCenterRewardsResource.format_equity_rows([eq])[0]
        detail['source'] = eq.business_type.name
        detail['source_desc'] = gettext(eq.business_type.display_name)
        detail['reward_desc'] = cls.format_reward_desc(detail)
        return detail


@ns.route("/total-reward")
@respond_with_code
class RewardCenterTotalRewardResource(Resource):
    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls):
        """ 任务中心-我的奖励总额 """
        user = g.user

        display_fiat = "USD"
        prices = PriceManager.assets_to_usd()
        amount_map = dict(
            airdrop_reward_amount=cls.get_airdrop_reward_amount(user.id, prices),
            cashback_reward_amount=cls.get_cashback_reward_amount(user.id, prices),
            inv_increase_reward_amount=cls.get_inv_increase_reward_amount(user.id),
        )

        reward_amount = sum(amount_map.values())
        return {
            "reward_asset": display_fiat,
            "reward_amount": amount_to_str(reward_amount, 2),
        }

    @classmethod
    def get_airdrop_reward_amount(cls, user_id, prices):
        air_amount_query = UserAirdropEquity.query.filter(
            UserAirdropEquity.user_id == user_id,
            UserAirdropEquity.status == UserAirdropEquity.Status.FINISHED,
        ).group_by(
            UserAirdropEquity.value_asset
        ).with_entities(
            UserAirdropEquity.value_asset,
            func.sum(UserAirdropEquity.value_amount),
        ).all()
        reward_amount = Decimal()
        for asset, amount in air_amount_query:
            reward_amount += amount * prices.get(asset)
        return reward_amount

    @classmethod
    def get_cashback_reward_amount(cls, user_id, prices):

        cashback_amount_query = UserCashbackEquity.query.filter(
            UserCashbackEquity.user_id == user_id,
            UserCashbackEquity.status.in_(
                [
                    UserCashbackEquity.Status.USING,
                    UserCashbackEquity.Status.FINISHED,
                    UserCashbackEquity.Status.EXPIRED,
                ]
            ),
        ).group_by(
            UserCashbackEquity.cost_asset
        ).with_entities(
            UserCashbackEquity.cost_asset,
            func.sum(UserCashbackEquity.cost_amount),
        ).all()
        reward_amount = Decimal()
        for asset, amount in cashback_amount_query:
            reward_amount += amount * prices.get(asset, Decimal(1))
        return reward_amount

    @classmethod
    def get_inv_increase_reward_amount(cls, user_id):
        increase_amount_amount = UserInvestIncreaseEquity.query.filter(
            UserInvestIncreaseEquity.user_id == user_id,
        ).with_entities(
            func.sum(UserInvestIncreaseEquity.increase_usd).label("amount"),
        ).scalar() or Decimal()
        return increase_amount_amount


@ns.route("/remain-cashback")
@respond_with_code
class RewardCenterRemainCashbackResource(Resource):
    REQ_SCOPES = [UserCashbackEquity.CashbackScope.SPOT.name, UserCashbackEquity.CashbackScope.PERPETUAL.name]

    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            cashback_scope=EnumField(REQ_SCOPES, required=True),
        )
    )
    def get(cls, **kwargs):
        """ 任务中心-剩余返现USD额度汇总 """
        user = g.user

        display_fiat = "USD"
        cashback_scope = kwargs['cashback_scope']
        q = UserCashbackEquity.query.filter(
            UserCashbackEquity.user_id == user.id,
            UserCashbackEquity.status == UserCashbackEquity.Status.USING,
            UserCashbackEquity.end_time > now(),
        )
        if cashback_scope == UserCashbackEquity.CashbackScope.SPOT.name:
            q = q.filter(
                UserCashbackEquity.cashback_scope.in_(
                    [UserCashbackEquity.CashbackScope.SPOT, UserCashbackEquity.CashbackScope.ALL]
                )
            )
        elif cashback_scope == UserCashbackEquity.CashbackScope.PERPETUAL.name:
            q = q.filter(
                UserCashbackEquity.cashback_scope.in_(
                    [UserCashbackEquity.CashbackScope.PERPETUAL, UserCashbackEquity.CashbackScope.ALL]
                )
            )

        rows = q.with_entities(
            UserCashbackEquity.cost_asset,
            UserCashbackEquity.cost_amount,
            UserCashbackEquity.used_cost_amount,
        ).all()
        remain_amount = Decimal()
        cost_assets = [i.cost_asset for i in rows]
        usd_rates = PriceManager.assets_to_fiat(cost_assets, display_fiat)
        for i in rows:
            usd_rate = usd_rates.get(i.cost_asset, Decimal(1))
            remain_amount += max(i.cost_amount - i.used_cost_amount, Decimal(0)) * usd_rate

        return {
            "remain_asset": display_fiat,
            "remain_amount": amount_to_str(remain_amount, 2),
        }


@ns.route("/cashback-rewards")
@respond_with_code
class CashBackRewardsResource(Resource):
    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            cashback_scope=EnumField(RewardCenterRemainCashbackResource.REQ_SCOPES, required=True),
        )
    )
    def get(cls, **kwargs):
        """ 任务中心-返现权益奖励弹窗列表 """
        user = g.user

        cashback_scope = kwargs['cashback_scope']
        q = UserCashbackEquity.query.filter(
            UserCashbackEquity.user_id == user.id,
            UserCashbackEquity.status == UserCashbackEquity.Status.USING,
            UserCashbackEquity.end_time > now(),
        )
        if cashback_scope == UserCashbackEquity.CashbackScope.SPOT.name:
            q = q.filter(
                UserCashbackEquity.cashback_scope.in_(
                    [UserCashbackEquity.CashbackScope.SPOT, UserCashbackEquity.CashbackScope.ALL]
                )
            )
        elif cashback_scope == UserCashbackEquity.CashbackScope.PERPETUAL.name:
            q = q.filter(
                UserCashbackEquity.cashback_scope.in_(
                    [UserCashbackEquity.CashbackScope.PERPETUAL, UserCashbackEquity.CashbackScope.ALL]
                )
            )

        rows: List[UserCashbackEquity] = q.order_by(UserCashbackEquity.id.desc()).limit(50).all()  # 目前不分页
        rows.sort(key=CashbackSettleHelper.equity_sort_func_for_using)

        items = []
        for cb in rows:
            item = {
                'reward_id': cb.user_equity_id,
                'value_type': cb.cost_asset,
                'value': cb.cost_amount,
                'created_at': cb.start_time,
                'expired_at': cb.end_time,
                'reward_type': EquityType.CASHBACK.name,
                'status': cb.status.name,
                'extra': {
                    'remain_asset': cb.cost_asset,
                    'remain_amount': cb.remain_cost_amount,
                    'cashback_asset': cb.cashback_asset,
                    'cashback_ratio': quantize_amount(cb.cashback_ratio * Decimal(100), 0),
                    'cashback_scope': cb.cashback_scope.name,
                    'progress': RewardCenterRewardsResource.format_progress(
                        cb.used_cost_amount, cb.cost_amount, cb.cost_asset
                    )
                },
            }
            items.append(item)

        return dict(
            data=items,
        )


@ns.route("/invest-increase-rewards")
@respond_with_code
class InvestIncreaseRewardsResource(Resource):
    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls, **kwargs):
        """ 任务中心-理财加息权益奖励弹窗列表 """
        user = g.user
        rows = UserEquity.query.filter(
            UserEquity.user_id == user.id,
            UserEquity.type == EquityType.INVEST_INCREASE,
            UserEquity.status.in_([UserEquity.Status.USING, UserEquity.Status.PENDING]),
            UserEquity.finished_at > now(),
        ).order_by(UserEquity.id.desc()).limit(50).all()

        items = RewardCenterRewardsResource.format_invest_increase(rows)
        return dict(
            data=items,
        )


@ns.route("/invest-increase-asset")
@respond_with_code
class InvestIncreaseAssetResource(Resource):
    @classmethod
    @require_login(allow_sub_account=False)
    @ns.use_kwargs(
        dict(
            user_equity_id=mm_fields.Integer(required=True),
            asset=mm_fields.String(required=True),
        )
    )
    def post(cls, **kwargs):
        """用户选择理财加息币种"""
        user = g.user
        user_equity_id = kwargs["user_equity_id"]
        asset = kwargs["asset"]

        # 验证用户权益
        with CacheLock(LockKeys.invest_increase_equity(user_equity_id)):
            db.session.rollback()

            # 检查是否已激活加息权益
            eq_model = UserEquity
            user_eq_rows = eq_model.query.filter(
                eq_model.user_id == user.id,
                eq_model.type == EquityType.INVEST_INCREASE,
                eq_model.status.in_(
                    [
                        UserEquity.Status.PENDING,
                        UserEquity.Status.USING,
                    ]
                ),
            ).all()

            user_eq = None
            for eq in user_eq_rows:
                if eq.id == user_equity_id and eq.status == UserEquity.Status.PENDING:
                    user_eq = eq
                if eq.status == UserEquity.Status.USING:
                    raise InvIncreaseAssetUsed

            if not user_eq:
                raise InvalidArgument(message="invalid user equity")

            detail_model = UserInvestIncreaseEquity
            eq_detail = detail_model.query.filter(
                detail_model.user_id == user.id,
                detail_model.user_equity_id == user_equity_id,
            ).first()

            if eq_detail.investment_asset:
                raise InvalidArgument(message=gettext("已选择加息币种，不可更改"))

            inv_assets = list(AssetInvestmentConfig.get_valid_assets() & set(eq_detail.assets))
            if asset not in inv_assets:
                raise InvalidArgument(message=gettext("币种 %(asset)s 不在适用范围内", asset=asset))

            finished_at = now() + timedelta(days=eq_detail.usable_days)
            user_eq.status = UserEquity.Status.USING
            user_eq.finished_at = finished_at
            eq_detail.investment_asset = asset
            eq_detail.active_at = now()
            eq_detail.finished_at = finished_at
            db.session.commit()
