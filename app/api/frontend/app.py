# -*- coding: utf-8 -*-
from flask import redirect, g, current_app
from webargs import fields as wa_fields
from marshmallow import fields as mm_fields, validate
from typing import Optional, List
from app.caches.push import AppAutoPushReadCache, StrategyPushReadCache

from ..common import (
    Resource, Namespace, respond_with_code,
    get_request_user_agent_parsed, require_login,
    response_replace_host_url,
)
from ..common.decorators import require_verify_sign, limit_ip_frequency
from ..common.fields import <PERSON>umField, IntEnumField, DeviceIdField
from ..common.request import (get_request_language, get_request_user, get_request_ip, get_request_platform,
                              is_old_app_request, get_request_version)
from ... import config
from ...business import UserPreferences, BusinessSettings
from ...business.alert import send_alert_notice
from ...business.analytics import save_apple_ads_info
from ...business.push_statistic import AppPushReadCountStatistic
from ...business.clients.biz_monitor import biz_monitor
from ...caches import AssetInformationCache
from ...caches.investment import InvestmentConfigCache
from ...common import Language, IncreaseEvent, LastPushReadType
from ...caches.activity import AppStartPageCache
from ...caches.operation import AppQuickEntranceListCache, \
    AppNotificationAvailableCache, AppActivateCache, NewAppActivateCache, LastPushReadTimeCache, \
    event_cache_mapping, event_user_cache_mapping, GesturePwdAvaiableUserCache, FingerPwdAvaiableUserCache, \
    FaceIDAvaiableUserCache
from ...caches.push_tag import UpdatePushTagCache
from ...exceptions import InvalidArgument
from ...models import AppVersion, db, MaskUser, OperationLog
from ...models.app import IOSSupportInformation, AppCertFingerprint
from ...models.operation import AppStartPage
from ...models.mongo.user_business import UserAfInfoMySQL
from ...models.system import AppActivate, AppQuickEntranceList, NewAppActivate
from ...utils import new_hex_token, now
from ...utils.push import PushChannel, MobilePusher, PushType
from ...utils.files import AWSBucketPublic

ns = Namespace('App')


@ns.route('/download/')
class DownloadResource(Resource):

    @classmethod
    def get(cls):
        os = get_request_user_agent_parsed().os.family.lower()
        if 'ipad' in os or 'iphone' in os:
            row: AppVersion = AppVersion.query.filter(
                AppVersion.platform == AppVersion.Platform.IOS,
                AppVersion.status == AppVersion.Status.VALID
            ).order_by(AppVersion.id.desc()).first()
        else:
            row: AppVersion = AppVersion.query.filter(
                AppVersion.platform == AppVersion.Platform.ANDROID,
                AppVersion.status == AppVersion.Status.VALID
            ).order_by(AppVersion.id.desc()).first()
        url = row.file_url if row is not None else 'https://www.coinex.com'
        return redirect(url)


@ns.route('/download/url/')
@respond_with_code
class DownloadURLResource(Resource):

    @classmethod
    def get(cls):
        platforms = AppVersion.Platform
        result = {}
        for platform, key in zip(
                (platforms.ANDROID, platforms.IOS_APPSTORE, platforms.ANDROID_SAMSUNG_GALAXY_STORE,
                 platforms.ANDROID_GOOGLE_PLAY, platforms.IOS),
                ('Android', 'iOS_appstore', 'android_galaxystore_version', 'android_googleplay_version', 'iOS')):
            if (row := AppVersion.query
                    .filter(AppVersion.platform == platform,
                            AppVersion.status == AppVersion.Status.VALID)
                    .order_by(AppVersion.id.desc())
                    .first()) is not None:
                result[key] = row.file_url
        if "iOS" in result:
            result["iOS_testflight"] = result["iOS"]
        return result


@ns.route('/setting/')
@respond_with_code
class SettingListResource(Resource):

    @classmethod
    def get(cls):
        """
        设置接口
        """
        return dict(
            upgrade_build='',
            upgrade_version='',
            upgrade_level='none',  # force, notice, none
            upgrade_desc='',
            download_url='',
            ad_url='',
            ad_start_time='',
            ad_end_time='',
            wechat_grp_url=AWSBucketPublic.get_file_url('coinex/Wechat1.jpg'),
            enable_buy_crypto=True
        )


@ns.route('/setting/new-entrances')
@respond_with_code
class NewEntranceListResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        version=IntEnumField(AppQuickEntranceList.Version,
                             enum_by_value=True,
                             missing=AppQuickEntranceList.Version.VERSION_1),
    ))
    @response_replace_host_url
    def get(cls, **kwargs):
        """
        新快捷方式入口
        """
        req_platform = get_request_platform()
        if req_platform.is_ios():
            platform = AppQuickEntranceList.Platform.IOS.name
        elif req_platform.is_android():
            platform = AppQuickEntranceList.Platform.ANDROID.name
        else:
            platform = ''

        return cls._get(kwargs["version"].value, platform)

    @classmethod
    def _get(cls, version: int, platform: str) -> List:
        version_res = AppQuickEntranceListCache().read_by_version(version)
        if not platform:
            return version_res

        result = []
        for i in version_res:
            ps = i.get("platforms", [])
            if platform in ps:
                result.append(i)
        return result


@ns.route('/setting/new-entrances/sort')
@respond_with_code
class NewEntranceSortResource(Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(
        dict(
            version=IntEnumField(
                AppQuickEntranceList.Version,
                enum_by_value=True,
                missing=AppQuickEntranceList.Version.VERSION_2)
        )
    )
    def get(cls, **kwargs):
        """ 用户快捷入口排序 """
        user_id = g.user.id
        platform = get_request_platform()
        version = kwargs['version']
        is_ios = platform.is_ios()
        match (version, is_ios):
            case (AppQuickEntranceList.Version.VERSION_3, True):
                return UserPreferences(user_id).ios_sorted_new_entrances_version3
            case (AppQuickEntranceList.Version.VERSION_3, False):
                return UserPreferences(user_id).sorted_new_entrances_version3
            case (_, True):
                return UserPreferences(user_id).ios_sorted_new_entrances
            case (_, False):
                return UserPreferences(user_id).sorted_new_entrances

    @classmethod
    @require_login
    @ns.use_kwargs(
        dict(
            id_list=wa_fields.List(wa_fields.Integer, validate=lambda x: len(x) <= 100, required=True),
            version=IntEnumField(
                    AppQuickEntranceList.Version,
                    enum_by_value=True,
                    missing=AppQuickEntranceList.Version.VERSION_2)
        )
    )
    def post(cls, **kwargs):
        """ 编辑快捷入口排序 """
        user_id = g.user.id
        platform = get_request_platform()
        version = kwargs['version']
        is_ios = platform.is_ios()
        id_list = kwargs["id_list"]
        match (version, is_ios):
            case (AppQuickEntranceList.Version.VERSION_3, True):
                UserPreferences(user_id).ios_sorted_new_entrances_version3 = id_list
            case (AppQuickEntranceList.Version.VERSION_3, False):
                UserPreferences(user_id).sorted_new_entrances_version3 = id_list
            case (_, True):
                UserPreferences(user_id).ios_sorted_new_entrances = id_list
            case (_, False):
                UserPreferences(user_id).sorted_new_entrances = id_list


class AppActivateMixin:

    @staticmethod
    def _handle_ret(ret, platform):
        lang = get_request_language()
        result = []
        for i in ret:
            if lang.value not in i['trans']:
                continue
            ps = i.get("platforms", [])
            if platform and platform not in ps:
                continue
            result.append(i)
        return result


@ns.route('/setting/app-activate')
@respond_with_code
class AppActivateResource(AppActivateMixin, Resource):

    @classmethod
    @ns.use_kwargs(dict(
        activate_type=EnumField(AppActivate.ActivateType),
    ))
    @response_replace_host_url
    def get(cls, **kwargs):
        req_platform = get_request_platform()
        if req_platform.is_ios():
            platform = AppActivate.Platform.IOS.name
        elif req_platform.is_android():
            platform = AppActivate.Platform.ANDROID.name
        else:
            platform = ''

        return cls._get(kwargs.get("activate_type"), platform)

    @classmethod
    def _get(cls, activate_type: Optional[AppActivate.ActivateType], platform: Optional[str]) -> List:
        type_res = AppActivateCache().read_by_type(activate_type)
        return cls._handle_ret(ret=type_res, platform=platform)


@ns.route('/setting/app-activate-introduction')
@respond_with_code
class NewAppActivateResource(AppActivateMixin, Resource):

    @classmethod
    @response_replace_host_url
    def get(cls):
        req_platform = get_request_platform()
        if req_platform.is_ios():
            platform = NewAppActivate.Platform.IOS.name
        elif req_platform.is_android():
            platform = NewAppActivate.Platform.ANDROID.name
        else:
            platform = ''

        return cls._get(platform)

    @classmethod
    def _get(cls, platform: Optional[str]) -> List:
        type_res = NewAppActivateCache().read_by_type(NewAppActivate.ActivateType.introduction)
        return cls._handle_ret(ret=type_res, platform=platform)


@ns.route('/setting/app-activate-publicity')
@respond_with_code
class AppActivatePublicityResource(AppActivateMixin, Resource):

    @classmethod
    @response_replace_host_url
    @ns.use_kwargs(
        dict(
            version=wa_fields.Integer(missing=NewAppActivate.Version.VERSION_2),
        )
    )
    def get(cls, **kwargs):
        req_platform = get_request_platform()
        if req_platform.is_ios():
            platform = AppActivate.Platform.IOS.name
        elif req_platform.is_android():
            platform = AppActivate.Platform.ANDROID.name
        else:
            platform = ''

        return cls._get(platform, kwargs['version'])

    @classmethod
    def _get(cls, platform: Optional[str], version) -> List:
        if is_old_app_request(3280, 49):
            type_res = AppActivateCache().read_by_type(AppActivate.ActivateType.publicity)
        else:
            type_res = NewAppActivateCache().read_by_type(NewAppActivate.ActivateType.publicity, version)
        return cls._handle_ret(ret=type_res, platform=platform)


@ns.route('/setting/start-page')
@respond_with_code
class StartPageResource(Resource):

    @classmethod
    @response_replace_host_url
    def get(cls):
        platform = AppStartPage.Platform.ANDROID
        if get_request_platform().is_ios():
            platform = AppStartPage.Platform.IOS
        elif get_request_platform().is_android():
            platform = AppStartPage.Platform.ANDROID
        lang = Language(lang) if (lang := g.get('lang')) else Language.DEFAULT
        if lang not in AppStartPage.AVAILABLE_LANGS:
            lang = Language.DEFAULT
        user_id = user.id if (user := get_request_user()) else None
        default_data = dict(
            return_url="",
            img_src="",
            frequency="ONCE",
            show_time=0,
        )

        if user and user.is_sub_account:
            return default_data
        page_rows = AppStartPageCache(platform, lang).filter_by_user(user_id)
        return page_rows[0] if page_rows else default_data


@ns.route('/push/report')
class PushReportResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            push_id=wa_fields.String(required=True),
            channel=EnumField(PushChannel, enum_by_value=True, required=True),
            info=wa_fields.Raw(required=True),
        )
    )
    def post(cls, **kwargs):
        user_id = user.id if (user := get_request_user()) else None
        push_id, channel = kwargs["push_id"], kwargs["channel"]
        info = kwargs["info"]
        if not isinstance(info, dict):
            raise InvalidArgument
        required_fields = ["device_id"]
        for field in required_fields:
            if field not in info:
                raise InvalidArgument
            if field == "device_id":
                info[field] = DeviceIdField().deserialize(info[field])
        check_field = "disable_notifications"
        platform = get_request_platform()
        if check_field in info:
            device_id = info['device_id']
            tag = biz_monitor.platform_to_event_tag(platform)
            cache = AppNotificationAvailableCache(tag.name)
            if info[check_field]:
                cache.del_device(device_id)
            else:
                cache.add_device(device_id)
        # check cert
        cert_field = "cert"
        if cert_field in info:
            cert = info[cert_field]
            result = AppCertFingerprint.query.filter(
                AppCertFingerprint.cert == cert,
                AppCertFingerprint.status == AppCertFingerprint.Status.VALID
            ).with_entities(AppCertFingerprint.id).all()
            if not result:
                send_alert_notice(f"证书检查不通过，传入证书签名为{cert}",
                                  config["ADMIN_CONTACTS"]["web_notice"],
                                  60 * 15,
                                  f"cert_check:{cert}")

        lang = g.lang

        # 添加更新推送Tag队列
        try:
            UpdatePushTagCache().add(user_id, channel, UpdatePushTagCache.Event.REPORT, lang, push_id)
        except ValueError:
            pass

        return MobilePusher().send_device_report(push_id, channel, lang, info, user_id)


@ns.route('/push/report/logout')
class PushReportLogoutResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            push_id=wa_fields.String(required=True),
            user_id=wa_fields.Integer(),
            channel=EnumField(PushChannel, enum_by_value=True, required=True),
        )
    )
    def post(cls, **kwargs):
        # APP 暂时没有加 user_id 的检查 暂时现在业务中处理，待后续优化
        user_id = kwargs.get('user_id', None)
        if not user_id:
            platform = get_request_platform()
            current_app.logger.error(f"push report logout interface not find user_id, from {platform.name}")
            return
        push_id, channel = kwargs["push_id"], kwargs["channel"]
        # 添加更新推送Tag队列
        try:
            UpdatePushTagCache().add(user_id, channel, UpdatePushTagCache.Event.LOGOUT, Language.EN_US, push_id)
        except ValueError:
            pass
        return MobilePusher().send_device_report_logout(push_id, channel, user_id)


@ns.route('/event/report')
@respond_with_code
class EventReportResource(Resource):

    event_user_cache_type_mapping = {
        GesturePwdAvaiableUserCache: OperationLog.Operation.UPDATE_APP_GESTURE_PWD_STATUS,
        FingerPwdAvaiableUserCache: OperationLog.Operation.UPDATE_APP_FINGER_PWD_STATUS,
        FaceIDAvaiableUserCache: OperationLog.Operation.UPDATE_APP_FACE_ID_STATUS,
    }

    @classmethod
    @limit_ip_frequency(30, 300)
    @require_verify_sign
    @ns.use_kwargs(dict(
        device_id=DeviceIdField(required=True),
        report_user=wa_fields.Boolean(required=False, default=False),
        events=wa_fields.List(wa_fields.Dict(), required=True)
    ))
    def put(cls, **kwargs):
        """App各种push开关相关事件上报"""
        device_id, events = kwargs['device_id'], kwargs['events']
        platform = get_request_platform()
        app_version = ""
        if platform.is_mobile():
            app_version = get_request_version()

        tag = biz_monitor.platform_to_event_tag(platform)
        for item in events:
            event = item['event']
            status = item['status']
            cache_class = event_cache_mapping.get(event)
            if not cache_class:
                continue
            cache = cache_class(tag.name)
            if status:
                cache.add_device(device_id)
            else:
                cache.del_device(device_id)

        if not kwargs.get('report_user', False):
            return {}

        user = get_request_user()
        if not user:
            raise InvalidArgument(message='should report after login')

        _now = now()  # 取上报event的时间作为操作时间，容许与实际操作时间的延迟
        for item in events:
            event = item['event']
            status = item['status']
            cache_class = event_user_cache_mapping.get(event)
            if not cache_class:
                continue
            cache = cache_class(tag.name)
            event_type = cls.event_user_cache_type_mapping.get(cache_class)

            if status:
                cache.add_device(user.id, device_id)
                if event_type:
                    cls.add_op_log(user.id, app_version, platform, _now, device_id, event_type, status='打开')
            else:
                has_del = cache.del_device(user.id, device_id)
                if event_type and has_del:  # 重复del只记录状态变更时的那一次
                    cls.add_op_log(user.id, app_version, platform, _now, device_id, event_type, status="关闭")

        return {}

    @classmethod
    def add_op_log(self, user_id, app_version, platform, op_time, device_id, event_type, status='打开'):
        OperationLog.add(user_id, event_type, {
            "app_version": app_version,
            "device_id": device_id,
            "status": status,
        }, platform)


@ns.route('/ios-support')
@respond_with_code
class IOSSupportResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            body=wa_fields.String(required=True)
        )
    )
    def post(cls, **kwargs):
        r = IOSSupportInformation(
            request_ip=get_request_ip(),
            body=kwargs["body"]
        )
        db.session_add_and_commit(r)


@ns.route("/push/submit/<business_push_id>")
@respond_with_code
class PushSubmitResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        push_type=EnumField(PushType, required=False, allow_none=True)
    ))
    def post(cls, business_push_id, **kwargs):
        """APP推送上报"""
        push_type = kwargs.get('push_type')
        if push_type == PushType.AUTO_PUSH:
            try:
                push_id = int(business_push_id)
                event = IncreaseEvent(push_id)
                biz_monitor.increase_counter(event, with_source=True)
                if push_id in [IncreaseEvent.READ_PRICE_NOTICE_SPOT_COUNT.value,
                               IncreaseEvent.READ_PRICE_NOTICE_PERPETUAL_COUNT.value]:
                    biz_monitor.increase_counter(IncreaseEvent.READ_PRICE_NOTICE_ALL_COUNT, with_source=True)
            except Exception as e:
                raise InvalidArgument(message='{}'.format(e))
        if push_type == PushType.MANUAL_PUSH or push_type is None:
            AppPushReadCountStatistic.add_to_cache(business_push_id, new_hex_token())
        if push_type == PushType.BROADCAST:
            AppAutoPushReadCache(business_push_id).pfadd(new_hex_token())
        if push_type == PushType.STRATEGY_PUSH:
            business_parse_lis = business_push_id.split(':')
            if (len(business_parse_lis) == 2 and business_parse_lis[0] == 'auto_push_strategy_send_history'
                    and business_parse_lis[1].isdigit()):
                id_ = int(business_parse_lis[1])
                StrategyPushReadCache().add(id_)

        # 记录用户最后打开推送时间
        if user := get_request_user():
            LastPushReadTimeCache(LastPushReadType.APP).set_time(user.id)


@ns.route('/dynamic-falling')
@respond_with_code
class DynamicFallingResource(Resource):

    @classmethod
    def get(cls):
        # 兼容旧版本安卓app
        return []


@ns.route('/ios-ads')
@respond_with_code
class IOSAdsResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            token=mm_fields.String(required=True),
            device_id=DeviceIdField(required=True),
        )
    )
    def post(cls, **kwargs):
        save_apple_ads_info.delay(kwargs["device_id"], kwargs["token"])
        return


@ns.route("/cert")
@respond_with_code
class AppCertResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            cert=mm_fields.String(validate=validate.Length(max=128), required=True),
        )
    )
    def post(cls, **kwargs):
        cert = kwargs["cert"]
        result = AppCertFingerprint.query.filter(
            AppCertFingerprint.cert == cert
        ).with_entities(AppCertFingerprint.id).all()
        enabled = BusinessSettings.app_cert_check_enabled
        return dict(
            cert_check_enabled=enabled,
            result=bool(result)
        )


@ns.route("/appsflyer")
@respond_with_code
class AppsflyerResource(Resource):

    @classmethod
    @require_login
    @ns.use_kwargs(
        dict(
            af_id=mm_fields.String(required=True),
            register_af_id=mm_fields.String()
        )
    )
    def post(cls, **kwargs):
        user_id = g.user.id
        if not get_request_platform().is_mobile():
            return
        current_platform = get_request_platform()
        if row := UserAfInfoMySQL.query.filter_by(user_id=user_id).first():
            row.af_id = kwargs["af_id"]
            row.platform = current_platform
            db.session.commit()
            return
        # 新用户
        if r_af_id := kwargs.get("register_af_id"):
            new_row = UserAfInfoMySQL(
                user_id=user_id,
                af_id=kwargs["af_id"],
                platform=current_platform,
                register_af_id=r_af_id,
                register_platform=current_platform,
                mask_id=MaskUser.get_mask_id(user_id),
                is_new=True
            )
            db.session.add(new_row)
            db.session.commit()


@ns.route('/invest/summary')
@respond_with_code
class AppInvestSummaryResource(Resource):
    ASSET = "USDT"

    @classmethod
    def get(cls):
        """app首页理财信息"""
        asset_info = AssetInformationCache().get_asset_data(cls.ASSET)
        logo = ""
        if asset_info:
            logo = asset_info["logo"]

        is_sub = False
        if user := get_request_user():
            is_sub = user.is_sub_account
        rate = InvestmentConfigCache().get_asset_show_rate(cls.ASSET, is_sub)

        return dict(
            asset=cls.ASSET,
            logo=logo,
            rate=rate,
        )


@ns.route('/homepage/market')
@respond_with_code
class AppHomepageMarketResource(Resource):

    @classmethod
    def get(cls):
        """
        app首页需展示 CETUSDT 市场
        """
        market_name = 'CETUSDT'
        return {
            "market_name": market_name
        }
