# -*- coding: utf-8 -*-
import copy
import datetime
import json
from collections import defaultdict
from decimal import Decimal
from json import loads as json_loads
from typing import List, Dict

from flask import g
from flask_babel import gettext as _
from flask_restx import marshal, fields
from webargs import fields as wa_fields

from app.models.quotes import UserFavoriteAsset, CoinInformation
from ..common import (
    Resource, Namespace, respond_with_code, require_login,
    get_request_user, limit_user_frequency, get_request_platform, json_string_success, limit_ip_frequency,
)
from ..common.fields import EnumField, DecimalType, LimitField, \
    TimestampMarshalField
from ... import Language
from ...assets import list_all_assets, AssetUtils, normalise_asset_code
from ...assets.asset import get_assets_min_order_amounts
from ...business import ServerClient, PerpetualServerClient, cached, mem_cached, UserPreferences
from ...business.follow_market import FavoriteAssetBusiness
from ...business.kline import aggregate_price_kline_data, AggregatePeriodType, TrendingSearchHelper
from ...caches import MarketCache, PerpetualMarketCache
from ...caches.activity import NewAssetRecentCache, NewAssetSoonCache, ToOnlineAssetCache
from ...caches.kline import (
    AssetQuotesDataCache, AssetPeriodPriceCache, AssetRankCache,
    AssetTopicsCache, TagAssetsCache, AssetTrendingSearchCache, MarketTrendingSearchCache,
    DepositWithdrawalAssetSearchCache, SearchClickCache, AssetInformationCache,
    TagInfoCache, TagQuoteDataCache, AssetRankEntryRuleCache,
)
from ...caches.prices import AssetRealTimePriceCache, AssetRealTimeRateCache
from ...caches.spot import OnlineMarketAssetCache, SpotMarketUpDownCache, SpotBuySellDistributionCache
from ...caches.report import DailyAssetAppSearchCache
from ...exceptions import InvalidArgument, AssetPriceNotFound
from ...models import DailyAssetTradeCirculationReport, UserLiquidity
from ...utils import group_by, current_timestamp, amount_to_str, today


ns = Namespace('Quotes')

# 前端行情页面主动刷新间隔
QuoteCacheTTL = 60


@ns.route('/favorite/assets')
@respond_with_code
class FavoriteAssets(Resource):

    @classmethod
    @require_login
    def get(cls):
        """用户自选币种"""
        business = FavoriteAssetBusiness(g.user.id)
        return business.get_many()

    @classmethod
    @require_login
    @limit_user_frequency(count=5, interval=2)
    @ns.use_kwargs(
        dict(
            assets=wa_fields.List(wa_fields.String(required=True), required=True)
        )
    )
    def post(cls, **kwargs):
        assets = kwargs['assets']
        user_id = g.user.id
        cls.check_to_online_asset(assets)
        business = FavoriteAssetBusiness(user_id)
        business.reset(assets)

    @classmethod
    @require_login
    @limit_user_frequency(count=5, interval=2)
    @ns.use_kwargs(
        dict(
            assets=wa_fields.List(wa_fields.String(required=True), required=True)
        )
    )
    def put(cls, **kwargs):
        assets = kwargs['assets']
        cls.check_to_online_asset(assets)
        user_id = g.user.id
        business = FavoriteAssetBusiness(user_id)
        business.add_many(assets)
        business.auto_add_follow_markets(assets)

    @classmethod
    @require_login
    @limit_user_frequency(count=5, interval=2)
    @ns.use_kwargs(
        dict(
            asset=wa_fields.String(required=True)
        )
    )
    def delete(cls, **kwargs):
        asset = kwargs['asset']
        online_assets = list_all_assets()
        if asset not in online_assets:
            raise InvalidArgument(message=f"{asset} error")
        user_id = g.user.id

        business = FavoriteAssetBusiness(user_id)
        business.delete_one(asset)
        business.auto_delete_follow_markets([asset])

    @classmethod
    def get_all_valid_assets(self):
        wallet_assets = list_all_assets()
        web_assets = AssetQuotesDataCache().hkeys()
        return set(wallet_assets) & set(web_assets)

    @classmethod
    def check_to_online_asset(cls, assets):
        lang = Language(lang) if (lang := g.get('lang')) else Language.DEFAULT
        to_online_asset = {i["asset"] for i in ToOnlineAssetCache(lang).read_data()}
        asset_set = set(assets)
        if asset_set & to_online_asset:
            raise InvalidArgument(message=_("集合竞价或倒计时中，不支持收藏"))

        online_assets = cls.get_all_valid_assets()
        if asset_set - online_assets:
            raise InvalidArgument(message=_("新币还未上线，暂不支持收藏"))


@ns.route('/own/assets')
@respond_with_code
class OwnAssets(Resource):

    @classmethod
    @require_login
    def get(cls):
        """ 用户持仓币种 """
        user_id = g.user.id
        sorted_own_assets = AssetsQuotes.get_user_own_assets(user_id)
        return dict(
            assets=[dict(asset=asset, rank=idx) for idx, asset in enumerate(sorted_own_assets)],
        )

    @classmethod
    @require_login
    @limit_user_frequency(count=5, interval=2)
    @ns.use_kwargs(
        dict(
            assets=wa_fields.List(wa_fields.String(required=True), required=True)
        )
    )
    def post(cls, **kwargs):
        """ 编辑持仓币种的排序 """
        user_id = g.user.id
        online_assets = list_all_assets()
        assets = kwargs['assets']
        assets = sorted(set(assets), key=assets.index)
        # 不能超过2048个字符，只存100个币种的顺序，没存的币种默认最后
        assets = [i for i in assets if i in online_assets][:100]
        UserPreferences(user_id).sorted_own_assets = assets


@ns.route('/asset/<asset>')
@respond_with_code
class AssetQuotes(Resource):
    @classmethod
    @ns.use_kwargs(dict(
        start=wa_fields.Integer(required=True),
        end=wa_fields.Integer(required=True),
        period=wa_fields.Integer(required=True)
    ))
    def get(cls, asset, **kwargs):
        """单个币种的k线数据"""
        online_assets = list_all_assets()
        if asset not in online_assets:
            raise InvalidArgument(message=f"asset {asset} error")
        a_period_type = AggregatePeriodType.from_interval(kwargs['period'])
        kline_data = [v for v in
                      aggregate_price_kline_data(asset, kwargs['start'], kwargs['end'],
                                                 a_period_type).items()]
        kline_data.sort(key=lambda x: x[0], reverse=True)
        asset_data = AssetQuotesDataCache().get_asset_data(asset)
        circulation = asset_data["circulation"] if asset_data else '0'
        return dict(
            klines=kline_data,
            circulations=[(v[0], Decimal(v[1]) * Decimal(circulation)) for v in kline_data]
        )


@ns.route('/asset/volatility/<asset>')
@respond_with_code
class AssetsVolatility(Resource):

    @classmethod
    def get(cls, asset):
        online_assets = list_all_assets()
        if asset not in online_assets:
            raise InvalidArgument(message=f"{asset} error")
        data = AssetPeriodPriceCache().get_asset_data(asset)
        if not data:
            raise AssetPriceNotFound
        change_rate = AssetRealTimeRateCache().get_asset_real_time_rate(asset)
        if change_rate:
            data["one_day"]["change_rate"] = change_rate
        return data


@ns.route('/asset/volatility/list')
@respond_with_code
class AssetsVolatilityList(Resource):

    @classmethod
    @limit_ip_frequency(30, 300)
    @ns.use_kwargs(dict(
        assets=wa_fields.List(wa_fields.String(required=True), required=True)
    ))
    def get(cls, **kwargs):
        assets = kwargs['assets']
        online_assets = list_all_assets()
        to_query_assets = []
        for asset in assets:
            if asset in online_assets:
                to_query_assets.append(asset)

        res = []
        prices = AssetRealTimePriceCache().get_asset_real_time_prices(to_query_assets)
        rates = AssetRealTimeRateCache().get_asset_real_time_rates(to_query_assets)
        assets_data = AssetPeriodPriceCache().get_assets_data(to_query_assets)
        for asset, data in zip(to_query_assets, assets_data):
            if not data:
                continue
            change_rate = rates.get(asset, Decimal(0))
            price_usd = prices.get(asset, Decimal(0))
            if change_rate:
                data["one_day"]["change_rate"] = change_rate
            data["one_day"]["price_usd"] = price_usd
            data['asset'] = asset
            res.append(data)
        return res


class _QuotesMixin:

    @classmethod
    def update_assets_info_by_platform(cls, assets, result, is_kline=True):
        info = cls.get_assets_info(assets)
        if info is not None:
            info = copy.deepcopy(info)
            is_app = get_request_platform().is_mobile()
            if is_app or not is_kline:  # APP 不返回k线数据
                for value_dict in info:
                    value_dict.pop("klines", None)
                    value_dict.pop("klines_30d", None)

            result.update(data=info)

    @classmethod
    @mem_cached(QuoteCacheTTL)
    def get_assets_info(cls, assets: list):
        data = []
        if assets:
            data = AssetQuotesDataCache().get_assets_data(assets)
            change_rate_dict = AssetRealTimeRateCache().get_asset_real_time_rates(assets)
            for item in data:
                item["change_rate"] = change_rate_dict.get(item["asset"], Decimal())
        return data


@ns.route('/assets')
@respond_with_code
class AssetsQuotes(_QuotesMixin, Resource):
    default_sort_type = "circulation_usd"
    sort_types = tuple(list(AssetRankCache.sort_types) + ["own", "favorite"])

    @classmethod
    @ns.use_kwargs(
        dict(
            sort_type=EnumField(sort_types, default=default_sort_type, missing=default_sort_type),
            tag_id=wa_fields.Integer(),
            query_type=EnumField(["own", 'favorite']),
            offset=wa_fields.Integer(missing=0),
            limit=wa_fields.Integer(missing=50),
        )
    )
    def get(cls, **kwargs):
        """所有币种"""
        offset = kwargs['offset']
        limit = kwargs['limit']
        sort_type = kwargs["sort_type"]
        tag_id = kwargs.get("tag_id")
        query_type = kwargs.get("query_type")

        assets = cls.get_assets(sort_type, tag_id, query_type)

        result_assets = assets[offset:offset + limit]
        result = dict(count=len(result_assets), data=result_assets, total=len(assets))
        cls.update_assets_info_by_platform(result_assets, result)

        return result

    @classmethod
    def get_assets(cls, sort_type, tag_id=None, query_type=None):
        assets = cls.get_general_assets(sort_type, tag_id)

        if query_type and query_type in ["own", "favorite"]:
            user = get_request_user(allow_none=True)
            user_id = user.id if user else None

            if user_id:
                user_assets = []
                if query_type == 'favorite':
                    user_assets = cls.get_user_favorite_asset(user_id)
                    if sort_type == "favorite":
                        assets = user_assets
                    else:
                        assets = cls.get_lists_same_elements(assets, user_assets)

                elif query_type == "own":
                    user_assets = cls.get_user_own_assets(user_id)
                    if sort_type == "own":
                        assets = user_assets
                    else:
                        assets = cls.get_lists_same_elements(assets, user_assets)

                else:
                    assets = user_assets
            else:
                assets = []
        if assets:
            wallet_on_line_assets = set(list_all_assets())
            coin_info_assets = set(AssetInformationCache().hkeys())
            coin_quote_assets = set(AssetQuotesDataCache().hkeys())
            online_assets = wallet_on_line_assets & coin_info_assets & coin_quote_assets
            assets = cls.get_lists_same_elements(assets, online_assets)
        return assets

    @classmethod
    @mem_cached(QuoteCacheTTL)
    def get_general_assets(cls, sort_type, tag_id=None):
        assets = AssetRankCache(sort_type).read_assets()
        if tag_id:
            tag_assets = TagAssetsCache().get_tag_assets(tag_id)
            assets = cls.get_lists_same_elements(assets, tag_assets)

        return assets

    @classmethod
    def get_lists_same_elements(cls, l1, l2):
        new_list = set(l1) & set(l2)
        return sorted(list(new_list), key=lambda x: l1.index(x))

    @classmethod
    def get_user_favorite_asset(cls, user_id_):
        _q = UserFavoriteAsset.query.filter(
            UserFavoriteAsset.user_id == user_id_,
            UserFavoriteAsset.status == UserFavoriteAsset.StatusType.PASSED
        ).order_by(UserFavoriteAsset.rank.asc()).all()
        return [v.asset for v in _q]

    @classmethod
    def get_user_own_assets(cls, user_id_):
        """ 获取排序后的用户持仓币种 """
        sorted_own_assets = UserPreferences(user_id_).sorted_own_assets or []

        def _sort_func(_asset: str):
            # 优先按用户设置的持仓币种排序
            if _asset in sorted_own_assets:
                return [1, sorted_own_assets.index(_asset)]
            if _asset in AssetUtils.TOP_ASSETS:
                return [2, AssetUtils.TOP_ASSETS.index(_asset)]
            return [3, _asset]

        own_assets = cls._get_user_own_assets(user_id_)
        if own_assets:
            cache = AssetInformationCache()
            online_assets = cache.get_both_wallet_and_coin_info_online_assets()
            own_assets = cls.get_lists_same_elements(own_assets, online_assets)
        return sorted(own_assets, key=lambda _asset: _sort_func(_asset))

    @classmethod
    @mem_cached(600)
    def spot_market_detail(cls):
        return MarketCache.online_markets_detail()

    @classmethod
    @cached(60 * 5)
    def _get_user_own_assets(cls, user_id_):
        """ 获取用户持仓币种 """
        client = ServerClient()
        p_client = PerpetualServerClient()
        res = client.get_user_accounts_balances(user_id_)
        min_balance = Decimal('1e-8')

        asset_balance_dic = defaultdict(Decimal)
        for account, account_v in res.items():
            for ass, bal in account_v.items():
                balance = bal['available'] + bal['frozen']
                if balance < min_balance:
                    continue
                asset_balance_dic[ass] += balance
        p_res = p_client.get_user_balances(user_id_)
        for ass, bal in p_res.items():
            balance = bal['available'] + bal['frozen']
            if balance < min_balance:
                continue
            asset_balance_dic[ass] += balance
        available_assets = set()
        assets_min_order_amount_dic = get_assets_min_order_amounts()
        for ass, bal in asset_balance_dic.items():
            min_order_amount = assets_min_order_amount_dic.get(ass, 0)
            if bal >= min_order_amount:
                available_assets.add(ass)

        r = p_client.position_pending(user_id_)
        market_list = [_v["market"] for _v in r]
        position_assets = set()
        if market_list:
            for market, _v in PerpetualMarketCache().read_aside().items():
                if market in market_list:
                    add_assets = {_v["money"], _v["stock"]}
                    if "USD" in add_assets:
                        add_assets.remove("USD")
                    position_assets |= add_assets
        # add amm assets
        markets = {v.market for v in UserLiquidity.query.filter(
            UserLiquidity.user_id == user_id_,
            UserLiquidity.liquidity > 0
        ).with_entities(UserLiquidity.market).all()}
        markets_dict = cls.spot_market_detail()
        amm_asset = set()
        for market in markets:
            if market not in markets_dict:
                continue
            _data = markets_dict[market]
            amm_asset.add(_data["base_asset"])
            amm_asset.add(_data["quote_asset"])

        return list(available_assets | position_assets | amm_asset)


@ns.route('/rank/assets')
@respond_with_code
class HomePageAssetsQuotes(_QuotesMixin, Resource):
    default_sort_type = "change_rate_asc"
    sort_types = AssetRankCache.homepage_sort_types

    exclude_assets = ('USDC', 'USDT', 'DAI', 'WBTC', 'BUSD')

    @classmethod
    @ns.use_kwargs(
        dict(
            sort_type=EnumField(sort_types, missing=default_sort_type),
            is_kline=wa_fields.Boolean(missing=True),  # 行情大数据页面不需要k线
            offset=wa_fields.Integer(missing=0),
            limit=wa_fields.Integer(missing=50),
        )
    )
    def get(cls, **kwargs):
        """首页币种排行榜"""
        offset = kwargs['offset']
        limit = kwargs['limit']
        sort_type = kwargs["sort_type"]

        assets = cls.get_assets(sort_type)

        result_assets = assets[offset:offset + limit]
        result = dict(count=len(result_assets), data=result_assets, total=len(assets))
        cls.update_assets_info_by_platform(result_assets, result, kwargs["is_kline"])

        return result

    @classmethod
    @mem_cached(QuoteCacheTTL)
    def get_assets(cls, sort_type):
        assets = AssetRankCache(sort_type).read_assets()
        if sort_type == 'change_rate_desc':  # 涨幅榜按规则过滤限制币种
            exclude_assets = cls.get_exclude_assets()
        else:
            exclude_assets = set(cls.exclude_assets)
        online_assets = set(OnlineMarketAssetCache().read()) & set(AssetQuotesDataCache().hkeys()) & set(list_all_assets())
        return [asset for asset in assets if (asset not in exclude_assets and asset in online_assets)]

    @classmethod
    def get_exclude_assets(cls) -> set:
        assets = AssetRankEntryRuleCache().read()
        exclude_assets = cls.exclude_assets
        exclude_assets = set(exclude_assets) | set(assets)
        return exclude_assets


@ns.route('/rank/restricted-assets')
@respond_with_code
class RestrictedAssetsQuotes(Resource):

    @classmethod
    def get(cls):
        """涨幅榜单被限制币种"""
        return cls._get_assets()

    @classmethod
    @mem_cached(QuoteCacheTTL)
    def _get_assets(cls):
        return AssetRankEntryRuleCache().read()


@ns.route("/tags")
@respond_with_code
class Tags(Resource):

    @classmethod
    @mem_cached(60 * 2)
    def get(cls):
        """ 所有标签，已废弃，兼容APP保留 """
        return TagAssetsCache().get_all_tags_info()


@ns.route("/tags/info")
@respond_with_code
class TagsInfo(Resource):

    @classmethod
    def get(cls):
        """ 所有标签的信息 """
        lang = lang if (lang := g.get('lang')) else Language.DEFAULT.value
        return cls._get(lang)

    @classmethod
    @mem_cached(60 * 2)
    def _get(cls, lang: str):
        tag_info_map = TagInfoCache().get_all_tags_info()
        trans_info_map = TagInfoCache.get_trans_info(list(tag_info_map), Language(lang))
        result = []
        for tag_id, tag_info in tag_info_map.items():
            tran_info = trans_info_map[tag_id]
            tag_info.update(tran_info)
            result.append(tag_info)
        result.sort(key=lambda x: x.get("rank", len(tag_info_map)))
        return result


@ns.route("/tags/data")
@respond_with_code
class TagsData(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        tag_id=wa_fields.Integer,
    ))
    @mem_cached(60 * 2)
    def get(cls, **kwargs):
        """ 所有标签的行情数据 """
        cache = TagQuoteDataCache()
        if data := cache.read():
            if tag_id := kwargs.get('tag_id'):
                json_data = json.loads(data)
                return [i for i in json_data if i['id'] == tag_id]
            else:
                return json_string_success(data)
        return []


@ns.route('/new/soon/assets')
@respond_with_code
class NewSoonAssetsQuotes(Resource):

    def get(cls):
        """
        新币专区-即将上线
        """
        lang = Language(lang) if (lang := g.get('lang')) else Language.DEFAULT
        if lang not in CoinInformation.AVAILABLE_LANGS:
            lang = Language.DEFAULT
        result = cls._get(lang.value)
        return dict(
            data=result,
            total=len(result),
        )

    @classmethod
    @mem_cached(QuoteCacheTTL)
    def _get(cls, lang):
        if data := NewAssetSoonCache(lang).read():
            return json_loads(data)
        return []


@ns.route('/new/recent/assets')
@respond_with_code
class NewRecentAssetsQuotes(Resource):

    @classmethod
    def get(cls):
        """
        新币专区-最近上线
        """
        lang = Language(lang) if (lang := g.get('lang')) else Language.DEFAULT
        if lang not in CoinInformation.AVAILABLE_LANGS:
            lang = Language.DEFAULT
        result = cls._get(lang.value)
        return dict(
            data=result,
            total=len(result),
        )

    @classmethod
    @mem_cached(QuoteCacheTTL)
    def _get(cls, lang):
        return NewAssetRecentCache(lang).read()


@ns.route("/topics")
@respond_with_code
class Topics(Resource):
    @classmethod
    def get(cls):
        """ 所有专题 """
        return AssetTopicsCache().get_all_topics()


@ns.route("/asset/trending")
@respond_with_code
class AssetTrendingResource(Resource):

    @classmethod
    @cached(QuoteCacheTTL)
    def get(cls):
        result = []
        assets_data = AssetTrendingSearchCache().get_top_assets_data()
        if not assets_data:
            return result
        assets = [i[0] for i in assets_data]
        asset_data_list = AssetQuotesDataCache().get_assets_data(assets)
        asset_data_dict = {i["asset"]: i['price_usd'] for i in asset_data_list}
        change_rate_dict = AssetRealTimeRateCache().get_asset_real_time_rates(list(asset_data_dict.keys()))
        for idx, (asset, modulus) in enumerate(assets_data):
            if asset not in asset_data_dict:
                continue
            result.append({
                "asset": asset,
                "rank": idx + 1,
                "change_rate": change_rate_dict.get(asset, Decimal()),
                "price_usd": asset_data_dict.get(asset, Decimal()),
                "modulus": modulus
            })
        return result


@ns.route("/market/trending")
@respond_with_code
class MarketTrendingResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        trade_type=EnumField([
            TrendingSearchHelper.SearchType.PERPETUAL.value,
            TrendingSearchHelper.SearchType.SPOT.value
        ], required=True)
    ))
    @mem_cached(60 * 2)
    def get(cls, **kwargs):
        return MarketTrendingSearchCache(kwargs['trade_type']).read()


@ns.route("/assets/show")
@respond_with_code
class AssetsShowResource(Resource):
    FIXED_PREV_ASSETS = ['BTC', 'ETH']
    FIXED_LAST_ASSETS = ['CET']
    EXCLUDE_ASSETS = ['USDT', 'USDC']

    @classmethod
    @cached(QuoteCacheTTL)
    def get(cls):
        trending_assets = cls._get_trending_top_2_assets()
        if not trending_assets:
            assets = cls.FIXED_PREV_ASSETS + cls.FIXED_LAST_ASSETS
        else:
            assets = cls.FIXED_PREV_ASSETS + trending_assets + cls.FIXED_LAST_ASSETS
        res = cls._get_assets_data(assets)
        return res

    @classmethod
    def _get_trending_top_2_assets(cls) -> List[str]:
        """热搜前2的币种"""
        all_exclude_assets = cls.FIXED_PREV_ASSETS + cls.FIXED_LAST_ASSETS + cls.EXCLUDE_ASSETS
        trending_assets = [x for x in AssetTrendingSearchCache().get_top_assets() if x not in all_exclude_assets]
        top_2_assets = []
        for asset in trending_assets:
            asset_data = AssetQuotesDataCache().get_asset_data(asset)
            if len(top_2_assets) >= 2:
                break
            price_usd = Decimal(asset_data.get("price_usd", '0'))
            circulation_usd = Decimal(asset_data.get("circulation_usd", '0'))
            if price_usd and circulation_usd:
                top_2_assets.append(asset)
        return top_2_assets

    @classmethod
    def _get_assets_data(cls, assets: List) -> List[Dict]:
        result = []
        asset_data_list = AssetQuotesDataCache().get_assets_data(assets)
        asset_data_dict = {i["asset"]: i for i in asset_data_list}
        change_rate_dict = AssetRealTimeRateCache().get_asset_real_time_rates(assets)
        for idx, asset in enumerate(assets):
            asset_data = asset_data_dict.get(asset, {})
            result.append({
                "asset": asset,
                "rank": idx + 1,
                "full_name": asset_data.get("full_name", ""),
                "change_rate": change_rate_dict.get(asset, "0"),
                "price_usd": asset_data.get("price_usd", "0"),
                "circulation_usd": asset_data.get("circulation_usd", "0"),
                "klines": asset_data.get("klines", []),
            })
        return result


@ns.route("/deposit/withdrawal/trending")
@respond_with_code
class DepositWithdrawalTrendingResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        type=EnumField([
            TrendingSearchHelper.SearchType.DEPOSIT.value,
            TrendingSearchHelper.SearchType.WITHDRAWAL.value
        ], required=True)
    ))
    @mem_cached(60 * 2)
    def get(cls, **kwargs):
        return DepositWithdrawalAssetSearchCache(kwargs["type"]).read()


@ns.route("/search/click")
@respond_with_code
class SearchClickResource(Resource):


    @staticmethod
    def _is_search_market(search_type):
        if search_type in [
            TrendingSearchHelper.SearchType.SPOT.value,
            TrendingSearchHelper.SearchType.PERPETUAL.value,
        ]:
            return True
        return False

    @staticmethod
    def _market_to_base_asset(search_type, market_name):
        base = ""
        try:
            match search_type:
                case TrendingSearchHelper.SearchType.SPOT.value:
                    market_info = MarketCache(market_name).dict
                    if market_info:
                        base = market_info.get("base_asset")
                case TrendingSearchHelper.SearchType.PERPETUAL.value:
                    base = PerpetualMarketCache().get_amount_asset(market_name)
        except Exception:
            return ""
        return base
    
    @classmethod
    @ns.use_kwargs(dict(
        value=wa_fields.String(required=True),
        search_type=EnumField([
            TrendingSearchHelper.SearchType.PERPETUAL.value,
            TrendingSearchHelper.SearchType.SPOT.value,
            TrendingSearchHelper.SearchType.ASSET.value
        ], required=True)
    ))
    @require_login
    def post(cls, **kwargs):
        search_type, value = kwargs["search_type"], kwargs["value"]
        # search hot top
        cache = SearchClickCache(search_type, value)
        cache.set_one(g.user.id)

        # if report value is market, process it to asset
        if cls._is_search_market(search_type):
            asset = cls._market_to_base_asset(search_type, value)
        else:
            asset = value
        if not asset:
            return
        # store search user for every day
        today_ = today()
        search_cache = DailyAssetAppSearchCache(today_, search_type, asset)
        search_cache.sadd(str(g.user.id))
        search_cache.expire(search_cache.ttl)


@ns.route('/st-check/<coin>')
@respond_with_code
class StCheckResource(Resource):

    @classmethod
    @mem_cached(300)
    def get(cls, coin):
        """币种是否被标记为st（即将下架币种）"""
        cache = AssetQuotesDataCache()
        asset_data = cache.get_asset_data(coin)
        return dict(is_st=asset_data.get('is_st', False))


class CirDealMixin:
    TOP_ASSETS_NUM = 10  # 不包含ALL(所有的币种数据加总值)的排名前十的币种
    query_model = DailyAssetTradeCirculationReport

    @classmethod
    def get_latest_date(cls) -> datetime.date:
        first_rec = cls.query_model.query.order_by(cls.query_model.report_date.desc()).first()
        report_date = first_rec.report_date
        return report_date

    @classmethod
    def _check(cls, assets: List[str]):
        all_assets = list_all_assets()
        for asset in assets:
            if asset != 'ALL':
                if asset not in all_assets:
                    raise InvalidArgument

    @classmethod
    def _extract_valid(cls, assets: List[str]):
        all_assets = list_all_assets()
        valid_assets = []
        for asset in assets:
            if asset == 'ALL' or asset in all_assets:
                valid_assets.append(asset)
        return valid_assets

    @classmethod
    def get_top_assets(cls, query_type: str, report_date: datetime.date) -> List[str]:
        if query_type == 'deal_usd':
            return cls.get_deal_top_assets(report_date)
        else:
            return cls.get_circulation_top_assets(report_date)

    @classmethod
    def get_deal_top_assets(cls, report_date: datetime.date) -> List[str]:

        records = cls.query_model.query.filter(
            cls.query_model.report_date == report_date,
            cls.query_model.asset != 'ALL'
        ).order_by(
            cls.query_model.deal_usd.desc()
        ).limit(
            cls.TOP_ASSETS_NUM
        ).with_entities(
            cls.query_model.asset
        ).all()
        assets = [i[0] for i in records]
        assets.insert(0, 'ALL')
        return assets

    @classmethod
    def get_circulation_top_assets(cls, report_date):
        exclude_assets = cls.EXCLUDE_ASSETS + ['ALL']
        records = cls.query_model.query.filter(
            cls.query_model.report_date == report_date,
            cls.query_model.asset.notin_(exclude_assets)
        ).order_by(
            cls.query_model.circulation_usd.desc()
        ).limit(
            cls.TOP_ASSETS_NUM
        ).with_entities(
            cls.query_model.asset
        ).all()
        assets = [i[0] for i in records]
        assets.insert(0, 'ALL')
        return assets

    @classmethod
    def gen_res(cls, assets: List[str], start: datetime.date, end: datetime.date) -> List[Dict]:
        records = cls.query_model.query.filter(
            cls.query_model.report_date >= start,
            cls.query_model.report_date <= end,
            cls.query_model.asset.in_(assets)
        ).all()
        return marshal(records, cls.marshal_fields)


@ns.route('/circulation/series')
@respond_with_code
class CirculationHistoryResource(Resource, CirDealMixin):
    EXCLUDE_ASSETS = ['USDT', 'USDC', 'WBTC', 'DAI', 'BUSD']  # 市值曲线展示去除稳定币和类稳定币

    marshal_fields = {
        'asset': fields.String,
        'report_date': TimestampMarshalField,
        'circulation_usd': DecimalType,
    }

    @classmethod
    @ns.use_kwargs(
        dict(
            interval=LimitField(missing=7),
            assets=wa_fields.String
        )
    )
    @cached(2 * 60)
    def get(cls, **kwargs):
        """现货资产历史流通市值"""
        latest_date = cls.get_latest_date()
        assets = kwargs.get('assets', '')
        if assets:
            assets_list = list(map(normalise_asset_code, assets.split(',')))
            cls._check(assets_list)
        else:
            assets_list = cls.get_top_assets('circulation_usd', latest_date)
        start_date = latest_date - datetime.timedelta(days=kwargs['interval'] - 1)
        result = cls.gen_res(assets_list, start_date, latest_date)
        return cls.fmt_res(result, assets_list)

    @classmethod
    def fmt_res(cls, items: List[Dict], assets: List[str]):
        group_res = group_by(lambda x: x['asset'], items)
        res = []
        for asset in assets:
            asset_items = group_res[asset]
            data = [(item['report_date'], item['circulation_usd']) for item in asset_items]
            res.append({asset: data})
        return res


@ns.route('/deal/series')
@respond_with_code
class DealHistoryResource(Resource, CirDealMixin):
    marshal_fields = {
        'asset': fields.String,
        'report_date': TimestampMarshalField,
        'deal_usd': DecimalType,
    }

    @classmethod
    @ns.use_kwargs(
        dict(
            interval=LimitField(missing=7),
            assets=wa_fields.String
        )
    )
    @cached(2 * 60)
    def get(cls, **kwargs):
        """现货资产历史成交"""
        latest_date = cls.get_latest_date()
        assets = kwargs.get('assets', '')
        if assets:
            assets_list = list(map(normalise_asset_code, assets.split(',')))
            cls._check(assets_list)
        else:
            assets_list = cls.get_top_assets('deal_usd', latest_date)
        start_date = latest_date - datetime.timedelta(days=kwargs['interval'] - 1)
        result = cls.gen_res(assets_list, start_date, latest_date)
        return cls.fmt_res(result, assets_list)

    @classmethod
    def fmt_res(cls, items: List[Dict], assets: List[str]):
        group_res = group_by(lambda x: x['asset'], items)
        res = []
        for asset in assets:
            asset_items = group_res[asset]
            data = [(item['report_date'], item['deal_usd']) for item in asset_items]
            res.append({asset: data})
        return res


@ns.route('/up-down-distribution')
@respond_with_code
class UpDownDistribution(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        interval=wa_fields.Integer(validate=lambda x: x in [1, 7, 30], required=True))
    )
    def get(cls, **kwargs):
        spot_mark_cache = SpotMarketUpDownCache().read()
        rate_dict = spot_mark_cache.get(str(kwargs["interval"]), {})
        # 聚合图表数据
        ret = []
        for i in SpotMarketUpDownCache.ALL_INTERVAL:
            str_i = str(i)
            if str_i in rate_dict:
                ret.append(len(rate_dict[str_i]))
            else:
                ret.append(0)
        return ret


@ns.route('/buy-sell-distribution')
@respond_with_code
class BuySellDistribution(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        interval=wa_fields.Integer(validate=lambda x: x in [6, 12, 24], required=True))
    )
    def get(cls, **kwargs):
        hour = 3600
        ts = current_timestamp(to_int=True)
        cache = SpotBuySellDistributionCache
        # 因为 server 生成的时候有延迟，web 记录的时候会放大延迟以保证获取数据，所以获取上一个小时的数据
        newest_ts = ts - ts % hour - hour

        cur = newest_ts
        target_ts = newest_ts - kwargs["interval"] * hour
        all_buy_gap, all_sell_gap = 0, 0
        while cur > target_ts:
            data = cache(cur).read()
            all_buy_gap += Decimal(data[cache.buy_gap])
            all_sell_gap += Decimal(data[cache.sell_gap])
            cur -= hour

        return {
            cache.buy_amount: amount_to_str(all_buy_gap, 2),
            cache.sell_amount: amount_to_str(all_sell_gap, 2),
        }


@ns.route("/real-time/assets")
@respond_with_code
class AssetsPricesResource(Resource, CirDealMixin):
    """行情页近实时价格"""

    @classmethod
    @ns.use_kwargs(dict(
        assets=wa_fields.String(required=True),     # 逗号分隔，如 `BTC,ETH`
    ))
    def get(cls, **kwargs):
        """
        return：给定币种列表近实时价格。如果给定列表中有币种不存在，则返回字典会忽略这个币种。
        """
        assets = kwargs['assets']
        assets_list = list(map(normalise_asset_code, assets.split(',')))
        return cls.get_asset_data(assets_list)

    @classmethod
    @ns.use_kwargs(dict(
        assets=wa_fields.List(wa_fields.String(), required=True),       # 币种列表，如 ["BTC", "ETH"]
    ))
    def post(cls, **kwargs):
        """
        return：给定币种列表近实时价格。如果给定列表中有币种不存在，则返回字典会忽略这个币种。
        """
        assets_list = kwargs['assets']
        assets_list = list(map(normalise_asset_code, assets_list))
        return cls.get_asset_data(assets_list)

    @classmethod
    @cached(10)
    def get_asset_data(cls, assets_list):
        if not assets_list:
            # 批量接口不报错
            return {}

        valid_assets = cls._extract_valid(assets_list)

        prices = AssetRealTimePriceCache().get_asset_real_time_prices(valid_assets)
        rates = AssetRealTimeRateCache().get_asset_real_time_rates(valid_assets)
        ret = {}
        # 如果币种未知（比如在钱包端未上架），价格和涨跌幅都返回0
        for asset in valid_assets:
            ret[asset] = {
                "price_usd": prices.get(asset, Decimal(0)),
                "change_rate": rates.get(asset, Decimal(0)),
            }
        return ret


@ns.route("/to-online/assets")
@respond_with_code
class ToOnlineAssetResource(Resource):
    """倒计时-竞价中的币种"""

    @classmethod
    @mem_cached(QuoteCacheTTL)
    def get(cls, **kwargs):
        lang = Language(lang) if (lang := g.get('lang')) else Language.DEFAULT
        return ToOnlineAssetCache(lang).read_data()


@ns.route('/assets/overview')
@respond_with_code
class AssetsQuotesOverview(_QuotesMixin, Resource):
    default_sort_type = "circulation_usd"
    sort_types = tuple(list(AssetRankCache.sort_types) + ["own", "favorite"])

    @classmethod
    @ns.use_kwargs(
        dict(
            assets=wa_fields.String(required=True),
        )
    )
    def get(cls, **kwargs):
        """币种行情信息概览-目前用于学院文章"""
        assets = kwargs['assets'].split(',')
        ret = {}
        info = cls.get_assets_info(assets)
        for item in info:
            ret[item['asset']] = {
                'asset': item['asset'],
                'price_usd': item['price_usd'],
                'full_name': item['full_name'],
                'klines': item['klines'],
            }
        return list(ret.values())