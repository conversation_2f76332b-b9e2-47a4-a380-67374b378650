# -*- coding: utf-8 -*-
import json
import os.path
from typing import Dict

from flask import g, request
from flask_restx import fields as fx_fields, marshal
from sqlalchemy import func
from webargs import fields as wa_fields
from werkzeug.utils import secure_filename

from ..common.decorators import limit_user_frequency

from ..common import (Namespace, Resource, respond_with_code, require_login)
from ..common import fields as ex_fields
from ..common.fields import EnumField
from ...assets import list_all_assets, is_pre_asset
from ...caches.kline import AssetQuotesDataCache, AssetInformationCache, AssetTagsCache, AssetPeriodPriceCache
from ...caches.prices import AssetRealTimeRateCache
from ...caches.assets import CoinIncomeRateCache
from ...common import ByteLengthEnum, Language

from ...exceptions import (
    InvalidArgument, ProjectExists, ProjectNotExists,
    ServiceUnavailable, FileTooBig, AlreadyKolError, AlreadySubmitKolApplyError,
)
from ...business import cached
from ...models import (
    CoinInformation, CoinListingApplication, db, File,
    CoinApplicationFiles, IeoApplicationFiles, IeoListingApplication, IeoAssetInformation, KolApply, KolUser, Market,
    IeoActivity,
)
from ...models.activity import LaunchMiningProject
from ...utils import AWSBucketPublic, now, new_hex_token, validate_mobile

ns = Namespace('Projects')
url_prefix = '/vote2'


@ns.route('/project')
@respond_with_code
class CoinInformationList(Resource):

    @classmethod
    @cached(120)
    def get(cls):
        """
        币种资料
        """
        cache = AssetInformationCache()
        all_assets = [id_ for id_ in cache.hkeys() if not id_.isdigit()]
        all_assets.sort()
        cache_data = cache.get_assets_data(all_assets)
        result = [
            dict(
                vote2_project_id=d.get("vote2_project_id", 0),
                short_name=d.get("short_name", ""),
                full_name=d.get("full_name", ""),
                logo=d.get("logo", ""),
                thumbnail_logo=d.get("thumbnail_logo", d.get("logo", "")),
                status=d.get('status', 'online'),
                votes=0,
            )
            for d in cache_data
        ]
        return result

    marshal_fields = {
        'vote2_project_id': fx_fields.Integer(attribute='id'),
        'short_name': fx_fields.String(attribute='code'),
        'full_name': fx_fields.String(attribute='name'),
        'logo': ex_fields.IconField(attribute='icon'),
        'status': fx_fields.String(attribute=lambda x: 'online'),
        'votes': fx_fields.Integer(attribute=lambda x: 0),
    }

    @classmethod
    @require_login
    @limit_user_frequency(count=5, interval=3600)
    @ns.use_kwargs(dict(
        logo=wa_fields.Dict(
            keys=wa_fields.String(validate=lambda x: len(x) <= ByteLengthEnum.BYTE_128),
            values=wa_fields.String(validate=lambda x: len(x) <= ByteLengthEnum.BYTE_128),
            required=True),
        short_name=wa_fields.String(required=True, validate=lambda x: len(x) <= ByteLengthEnum.BYTE_32),
        full_name=wa_fields.String(required=True, validate=lambda x: len(x) <= ByteLengthEnum.BYTE_64),
        chain_type=wa_fields.String(validate=lambda x: len(x) <= ByteLengthEnum.BYTE_32),
        issue_type=wa_fields.String(required=True, validate=lambda x: len(x) <= ByteLengthEnum.BYTE_32),
        issue_time=ex_fields.TimestampField(required=True),
        issue_amount=wa_fields.String(required=True, validate=lambda x: len(x) <= ByteLengthEnum.BYTE_64),
        total_circulate=wa_fields.String(required=True, example="流通总量", validate=lambda x: len(x) <= ByteLengthEnum.BYTE_64),
        usd_circulate=wa_fields.String(required=True, example="流通市值", validate=lambda x: len(x) <= ByteLengthEnum.BYTE_64),
        issue_price=wa_fields.List(
            wa_fields.Dict(keys=wa_fields.String(validate=lambda x: len(x) <= ByteLengthEnum.BYTE_32),
                           values=wa_fields.String(validate=lambda x: len(x) <= ByteLengthEnum.BYTE_32)),
            missing=None, example='发行价'),
        website_url=wa_fields.String(required=True, validate=lambda x: len(x) <= ByteLengthEnum.BYTE_128),
        white_paper_url=wa_fields.String(missing='', validate=lambda x: len(x) <= ByteLengthEnum.BYTE_512),
        src_url=wa_fields.String(missing='', validate=lambda x: len(x) <= ByteLengthEnum.BYTE_128),
        browser_url=wa_fields.String(missing='', validate=lambda x: len(x) <= ByteLengthEnum.BYTE_128),
        social_url=wa_fields.List(wa_fields.Dict(
            keys=wa_fields.String(validate=lambda x: len(x) <= ByteLengthEnum.BYTE_64),
            values=wa_fields.String(validate=lambda x: len(x) <= ByteLengthEnum.BYTE_512)), missing=None),
        introduction_en=wa_fields.String(missing='', default='', validate=lambda x: len(x) <= ByteLengthEnum.BYTE_65536),
        introduction_cn=wa_fields.String(missing='', default='', validate=lambda x: len(x) <= ByteLengthEnum.BYTE_65536),
        introduction=wa_fields.String(missing='', default='', validate=lambda x: len(x) <= ByteLengthEnum.BYTE_65536),
        fundraising=wa_fields.String(missing='', default='', example='募资情况', validate=lambda x: len(x) <= ByteLengthEnum.BYTE_65536),
        token_distribution=wa_fields.String(missing='', default='', example='代币分配信息',
                                            validate=lambda x: len(x) <= ByteLengthEnum.BYTE_65536),
        linkedin_or_cv=wa_fields.String(missing='', default='', example='创始人LinkedIn or CV',
                                        validate=lambda x: len(x) <= ByteLengthEnum.BYTE_512),
        applicant_name=wa_fields.String(required=True, example='申请人姓名', validate=lambda x: len(x) <= ByteLengthEnum.BYTE_128),
        applicant_role=wa_fields.String(required=True, example='申请人在项目团队角色',
                                        validate=lambda x: len(x) <= ByteLengthEnum.BYTE_128),
        applicant_email=ex_fields.EmailField(required=True, validate=lambda x: len(x) <= ByteLengthEnum.BYTE_128),
        applicant_mobile=wa_fields.String(required=True, example='申请人手机号', validate=lambda x: len(x) <= ByteLengthEnum.BYTE_64),
        vote_type=wa_fields.String(validate=lambda x: len(x) <= ByteLengthEnum.BYTE_32),
        coin_id=wa_fields.Integer(),
        securities_id=wa_fields.Integer(),
        commitment_id=wa_fields.Integer(),
        law_ids=wa_fields.List(wa_fields.Integer, missing=[]),
        audit_id=wa_fields.Integer(missing=None),
    ))
    def post(cls, **kwargs):
        """
        币种申请
        """
        # 二期前端需要改为传日期字符串
        issued_date = (kwargs['issue_time'].astimezone(g.user_tz)).date()
        if issued_date > now().date():
            raise InvalidArgument

        if CoinInformation.query.filter(
            CoinInformation.code == kwargs['short_name']
        ).first():
            raise ProjectExists

        if social_data := kwargs['social_url']:
            social_mapping = {item["media"]: item.get("url", None) for item in social_data if item.get("media")}
        else:
            social_mapping = {}

        if 'Twitter' not in social_mapping or not social_mapping.get('Twitter'):
            raise InvalidArgument

        def _check_file_exists(*ids):
            for id_ in ids:
                if id_ and not File.query.get(id_):
                    raise InvalidArgument

        # coin_id = kwargs['coin_id']
        # securities_id = kwargs['securities_id']
        # commitment_id = kwargs['commitment_id']
        # audit_id = kwargs['audit_id']
        law_ids = kwargs['law_ids']
        file_type_map = {}
        if not (icon := kwargs['logo'].get('file_key')):
            raise InvalidArgument

        # file_type_map = {
        #     coin_id: CoinApplicationFiles.FileType.COIN,
        #     securities_id: CoinApplicationFiles.FileType.SECURITIES,
        #     commitment_id: CoinApplicationFiles.FileType.COMMITMENT,
        # }
        # if audit_id:
        #     file_type_map[audit_id] = CoinApplicationFiles.FileType.AUDIT
        for law_id in law_ids:
            file_type_map[law_id] = CoinApplicationFiles.FileType.LAW
        if not validate_mobile(kwargs['applicant_mobile']):
            raise InvalidArgument(message="mobile error")
        _check_file_exists(*list(file_type_map.keys()))

        c = CoinListingApplication(
            created_by=g.user.id,
            updated_by=g.user.id,
            code=kwargs['short_name'],
            name=kwargs['full_name'],
            icon=icon,
            type=CoinListingApplication.Type.COIN
            if kwargs.get('chain_type', '') == 'public'
            else CoinListingApplication.Type.TOKEN,
            issued_date=issued_date,
            issued_price_data=json.dumps(kwargs['issue_price']),
            total_supply=kwargs.get('issue_amount', ''),
            official_website=kwargs['website_url'],
            white_paper=kwargs['white_paper_url'],
            source_code=kwargs['src_url'],
            explorer=kwargs['browser_url'],
            intro_en=kwargs.get('introduction_en', '') or kwargs.get('introduction', ''),
            intro_cn=kwargs.get('introduction_cn', '') or kwargs.get('introduction', ''),
            fundraising=kwargs['fundraising'],
            token_distribution=kwargs['token_distribution'],
            total_circulate=kwargs["total_circulate"],
            usd_circulate=kwargs["usd_circulate"],
            linkedin_or_cv=kwargs['linkedin_or_cv'],
            applicant_name=kwargs['applicant_name'],
            applicant_role=kwargs['applicant_role'],
            applicant_email=kwargs['applicant_email'],
            applicant_mobile=kwargs['applicant_mobile'],
            telegram=social_mapping.get('Telegram', ''),
            facebook=social_mapping.get('Facebook', ''),
            twitter=social_mapping.get('Twitter', ''),
            reddit=social_mapping.get('Reddit', ''),
            medium=social_mapping.get('Medium', ''),
            discord=social_mapping.get('Discord', ''),
            is_securities=True if kwargs.get('vote_type', '') == 'securities' else False,
            is_first=True if kwargs['issue_type'] == 'first' else False,
        )

        db.session.add(c)
        db.session.flush()

        for file_id, file_type in file_type_map.items():
            db.session.add(CoinApplicationFiles(
                coin_listing_application_id=c.id,
                file_id=file_id,
                name=File.query.get(file_id).name,
                user_id=g.user.id,
                file_type=file_type
            ))
        db.session.commit()
        return marshal(c, cls.marshal_fields)


@ns.route('/project/check')
@respond_with_code
class ProjectCheckResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        full_name=wa_fields.String(required=True),
    ))
    def get(cls, **kwargs):
        """
        币种检查
        """
        if CoinListingApplication.query.filter(
            CoinListingApplication.name == kwargs['full_name']
        ).first():
            return dict(repeat=True)
        return dict(repeat=False)


@ns.route('/project/idmap')
@respond_with_code
class ProjectIdMappingResource(Resource):

    @classmethod
    @cached(300)
    def get(cls):
        records = CoinInformation.query.filter(
            CoinInformation.status == CoinInformation.Status.VALID
        ).with_entities(
            CoinInformation.id,
            CoinInformation.code,
        ).all()
        return {r.code: r.id for r in records}


@ns.route('/project/<coin_str>')
@respond_with_code
class CoinInformationResource(Resource):

    @classmethod
    def get(cls, coin_str):
        data = AssetInformationCache().get_asset_data(coin_str)
        if not data:
            raise ProjectNotExists
        data['tags'] = AssetTagsCache().get_asset_tags(data["short_name"])
        cache = AssetQuotesDataCache()
        asset = data["short_name"]
        asset_data = cache.get_asset_data(asset)
        data["circulation_rank"] = asset_data.get("circulation_usd_rank", cache.hlen())
        data["volume_usd"] = asset_data.get("volume_usd", "0")
        data["is_st"] = asset_data.get("is_st", False)
        data["price_usd"] = asset_data.get("price_usd", "0")
        data["change_rate"] = AssetRealTimeRateCache().get_asset_real_time_rate(asset)
        data["is_pre"] = is_pre_asset(asset)
        lang = Language(g.lang)
        langs = [lang] if lang == Language.DEFAULT else [lang, Language.DEFAULT]
        data["translations"] = AssetInformationCache.get_info_translation(data["vote2_project_id"], *langs)
        return data


@ns.route('/project/<coin_str>/income-rate')
@respond_with_code
class CoinIncomeRateResource(Resource):

    @classmethod
    def get(cls, coin_str: str):
        return CoinIncomeRateCache().get(coin_str)


@ns.route('/upload')
@respond_with_code
class FileUploadResource(Resource):
    ALLOW_EXT_LIST = (
        ".png", ".jpg", ".jpeg", ".gif", ".csv",
        ".pdf", ".doc", ".docx", ".xlsx", ".xls",
        ".txt", ".ppt", ".pptx"
    )

    @classmethod
    def generate_file_key(cls, filename: str):
        date = now().strftime('%Y-%m-%d')
        token = new_hex_token()
        return f'{date}/{token}/{filename}'

    @classmethod
    @require_login
    @limit_user_frequency(10, 600)
    def post(cls):
        try:
            files = request.files.getlist('file[]')
        except Exception:
            raise InvalidArgument
        if int(request.headers['CONTENT_LENGTH']) > 1024 * 1024 * 10:
            raise FileTooBig
        result = []
        for file_ in files:
            ext = os.path.splitext(file_.filename)[1].lower()
            if ext not in cls.ALLOW_EXT_LIST:
                raise InvalidArgument
            filename = secure_filename(file_.filename)
            file_key = AWSBucketPublic.new_file_key(key=cls.generate_file_key(filename))
            if not AWSBucketPublic.put_file(file_key, file_):
                raise ServiceUnavailable
            f = File.new(g.user.id, file_key, filename)
            db.session.add(f)
            db.session.flush()
            result.append(dict(name=filename, url=f.static_url, id=f.id))
        db.session.commit()
        return result


@ns.route('/ieo')
@respond_with_code
class IeoInformationList(Resource):

    marshal_fields = {
        'vote2_ieo_id': fx_fields.Integer(attribute='id'),
        'short_name': fx_fields.String(attribute='code'),
        'full_name': fx_fields.String(attribute='name'),
        'logo': ex_fields.IconField(attribute='icon'),
        'status': fx_fields.String(attribute=lambda x: 'online'),
        'votes': fx_fields.Integer(attribute=lambda x: 0),
    }

    @classmethod
    @require_login
    @ns.use_kwargs(dict(
        logo=wa_fields.Dict(
            keys=wa_fields.String(validate=lambda x: len(x) <= ByteLengthEnum.BYTE_128),
            values=wa_fields.String(validate=lambda x: len(x) <= ByteLengthEnum.BYTE_128),
            required=True),
        short_name=wa_fields.String(required=True, validate=lambda x: len(x) <= ByteLengthEnum.BYTE_32),
        full_name=wa_fields.String(required=True, validate=lambda x: len(x) <= ByteLengthEnum.BYTE_64),
        chain_type=wa_fields.String(validate=lambda x: len(x) <= ByteLengthEnum.BYTE_32),
        issue_type=wa_fields.String(required=True, validate=lambda x: len(x) <= ByteLengthEnum.BYTE_32),
        issue_time=ex_fields.TimestampField(required=True),
        issue_amount=wa_fields.String(required=True, validate=lambda x: len(x) <= ByteLengthEnum.BYTE_64),
        total_circulate=wa_fields.String(required=True, example="流通总量", validate=lambda x: len(x) <= ByteLengthEnum.BYTE_64),
        usd_circulate=wa_fields.String(required=True, example="流通市值", validate=lambda x: len(x) <= ByteLengthEnum.BYTE_64),
        website_url=wa_fields.String(required=True, validate=lambda x: len(x) <= ByteLengthEnum.BYTE_128),
        white_paper_url=wa_fields.String(missing='', validate=lambda x: len(x) <= ByteLengthEnum.BYTE_512),
        src_url=wa_fields.String(missing='', validate=lambda x: len(x) <= ByteLengthEnum.BYTE_128),
        browser_url=wa_fields.String(missing='', validate=lambda x: len(x) <= ByteLengthEnum.BYTE_128),
        social_url=wa_fields.List(wa_fields.Dict(
            keys=wa_fields.String(validate=lambda x: len(x) <= ByteLengthEnum.BYTE_64),
            values=wa_fields.String(validate=lambda x: len(x) <= ByteLengthEnum.BYTE_512)), missing=None),
        introduction_en=wa_fields.String(missing='', default='', validate=lambda x: len(x) <= ByteLengthEnum.BYTE_65536),
        introduction_cn=wa_fields.String(missing='', default='', validate=lambda x: len(x) <= ByteLengthEnum.BYTE_65536),
        introduction=wa_fields.String(missing='', default='', validate=lambda x: len(x) <= ByteLengthEnum.BYTE_65536),
        fundraising=wa_fields.String(missing='', default='', example='募资情况', validate=lambda x: len(x) <= ByteLengthEnum.BYTE_65536),
        token_distribution=wa_fields.String(missing='', default='', example='代币分配信息',
                                            validate=lambda x: len(x) <= ByteLengthEnum.BYTE_65536),
        linkedin_or_cv=wa_fields.String(missing='', default='', example='创始人LinkedIn or CV',
                                        validate=lambda x: len(x) <= ByteLengthEnum.BYTE_512),
        applicant_name=wa_fields.String(required=True, example='申请人姓名', validate=lambda x: len(x) <= ByteLengthEnum.BYTE_128),
        applicant_role=wa_fields.String(required=True, example='申请人在项目团队角色',
                                        validate=lambda x: len(x) <= ByteLengthEnum.BYTE_128),
        applicant_email=ex_fields.EmailField(required=True, validate=lambda x: len(x) <= ByteLengthEnum.BYTE_128),
        applicant_telegram=wa_fields.String(required=True, example='申请人telegram', validate=lambda x: len(x) <= ByteLengthEnum.BYTE_64),
        vote_type=wa_fields.String(validate=lambda x: len(x) <= ByteLengthEnum.BYTE_32),
        coin_id=wa_fields.Integer(),
        securities_id=wa_fields.Integer(),
        commitment_id=wa_fields.Integer(),
        law_ids=wa_fields.List(wa_fields.Integer, missing=[]),
        audit_id=wa_fields.Integer(missing=None),
        avg_price=wa_fields.List(
            wa_fields.Dict(keys=wa_fields.String(validate=lambda x: len(x) <= ByteLengthEnum.BYTE_32),
                           values=wa_fields.String(validate=lambda x: len(x) <= ByteLengthEnum.BYTE_32)),
            missing=None, example='20日均价信息'),
        issue_price=wa_fields.List(
            wa_fields.Dict(keys=wa_fields.String(validate=lambda x: len(x) <= ByteLengthEnum.BYTE_32),
                           values=wa_fields.String(validate=lambda x: len(x) <= ByteLengthEnum.BYTE_32)),
            missing=None, example='发行价'),
        discount=wa_fields.String(required=True, example='融资折扣', validate=lambda x: len(x) <= ByteLengthEnum.BYTE_64),
        is_locked=wa_fields.Boolean(required=True, example='代币是否冻结'),
        unlock_condition=wa_fields.String(required=True, example='解锁条件', validate=lambda x: len(x) <= ByteLengthEnum.BYTE_256),
        product_url=wa_fields.String(example='已上线产品链接', missing='', validate=lambda x: len(x) <= ByteLengthEnum.BYTE_512),
        test_code=wa_fields.String(example='测试码', missing='', validate=lambda x: len(x) <= ByteLengthEnum.BYTE_64),
        support_chain_url=wa_fields.String(required=True, example='支持的公链', validate=lambda x: len(x) <= ByteLengthEnum.BYTE_512),
    ))
    def post(cls, **kwargs):
        """
        融资申请
        """
        # 参照上币申请，再次基础上加字段
        issued_date = (kwargs['issue_time'].astimezone(g.user_tz)).date()
        if issued_date > now().date():
            raise InvalidArgument

        # 判断是否这个融资币种
        if IeoAssetInformation.query.filter(
            IeoAssetInformation.asset == kwargs['short_name']
        ).first():
            raise ProjectExists

        if social_data := kwargs['social_url']:
            social_mapping = {item["media"]: item.get("url", None) for item in social_data if item.get("media")}
        else:
            social_mapping = {}

        def _check_file_exists(*ids):
            for id_ in ids:
                if id_ and not File.query.get(id_):
                    raise InvalidArgument

        law_ids = kwargs['law_ids']
        file_type_map = {}
        if not (icon := kwargs['logo'].get('file_key')):
            raise InvalidArgument

        for law_id in law_ids:
            file_type_map[law_id] = IeoApplicationFiles.FileType.LAW
        _check_file_exists(*list(file_type_map.keys()))

        c = IeoListingApplication(
            created_by=g.user.id,
            updated_by=g.user.id,
            code=kwargs['short_name'],
            name=kwargs['full_name'],
            icon=icon,
            type=IeoListingApplication.Type.COIN
            if kwargs.get('chain_type', '') == 'public'
            else IeoListingApplication.Type.TOKEN,
            issued_date=issued_date,
            total_supply=kwargs.get('issue_amount', ''),
            official_website=kwargs['website_url'],
            white_paper=kwargs['white_paper_url'],
            source_code=kwargs['src_url'],
            explorer=kwargs['browser_url'],
            intro_en=kwargs.get('introduction_en', '') or kwargs.get('introduction', ''),
            intro_cn=kwargs.get('introduction_cn', '') or kwargs.get('introduction', ''),
            fundraising=kwargs['fundraising'],
            token_distribution=kwargs['token_distribution'],
            total_circulate=kwargs["total_circulate"],
            usd_circulate=kwargs["usd_circulate"],
            linkedin_or_cv=kwargs['linkedin_or_cv'],
            applicant_name=kwargs['applicant_name'],
            applicant_role=kwargs['applicant_role'],
            applicant_email=kwargs['applicant_email'],
            applicant_telegram=kwargs['applicant_telegram'],
            telegram=social_mapping.get('Telegram', ''),
            facebook=social_mapping.get('Facebook', ''),
            twitter=social_mapping.get('Twitter', ''),
            reddit=social_mapping.get('Reddit', ''),
            medium=social_mapping.get('Medium', ''),
            discord=social_mapping.get('Discord', ''),
            is_securities=True if kwargs.get('vote_type', '') == 'securities' else False,
            is_first=True if kwargs['issue_type'] == 'first' else False,
            avg_price_data=json.dumps(kwargs['avg_price']),
            issued_price_data=json.dumps(kwargs['issue_price']),
            discount=kwargs['discount'],
            is_locked=kwargs['is_locked'],
            unlock_condition=kwargs['unlock_condition'],
            product_url=kwargs['product_url'],
            test_code=kwargs['test_code'],
            support_chain_url=kwargs['support_chain_url'],
        )

        db.session.add(c)
        db.session.flush()

        for file_id, file_type in file_type_map.items():
            db.session.add(IeoApplicationFiles(
                ieo_listing_application_id=c.id,
                file_id=file_id,
                name=File.query.get(file_id).name,
                user_id=g.user.id,
                file_type=file_type
            ))
        db.session.commit()
        return marshal(c, cls.marshal_fields)


@ns.route('/kol')
@respond_with_code
class KolApplyResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    def get(cls):
        kol_apply = KolApply.query.filter(
            KolApply.user_id == g.user.id
        ).first()
        if not kol_apply:
            return {}
        return dict(
            id=kol_apply.id,
            account_platform=kol_apply.account_platform.name,
            account_link=kol_apply.account_link,
            fans_range=kol_apply.fans_range.name,
            average_views=kol_apply.average_views,
            update_frequency=kol_apply.update_frequency.name,
            account_created_at=kol_apply.account_created_at,
            country_and_city=kol_apply.country_and_city,
            other_contact=kol_apply.other_contact,
            expected_salary=kol_apply.expected_salary,
            expectations=kol_apply.expectations
        )

    @classmethod
    @require_login(allow_sub_account=False)
    @limit_user_frequency(count=5, interval=3600)
    @ns.use_kwargs(dict(
        account_platform=EnumField(KolApply.AccountPlatform, required=True),
        account_link=wa_fields.String(required=True, example='账号链接',
                                      validate=lambda x: len(x) <= ByteLengthEnum.BYTE_256),
        fans_range=EnumField(KolApply.FansRange, required=True),
        average_views=wa_fields.String(required=True, example="平均播放次数",
                                       validate=lambda x: len(x) <= ByteLengthEnum.BYTE_128),
        update_frequency=EnumField(KolApply.UpdateFrequency, example="更新频率",  required=True),
        account_created_at=ex_fields.TimestampField(required=True),
        country_and_city=wa_fields.String(required=True, example='所在的国家以及城市',
                                          validate=lambda x: len(x) <= ByteLengthEnum.BYTE_256),
        other_contact=wa_fields.String(required=True, example="其他联系方式",
                                       validate=lambda x: len(x) <= ByteLengthEnum.BYTE_128),
        expected_salary=wa_fields.String(required=True,  example="期望月薪",
                                         validate=lambda x: len(x) <= ByteLengthEnum.BYTE_128),
        expectations=wa_fields.String(example="期望或者目标", missing="",
                                      validate=lambda x: len(x) <= ByteLengthEnum.BYTE_65536)
    ))
    def post(cls, **kwargs):
        user_id = g.user.id

        if KolUser.query.filter(
            KolUser.user_id == user_id
        ).first():
            raise AlreadyKolError

        if KolApply.query.filter(
            KolApply.user_id == user_id
        ).first():
            raise AlreadySubmitKolApplyError

        kol_apply = KolApply(
            user_id=user_id,
            account_created_at=kwargs['account_created_at'],
            account_platform=kwargs['account_platform'],
            account_link=kwargs['account_link'],
            fans_range=kwargs['fans_range'],
            average_views=kwargs['average_views'],
            update_frequency=kwargs['update_frequency'],
            country_and_city=kwargs['country_and_city'],
            other_contact=kwargs['other_contact'],
            expected_salary=kwargs['expected_salary'],
            expectations=kwargs.get('expectations'),
        )
        db.session_add_and_commit(kol_apply)
        return dict(id=kol_apply.id)


@ns.route('/kol/<int:apply_id>')
@respond_with_code
class KolApplyDetailResource(Resource):

    @classmethod
    @require_login(allow_sub_account=False)
    @limit_user_frequency(count=5, interval=3600)
    @ns.use_kwargs(dict(
        account_platform=EnumField(KolApply.AccountPlatform, required=True),
        account_link=wa_fields.String(required=True, example='账号链接',
                                      validate=lambda x: len(x) <= ByteLengthEnum.BYTE_256),
        fans_range=EnumField(KolApply.FansRange, required=True),
        average_views=wa_fields.String(required=True, example="平均播放次数",
                                       validate=lambda x: len(x) <= ByteLengthEnum.BYTE_128),
        update_frequency=EnumField(KolApply.UpdateFrequency, example="更新频率",  required=True),
        account_created_at=ex_fields.TimestampField(required=True),
        country_and_city=wa_fields.String(required=True, example='所在的国家以及城市',
                                          validate=lambda x: len(x) <= ByteLengthEnum.BYTE_256),
        other_contact=wa_fields.String(required=True, example="其他联系方式",
                                       validate=lambda x: len(x) <= ByteLengthEnum.BYTE_128),
        expected_salary=wa_fields.String(required=True,  example="期望月薪",
                                         validate=lambda x: len(x) <= ByteLengthEnum.BYTE_128),
        expectations=wa_fields.String(example="期望或者目标", missing="",
                                      validate=lambda x: len(x) <= ByteLengthEnum.BYTE_65536)
    ))
    def put(cls, apply_id, **kwargs):
        user_id = g.user.id
        kol_apply = KolApply.query.filter(
            KolApply.user_id == user_id,
            KolApply.id == apply_id
        ).first()
        if not kol_apply:
            raise InvalidArgument

        if KolUser.query.filter(
            KolUser.user_id == user_id
        ).first():
            raise AlreadyKolError

        kol_apply.account_created_at = kwargs['account_created_at']
        kol_apply.account_platform = kwargs['account_platform']
        kol_apply.account_link = kwargs['account_link']
        kol_apply.fans_range = kwargs['fans_range']
        kol_apply.average_views = kwargs['average_views']
        kol_apply.update_frequency = kwargs['update_frequency']
        kol_apply.country_and_city = kwargs['country_and_city']
        kol_apply.other_contact = kwargs['other_contact']
        kol_apply.expected_salary = kwargs['expected_salary']
        kol_apply.expectations = kwargs.get('expectations')
        db.session.commit()
        return dict(id=kol_apply.id)


@ns.route('/coin-info/<string:coin>')
@respond_with_code
class CoinPageInfoResource(Resource):

    @classmethod
    def get(cls, coin: str):
        """币种资料页-币种是否存在价格、区间涨跌幅、市场信息及币种活动信息"""
        if (coin not in list_all_assets()) and (not AssetInformationCache().hexists(coin)):
            raise InvalidArgument
        has_period_info, has_online_market, has_price_info = True, True, True
        period_data = AssetPeriodPriceCache().get_asset_data(coin)
        if not period_data:
            has_period_info = has_price_info = False
        if not cls.has_online_market(coin):
            has_online_market = False
        if coin == 'USDT':
            has_online_market = True
        dock_info = cls.get_dock_info(coin)
        mining_info = cls.get_mining_info(coin)
        return dict(
            has_price_info=has_price_info,
            has_period_info=has_period_info,
            has_online_market=has_online_market,
            dock=dock_info,
            mining=mining_info,
        )

    @classmethod
    @cached(ttl=600)
    def has_online_market(cls, coin: str):
        markets = Market.query.filter(
            Market.base_asset == coin,
            Market.status.in_((Market.Status.ONLINE, Market.Status.BIDDING, Market.Status.COUNTING_DOWN))
        ).with_entities(func.count('*')).scalar() or 0
        return bool(markets)

    @classmethod
    @cached(ttl=600)
    def get_dock_info(cls, coin: str) -> Dict:
        rec = IeoActivity.query.filter(
            IeoActivity.subscribe_asset == coin,
            IeoActivity.status == IeoActivity.StatusType.ONLINE
        ).order_by(
            IeoActivity.id.asc()
        ).with_entities(
            IeoActivity.id
        ).first()
        if rec:
            ret = dict(has_activity=True, activity_id=rec.id)
        else:
            ret = dict(has_activity=False, activity_id=None)
        return ret

    @classmethod
    @cached(ttl=600)
    def get_mining_info(cls, coin: str):
        recs = LaunchMiningProject.query.filter(
            LaunchMiningProject.status == LaunchMiningProject.Status.ONLINE,
            LaunchMiningProject.reward_asset == coin
        ).order_by(
            LaunchMiningProject.id.asc()
        ).all()
        for rec in recs:
            if rec.active_status != LaunchMiningProject.ActiveStatus.FINISHED:
                return dict(has_activity=True, activity_id=rec.id)

        return dict(has_activity=False, activity_id=None)
