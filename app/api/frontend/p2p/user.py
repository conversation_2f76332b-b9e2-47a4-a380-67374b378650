import re

from flask import g
from marshmallow import fields, Schema

from app import Language
from app.api.common import Namespace, Resource, respond_with_code, require_login, require_2fa, \
    get_request_user
from app.api.common.decorators import require_p2p_permission, require_p2p_permission_except_mobile
from app.api.common.fields import PageField, LimitField, EnumField, TimestampField
from app.business.p2p.advertising import P2pAdvertisingSearchManager, P2pAdvertisingBiz
from app.business.p2p.pay_channel import UserPayChannelBus
from app.business.p2p.user import P2pUserManger
from app.business.p2p.utils import P2pUtils
from app.business.user import UserRepository
from app.caches.p2p import P2pTPlusNNotifyCache
from app.common import MobileCodeType
from app.exceptions.p2p import P2pExceptionMap, P2pExceptionCode
from app.models.p2p import P2pMerchantDailyTradeSummary, P2pUserTPlusNRecord
from app.models.user import P2p<PERSON><PERSON><PERSON>t, P2pUser
from app.utils import quantize_amount

ns = Namespace('user')

RE_USER_NAME = re.compile(r'^[0-9a-zA-Z\u4e00-\u9fa5_]+$')


@ns.route('')
@respond_with_code
class P2pUserResource(Resource):

    @classmethod
    @require_p2p_permission
    def get(cls):
        user_id = g.user.id
        lang = Language(g.lang or Language.DEFAULT.value)
        info = P2pUserManger(user_id).get_self_info(lang)
        return info

    @classmethod
    @require_login(allow_sub_account=False)
    def post(cls):
        """成为p2p用户"""
        p2p_user = P2pUserManger(g.user.id).update_to_p2p_user()
        return dict(
            id=p2p_user.id,
            nickname=p2p_user.nickname
        )

    @classmethod
    @require_p2p_permission
    @ns.use_kwargs(dict(
        nickname=fields.String(required=True),
        account_name=fields.String
    ))
    def put(cls, **kwargs):
        """修改p2p用户昵称"""
        nickname, account_name = kwargs['nickname'], kwargs.get("account_name", "")
        p2p_user = P2pUserManger(g.user.id).update_user_info(nickname, account_name)
        return p2p_user.id


class UserPayChannelFormSchema(Schema):
    key = fields.String(required=True)
    value = fields.String(required=True)
    file_url = fields.String()


@ns.route("/pay-channel")
@respond_with_code
class P2pUserPayChanelResource(Resource):

    @classmethod
    @require_p2p_permission
    def get(cls):
        user = g.user
        lang = g.lang or Language.DEFAULT.value
        return UserPayChannelBus(user.id).get_user_pay_channel_data(
            Language(lang),
            inactive=True,
            is_update_scenario=True
        )

    @classmethod
    @require_2fa(MobileCodeType.P2P_ADD_USER_PAY_CHANNEL, allow_sub_account=False)
    @require_p2p_permission
    @ns.use_kwargs(dict(
        channel_id=fields.String(required=True),
        form=fields.Nested(UserPayChannelFormSchema, many=True, required=True),
        alias=fields.String,
        remark=fields.String,
    ))
    def post(cls, **kwargs):
        user_id = g.user.id
        channel = UserPayChannelBus(user_id).create(
            kwargs["channel_id"],
            kwargs['form'],
            kwargs.get("alias"),
            kwargs.get("remark"),
        )
        return str(channel.id)

    @classmethod
    @require_p2p_permission
    @ns.use_kwargs(dict(
        user_channel_id=fields.String(required=True),
        form=fields.Nested(UserPayChannelFormSchema, many=True, required=True),
        alias=fields.String,
        remark=fields.String,
    ))
    def put(cls, **kwargs):
        user_id = g.user.id
        user_channel_id = kwargs["user_channel_id"]
        channel = UserPayChannelBus(user_id).update(
            user_channel_id,
            kwargs["form"],
            kwargs.get("alias"),
            kwargs.get("remark"),
        )
        return str(channel.id)

    @classmethod
    @require_2fa(MobileCodeType.P2P_DELETE_USER_PAY_CHANNEL, allow_sub_account=False)
    @require_p2p_permission
    @ns.use_kwargs(dict(
        user_channel_id=fields.String(required=True)
    ))
    def delete(cls, **kwargs):
        user_id = g.user.id
        user_channel_id = kwargs["user_channel_id"]
        UserPayChannelBus(user_id).delete(user_channel_id)
        return {}


@ns.route("/feedback-channel")
@respond_with_code
class P2pUserFeedbackChannelResource(Resource):
    @classmethod
    @require_p2p_permission
    @ns.use_kwargs(dict(
        channel_name=fields.String(required=True, validate=lambda x: 0 < len(x) <= 60),
        fiat_name=fields.String(required=True, validate=lambda x: 0 < len(x) <= 60),
        remark=fields.String(allow_none=True)
    ))
    def post(cls, **kwargs):
        user_id = g.user.id
        feedback = UserPayChannelBus(user_id).feedback_channel(kwargs)
        return str(feedback.id)


@ns.route("/<string:biz_user_id>")
@respond_with_code
class P2pOtherUserInfoResource(Resource):

    @classmethod
    @require_p2p_permission_except_mobile
    def get(cls, biz_user_id):
        lang = g.lang or Language.DEFAULT.value
        user_id = P2pUtils.change_biz_user_id(biz_user_id)
        if not user_id:
            return {}
        info = P2pUserManger(user_id).get_public_user_info(Language(lang))
        return info


@ns.route("/<string:biz_user_id>/advertising")
@respond_with_code
class P2pMerchantAdvertisingResource(Resource):

    @classmethod
    @require_p2p_permission_except_mobile
    @ns.use_kwargs(dict(
        page=PageField(missing=1),
        limit=LimitField(missing=10)
    ))
    def get(cls, biz_user_id, **kwargs):
        page, limit = kwargs["page"], kwargs["limit"]
        self_lang = g.lang or Language.DEFAULT.value
        merchant_user_id = P2pUtils.change_biz_user_id(biz_user_id)
        if not merchant_user_id or not P2pUserManger(merchant_user_id).check_is_merchant():
            return {}
        user = get_request_user(allow_sub_account=False)
        return P2pAdvertisingSearchManager(user, Language(self_lang)).merchant_advertising(
            merchant_user_id,
            page,
            limit
        )


@ns.route("/merchant")
@respond_with_code
class P2pMerchantResource(Resource):

    @classmethod
    @require_p2p_permission(is_merchant=True)
    def get(cls):
        user_id = g.user.id
        self_lang = g.lang or Language.DEFAULT.value
        manger = P2pUserManger(user_id)
        merchant_info = manger.get_self_info(Language(self_lang))
        order_info = manger.get_order_info()
        advertising_info = P2pAdvertisingBiz.get_advertising_status_count(user_id)
        return dict(
            merchant_info=merchant_info,
            order_info=order_info,
            advertising_info=advertising_info
        )

    @classmethod
    @require_p2p_permission(is_merchant=True)
    @ns.use_kwargs(dict(
        nickname=fields.String(required=True, validate=lambda x: 0 < len(x) <= 16)
    ))
    def put(cls, **kwargs):
        """修改p2p商家的名称"""
        user_id = g.user.id
        nickname = kwargs['nickname']
        if not RE_USER_NAME.fullmatch(nickname):
            raise P2pExceptionMap[P2pExceptionCode.NICKNAME_ERROR]
        P2pUserManger(user_id).update_user_info(nickname)

    @classmethod
    @require_login(allow_sub_account=False)
    def post(cls, **kwargs):
        """开通商家权限"""
        user_id = g.user.id
        P2pUserManger(user_id).update_to_merchant(kwargs)


@ns.route("/merchant/spot")
@respond_with_code
class P2pMerchantSpotResource(Resource):
    @classmethod
    @require_p2p_permission(is_valid_mer=True)
    @ns.use_kwargs(dict(
        spot_status=EnumField(P2pMerchant.ShopStatus, required=True)
    ))
    def put(cls, **kwargs):
        user_id = g.user.id
        spot_status = kwargs['spot_status']
        manger = P2pUserManger(user_id)
        if spot_status == P2pMerchant.ShopStatus.OPEN:
            manger.open_shop()
        else:
            manger.close_shop()


@ns.route("/merchant/trading-data")
@respond_with_code
class P2pMerchantTradingDataResource(Resource):

    @classmethod
    @require_p2p_permission(is_merchant=True)
    @ns.use_kwargs(dict(
        trading_date=TimestampField(required=True, to_date=True)
    ))
    def get(cls, **kwargs):
        user_id = g.user.id
        summary = P2pMerchantDailyTradeSummary.query.filter(
            P2pMerchantDailyTradeSummary.user_id == user_id,
            P2pMerchantDailyTradeSummary.report_date == kwargs['trading_date']
        ).with_entities(
            P2pMerchantDailyTradeSummary.current_buy_amount,
            P2pMerchantDailyTradeSummary.current_sell_amount,
            P2pMerchantDailyTradeSummary.sum_buy_amount,
            P2pMerchantDailyTradeSummary.sum_sell_amount
        ).first()
        return dict(
            current_buy_amount=quantize_amount(summary.current_buy_amount, 2) if summary else 0,
            current_sell_amount=quantize_amount(summary.current_sell_amount, 2) if summary else 0,
            sum_buy_amount=quantize_amount(summary.sum_buy_amount, 2) if summary else 0,
            sum_sell_amount=quantize_amount(summary.sum_sell_amount, 2) if summary else 0,
        )


@ns.route("/nicknames")
@respond_with_code
class P2pNicknamesResource(Resource):

    @classmethod
    @require_p2p_permission
    @ns.use_kwargs(dict(
        im_user_ids=fields.List(fields.String(), required=True)
    ))
    def post(cls, **kwargs):
        model = P2pUser
        im_user_id = kwargs["im_user_ids"]
        data = {}
        if not im_user_id:
            return data
        else:
            rows = model.query.filter(
                model.biz_user_id.in_(im_user_id)
            ).with_entities(
                model.biz_user_id,
                model.user_id
            ).all()
            user_ids = {i.user_id for i in rows}
            account_info_mapper = UserRepository.get_user_account_info_mapper(user_ids)
            for i in rows:
                account_info = account_info_mapper.get(i.user_id, {})
                data[i.biz_user_id] = dict(
                    nickname=account_info.get("name", ""),
                    account_name=account_info.get("account_name", ""),
                    avatar=account_info.get("avatar", "")
                )
            return data


@ns.route("/merchant/margin/payment")
@respond_with_code
class P2pMarginPaymentResource(Resource):

    @classmethod
    @require_p2p_permission(is_merchant=True)
    @ns.use_kwargs(dict(
        amount=fields.Decimal(required=True, validate=lambda x: x > 0)
    ))
    def post(cls, **kwargs):
        user_id = g.user.id
        amount = kwargs["amount"]
        manger = P2pUserManger(user_id)
        manger.check_merchant_valid_status(is_margin=False)
        manger.payment_margin(amount)


@ns.route("/merchant/cancel")
@respond_with_code
class P2pMerchantCancelResource(Resource):

    @classmethod
    @require_p2p_permission(is_merchant=True)
    def get(cls, **kwargs):
        user_id = g.user.id
        check, error = P2pUserManger(user_id).check_cancel_merchant_step()
        return {
            "can_cancel": check,
            **error
        }

    @classmethod
    @require_p2p_permission(is_merchant=True)
    def post(cls, **kwargs):
        user_id = g.user.id
        P2pUserManger(user_id).cancel_merchant()


@ns.route("/t-plus-n/notify")
@respond_with_code
class P2pTPlusNNotifyResource(Resource):

    @classmethod
    @require_p2p_permission
    def post(cls):
        user_id = g.user.id
        cache = P2pTPlusNNotifyCache()
        if has_change := cache.has_user_change(user_id):
            cache.del_user_notify(user_id)
        t_plus_n_record = P2pUserTPlusNRecord.query.filter(
            P2pUserTPlusNRecord.user_id == user_id,
        ).first()
        t_plus_n_days = t_plus_n_record.real_days if t_plus_n_record else None
        return {
            'has_change': has_change,
            't_plus_n_days': t_plus_n_days
        }
