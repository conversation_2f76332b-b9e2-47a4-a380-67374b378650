# -*- coding: utf-8 -*-
import datetime
from collections import defaultdict
from decimal import Decimal
from enum import Enum
from typing import Named<PERSON>up<PERSON>, Optional
from urllib.parse import urlparse

from flask import request, g
from sqlalchemy import func
from webargs import fields

from app.config import config
from app.business.redshift import PerpetualRedShiftDB, TradeRedShiftDB
from app.caches.user import UserVisitPermissionCache
from app.exceptions.basic import HistoryDataNotReady
from app.utils.date_ import this_month
from app.utils.files import AWSBucketPublic

from ..common import (
    Resource, Namespace, respond_with_code,
    get_request_ip, get_request_user, json_string_success,
    get_request_user_agent
)
from ..common.fields import EnumField, MarketField, PerpetualMarketField, TimestampField
from ..common.request import is_old_app_request, get_request_platform
from ...business import (
    mem_cached, cached, get_request_language, force_locale,
    gettext, BusinessSettings, CountrySettings, UserPreferences
)
from ...business.user import get_forbidden_region_code
from ...caches import PerpetualMarketDataCache
from ...caches.kline import Asset<PERSON>uotesDataCache
from ...caches.kline import AssetRankCache
from ...caches.report import SiteTradeInfoViewCache
from ...caches.spot import IlliquidMarketCache, MarketCache
from ...caches.system import IpWhiteListCache, GoogleBotIpCache, CountryStateCache
from ...common import Language, list_country_codes_3, get_country, TradeBusinessType, PerpetualMarketType, Media
from ...exceptions import InvalidArgument
from ...models import AppVersion, PositionCountry, FooterConfig
from ...models.system import VerifyChannel, LocalAreasBlock, LocalAreasBlockContent, CountrySetting
from ...utils import current_timestamp, GeoIP, url_join

ns = Namespace('System')


@ns.route('/time')
@respond_with_code
class TimeResource(Resource):

    @classmethod
    def get(cls):
        return dict(
            current_timestamp=current_timestamp(to_int=True)
        )


@ns.route('/market')
@respond_with_code
class HomeMarketResource(Resource):

    WEB_MARKET_COUNT = 12
    APP_MARKET_COUNT = 6

    @classmethod
    @ns.use_kwargs(dict(
        business_type=EnumField(TradeBusinessType, missing=TradeBusinessType.SPOT),
        market_type=EnumField(PerpetualMarketType, missing=PerpetualMarketType.DIRECT),
    ))
    def get(cls, **kwargs):
        """首页市场"""
        business_type = kwargs['business_type'].value
        market_type = kwargs['market_type'].value
        platform = 'web'
        if get_request_platform().is_mobile():
            platform = 'mobile'
        is_old_app = False
        if is_old_app_request(3270, 46):
            is_old_app = True
        res = cls._get_data(platform, is_old_app, business_type, market_type)
        return res

    @classmethod
    @cached(60)
    def _get_data(
            cls,
            platform,
            is_old_app,
            business_type=TradeBusinessType.SPOT.value,
            market_type=PerpetualMarketType.DIRECT.value,
    ):
        if business_type == TradeBusinessType.SPOT.value:
            res = cls._get_spot_info()
            if platform == 'mobile' and is_old_app:
                res = res[:cls.APP_MARKET_COUNT]
        else:
            res_mapping = cls._get_perpetual_info()
            res = res_mapping.get(market_type) or []
            res = res[:cls.WEB_MARKET_COUNT]
            if platform == 'mobile':
                res = res[:cls.APP_MARKET_COUNT]
        return res

    @classmethod
    def _get_spot_info(cls):
        assets = AssetRankCache('volume_usd').read_assets()
        markets = MarketCache.list_online_markets()
        res = ['CETUSDT', 'BTCUSDT', 'ETHUSDT']
        exclude_assets = ['CET', 'BTC', 'ETH', 'USDC', 'USDT']
        for asset in assets:
            market = asset + 'USDT'
            if market in markets and asset not in exclude_assets:
                res.append(market)
            if len(res) == cls.WEB_MARKET_COUNT:
                break

        return res

    @classmethod
    def _get_perpetual_info(cls):
        market_info = dict()

        top2_direct_markets = ['BTCUSDT', 'ETHUSDT']
        top2_inverse_markets = ['BTCUSD', 'ETHUSD']
        direct_markets = []
        inverse_markets = []

        cache = PerpetualMarketDataCache()
        for market, data in cache.market_data.items():
            volume_usd = Decimal(data['volume_usd'])
            if data['market_type'] == PerpetualMarketType.DIRECT:
                if market in top2_direct_markets:
                    continue
                direct_markets.append((market, volume_usd))
            else:
                if market in top2_inverse_markets:
                    continue
                inverse_markets.append((market, volume_usd))

        direct_markets.sort(key=lambda t: t[1], reverse=True)
        inverse_markets.sort(key=lambda t: t[1], reverse=True)
        direct = PerpetualMarketType.DIRECT.value
        inverse = PerpetualMarketType.INVERSE.value
        market_info[direct] = top2_direct_markets + [market for market, _ in direct_markets]
        market_info[inverse] = top2_inverse_markets + [market for market, _ in inverse_markets]
        return market_info


@ns.route('/market/rank')
@respond_with_code
class HomeMarketRankResource(Resource):

    @classmethod
    def get(cls):
        """首页市场排行"""
        return dict(
            blacklist_markets=list(IlliquidMarketCache().smembers())
        )


@ns.route('/asset/circulation')
@respond_with_code
class AssetCirculationUsdResource(Resource):

    @classmethod
    def get(cls):
        """币种流通市值"""
        res = cls._get_data()
        return res

    @classmethod
    @mem_cached(60)
    def _get_data(cls):
        res = {}
        assets_data = AssetQuotesDataCache().get_all_data()
        for asset_data in assets_data:
            res.update({asset_data['asset']: asset_data['circulation_usd']})
        return res


@ns.route('/app/version')
@respond_with_code
class AppVersionResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        platform=EnumField(AppVersion.Platform, enum_by_value=True)
    ))
    def get(cls, **kwargs):
        headers = request.headers
        if (platform := kwargs.get('platform')) is None:
            try:
                platform = AppVersion.Platform(headers.get('PLATFORM'))
            except ValueError:
                raise InvalidArgument
        cur_build = headers.get('build', 0)
        try:
            lang = Language(headers.get('Accept-Language', 'en_US'))
        except ValueError:
            lang = Language.EN_US

        if lang not in AppVersion.AVAILABLE_LANGS:
            lang = AppVersion.AVAILABLE_LANGS[0]

        query = AppVersion.query \
            .filter(AppVersion.platform == platform,
                    AppVersion.file_url != '',
                    AppVersion.status == AppVersion.Status.VALID)

        if (latest := query.order_by(AppVersion.id.desc()).first()) is None:
            raise InvalidArgument

        update_types = AppVersion.UpdateType
        update_type = update_types.NONE
        current_version = headers.get('version', '')
        desc = d.desc if (d := latest.lang_details.filter_by(lang=lang).first()) is not None else ''
        for ut, in query \
                .filter(AppVersion.build > cur_build) \
                .with_entities(func.distinct(AppVersion.update_type)):
            if isinstance(ut, str):
                ut = getattr(update_types, ut)
            if ut is update_types.FORCE:
                update_type = ut
                break
            if ut is update_types.NOTICE:
                update_type = ut
            if ut is update_types.ONLY_ONCE:
                update_type = ut


        class VersionNum(NamedTuple):
            first: int
            second: int
            third: int

        def get_version_data(version_num_str) -> Optional[VersionNum]:
            version_num_list = version_num_str.split('.')
            if len(version_num_list) in (3, 4) and all([v.isdigit() for v in version_num_list]):
                return VersionNum(first=int(version_num_list[0]),
                                  second=int(version_num_list[1]),
                                  third=int(version_num_list[2]))
            return None

        current_version_data = get_version_data(current_version)
        latest_version_data = get_version_data(latest.version)

        if latest_version_data is not None and current_version_data is not None:

            if current_version_data.first != latest_version_data.first \
                    or current_version_data.second != latest_version_data.second:
                find_version = '.'.join(
                    map(str, [latest_version_data.first, latest_version_data.second, 0]))
                r = query.filter(AppVersion.version == find_version).first()
                if r:
                    desc = d.desc \
                        if (d := r.lang_details.filter_by(lang=lang).first()) is not None \
                        else ''

        return dict(
            support_app_update=BusinessSettings.support_app_update,
            upgrade_build=latest.build,
            upgrade_version=latest.version,
            upgrade_level=update_type,
            upgrade_desc=desc,
            download_url=latest.file_url,
            ad_url='',
            ad_start_time='',
            ad_end_time=''
        )


@ns.route('/ip')
@respond_with_code
class IPResource(Resource):

    @classmethod
    def get(cls):
        is_forbidden = False
        ip_info = GeoIP(get_request_ip())

        if cls.is_ip_forbidden(ip_info) and not get_request_user():
            is_forbidden = True

        forbidden_list = IpCountryResource.get_forbidden_country_list(g.lang)
        return dict(
            is_forbidden=is_forbidden,
            is_acc_forbidden=is_forbidden, # 兼容保留
            forbidden_list=forbidden_list,
        )

    @classmethod
    def get_ip_forbidden_detail(cls, ip_info: GeoIP) -> dict:
        """获取IP禁止的详细信息，包括禁止类型和具体地区信息"""
        is_forbidden = False
        forbidden_type = None
        country_code = ip_info.country_code
        state_code = ip_info.state_code
        if cls.is_search_engine_bot(ip_info.ip):
            is_forbidden = False
        elif ip_info.ip in IpWhiteListCache().get_ip_list():
            is_forbidden = False
        elif country_code in get_forbidden_region_code():
            is_forbidden = True
            forbidden_type = 'country'
        elif (country_code, state_code) in cls.get_forbidden_state_code():
            is_forbidden = True
            forbidden_type = 'state'
        return {
            'is_forbidden': is_forbidden,
            'forbidden_type': forbidden_type,
            'country_code': country_code,
            'state_code': state_code,
        }

    @classmethod
    def is_ip_forbidden(cls, ip_info: GeoIP):
        """修改此方法逻辑，需要同步修改上面的get_ip_forbidden_detail"""
        if cls.is_search_engine_bot(ip_info.ip):
            return False
        if ip_info.ip in IpWhiteListCache().get_ip_list():
            return False
        country_code = ip_info.country_code
        if country_code in get_forbidden_region_code():
            return True
        if (country_code, ip_info.state_code) in cls.get_forbidden_state_code():
            return True
        return False

    @classmethod
    def is_ip_reminded_only(cls, ip_info: GeoIP):
        country_code = ip_info.country_code
        if (country_code, ip_info.state_code) in cls.get_reminded_only_state_code():
            return True
        
        return False

    @classmethod
    def is_kyc_required(cls, ip_info: GeoIP):
        country_code = ip_info.country_code
        if country_code in cls.get_kyc_required_code():
            return True
        return False

    @classmethod
    def is_search_engine_bot(cls, ip: str):
        ua = get_request_user_agent().lower()
        if 'google' in ua or 'bingbot' in ua or 'semrushbot' in ua:
            return True
        if GoogleBotIpCache.has_ip(ip):
            return True
        return False

    @classmethod
    @mem_cached(600)
    def get_kyc_required_code(cls):
        q = CountrySetting.query.filter(
            CountrySetting.key == CountrySettings.kyc_required.name,
            CountrySetting.value == '1',
            CountrySetting.status == CountrySetting.Status.VALID
        ).with_entities(CountrySetting.code).all()
        return {get_country(v.code).iso_2 for v in q}

    @classmethod
    @mem_cached(600)
    def get_forbidden_state_code(cls):
        q = LocalAreasBlock.query.filter(
            LocalAreasBlock.status == LocalAreasBlock.Status.VALID,
            LocalAreasBlock.type == LocalAreasBlock.Type.NO_ACCESS,
        ).with_entities(LocalAreasBlock.country_code, LocalAreasBlock.state_code).all()
        return {(v.country_code, v.state_code) for v in q}

    @classmethod
    @mem_cached(600)
    def get_reminded_only_state_code(cls):
        q = LocalAreasBlock.query.filter(
            LocalAreasBlock.status == LocalAreasBlock.Status.VALID,
            LocalAreasBlock.type == LocalAreasBlock.Type.REMINDED_ONLY,
        ).with_entities(LocalAreasBlock.country_code, LocalAreasBlock.state_code).all()
        return {(v.country_code, v.state_code) for v in q}

    @classmethod
    def get_forbidden_message(cls, lang: Language, country_code: str, state_code: str) -> str:
        """由后端返回IP不支持访问的富文本文案，前端/APP直接展示"""
        telegram_url = cls.get_sns_site_telegram_url(lang.name)
        support_url = url_join(config["SUPPORT_URL"], "/hc/requests/new")
        country_info = get_country(country_code)
        if country_code and state_code:
            # 局部地区不提供服务，则展示地区名（如果是局部地区，展示地区英文名即可）
            state_map = CountryStateCache().get_state_map_by_iso2(country_info.iso_2) if country_info else {}
            region_name = state_map.get(state_code) or ""
        else:
            # 国家不提供服务，则展示国家名
            region_name = gettext(country_info.cn_name) if country_info else ""

        msg1 = gettext('亲爱的用户：')
        rich_msg1 = f'<p>{msg1}</p>'
        msg2 = gettext(
            '根据相关部门针对数字货币产业的监管要求，我们无法为您IP所在地区（%(region_name)s）的用户提供服务，感谢您对CoinEx的关注与支持。',
            region_name=region_name,
        )
        rich_msg2 = f'<p>{msg2}</p>'
        msg3 = gettext(
            '如遇任何问题，可<a href="%(support_url)s" rel="noopener noreferrer" target="_blank">提交工单</a>或加入'
            '<a href="%(telegram_url)s" rel="noopener noreferrer" target="_blank">telegram电报群</a>参与讨论。',
            support_url=support_url,
            telegram_url=telegram_url,
        )
        rich_msg3 = f'<p>{msg3}</p>'
        full_msg = rich_msg1 + rich_msg2 + rich_msg3
        return full_msg

    @classmethod
    @cached(600)
    def get_sns_site_telegram_url(cls, lang_name: str) -> str:
        row: FooterConfig = FooterConfig.query.filter(
            FooterConfig.business == FooterConfig.Business.SNS_SITE,
            FooterConfig.media == Media.TELEGRAM,
            FooterConfig.status == FooterConfig.Status.VALID,
            FooterConfig.lang == Language[lang_name],
        ).with_entities(
            FooterConfig.url
        ).first()
        return row.url if row else ""


@ns.route('/ip/country')
@respond_with_code
class IpCountryResource(Resource):
    @classmethod
    @mem_cached(600)
    def get_local_areas_list(cls):
        local_query = LocalAreasBlock.query.filter(
            LocalAreasBlock.status == LocalAreasBlock.Status.VALID,
            LocalAreasBlock.type == LocalAreasBlock.Type.NO_ACCESS,
        ).with_entities(
            LocalAreasBlock.id,
            LocalAreasBlock.country_code,
            LocalAreasBlock.state_code,
            LocalAreasBlock.type,
        ).all()
        local_content_query = LocalAreasBlockContent.query.filter(
            LocalAreasBlockContent.local_areas_block_id.in_([i.id for i in local_query])
        ).with_entities(
            LocalAreasBlockContent.local_areas_block_id,
            LocalAreasBlockContent.lang,
            LocalAreasBlockContent.title,
        ).all()
        content_map = defaultdict(dict)
        for row in local_content_query:
            content_map[row.local_areas_block_id][row.lang.value] = row.title
        result = [dict(
            country_code=i.country_code,
            state_code=i.state_code,
            type=i.type.name,
            id=i.id,
            content_map=content_map[i.id],
        ) for i in local_query]

        return result

    @classmethod
    def get_forbidden_country_list(cls, lang: str):
        lang = Language.ZH_HANT_HK.value if lang in (
            Language.ZH_HANS_CN.value, Language.ZH_HANT_HK.value
        ) else Language.DEFAULT.value
        region_codes = get_forbidden_region_code()
        local_areas_list = cls.get_local_areas_list()
        forbidden_list = []
        region = ''  # 历史字段兼容
        with force_locale(lang):
            for country_code in region_codes:
                forbidden_list.append(dict(
                    country=gettext(get_country(country_code).cn_name),
                    region=region,
                ))
        for row in local_areas_list:
            if row['country_code'] in region_codes:
                continue
            tmp = dict(country=row['content_map'].get(lang, ''),
                       region=region,
                       )
            forbidden_list.append(tmp)

        return forbidden_list

    @classmethod
    def get(cls):
        """is_forbidden 用于前端/APP判断地区限制访问"""
        is_reminded_only = False
        country_ip_reminded_only = False
        ip_info = GeoIP(get_request_ip())
        forbidden_list = cls.get_forbidden_country_list(g.lang)

        forbidden_detail = IPResource.get_ip_forbidden_detail(ip_info)
        is_forbidden = forbidden_detail['is_forbidden']
        forbidden_message = ""
        if is_forbidden:
            lang = Language(lang) if (lang := g.get('lang')) else Language.DEFAULT
            _state_code = '' if forbidden_detail['forbidden_type'] == 'country' else forbidden_detail['state_code']
            forbidden_message = IPResource.get_forbidden_message(lang, forbidden_detail['country_code'], _state_code)
        elif IPResource.is_ip_reminded_only(ip_info):
            is_reminded_only = True

        province = ip_info.region
        is_kyc_required = IPResource.is_kyc_required(ip_info)

        if is_kyc_required and (user := get_request_user()):
            only_withdraw_status = UserVisitPermissionCache().get_user_permission(user.id)
            if only_withdraw_status == UserVisitPermissionCache.ONLY_WITHDRAWAL_WHITELIST_VALUE:
                is_kyc_required = False
            if ip_info.country_code and user.id <= CountrySettings(ip_info.country_code).kyc_required_after_user_id:
                is_kyc_required = False
            user_pref = UserPreferences(user.id)
            if user_pref.timezone_offset in BusinessSettings.not_require_kyc_by_timezone_offsets:
                # 按照用户的时区判断不强制KYC限制
                is_kyc_required = False
        if ip_info.country_code and CountrySettings(ip_info.country_code).ip_reminder_on:
            country_ip_reminded_only = True
        return dict(
            country_code=ip_info.country_code,
            province=province,
            is_forbidden=is_forbidden,
            is_reminded_only=is_reminded_only,
            country_ip_reminded_only=country_ip_reminded_only,
            forbidden_list=forbidden_list,
            is_kyc_required=is_kyc_required,
            forbidden_message=forbidden_message,
        )


@ns.route('/trade/info')
@respond_with_code
class SiteTradeInfoResponse(Resource):

    @classmethod
    @mem_cached(120)
    def get(cls):
        if data := SiteTradeInfoViewCache().read():
            return json_string_success(data)
        return {}


@ns.route("/verify/channel")
@respond_with_code
class SiteVerifyChannel(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        type=EnumField(VerifyChannel.ChannelType, required=True),
        content=fields.String(required=True)
    ))
    def post(cls, **kwargs):
        channel_type = kwargs['type']
        content: str = kwargs['content'].strip()
        if channel_type not in VerifyChannel.Special_Community:
            rec = VerifyChannel.query.filter(
                VerifyChannel.channel_type == channel_type,
                VerifyChannel.content == content
            ).first()
            is_exist = True if rec else False
            channel_type = "TENCENT" if channel_type == VerifyChannel.ChannelType.WECHAT else channel_type.name
            return dict(
                is_exits=is_exist,
                channel_type=channel_type,
                content=rec.content if rec else content
            )
        else:
            if channel_type == VerifyChannel.ChannelType.X:
                return cls.get_x_result(content)
            elif channel_type == VerifyChannel.ChannelType.TELEGRAM:
                return cls.get_tg_result(content)
            elif channel_type == VerifyChannel.ChannelType.FACEBOOK:
                return cls.get_fb_result(content)
            elif channel_type == VerifyChannel.ChannelType.INSTAGRAM:
                return cls.get_insta_result(content)
            else:
                raise InvalidArgument

    @classmethod
    def get_x_result(cls, content: str):
        possible_values = {content}
        if content.find(".") > 0:
            url_content = content
            if not (url_content.startswith("https://") or url_content.startswith("http://")):
                url_content = f"https://{url_content}"
                possible_values.add(url_content)
                if 'x.com' in url_content:
                    twitter_domain = url_content.replace('x.com', 'twitter.com')
                    possible_values.add(twitter_domain)
                if 'twitter.com' in url_content:
                    x_domain = url_content.replace('twitter.com', 'x.com')
                    possible_values.add(x_domain)
            parse = urlparse(url_content)
            net_path = parse.path.lstrip("/")
            if net_path:
                possible_values.add(net_path)
                possible_values.add(f'@{net_path}')
        else:
            net_path = content
            if content.startswith('@'):
                net_path = content.lstrip('@')
                possible_values.add(net_path)
            else:
                possible_values.add(f'@{content}')
            twitter_url = f'https://twitter.com/{net_path}'
            x_url = f'https://x.com/{net_path}'
            possible_values.update({twitter_url, x_url})
        rec = VerifyChannel.query.filter(
            VerifyChannel.channel_type == VerifyChannel.ChannelType.X,
            VerifyChannel.content.in_(possible_values)
        ).first()
        if not rec:
            return dict(
                is_exits=False,
                channel_type=VerifyChannel.ChannelType.X.name,
                content=content
            )
        ret_content: str = rec.content
        if ret_content.startswith('https'):
            ret_content.replace('twitter.com', 'x.com')
        else:
            ret_content = f'https://x.com/{ret_content.lstrip("@")}'
        return dict(
                is_exits=True,
                channel_type=VerifyChannel.ChannelType.X.name,
                content=ret_content
            )

    @classmethod
    def get_tg_result(cls, content: str):
        domain = 't.me'
        return cls._get_special_community_result(content, domain, VerifyChannel.ChannelType.TELEGRAM)

    @classmethod
    def get_fb_result(cls, content: str):
        domain = 'www.facebook.com'
        return cls._get_special_community_result(content, domain, VerifyChannel.ChannelType.FACEBOOK)

    @classmethod
    def get_insta_result(cls, content: str):
        domain = 'www.instagram.com'
        return cls._get_special_community_result(content, domain, VerifyChannel.ChannelType.INSTAGRAM)

    @classmethod
    def _get_special_community_result(cls, content: str, domain: str, channel_type: VerifyChannel.ChannelType):
        possible_values = {content}
        if content.find(".") > 0:
            url_content = content
            if not (url_content.startswith("https://") or url_content.startswith("http://")):
                url_content = f"https://{url_content}"
                possible_values.add(url_content)
            parse = urlparse(url_content)
            net_path = parse.path.lstrip("/")
            if net_path:
                possible_values.add(net_path)
                possible_values.add(f'@{net_path}')
        else:
            net_path = content
            if content.startswith('@'):
                net_path = content.lstrip('@')
                possible_values.add(net_path)
            else:
                possible_values.add(f'@{content}')
            url_content = f'https://{domain}/{net_path}'
            possible_values.add(url_content)
        rec = VerifyChannel.query.filter(
            VerifyChannel.channel_type == channel_type,
            VerifyChannel.content.in_(possible_values)
        ).first()
        if not rec:
            return dict(
                is_exits=False,
                channel_type=channel_type.name,
                content=content
            )
        ret_content: str = rec.content
        if not ret_content.startswith('https'):
            ret_content = f'https://{domain}/{ret_content.lstrip("@")}'
        return dict(
            is_exits=True,
            channel_type=channel_type.name,
            content=ret_content
        )


@ns.route('/countries')
@respond_with_code
class CountriesResource(Resource):

    @classmethod
    def get(cls):
        _lang = get_request_language()
        countries = []
        for code in list_country_codes_3():
            with force_locale(_lang.value):
                name = gettext(get_country(code).cn_name)
                countries.append(
                    dict(name=name, value=code)
                )
        if g.lang != Language.ZH_HANS_CN.value:  # 英文字母序
            countries.sort(key=lambda country: country['name'])
        return countries


@ns.route('/lang/countries')
@respond_with_code
class LanguageCountriesResource(Resource):

    @classmethod
    def get(cls):
        countries = {}
        for lang, codes in PositionCountry.COUNTRY_LANG_INDEX.items():
            items = []
            for code in codes:
                if g.lang == Language.ZH_HANS_CN.value:
                    name = get_country(code).cn_name
                else:
                    name = get_country(code).en_name
                items.append(
                    dict(name=name, value=code)
                )

            if g.lang != Language.ZH_HANS_CN.value:  # 英文字母序
                items.sort(key=lambda item: item['name'])
            countries.update({lang.value: items})

        return countries


@ns.route('/historical-data')
@respond_with_code
class TradeHistoricalDataResource(Resource):

    kline_headers = ['timestamp', 'open', 'close', 'high', 'low']
    trade_history_headers = ['time', 'deal_id', 'price', 'amount', 'side']

    class Type(Enum):
        KLINE = 'kline'
        TRADE_HISTORY = 'trade_history'
    class MarketType(Enum):
        SPOT = 'spot'
        DIRECT_PERPETUAL = 'direct_perpetual'
        INVERSE_PERPETUAL = 'inverse_perpetual'
    

    KLINE_TYPES = ('DEAL_PRICE', 'INDEX_PRICE', 'SIGN_PRICE')

    @classmethod
    def _check(cls, kwargs):
        start_date = kwargs['start_time'].date()
        this_month_ = this_month()
        earliest_date = datetime.date(2020, 1, 1)
        if not start_date.day == 1 or not earliest_date <= start_date < this_month_:
            raise InvalidArgument
        market = kwargs['market']
        if kwargs['market_type'] == cls.MarketType.SPOT:
            MarketField().deserialize(market)
        else:
            PerpetualMarketField().deserialize(market)
        if kwargs['type'] == cls.Type.KLINE:
            if not kwargs.get('kline_type') or not kwargs.get('kline_interval'):
                raise InvalidArgument
    
    @classmethod
    def _get_file_name(cls, date_, market, market_type, kline_type=None, kline_interval=None):
        if market_type == cls.MarketType.SPOT:
            type_ = 'Spot'
        else:
            type_ = 'Perpetual'
        if kline_type is None:
            return f'{market}-Trades-{type_}-{date_.strftime("%Y-%m")}.zip'        
        if kline_type in (TradeRedShiftDB.KLINE_TYPE.INDEX_PRICE.name,
                          PerpetualRedShiftDB.KLINE_TYPE.INDEX_PRICE.name):
            file_type = 'Index'
        elif kline_type == PerpetualRedShiftDB.KLINE_TYPE.SIGN_PRICE.name:
            file_type = 'Mark'
        else:
            file_type = 'Kline'

        return f'{market}-{file_type}-{kline_interval.name}-{type_}-{date_.strftime("%Y-%m")}.zip'


    @classmethod
    @ns.use_kwargs(dict(
        market=fields.String(required=True),
        market_type=EnumField(MarketType, required=True),
        start_time=TimestampField(required=True),
        type=EnumField(Type, required=True),
        kline_type=EnumField(enum=KLINE_TYPES),
        kline_interval=EnumField(TradeRedShiftDB.KLINE_INTERVAL),
    ))
    def get(cls, **kwargs):
        cls._check(kwargs)
        file_name = AWSBucketPublic.new_file_key(key=cls._get_file_name(kwargs['start_time'],
                                       kwargs['market'],
                                       kwargs['market_type'],
                                       kwargs.get('kline_type'),
                                       kwargs.get('kline_interval')))
        url = AWSBucketPublic.get_file_url(file_name)
        if not AWSBucketPublic.check_exists(file_name):
            raise HistoryDataNotReady
        return dict(
            file_url=url
        )


@ns.route('/user/ip')
@respond_with_code
class UserIpResource(Resource):

    @classmethod
    def get(cls):
        ip = get_request_ip()
        return dict(ip=ip)
