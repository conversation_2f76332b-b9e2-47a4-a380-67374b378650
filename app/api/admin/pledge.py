# -*- coding: utf-8 -*-
import json
from collections import defaultdict
from decimal import Decimal
from datetime import timed<PERSON><PERSON>
from typing import List, Dict
from webargs import fields
from marshmallow import Schema, EXCLUDE

from flask import g
from sqlalchemy import func, distinct

from app.api.admin.margin.utils import Pagination
from app.business.external_dbs import TradeLogDB
from app.business.market import MarketOfflineHelper
from app.caches.statistics import PledgeStatisticsCache, PledgeStatisticsSumamryCache
from app.common.constants import PrecisionEnum, ServerBalanceType
from app.models import db, User, AssetPrice, Market
from app.models.margin import MarginAssetRule
from app.models.pledge import (
    MAX_PLEDGE_ACCOUNT_ID,
    MIN_PLEDGE_ACCOUNT_ID,
    LoanAsset,
    LoanAssetLiquidationRate,
    PledgeAssetStageConfig,
    PledgeAsset,
    LoanAssetUserRate,
    PledgeLiquidationWhitelistUser,
    PledgePosition,
    PledgePositionTrace,
    PledgeLoanOrder,
    PledgeLoanOrder<PERSON><PERSON>ay<PERSON><PERSON>ory,
    PledgeLoanOrderInterestHistory,
    PledgeLoanOrderRenewHistory,
    PledgeLiquidationHistory,
    PledgeTransferHistory,
    PledgeHistory,
    PledgeLoanHistory,
    PledgeRepayHistory,
    PledgeInterestHistory,
    PledgeExchangeHistory,
    PledgeExchangeOrderHistory,
)
from app.models.mongo.op_log import (AdminOperationLogMySQL as AdminOperationLog,
                                     OPNamespaceObjectUser, OPNamespaceObjectSpot)
from app.exceptions import InvalidArgument
from app.business import get_admin_user_name_map, ServerClient, CacheLock, PriceManager
from app.business.pledge.helper import (
    USDT_ASSET,
    PledgeValueHelper,
    is_pledge_account,
    update_loan_asset_caches,
    update_pledge_asset_caches,
    get_loan_asset_info,
    get_loan_assets_infos,
    get_cached_market_index_prices,
    get_user_pledge_unflat_amount_dict,
)
from app.business.pledge.position import PositionManager, InterestHelper
from app.assets import get_asset_config, list_all_assets, list_pre_assets
from app.api.common import Namespace, Resource, respond_with_code, lock_request
from app.api.common.fields import (
    LimitField,
    PageField,
    EnumField,
    TimestampField,
    PositiveDecimalField,
    BoolField,
)
from app.caches import MarketCache
from app.caches.pledge import AdminPledgePositionLtvCache
from app.utils import today, batch_iter, quantize_amount
from app.utils.amount import amount_to_str
from app.utils.date_ import current_timestamp, timestamp_to_datetime


ns = Namespace("Pledge-Admin")


@ns.route("/loan-assets")
@respond_with_code
class LoanAssetsResource(Resource):
    @classmethod
    def get_loan_assets(cls, only_open=False) -> List[str]:
        q = LoanAsset.query
        if only_open:
            q = q.filter(LoanAsset.status == LoanAsset.Status.OPEN)
        rows = q.with_entities(LoanAsset.asset).all()
        return [r.asset for r in rows]

    @classmethod
    @ns.use_kwargs(
        dict(
            asset=fields.String(),
            status=EnumField(LoanAsset.Status),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 借贷-借币配置列表 """
        q = LoanAsset.query.order_by(LoanAsset.id.desc())
        if asset := kwargs.get("asset"):
            q = q.filter(LoanAsset.asset == asset)
        if status := kwargs.get("status"):
            q = q.filter(LoanAsset.status == status)

        pagination = q.paginate(kwargs["page"], kwargs["limit"], error_out=False)
        total = pagination.total
        rows = pagination.items

        assets = [i.asset for i in rows]
        asset_liq_rows: List[LoanAssetLiquidationRate] = LoanAssetLiquidationRate.query.filter(
            LoanAssetLiquidationRate.asset.in_(assets),
            LoanAssetLiquidationRate.status == LoanAssetLiquidationRate.Status.VALID,
        ).order_by(LoanAssetLiquidationRate.liquidation_amount).all()
        asset_liq_rates_dict = defaultdict(list)
        for r in asset_liq_rows:
            asset_liq_rates_dict[r.asset].append(r)

        items = []
        for r in rows:
            item = r.to_dict(enum_to_name=True)
            item["remain_loanable_amount"] = get_asset_config(r.asset).lendable_amount
            liq_rate_rows = asset_liq_rates_dict[r.asset]
            liq_rate_rows.sort(key=lambda x: x.liquidation_amount)
            liq_rates = [
                {
                    "id": r.id,
                    "liquidation_amount": r.liquidation_amount,
                    "liquidation_ltv": r.liquidation_ltv,
                }
                for r in liq_rate_rows
            ]
            item["liq_rates"] = liq_rates
            items.append(item)

        return dict(
            items=items,
            total=total,
            extra=dict(
                status_dict={i.name: i.value for i in LoanAsset.Status},
                assets=list_all_assets(),
            ),
        )

    class LoanAssetLiqRateSchema(Schema):
        """ 借币币种阶梯强平率 """

        class Meta:
            UNKNOWN = EXCLUDE

        id = fields.Integer(required=False, allow_none=True)  # for put
        liquidation_amount = PositiveDecimalField(required=True)
        liquidation_ltv = PositiveDecimalField(required=True)

    @classmethod
    def calc_min_loan_amount(cls, asset: str) -> Decimal:
        yesterday = today() - timedelta(days=1)
        asset_price_map = AssetPrice.get_close_price_map(yesterday)
        price = asset_price_map[asset]
        return LoanAsset.calc_min_loan_amount(price)

    @classmethod
    def check_asset(cls, asset: str, is_loan_asset: bool = False):
        """ 检查借币币种或质押币种，币种存在市场和指数价格  """

        if asset in list_pre_assets():
            raise InvalidArgument(message=f'币种{asset}为pre token，不可配置')
        if asset == USDT_ASSET:
            return
        if is_loan_asset:
            rule_asset = MarginAssetRule.query.filter(
                MarginAssetRule.asset == asset,
                MarginAssetRule.status == MarginAssetRule.StatusType.OPEN,
            ).first()
            if not rule_asset:
                raise InvalidArgument(message=f'币种{asset} 缺少杠杆借币配置（基础利率）')
        m = f"{asset}{USDT_ASSET}"
        market_row: Market = Market.query.filter(Market.name == m).first()
        if not market_row:
            raise InvalidArgument(message=f'币种{asset} 市场{m}不存在')
        if market_row.status != Market.Status.ONLINE:
            raise InvalidArgument(message=f'币种{asset} 市场{m}不是上架中')
        if market_row.trading_disabled:
            raise InvalidArgument(message=f'币种{asset} 市场{m}交易关闭')
        if m not in get_cached_market_index_prices():
            raise InvalidArgument(message=f'币种{asset} 市场{m}无指数价')
        MarketOfflineHelper.check_permission(m, MarketOfflineHelper.BusinessType.PLEDGE)

    @classmethod
    def check_ltvs(cls, initial_ltv: Decimal, warning_ltv: Decimal, min_liquidation_ltv: Decimal, liq_rates):
        """ 需要满足： 初始 < 预警 < 最小强平ltv < 阶梯强平 """
        if not (initial_ltv < warning_ltv < min_liquidation_ltv):
            raise InvalidArgument(message=f"不满足 初始质押率 < 预警质押率 < 最小强平质押率")
        if liq_rates:
            min_liq = min([i['liquidation_ltv'] for i in liq_rates])
            if min_liq <= min_liquidation_ltv:
                raise InvalidArgument(message=f"不满足 阶梯强平质押率 > 最小强平质押率")
            # 待还资产从小到大配置，对应阶梯质押率应该是从大到小
            amounts = [Decimal(i["liquidation_amount"]) for i in liq_rates]
            amounts.sort()
            ltvs = [Decimal(i["liquidation_ltv"]) for i in liq_rates]
            ltvs.sort(reverse=True)
            if len(set(amounts)) != len(amounts):
                raise InvalidArgument(message=f"阶梯强平质押率 待还数重复")
            if len(set(ltvs)) != len(ltvs):
                raise InvalidArgument(message=f"阶梯强平质押率重复")
            for r in liq_rates:
                index1 = amounts.index(r["liquidation_amount"])
                index2 = ltvs.index(r["liquidation_ltv"])
                if index1 != index2:
                    raise InvalidArgument(message=f"阶梯强平质押率 配置错误")

    @classmethod
    def on_after_change(cls):
        """ 借币配置更新后的一些操作，通知server、更新缓存等 """
        client = ServerClient()
        client.update_assets()  # 更新账户中的币种（资产相关操作）
        client.update_market()  # 更新市场中的账户ID（下单时的account_id）
        update_loan_asset_caches()

    @classmethod
    @lock_request()
    @ns.use_kwargs(
        dict(
            asset=fields.String(required=True),
            # min_loan_amount=PositiveDecimalField(required=True),
            max_loan_amount=PositiveDecimalField(required=True),
            initial_ltv=PositiveDecimalField(required=True),
            warning_ltv=PositiveDecimalField(required=True),
            min_liquidation_ltv=PositiveDecimalField(required=True),
            status=EnumField(LoanAsset.Status, required=True),
            liq_rates=fields.Nested(LoanAssetLiqRateSchema, many=True, required=False),
        )
    )
    def post(cls, **kwargs):
        """ 借贷-新增借币配置 """
        asset = kwargs["asset"].upper()
        cls.check_asset(asset, is_loan_asset=True)

        min_loan_amount = cls.calc_min_loan_amount(asset)
        max_loan_amount = kwargs["max_loan_amount"]
        if min_loan_amount <= Decimal():
            raise InvalidArgument(message=f'计算最小借币数错误: {min_loan_amount}')
        if min_loan_amount > max_loan_amount:
            raise InvalidArgument(message=f'最小借币数:{min_loan_amount} 超过最大借币数:{max_loan_amount}')

        initial_ltv = kwargs["initial_ltv"]
        warning_ltv = kwargs["warning_ltv"]
        min_liquidation_ltv = kwargs["min_liquidation_ltv"]
        asset_liq_rates = kwargs.get("liq_rates") or []
        asset_liq_rates.sort(key=lambda x: x["liquidation_amount"])
        cls.check_ltvs(initial_ltv, warning_ltv, min_liquidation_ltv, asset_liq_rates)

        with CacheLock(key="new_pledge_loan_asset", wait=False):
            row: LoanAsset = LoanAsset.query.filter(LoanAsset.asset == asset).first()
            if row:
                raise InvalidArgument(message=f'当前已有币种{asset}的借币配置信息')

            for asset_liq_rate in asset_liq_rates:
                asset_liq_rate_row = LoanAssetLiquidationRate(
                    asset=asset,
                    liquidation_amount=asset_liq_rate["liquidation_amount"],
                    liquidation_ltv=asset_liq_rate["liquidation_ltv"],
                    status=LoanAssetLiquidationRate.Status.VALID,
                )
                db.session.add(asset_liq_rate_row)
            row = LoanAsset(
                asset=asset,
                account_id=LoanAsset.get_new_account_id(),
                min_loan_amount=min_loan_amount,
                max_loan_amount=max_loan_amount,
                day_rate=0,
                initial_ltv=initial_ltv,
                warning_ltv=warning_ltv,
                min_liquidation_ltv=min_liquidation_ltv,
                status=kwargs["status"],
            )
            db.session.add(row)
            db.session.commit()
        cls.on_after_change()

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSpot.LoanAsset,
            detail=kwargs,
        )

    @classmethod
    @ns.use_kwargs(
        dict(
            asset=fields.String(required=True),
            # min_loan_amount=PositiveDecimalField(required=True),
            max_loan_amount=PositiveDecimalField(required=True),
            initial_ltv=PositiveDecimalField(required=True),
            warning_ltv=PositiveDecimalField(required=True),
            min_liquidation_ltv=PositiveDecimalField(required=True),
            status=EnumField(LoanAsset.Status, required=True),
            liq_rates=fields.Nested(LoanAssetLiqRateSchema, many=True, required=False),
        )
    )
    def put(cls, **kwargs):
        """ 借贷-编辑借币配置 """
        asset = kwargs["asset"].upper()
        row: LoanAsset = LoanAsset.query.filter(LoanAsset.asset == asset).first()
        if not row:
            raise InvalidArgument(message=f'币种{asset}不存在借币配置信息')
        old_data = row.to_dict(enum_to_name=True)

        min_loan_amount = cls.calc_min_loan_amount(asset)
        max_loan_amount = kwargs["max_loan_amount"]
        if min_loan_amount <= Decimal():
            raise InvalidArgument(message=f'计算最小借币数错误: {min_loan_amount}')
        if min_loan_amount > max_loan_amount:
            raise InvalidArgument(message=f'最小借币数:{min_loan_amount} 超过最大借币数:{max_loan_amount}')

        param_liq_rates = kwargs.get("liq_rates") or []
        param_liq_rates.sort(key=lambda x: x["liquidation_amount"])
        initial_ltv = kwargs["initial_ltv"]
        warning_ltv = kwargs["warning_ltv"]
        min_liquidation_ltv = kwargs["min_liquidation_ltv"]
        cls.check_ltvs(initial_ltv, warning_ltv, min_liquidation_ltv, param_liq_rates)

        new_status = kwargs["status"]
        if new_status == LoanAsset.Status.OPEN:
            cls.check_asset(asset, is_loan_asset=True)
        row.status = new_status

        row.min_loan_amount = min_loan_amount
        row.max_loan_amount = max_loan_amount
        row.initial_ltv = initial_ltv
        row.warning_ltv = warning_ltv
        row.min_liquidation_ltv = min_liquidation_ltv

        # 处理阶梯强平率
        db_liq_rate_rows = LoanAssetLiquidationRate.query.filter(
            LoanAssetLiquidationRate.asset == asset,
            LoanAssetLiquidationRate.status == LoanAssetLiquidationRate.Status.VALID,
        )
        param_id_liq_rate_map = {i["id"]: i for i in param_liq_rates if i.get("id")}
        # update or delete
        for db_rate_row in db_liq_rate_rows:
            if rate_param := param_id_liq_rate_map.get(db_rate_row.id):
                db_rate_row.liquidation_amount = rate_param["liquidation_amount"]
                db_rate_row.liquidation_ltv = rate_param["liquidation_ltv"]
            else:
                db_rate_row.status = LoanAssetLiquidationRate.Status.DELETED
        # insert
        to_add_param_rates = [i for i in param_liq_rates if not i.get("id")]
        for asset_liq_rate in to_add_param_rates:
            asset_liq_rate_row = LoanAssetLiquidationRate(
                asset=asset,
                liquidation_amount=asset_liq_rate["liquidation_amount"],
                liquidation_ltv=asset_liq_rate["liquidation_ltv"],
                status=LoanAssetLiquidationRate.Status.VALID,
            )
            db.session.add(asset_liq_rate_row)
        db.session.commit()
        cls.on_after_change()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSpot.LoanAsset,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
            special_data=dict(liq_rates=param_liq_rates),
        )

    @classmethod
    @ns.use_kwargs(
        dict(
            asset=fields.String(required=True),
            status=EnumField(LoanAsset.Status, required=True),
        )
    )
    def patch(cls, **kwargs):
        """ 借贷-借币配置开关 """
        asset = kwargs["asset"].upper()
        row: LoanAsset = LoanAsset.query.filter(LoanAsset.asset == asset).first()
        if not row:
            raise InvalidArgument(message=f'币种{asset}不存在借币配置信息')
        old_status = row.status

        new_status = kwargs["status"]
        if new_status == LoanAsset.Status.OPEN:
            cls.check_asset(asset, is_loan_asset=True)
        row.status = new_status
        db.session.commit()
        cls.on_after_change()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSpot.LoanAsset,
            old_data=dict(status=old_status),
            new_data=dict(status=row.status),
        )


@ns.route("/pledge-asset-stages")
@respond_with_code
class PledgeAssetStagesResource(Resource):
    @classmethod
    def get(cls):
        """ 借贷-质押币档位配置列表 """
        rows = PledgeAssetStageConfig.query.all()
        return dict(
            items=[r.to_dict(enum_to_name=True) for r in rows],
            total=len(rows),
            extra=dict(),
        )

    @classmethod
    def get_stage_row(cls, stage: int) -> PledgeAssetStageConfig:
        row: PledgeAssetStageConfig = PledgeAssetStageConfig.query.filter(
            PledgeAssetStageConfig.stage == stage,
        ).first()
        return row

    @classmethod
    def on_after_change(cls):
        """ 质押配置更新后的一些操作，更新缓存等 """
        update_pledge_asset_caches()

    @classmethod
    @ns.use_kwargs(
        dict(
            stage=fields.Integer(required=True),
            collateral_ratio=PositiveDecimalField(required=True),
        )
    )
    def post(cls, **kwargs):
        """ 借贷-新增质押币档位配置 """
        stage = kwargs["stage"]
        row = cls.get_stage_row(stage)
        if row:
            raise InvalidArgument(message=f'档位{stage}已存在')

        row = PledgeAssetStageConfig(
            stage=stage,
            collateral_ratio=kwargs["collateral_ratio"],
        )
        db.session.add(row)
        db.session.commit()
        cls.on_after_change()

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSpot.LoanAssetStageConfig,
            detail=row.to_dict(enum_to_name=True),
        )

    @classmethod
    @ns.use_kwargs(
        dict(
            stage=fields.Integer(required=True),
            collateral_ratio=PositiveDecimalField(required=True),
        )
    )
    def put(cls, **kwargs):
        """ 借贷-修改质押币档位配置 """
        stage = kwargs["stage"]
        row = cls.get_stage_row(stage)
        if not row:
            raise InvalidArgument(message=f'档位{stage}不存在')
        old_data = row.to_dict(enum_to_name=True)

        row.collateral_ratio = kwargs["collateral_ratio"]
        db.session.commit()
        cls.on_after_change()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSpot.LoanAssetStageConfig,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
        )


@ns.route("/pledge-assets")
@respond_with_code
class PledgeAssetResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            asset=fields.String(),
            status=EnumField(PledgeAsset.Status),
            stage=fields.Integer(),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 借贷-质押币配置列表 """
        q = PledgeAsset.query.order_by(PledgeAsset.id.desc())
        if asset := kwargs.get("asset"):
            q = q.filter(PledgeAsset.asset == asset)
        if status := kwargs.get("status"):
            q = q.filter(PledgeAsset.status == status)
        if stage := kwargs.get("stage"):
            q = q.filter(PledgeAsset.stage == stage)

        pagination = q.paginate(kwargs["page"], kwargs["limit"], error_out=False)
        total = pagination.total
        rows = pagination.items

        stage_rows = PledgeAssetStageConfig.query.all()
        stage_row_map = {r.stage: r for r in stage_rows}

        items = []
        for r in rows:
            item = r.to_dict(enum_to_name=True)
            stage_row = stage_row_map[r.stage]
            item["collateral_ratio"] = stage_row.collateral_ratio
            items.append(item)

        return dict(
            items=items,
            total=total,
            extra=dict(
                status_dict={i.name: i.value for i in PledgeAsset.Status},
                stages=list(stage_row_map),
                assets=list_all_assets(),
            ),
        )

    @classmethod
    def on_after_change(cls):
        """ 质押配置更新后的一些操作，通知server、更新缓存等 """
        client = ServerClient()
        client.update_assets()
        client.update_market()
        update_pledge_asset_caches()

    @classmethod
    @ns.use_kwargs(
        dict(
            asset=fields.String(required=True),
            stage=fields.Integer(required=True),
            max_pledge_usd=PositiveDecimalField(required=True),
            status=EnumField(PledgeAsset.Status, required=True),
        )
    )
    def post(cls, **kwargs):
        """ 借贷-新增质押币配置 """
        asset = kwargs["asset"]
        LoanAssetsResource.check_asset(asset)
        row: PledgeAsset = PledgeAsset.query.filter(
            PledgeAsset.asset == asset,
        ).first()
        if row:
            raise InvalidArgument(message=f'质押币{asset}配置已存在')

        stage = kwargs["stage"]
        stage_row = PledgeAssetStagesResource.get_stage_row(stage)
        if not stage_row:
            raise InvalidArgument(message=f'档位{stage}不存在')

        row = PledgeAsset(
            asset=asset,
            stage=stage,
            max_pledge_usd=kwargs["max_pledge_usd"],
            status=kwargs["status"],
        )
        db.session.add(row)
        db.session.commit()
        cls.on_after_change()

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSpot.LoanPledgeAsset,
            detail=row.to_dict(enum_to_name=True),
        )

    @classmethod
    @ns.use_kwargs(
        dict(
            asset=fields.String(required=True),
            stage=fields.Integer(required=True),
            max_pledge_usd=PositiveDecimalField(required=True),
            status=EnumField(PledgeAsset.Status, required=True),
        )
    )
    def put(cls, **kwargs):
        """ 借贷-修改质押币配置 """
        asset = kwargs["asset"]
        status = kwargs["status"]
        if status == PledgeAsset.Status.OPEN:
            LoanAssetsResource.check_asset(asset)
        row: PledgeAsset = PledgeAsset.query.filter(
            PledgeAsset.asset == asset,
        ).first()
        if not row:
            raise InvalidArgument(message=f'质押币{asset}不已存在')
        old_data = row.to_dict(enum_to_name=True)

        stage = kwargs["stage"]
        stage_row = PledgeAssetStagesResource.get_stage_row(stage)
        if not stage_row:
            raise InvalidArgument(message=f'档位{stage}不存在')

        row.stage = stage
        row.max_pledge_usd = kwargs["max_pledge_usd"]
        row.status = status
        db.session.commit()
        cls.on_after_change()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSpot.LoanPledgeAsset,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
        )

    @classmethod
    @ns.use_kwargs(
        dict(
            asset=fields.String(required=True),
            status=EnumField(PledgeAsset.Status, required=True),
        )
    )
    def patch(cls, **kwargs):
        """ 借贷-质押币配置开关 """
        asset = kwargs["asset"]
        status = kwargs["status"]
        if status == PledgeAsset.Status.OPEN:
            LoanAssetsResource.check_asset(asset)
        row: PledgeAsset = PledgeAsset.query.filter(
            PledgeAsset.asset == asset,
        ).first()
        if not row:
            raise InvalidArgument(message=f'质押币{asset}不已存在')
        old_data = row.to_dict(enum_to_name=True)
        
        row.status = status
        if status == PledgeAsset.Status.TEMP_OFFLINE:
            index_price_map = ServerClient().get_all_indices()
            market = f"{asset}{USDT_ASSET}"
            price = index_price_map.get(market, 0)
            if not price:
                today_str = today().strftime("%Y%m%d")
                table = TradeLogDB.table(f'indexlog_{today_str}')
                r = None
                if table.exists():
                    r = table.select("price", where=f'market="{market}"',
                                     order_by='id desc', limit=1)
                if r:
                    price = quantize_amount(r[0][0], PrecisionEnum.COIN_PLACES)
            if not price:
                raise InvalidArgument(f"获取不到{asset}{USDT_ASSET}市场的指数价格")
            row.locked_index_price = price

        update_loan_asset_caches()
        update_pledge_asset_caches()
        db.session.commit()
        cls.on_after_change()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSpot.LoanPledgeAsset,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
        )


@ns.route("/user-rates")
@respond_with_code
class LoanAssetUserRatesResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            asset=fields.String,
            user_id=fields.Integer,
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 用户-特殊配置-特殊借贷费率->列表 """
        q = LoanAssetUserRate.query.filter(
            LoanAssetUserRate.status == LoanAssetUserRate.Status.VALID,
        ).order_by(LoanAssetUserRate.id.desc())
        if asset := kwargs.get("asset", "").strip():
            q = q.filter(LoanAssetUserRate.asset == asset)
        if user_id := kwargs.get("user_id"):
            q = q.filter(LoanAssetUserRate.user_id == user_id)

        pagination = q.paginate(kwargs["page"], kwargs["limit"])
        total = pagination.total
        rows: List[LoanAssetUserRate] = pagination.items

        user_ids = {i.user_id for i in rows}
        user_email_map = {}
        for ids_ in batch_iter(user_ids, 1000):
            chunk_users = User.query.filter(User.id.in_(ids_)).with_entities(User.id, User.email).all()
            user_email_map.update(dict(chunk_users))

        op_user_ids = list({i.updated_by for i in rows if i.updated_by})
        admin_user_name_map = get_admin_user_name_map(op_user_ids)

        items = []
        for i in rows:
            items.append(
                {
                    "id": i.id,
                    "user_id": i.user_id,
                    "email": user_email_map.get(i.user_id),
                    "asset": i.asset,
                    "day_rate": i.day_rate,
                    "expired_time": i.expired_time,
                    "remark": i.remark,
                    "operator": admin_user_name_map.get(i.updated_by),
                    "operator_id": i.updated_by,
                }
            )
        return dict(
            items=items,
            total=total,
            extra=dict(
                assets=LoanAssetsResource.get_loan_assets(),
            ),
        )

    @classmethod
    def check_precision(cls, amount: Decimal, precision: int) -> bool:
        return amount % Decimal(10) ** -precision == 0

    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=fields.Integer(required=True),
            asset=fields.String(required=True),
            day_rate=PositiveDecimalField(required=True),
            expired_time=TimestampField(is_ms=True, allow_none=True),
            remark=fields.String(missing=""),
        )
    )
    def post(cls, **kwargs):
        """ 用户-特殊配置-特殊借贷费率->新增 """
        day_rate = kwargs["day_rate"]
        if not cls.check_precision(day_rate, 5):
            raise InvalidArgument(message="日利率精度不能超过五位小数")

        user_id = kwargs["user_id"]
        asset = kwargs["asset"]
        user_rate = LoanAssetUserRate.query.filter(
            LoanAssetUserRate.user_id == user_id,
            LoanAssetUserRate.asset == asset,
        ).first()
        if user_rate and user_rate.status == LoanAssetUserRate.Status.VALID:
            raise InvalidArgument(message="当前已有对应的币种利率配置")
        if not user_rate:
            user_rate = LoanAssetUserRate(
                user_id=user_id,
                asset=asset,
            )
        user_rate.status = LoanAssetUserRate.Status.VALID
        user_rate.day_rate = day_rate
        user_rate.expired_time = kwargs.get("expired_time")
        user_rate.remark = kwargs.get("remark") or ""
        user_rate.updated_by = g.user.id
        db.session.add(user_rate)
        db.session.commit()

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSpot.LoanAssetUserRate,
            detail=user_rate.to_dict(enum_to_name=True),
            target_user_id=user_id,
        )

    @classmethod
    @ns.use_kwargs(
        dict(
            id=fields.Integer(required=True),
            day_rate=PositiveDecimalField(required=True),
            expired_time=TimestampField(is_ms=True, allow_none=True),
            remark=fields.String(missing=""),
        )
    )
    def put(cls, **kwargs):
        """ 用户-特殊配置-特殊借贷费率->编辑 """
        day_rate = kwargs["day_rate"]
        if not cls.check_precision(day_rate, 5):
            raise InvalidArgument(message="日利率精度不能超过五位小数")

        user_rate = LoanAssetUserRate.query.filter(
            LoanAssetUserRate.id == kwargs["id"],
        ).first()
        if not user_rate:
            raise InvalidArgument(message="未找到当前特殊借贷费率记录")
        old_data = user_rate.to_dict(enum_to_name=True)
        user_rate.day_rate = day_rate
        user_rate.expired_time = kwargs.get("expired_time")
        user_rate.remark = kwargs.get("remark") or ""
        user_rate.updated_by = g.user.id
        db.session.add(user_rate)
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSpot.LoanAssetUserRate,
            old_data=old_data,
            new_data=user_rate.to_dict(enum_to_name=True),
            target_user_id=user_rate.user_id,
        )

    @classmethod
    @ns.use_kwargs(
        dict(
            id=fields.Integer(required=True),
        )
    )
    def delete(cls, **kwargs):
        """ 用户-特殊配置-特殊借贷费率->删除 """
        user_rate = LoanAssetUserRate.query.filter(
            LoanAssetUserRate.id == kwargs["id"],
        ).first()
        if not user_rate:
            raise InvalidArgument(message="未找到当前特殊借贷费率记录")
        user_rate.status = LoanAssetUserRate.Status.DELETED
        db.session.commit()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSpot.LoanAssetUserRate,
            detail=user_rate.to_dict(enum_to_name=True),
            target_user_id=user_rate.user_id,
        )


@ns.route("/liq-whitelist")
@respond_with_code
class PledgeLiqWhitelistUserResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=fields.Integer,
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 借贷-清算白名单列表 """
        model = PledgeLiquidationWhitelistUser
        query = model.query.filter(model.status == model.Status.VALID)
        if user_id := kwargs.get("user_id"):
            query = query.filter(model.user_id == user_id)

        pagination = query.paginate(kwargs["page"], kwargs["limit"])
        total = pagination.total
        rows: List[PledgeLiquidationWhitelistUser] = pagination.items

        op_user_ids = list({i.updated_by for i in rows if i.updated_by})
        admin_user_name_map = get_admin_user_name_map(op_user_ids)

        user_ids = {i.user_id for i in rows}
        user_email_map = {}
        for ids_ in batch_iter(user_ids, 1000):
            chunk_users = User.query.filter(User.id.in_(ids_)).with_entities(User.id, User.email).all()
            user_email_map.update(dict(chunk_users))

        items = []
        for row in rows:
            item = row.to_dict(enum_to_name=True)
            item.update(
                email=user_email_map.get(row.user_id),
                operator_id=row.updated_by,
                operator=admin_user_name_map.get(row.updated_by),
            )
            items.append(item)

        return dict(
            items=items,
            total=total,
        )

    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=fields.Integer(required=True),
            remark=fields.String(missing=""),
        )
    )
    def post(cls, **kwargs):
        """ 借贷-清算白名单-新增 """
        user_id = kwargs["user_id"]
        user = User.query.filter(User.id == user_id).first()
        if not user:
            raise InvalidArgument(message=f"用户{user_id}不存在")
        if user.is_sub_account:
            raise InvalidArgument(message=f"用户{user_id}是子账号")

        model = PledgeLiquidationWhitelistUser
        row: PledgeLiquidationWhitelistUser = model.query.filter(
            model.user_id == user_id,
        ).first()
        if row and row.status == model.Status.VALID:
            raise InvalidArgument(message=f"用户{user_id}已在名单中")
        if not row:
            row = model(user_id=user_id)
        row.status = model.Status.VALID
        row.remark = kwargs.get("remark") or ""
        row.updated_by = g.user.id
        db.session.add(row)
        db.session.commit()

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSpot.PledgeLiqWhitelist,
            detail=row.to_dict(enum_to_name=True),
            target_user_id=row.user_id,
        )

    @classmethod
    @ns.use_kwargs(
        dict(
            id=fields.Integer(required=True),
            remark=fields.String(missing=""),
        )
    )
    def put(cls, **kwargs):
        """ 借贷-清算白名单-编辑 """
        model = PledgeLiquidationWhitelistUser
        row: PledgeLiquidationWhitelistUser = model.query.filter(
            model.id == kwargs["id"],
            model.status == model.Status.VALID,
        ).first()
        if row is None:
            raise InvalidArgument(message=f"记录不存在")
        old_data = row.to_dict(enum_to_name=True)

        if (remark := kwargs.get("remark")) is not None:
            row.remark = remark
            row.updated_by = g.user.id
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSpot.PledgeLiqWhitelist,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
            target_user_id=row.user_id,
        )

    @classmethod
    @ns.use_kwargs(
        dict(
            id=fields.Integer(required=True),
        )
    )
    def delete(cls, **kwargs):
        """ 借贷-清算白名单-删除 """
        model = PledgeLiquidationWhitelistUser
        row: PledgeLiquidationWhitelistUser = model.query.filter(
            model.id == kwargs["id"],
            model.status == model.Status.VALID,
        ).first()
        if row is None:
            raise InvalidArgument(message=f"记录不存在")

        row.status = model.Status.DELETED
        db.session.commit()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSpot.PledgeLiqWhitelist,
            detail=row.to_dict(enum_to_name=True),
            target_user_id=row.user_id,
        )


@ns.route("/user-accounts")
@respond_with_code
class UserPledgeAccountsResource(Resource):
    @classmethod
    def get_user_pledge_account_balances(cls, user_id) -> Dict[int, Dict]:
        account_balances_dict = defaultdict(dict)
        accounts_balances_dict = ServerClient().get_user_accounts_balances(user_id)
        for account_id, balances in accounts_balances_dict.items():
            account_id = int(account_id)
            if not is_pledge_account(account_id):
                continue
            for asset, values in balances.items():
                total = values['available'] + values['frozen']
                account_balances_dict[account_id][asset] = total
        return account_balances_dict

    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=fields.Integer(required=True),
        )
    )
    def get(cls, **kwargs):
        """ 用户详情-借贷账户 """
        loan_asset_rows: List[LoanAsset] = LoanAsset.query_open_rows()

        user_id = kwargs["user_id"]
        positions: List[PledgePosition] = PledgePosition.query.filter(
            PledgePosition.user_id == user_id,
            PledgePosition.status.in_(PledgePosition.ACTIVE_STATUSES),
        ).all()
        asset_position_dict: Dict[str, PledgePosition] = {i.loan_asset: i for i in positions}
        position_ltvs_dict = PositionManager.batch_calc_ltv_and_liq_ltvs(positions)

        market_index_price_dict = get_cached_market_index_prices()
        account_balances = cls.get_user_pledge_account_balances(user_id)

        account_info_list = []
        pledge_account_total_usd = Decimal()
        asset_price_map = PriceManager.assets_to_usd()
        for row in loan_asset_rows:
            balances = account_balances.get(row.account_id, {})
            asset_pledge_value_dict = PledgeValueHelper.calc_asset_pledge_values(balances, market_index_price_dict)
            pledge_assets = []
            for asset, amount in balances.items():
                pledge_assets.append(
                    {
                        "asset": asset,
                        "amount": amount,
                        "pledge_value": asset_pledge_value_dict.get(asset, 0),
                    }
                )
                pledge_account_total_usd += asset_price_map.get(asset, 0) * amount

            info = {
                "loan_asset": row.asset,
                "account_id": row.account_id,
                "day_rate": row.day_rate,
                "pledge_assets": pledge_assets,
            }
            position = asset_position_dict.get(row.asset)
            if position:
                cur_ltv, warning_ltv, liq_ltv = position_ltvs_dict[position.id]
                info["position"] = {
                    "position_id": position.id,
                    "status": position.status.name,
                    "status_str": position.status.value,
                    "debt_amount": position.debt_amount,
                    "interest_amount": position.interest_amount,
                    "total_unflat_amount": position.total_unflat_amount,
                    "cur_ltv": cur_ltv,
                    "warning_ltv": warning_ltv,
                    "liq_ltv": liq_ltv,
                }
            else:
                info["position"] = None
            account_info_list.append(info)

        pledge_account_unflat_usd = Decimal()
        pledge_unflat_amount_dict = get_user_pledge_unflat_amount_dict(user_id)
        for _asset, _unflat_amount in pledge_unflat_amount_dict.items():
            pledge_account_unflat_usd += _unflat_amount * asset_price_map.get(_asset, 0)

        pledge_account_total_usd = quantize_amount(pledge_account_total_usd, 2)
        pledge_account_unflat_usd = quantize_amount(pledge_account_unflat_usd, 2)
        result = {
            "account_list": account_info_list,
            "summary_info": {
                "total_usd": pledge_account_total_usd,
                "unflat_usd": pledge_account_unflat_usd,
                "equity_usd": pledge_account_total_usd - pledge_account_unflat_usd,
            },
        }

        AdminOperationLog.new_query(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.PledgeAssets,
            target_user_id=user_id,
        )

        return result


@ns.route("/positions")
@respond_with_code
class PledgePositionsResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            asset=fields.String,
            user_id=fields.Integer,
            position_id=fields.Integer,
            status=EnumField(PledgePosition.Status),
            start=TimestampField,
            end=TimestampField,
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 借贷-借币记录列表 """
        model = PledgePosition
        q = model.query
        if asset := kwargs.get("asset"):
            q = q.filter(model.loan_asset == asset)
        if status := kwargs.get("status"):
            q = q.filter(model.status == status)
        if user_id := kwargs.get("user_id"):
            q = q.filter(model.user_id == user_id)
        if position_id := kwargs.get("position_id"):
            q = q.filter(model.id == position_id)
        if start_time := kwargs.get("start"):
            q = q.filter(model.created_at >= start_time)
        if end_time := kwargs.get("end"):
            q = q.filter(model.created_at <= end_time)

        if asset:
            # 筛选币种时，按待还数排序
            q = q.order_by(model.debt_amount.desc(), model.id.desc())
        else:
            q = q.order_by(model.id.desc())

        pagination = q.paginate(kwargs["page"], kwargs["limit"])
        total = pagination.total
        rows: List[PledgePosition] = pagination.items

        pos_ids = [i.id for i in rows]
        if pos_ids:
            traces: List[PledgePositionTrace] = PledgePositionTrace.query.filter(
                PledgePositionTrace.position_id.in_(pos_ids),
            ).all()
            pos_trace_dict = {i.position_id: i for i in traces}
            loan_orders = PledgeLoanOrder.query.filter(
                PledgeLoanOrder.position_id.in_(pos_ids),
                PledgeLoanOrder.status.in_(PledgeLoanOrder.REPAYABLE_STATUSES),
            ).with_entities(
                PledgeLoanOrder.id,
                PledgeLoanOrder.position_id,
                PledgeLoanOrder.loan_amount,
            ).all()
            pos_loan_amount_dict = defaultdict(Decimal)
            for o in loan_orders:
                pos_loan_amount_dict[o.position_id] += o.loan_amount
            pos_ltv_map = AdminPledgePositionLtvCache().get_pos_ltv_map(pos_ids)
        else:
            pos_trace_dict = {}
            pos_loan_amount_dict = defaultdict(Decimal)
            pos_ltv_map = {}

        user_ids = {i.user_id for i in rows}
        user_email_map = {}
        for ids_ in batch_iter(user_ids, 1000):
            chunk_users = User.query.filter(User.id.in_(ids_)).with_entities(
                User.id,
                User.email,
            ).all()
            user_email_map.update(dict(chunk_users))

        loan_assets = {i.loan_asset for i in rows}
        loan_asset_info_dict = {i.asset: i for i in get_loan_assets_infos(list(loan_assets))}

        items = []
        for row in rows:
            trace: PledgePositionTrace = pos_trace_dict.get(row.id)
            loan_asset_info = loan_asset_info_dict[row.loan_asset]
            item = row.to_dict(enum_to_name=True)
            item["user_email"] = user_email_map.get(row.user_id, "")
            item["total_unflat_amount"] = row.total_unflat_amount
            item["account_id"] = loan_asset_info.account_id
            if row.status in [model.Status.FINISHED, model.Status.ARREARS]:
                cur_ltv = Decimal(0)
            else:
                cur_ltv = pos_ltv_map.get(row.id, Decimal(0))
            item["cur_ltv"] = cur_ltv
            item["initial_ltv"] = loan_asset_info.initial_ltv
            item["warning_ltv"] = loan_asset_info.warning_ltv
            item["min_liquidation_ltv"] = loan_asset_info.min_liquidation_ltv
            item["loan_amount"] = pos_loan_amount_dict[row.id]
            item["total_repay_interest_amount"] = trace.total_repay_interest_amount if trace else Decimal()
            item["last_day_rate"] = trace.last_day_rate if trace else Decimal()
            items.append(item)

        if asset:
            q = model.query.filter(model.status.in_(model.ACTIVE_STATUSES))
            if asset:
                q = q.filter(model.loan_asset == asset)
            if user_id:
                q = q.filter(model.user_id == user_id)
            if position_id:
                q = q.filter(model.id == position_id)
            if start_time:
                q = q.filter(model.created_at >= start_time)
            if end_time:
                q = q.filter(model.created_at <= end_time)
            sum_row = q.with_entities(
                func.count(distinct(model.user_id)).label('loan_user_count'),
                func.sum(model.debt_amount).label('sum_debt_amount'),
                func.sum(model.interest_amount).label('sum_interest_amount'),
            ).first()
            price = PriceManager.asset_to_usd(asset)
        else:
            sum_row = None
            price = 0
        summary_data = {
            "loan_user_count": sum_row.loan_user_count if sum_row and sum_row.loan_user_count else 0,
            "sum_debt_amount": sum_row.sum_debt_amount if sum_row and sum_row.sum_debt_amount else 0,
            "sum_interest_amount": sum_row.sum_interest_amount if sum_row and sum_row.sum_interest_amount else 0,
        }
        summary_data["sum_debt_usd"] = quantize_amount(summary_data["sum_debt_amount"] * price, 2)
        summary_data["sum_interest_usd"] = quantize_amount(summary_data["sum_interest_amount"] * price, 2)

        return dict(
            items=items,
            total=total,
            summary_data=summary_data,
            extra=dict(
                status_dict={i.name: i.value for i in PledgePosition.Status},
                finish_type_dict={i.name: i.value for i in PledgePosition.FinishType},
                assets=LoanAssetsResource.get_loan_assets(),
            ),
        )


@ns.route("/position-detail")
@respond_with_code
class PledgePositionDetailResource(Resource):
    @classmethod
    def get_user_pledge_assets(cls, user_id: int, account_id: int) -> List[Dict]:
        pledge_assets = []
        balances = ServerClient().get_user_balances(user_id, account_id=account_id)
        for asset, values in balances.items():
            total = values['available'] + values['frozen']
            if total > Decimal():
                pledge_assets.append({"asset": asset, "amount": total})
        return pledge_assets

    @classmethod
    @ns.use_kwargs(
        dict(
            position_id=fields.Integer(required=True),
        )
    )
    def get(cls, **kwargs):
        """ 借贷-借币详情 """
        model = PledgePosition
        position_id = kwargs["position_id"]
        row: PledgePosition = model.query.filter(model.id == position_id).first()
        if row:
            data = row.to_dict(enum_to_name=True)
            loan_asset_info = get_loan_asset_info(row.loan_asset)
            day_rate = InterestHelper.get_user_day_rate(row.user_id, row.loan_asset)
            trace: PledgePositionTrace = PledgePositionTrace.query.filter(
                PledgePositionTrace.position_id == position_id
            ).first()
            data["total_unflat_amount"] = row.total_unflat_amount
            data["account_id"] = loan_asset_info.account_id
            data["initial_ltv"] = loan_asset_info.initial_ltv
            data["min_liquidation_ltv"] = loan_asset_info.min_liquidation_ltv
            data["day_rate"] = day_rate
            data["max_debt_amount"] = trace.max_debt_amount if trace else Decimal()
            data["total_repay_interest_amount"] = trace.total_repay_interest_amount if trace else Decimal()
            data["last_day_rate"] = trace.last_day_rate if trace else Decimal()
            if row.status in model.CAN_LIQ_STATUSES:
                cur_ltv, warning_ltv, liq_ltv = PositionManager.calc_ltv_and_liq_ltvs(row)
            else:
                cur_ltv = warning_ltv = liq_ltv = Decimal()
            if row.status == model.Status.FINISHED:
                pledge_assets = []
            else:
                pledge_assets = cls.get_user_pledge_assets(row.user_id, loan_asset_info.account_id)
            data["cur_ltv"] = cur_ltv
            data["warning_ltv"] = warning_ltv
            data["liq_ltv"] = liq_ltv
            data["pledge_assets"] = pledge_assets
        else:
            data = {}

        return dict(
            data=data,
            extra=dict(
                finish_type_dict={i.name: i.value for i in PledgePosition.FinishType},
                status_dict={i.name: i.value for i in PledgePosition.Status},
                repay_status_dict={i.name: i.value for i in PledgeRepayHistory.Status},
                repay_type_dict={i.name: i.value for i in PledgeRepayHistory.Type},
                pledge_type_dict={i.name: i.value for i in PledgeHistory.Type},
                interest_type_dict={i.name: i.value for i in PledgeInterestHistory.Type},
                transfer_type_dict={i.name: i.value for i in PledgeTransferHistory.Type},
                transfer_status_dict={i.name: i.value for i in PledgeTransferHistory.Status},
                exchange_type_dict={i.name: i.value for i in PledgeExchangeHistory.Type},
                exchange_status_dict={i.name: i.value for i in PledgeExchangeHistory.Status},
            ),
        )


@ns.route("/position-pledge-assets")
@respond_with_code
class PledgePosPledgeAssetsResource(Resource):
    @classmethod
    def get_user_pledge_account_balances(cls, user_id) -> Dict[int, Dict]:
        account_balances_dict = defaultdict(dict)
        accounts_balances_dict = ServerClient().get_user_accounts_balances(user_id)
        for account_id, balances in accounts_balances_dict.items():
            account_id = int(account_id)
            if not is_pledge_account(account_id):
                continue
            for asset, values in balances.items():
                total = values['available'] + values['frozen']
                account_balances_dict[account_id][asset] = total
        return account_balances_dict

    @classmethod
    @ns.use_kwargs(
        dict(
            position_id=fields.Integer(missing=0),
        )
    )
    def get(cls, **kwargs):
        """ 借贷-借贷持仓-仓位质押币明细 """
        position_id = kwargs["position_id"]
        position: PledgePosition = PledgePosition.query.filter(
            PledgePosition.id == position_id,
        ).first()
        if not position:
            return dict(
                items=[],
                total=0,
            )

        user_id = position.user_id
        user_email = User.query.get(user_id).email
        loan_asset = position.loan_asset
        loan_asset_info = get_loan_asset_info(loan_asset)
        market_index_price_dict = get_cached_market_index_prices()
        if position.status in [PledgePosition.Status.FINISHED, PledgePosition.Status.ARREARS]:
            pledge_assets = []
        else:
            pledge_assets = PledgePositionDetailResource.get_user_pledge_assets(
                user_id, loan_asset_info.account_id,
            )
        balances = {i['asset']: i['amount'] for i in pledge_assets}
        asset_pledge_value_dict = PledgeValueHelper.calc_asset_pledge_values(balances, market_index_price_dict)

        result = []
        for asset, amount in balances.items():
            result.append(
                {
                    "user_id": user_id,
                    "user_email": user_email,
                    "position_id": position_id,
                    "loan_asset": loan_asset,
                    "asset": asset,
                    "amount": amount,
                    "pledge_value": asset_pledge_value_dict.get(asset, 0),
                }
            )
        return dict(
            items=result,
            total=len(result),
        )


@ns.route("/position/relation-history")
@respond_with_code
class PledgePositionRelationHisResource(Resource):

    RELATION_MAP = {
        "loan": PledgeLoanHistory,
        "repay": PledgeRepayHistory,
        "pledge": PledgeHistory,
        "interest": PledgeInterestHistory,
        "transfer": PledgeTransferHistory,
        "exchange": PledgeExchangeHistory,
    }

    @classmethod
    @ns.use_kwargs(
        dict(
            history_type=EnumField(RELATION_MAP),
            asset=fields.String,
            user_id=fields.Integer,
            position_id=fields.Integer,
            start=TimestampField,
            end=TimestampField,
            repay_status=EnumField(PledgeRepayHistory.Status),
            transfer_status=EnumField(PledgeTransferHistory.Status),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 借贷-借币详情-相关记录 """
        history_type = kwargs["history_type"]
        model = cls.RELATION_MAP[history_type]
        q = model.query.order_by(model.id.desc())
        if position_id := kwargs.get("position_id"):
            q = q.filter(model.position_id == position_id)
        if asset := kwargs.get("asset"):
            q = q.filter(model.loan_asset == asset)
        if user_id := kwargs.get("user_id"):
            q = q.filter(model.user_id == user_id)
        if start_time := kwargs.get("start"):
            q = q.filter(model.created_at >= start_time)
        if end_time := kwargs.get("end"):
            q = q.filter(model.created_at <= end_time)
        # special filter
        if model == PledgeRepayHistory and (repay_status := kwargs.get("repay_status")):
            q = q.filter(model.status == repay_status)
        if model == PledgeTransferHistory and (transfer_status := kwargs.get("transfer_status")):
            q = q.filter(model.status == transfer_status)
        if model == PledgeTransferHistory and position_id == 0:
            q = q.filter(model.position_id.is_(None))

        pagination = q.paginate(kwargs["page"], kwargs["limit"])
        total = pagination.total
        rows = pagination.items

        items = []
        json_cols = ["pledge_data", "used_pledge_data", "init_pledge_data", "finished_pledge_data"]
        for r in rows:
            item = r.to_dict(enum_to_name=True)
            for k, v in item.items():
                if k in json_cols:
                    item[k] = [{"asset": a, "amount": m} for a, m in json.loads(v).items()] if v else []
            items.append(item)

        return dict(
            items=items,
            total=total,
            extra=dict(
                transfer_type_dict={i.name: i.value for i in PledgeTransferHistory.Type},
                transfer_status_dict={i.name: i.value for i in PledgeTransferHistory.Status},
                exchange_type_dict={i.name: i.value for i in PledgeExchangeHistory.Type},
                exchange_status_dict={i.name: i.value for i in PledgeExchangeHistory.Status},
            ),
        )


@ns.route("/liq-list")
@respond_with_code
class PledgeLiqListResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            asset=fields.String,
            user_id=fields.Integer,
            position_id=fields.Integer,
            status=EnumField(PledgeLiquidationHistory.Status),
            has_fund_pay=BoolField(),
            start=TimestampField,
            end=TimestampField,
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 借贷-强平记录列表 """
        model = PledgeLiquidationHistory
        q = model.query.order_by(model.id.desc())
        if asset := kwargs.get("asset"):
            q = q.filter(model.loan_asset == asset)
        if status := kwargs.get("status"):
            q = q.filter(model.status == status)
        if position_id := kwargs.get("position_id"):
            q = q.filter(model.position_id == position_id)
        if user_id := kwargs.get("user_id"):
            q = q.filter(model.user_id == user_id)
        if (has_fund_pay := kwargs.get("has_fund_pay")) is not None:
            if has_fund_pay:
                q = q.filter(model.fund_repay_amount > 0)
            else:
                q = q.filter(model.fund_repay_amount == 0)
        if start_time := kwargs.get("start"):
            q = q.filter(model.created_at >= start_time)
        if end_time := kwargs.get("end"):
            q = q.filter(model.created_at <= end_time)

        pagination = q.paginate(kwargs["page"], kwargs["limit"])
        rows: List[PledgeLiquidationHistory] = pagination.items
        total = pagination.total

        user_ids = {i.user_id for i in rows}
        user_email_map = {}
        for ids_ in batch_iter(user_ids, 1000):
            chunk_users = User.query.filter(User.id.in_(ids_)).with_entities(
                User.id,
                User.email,
            ).all()
            user_email_map.update(dict(chunk_users))

        liq_exchange_ids = [i.exchange_id for i in rows]
        liq_exchange_rows = PledgeExchangeHistory.query.filter(
            PledgeExchangeHistory.id.in_(liq_exchange_ids),
        ).with_entities(
            PledgeExchangeHistory.position_id,
            PledgeExchangeHistory.init_pledge_data,
        ).all()
        pos_init_pledge_data_map = dict(liq_exchange_rows)

        items = []
        for row in rows:
            item = row.to_dict(enum_to_name=True)
            unflat_amount = row.init_total_unflat_amount - row.fund_repay_amount - row.repay_amount
            if row.status == model.Status.FINISHED:
                status_str = "已完成"
            elif unflat_amount > 0:
                status_str = "欠款中"
            else:
                status_str = "强平中"
            item["user_email"] = user_email_map.get(row.user_id, "")
            item["status_str"] = status_str
            item["unflat_amount"] = unflat_amount
            item["used_pledge_data"] = [
                {"asset": a, "amount": m}
                for a, m in json.loads(row.used_pledge_data).items()
            ] if row.used_pledge_data else []
            item["fee_data"] = [
                {"asset": a, "amount": m}
                for a, m in json.loads(row.fee_data).items()
            ] if row.fee_data else []
            init_pledge_data_ = pos_init_pledge_data_map.get(row.position_id)
            item["init_pledge_data"] = [
                {"asset": a, "amount": m}
                for a, m in json.loads(init_pledge_data_).items()
            ] if init_pledge_data_ else []

            items.append(item)

        sum_row = q.filter(model.fund_repay_amount > 0).with_entities(
            func.count(model.id).label('fund_repay_count'),
            func.sum(model.fund_repay_amount).label('sum_fund_repay_amount'),
        ).first()
        summary_data = {
            "liq_count": total,
            "fund_repay_count": sum_row.fund_repay_count if sum_row and sum_row.fund_repay_count else 0,
            "sum_fund_repay_amount": sum_row.sum_fund_repay_amount if sum_row and sum_row.sum_fund_repay_amount else 0,
        }

        return dict(
            items=items,
            total=total,
            summary_data=summary_data,
            extra=dict(
                status_dict={i.name: i.value for i in PledgeLiquidationHistory.Status},
                assets=LoanAssetsResource.get_loan_assets(),
            ),
        )


@ns.route("/exchange-order-list")
@respond_with_code
class PledgeExchangeOrderListResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            exchange_id=fields.Integer,
            market=fields.String,
            side=EnumField(['1', '2']),
            start=TimestampField,
            end=TimestampField,
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 借贷-兑换订单列表 """
        model = PledgeExchangeOrderHistory
        q = model.query.order_by(model.id.desc())
        if exchange_id := kwargs.get("exchange_id"):
            q = q.filter(model.exchange_id == exchange_id)
        if market := kwargs.get("market"):
            q = q.filter(model.market == market)
        if side := kwargs.get("side"):
            q = q.filter(model.side == side)
        if start_time := kwargs.get("start"):
            q = q.filter(model.created_at >= start_time)
        if end_time := kwargs.get("end"):
            q = q.filter(model.created_at <= end_time)

        pagination = q.paginate(kwargs["page"], kwargs["limit"])
        rows: List[PledgeExchangeOrderHistory] = pagination.items
        total = pagination.total

        items = []
        market_info_map = {}
        for row in rows:
            item = row.to_dict(enum_to_name=True)
            if row.market in market_info_map:
                market_info = market_info_map[row.market]
            else:
                market_info = MarketCache(row.market).dict
                market_info_map[row.market] = market_info
            item["base_asset"] = market_info["base_asset"]
            item["quote_asset"] = market_info["quote_asset"]
            items.append(item)

        return dict(
            items=items,
            total=total,
            extra=dict(
                markets=MarketCache.list_online_markets(),
            ),
        )


@ns.route("/active-position-detail")
@respond_with_code
class PledgeActivePositionDetailResource(Resource):

    ALL_ASSETS = 'ALL_ASSETS'

    @classmethod
    def _get_snapshot_balance(cls, ts, asset, account_id=None):
        table = None
        for t_ in (ts, ts - 3600):
            table = TradeLogDB.slice_balance_table(t_)
            if table:
                break
        if not table:
            return
        if account_id:
            account_cond = f"account={account_id}"
        else:
            account_cond = f"account >= {MIN_PLEDGE_ACCOUNT_ID} and account <= {MAX_PLEDGE_ACCOUNT_ID}"
        fields = ('user_id', 'asset', 'balance', 'account')
        results = table.select(
                "user_id", "asset", "balance", "account",
                where=f"{account_cond}"
                        f" and t = {ServerBalanceType.AVAILABLE.value}"
                        f" and asset = '{asset}'",
                )
        results = [dict(zip(fields, r)) for r in results]
        return results

    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=fields.Integer,
            loan_asset=fields.String,
            pledge_asset=fields.String(required=True),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 借贷-质押币持仓详情 """
        ts = current_timestamp(to_int=True)
        pledge_asset = kwargs["pledge_asset"]
        account_id = None
        if loan_asset:= kwargs.get("loan_asset"):
            account_id = get_loan_asset_info(loan_asset).account_id
        balances = cls._get_snapshot_balance(ts, pledge_asset, account_id)
        account_ids, user_ids = set(), set()
        user_asset_map = defaultdict(Decimal)
        for item in balances:
            account_ids.add(item['account'])
            user_ids.add(item['user_id'])
            user_asset_map[(item['user_id'], item['account'])] += item['balance']
        loan_asset_records = LoanAsset.query.filter(
            LoanAsset.account_id.in_(account_ids)
        ).with_entities(LoanAsset.account_id, LoanAsset.asset).all()
        account_loan_asset_map = dict(loan_asset_records)

        if user_id:= kwargs.get("user_id"):
            user_ids = {user_id}
        index_price_map = get_cached_market_index_prices()
        index_price_map = {k: Decimal(v) for k, v in index_price_map.items()}

        pagination = Pagination(kwargs['page'], kwargs['limit'], len(balances), balances, need_slice=True)

        user_ids = {i['user_id'] for i in pagination.items}
        user_emails = User.query.filter(User.id.in_(user_ids)).with_entities(User.id, User.email).all()
        user_email_map = dict(user_emails)

        config_map = PledgeValueHelper.get_pledge_asset_info_dict([pledge_asset])
        result = []
        for item in pagination.items:
            loan_asset = account_loan_asset_map[item['account']]
            account_id = item['account']
            balance = user_asset_map[(item['user_id'], account_id)]
            price = index_price_map.get(f'{pledge_asset}{USDT_ASSET}', 0)
            conf = config_map[pledge_asset]
            valid_value = min(balance * price * conf['collateral_ratio'],
                              conf['max_pledge_usd'])
            result.append(dict(
                user_id=item['user_id'],
                email=user_email_map.get(item['user_id'], ''),
                pledge_asset=pledge_asset,
                loan_asset=loan_asset,
                balance=amount_to_str(balance, PrecisionEnum.COIN_PLACES),
                value=amount_to_str(balance * price, PrecisionEnum.CASH_PLACES),
                valid_value=amount_to_str(valid_value, PrecisionEnum.CASH_PLACES)
            ))
        summary = {}
        update_ts = None
        cache = PledgeStatisticsSumamryCache(kwargs.get("loan_asset") or cls.ALL_ASSETS, pledge_asset)
        if data:= cache.read():
            data = json.loads(data)
            summary = data['summary']
            summary['pledge_asset'] = 'ALL'   # 汇总数据一律设为ALL
            update_ts = data['updated_at']
        result.insert(0, summary)
        return dict(
            items=result,
            total=len(balances),
            updated_at=timestamp_to_datetime(update_ts) if update_ts else None,
            loan_assets=LoanAssetsResource.get_loan_assets(),
            pledge_assets=[item.asset for item in PledgeAsset.query.with_entities(PledgeAsset.asset).all()]
        )


@ns.route("/active-position-summary")
@respond_with_code
class PledgeActivePositionSummaryResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            loan_asset=fields.String,
            pledge_asset=fields.String,
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 借贷-质押币持仓 """
        _ALL_ASSET = PledgeActivePositionDetailResource.ALL_ASSETS
        pledge_asset = kwargs.get("pledge_asset") or _ALL_ASSET
        loan_asset = kwargs.get("loan_asset") or _ALL_ASSET
        summary_cache = PledgeStatisticsSumamryCache(loan_asset, pledge_asset)
        detail_cache = PledgeStatisticsCache(loan_asset)

        summary, update_ts = {}, None
        detail = []
        if data:= summary_cache.read():
            data = json.loads(data)
            summary = data['summary']
            summary['pledge_asset'] = 'ALL'   # 汇总数据一律设为ALL
            summary['amount'] = None
            update_ts = data['updated_at']

        if data:= detail_cache.read():
            data = json.loads(data)
            detail = data['detail']
        if pledge_asset != _ALL_ASSET:
            detail = [item for item in detail if item['pledge_asset'] == pledge_asset]
        pagintation = Pagination(kwargs['page'], kwargs['limit'], len(detail), detail, need_slice=True)
        items = pagintation.items
        items.insert(0, summary)
        return dict(
            items=pagintation.items,
            total=len(detail),
            updated_at=timestamp_to_datetime(update_ts) if update_ts else None,
            loan_assets=LoanAssetsResource.get_loan_assets(),
            pledge_assets=[item.asset for item in PledgeAsset.query.with_entities(PledgeAsset.asset).all()]
        )


@ns.route("/loan-order-list")
@respond_with_code
class PledgeLoanOrderListResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            loan_order_id=fields.Integer,
            user_id=fields.Integer,
            position_id=fields.Integer,
            asset=fields.String,
            status=EnumField(PledgeLoanOrder.Status),
            start=TimestampField,
            end=TimestampField,
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 借贷-借币记录列表 """
        model = PledgeLoanOrder
        q = model.query.order_by(model.id.desc())
        if loan_order_id := kwargs.get("loan_order_id"):
            q = q.filter(model.id == loan_order_id)
        if user_id := kwargs.get("user_id"):
            q = q.filter(model.user_id == user_id)
        if position_id := kwargs.get("position_id"):
            q = q.filter(model.position_id == position_id)
        if loan_asset := kwargs.get("asset"):
            q = q.filter(model.loan_asset == loan_asset)
        if status := kwargs.get("status"):
            q = q.filter(model.status == status)
        if start_time := kwargs.get("start"):
            q = q.filter(model.created_at >= start_time)
        if end_time := kwargs.get("end"):
            q = q.filter(model.created_at <= end_time)

        pagination = q.paginate(kwargs["page"], kwargs["limit"])
        total = pagination.total
        rows: List[model] = pagination.items

        user_ids = {i.user_id for i in rows}
        user_email_map = {}
        for ids_ in batch_iter(user_ids, 1000):
            chunk_users = User.query.filter(User.id.in_(ids_)).with_entities(
                User.id,
                User.email,
            ).all()
            user_email_map.update(dict(chunk_users))

        loan_order_ids = [i.id for i in rows]
        last_renew_ids = PledgeLoanOrderRenewHistory.query.filter(
            PledgeLoanOrderRenewHistory.loan_order_id.in_(loan_order_ids),
        ).group_by(
            PledgeLoanOrderRenewHistory.loan_order_id,
        ).with_entities(
            PledgeLoanOrderRenewHistory.loan_order_id,
            func.max(PledgeLoanOrderRenewHistory.id).label('id'),
        ).all()
        last_renew_ids = [i.id for i in last_renew_ids]
        last_renew_rows = PledgeLoanOrderRenewHistory.query.filter(
            PledgeLoanOrderRenewHistory.id.in_(last_renew_ids),
        ).with_entities(
            PledgeLoanOrderRenewHistory.loan_order_id,
            PledgeLoanOrderRenewHistory.status,
        ).all()
        renew_delaying_loan_order_ids = {
            i.loan_order_id for i in last_renew_rows if i.status == PledgeLoanOrderRenewHistory.Status.DELAYING
        }

        items = []
        for row in rows:
            item = row.to_dict(enum_to_name=True)
            item["user_email"] = user_email_map.get(row.user_id, "")
            item["is_renew_delaying"] = row.id in renew_delaying_loan_order_ids
            item["total_unflat_amount"] = row.total_unflat_amount
            items.append(item)

        return dict(
            items=items,
            total=total,
            extra=dict(
                assets=LoanAssetsResource.get_loan_assets(),
                status_dict={i.name: i.value for i in PledgeLoanOrder.Status},
            ),
        )


@ns.route("/loan-order-detail")
@respond_with_code
class PledgeLoanOrderDetailResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            loan_order_id=fields.Integer(required=True),
        )
    )
    def get(cls, **kwargs):
        """ 借贷-借币订单详情 """
        model = PledgeLoanOrder
        loan_order_id = kwargs["loan_order_id"]
        row: model = model.query.filter(model.id == loan_order_id).first()
        if row:
            data = row.to_dict(enum_to_name=True)
            user_ = User.query.filter(User.id == row.user_id).with_entities(User.email).first()
            data['user_email'] = user_.email if user_ else ""
            last_renew_his = PledgeLoanOrderRenewHistory.query.filter(
                PledgeLoanOrderRenewHistory.loan_order_id == loan_order_id,
            ).order_by(
                PledgeLoanOrderRenewHistory.id.desc(),
            ).with_entities(
                PledgeLoanOrderRenewHistory.status,
            ).first()
            is_renew_delaying = last_renew_his and last_renew_his.status == PledgeLoanOrderRenewHistory.Status.DELAYING
            data['is_renew_delaying'] = is_renew_delaying
        else:
            data = {}

        return dict(
            data=data,
            extra=dict(
                status_dict={i.name: i.value for i in PledgeLoanOrder.Status},
                repay_type_dict={i.name: i.value for i in PledgeRepayHistory.Type},
                renew_status_dict={i.name: i.value for i in PledgeLoanOrderRenewHistory.Status},
            ),
        )


@ns.route("/loan-order/relation-history")
@respond_with_code
class PledgeLoanOrderRelationHisResource(Resource):

    RELATION_MAP = {
        "repay": PledgeLoanOrderRepayHistory,
        "renew": PledgeLoanOrderRenewHistory,
        "interest": PledgeLoanOrderInterestHistory,
    }

    @classmethod
    @ns.use_kwargs(
        dict(
            history_type=EnumField(RELATION_MAP),
            asset=fields.String,
            user_id=fields.Integer,
            position_id=fields.Integer,
            loan_order_id=fields.Integer,
            start=TimestampField,
            end=TimestampField,
            repay_type=EnumField(PledgeRepayHistory.Type),
            renew_status=EnumField(PledgeLoanOrderRenewHistory.Status),
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 借贷-借币订单详情-相关记录 """
        history_type = kwargs["history_type"]
        model = cls.RELATION_MAP[history_type]
        q = model.query.order_by(model.id.desc())
        if position_id := kwargs.get("position_id"):
            q = q.filter(model.position_id == position_id)
        if loan_order_id := kwargs.get("loan_order_id"):
            q = q.filter(model.loan_order_id == loan_order_id)
        if user_id := kwargs.get("user_id"):
            q = q.filter(model.user_id == user_id)
        if loan_asset := kwargs.get("asset"):
            q = q.filter(model.loan_asset == loan_asset)
        if start_time := kwargs.get("start"):
            q = q.filter(model.created_at >= start_time)
        if end_time := kwargs.get("end"):
            q = q.filter(model.created_at <= end_time)
        # special filter
        if model == PledgeLoanOrderRepayHistory and (repay_type := kwargs.get("repay_type")):
            q = q.filter(model.type == repay_type)
        if model == PledgeLoanOrderRenewHistory and (renew_status := kwargs.get("renew_status")):
            q = q.filter(model.status == renew_status)

        pagination = q.paginate(kwargs["page"], kwargs["limit"])
        total = pagination.total
        rows: List[model] = pagination.items

        items = []
        for r in rows:
            item = r.to_dict(enum_to_name=True)
            items.append(item)

        return dict(
            items=items,
            total=total,
        )


@ns.route("/loan-order/repay-history")
@respond_with_code
class PledgeLoanOrderRepayHistoryResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            history_id=fields.Integer,
            loan_order_id=fields.Integer,
            user_id=fields.Integer,
            position_id=fields.Integer,
            asset=fields.String,
            type=EnumField(PledgeRepayHistory.Type),
            start=TimestampField,
            end=TimestampField,
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 借贷-还币记录（借币订单） """
        model = PledgeLoanOrderRepayHistory
        q = model.query.order_by(model.id.desc())
        if history_id := kwargs.get("history_id"):
            q = q.filter(model.id == history_id)
        if loan_order_id := kwargs.get("loan_order_id"):
            q = q.filter(model.loan_order_id == loan_order_id)
        if position_id := kwargs.get("position_id"):
            q = q.filter(model.position_id == position_id)
        if user_id := kwargs.get("user_id"):
            q = q.filter(model.user_id == user_id)
        if loan_asset := kwargs.get("asset"):
            q = q.filter(model.loan_asset == loan_asset)
        if type_ := kwargs.get("type"):
            q = q.filter(model.type == type_)
        if end_time := kwargs.get("end"):
            q = q.filter(model.created_at <= end_time)
        if start_time := kwargs.get("start"):
            q = q.filter(model.created_at >= start_time)

        pagination = q.paginate(kwargs["page"], kwargs["limit"])
        total = pagination.total
        rows: List[model] = pagination.items

        user_ids = {i.user_id for i in rows}
        user_email_map = {}
        for ids_ in batch_iter(user_ids, 1000):
            chunk_users = User.query.filter(
                User.id.in_(ids_),
            ).with_entities(
                User.id,
                User.email,
            ).all()
            user_email_map.update(dict(chunk_users))

        items = []
        for row in rows:
            item = row.to_dict(enum_to_name=True)
            item["user_email"] = user_email_map.get(row.user_id, "")
            items.append(item)

        return dict(
            items=items,
            total=total,
            extra=dict(
                assets=LoanAssetsResource.get_loan_assets(),
                type_dict={i.name: i.value for i in PledgeRepayHistory.Type},
            ),
        )
