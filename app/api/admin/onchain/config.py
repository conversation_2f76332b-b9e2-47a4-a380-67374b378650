from decimal import Decimal

from marshmallow import fields

from flask import g

from app.config import config

from app.common.onchain import Chain

from app.exceptions import InvalidArgument

from app.api.common import Namespace
from app.api.common import Resource
from app.api.common import respond_with_code

from app.business.onchain.base import OnchainSettings

from app.models.mongo.op_log import OPNamespaceObjectSpot
from app.models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog

ns = Namespace('Onchain - Config')


@ns.route('/')
@respond_with_code
class OnchainConfigResource(Resource):

    @classmethod
    def _get_value(cls, key: str) -> Decimal | None:
        val = getattr(OnchainSettings, key)
        if not val:
            return None
        return val

    @classmethod
    def _get_by_chain(cls, chain: Chain) -> dict:
        quantity_keys = OnchainSettings.get_quantity_per_order_limit_keys(chain)
        return {
            'hot_limit': {
                key.value: cls._get_value(OnchainSettings.get_hot_limit_key(chain, key))
                for key in OnchainSettings.HotLimitKey
            },
            'quantity_per_order_limit': {
                key: cls._get_value(OnchainSettings.get_quantity_per_order_limit_key(chain, key))
                for key in quantity_keys
            },
        }

    @classmethod
    def _get(cls):
        result: dict = {chain.value: cls._get_by_chain(chain) for chain in Chain}
        result['fee_ratio'] = Decimal(config['ONCHAIN_CONFIGS']['fee_ratio']) * Decimal('100')  # 手续费比例
        return result

    @classmethod
    def get(cls):
        """链上交易-全局配置-列表"""
        return cls._get()

    @classmethod
    @ns.use_kwargs(dict(
        keys=fields.Dict(required=True),
    ))
    def put(cls, **kwargs):
        """链上交易-全局配置-编辑"""
        old_data = cls._get()
        for key, val in kwargs['keys'].items():
            if key == 'fee_ratio':
                continue
            try:
                chain = Chain(key)
                hot_limit = val['hot_limit']
                quantity_per_limit = val['quantity_per_order_limit']
            except ValueError:
                raise InvalidArgument(message=f'invalid key: {key}')
            for hot_key, hot_val in hot_limit.items():
                try:
                    hot_key = OnchainSettings.HotLimitKey(hot_key)
                except ValueError:
                    raise InvalidArgument(message=f'invalid key: {hot_key}')
                _k = OnchainSettings.get_hot_limit_key(chain, hot_key)
                if hot_val:
                    setattr(OnchainSettings, _k, hot_val)
                else:
                    delattr(OnchainSettings, _k)
            for quantity_key, quantity_val in quantity_per_limit.items():
                _k = OnchainSettings.get_quantity_per_order_limit_key(chain, quantity_key)
                if quantity_val:
                    setattr(OnchainSettings, _k, quantity_val)
                else:
                    delattr(OnchainSettings, _k)

        new_data = cls._get()
        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSpot.OnchainConfig,
            old_data=old_data,
            new_data=new_data,
        )
        return new_data
