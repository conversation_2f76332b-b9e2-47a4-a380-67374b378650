from decimal import Decimal

from marshmallow import fields

from flask import g

from app.exceptions import InvalidArgument

from app.assets.asset import get_asset
from app.assets.asset import list_all_assets

from app.api.common import Namespace
from app.api.common import Resource
from app.api.common import respond_with_code
from app.api.common.fields import EnumField
from app.api.common.fields import <PERSON><PERSON>ield
from app.api.common.fields import LimitField

from app.business.onchain.base import OnchainAddressHelper
from app.business.onchain.token import get_token_base_from_coinex_wallet

from app.common.onchain import Chain

from app.utils.onchain import decimal_mul
from app.utils.onchain import decimal_add

from app.models import db
from app.models.user import User
from app.models.onchain import OnchainToken
from app.models.onchain import OnchainTokenBalance
from app.models.onchain import OnchainTokenBlocklist
from app.models.mongo.op_log import OPNamespaceObjectSpot
from app.models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog

from app.caches.onchain import OnchainTokenQuoteCache

ns = Namespace('Onchain - Token')


@ns.route('/blocklist')
@respond_with_code
class OnchainTokenBlocklistResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        chain=EnumField(Chain),
        contract=fields.String(),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50),
    ))
    def get(cls, **kwargs):
        """链上交易-代币限制管理-列表"""
        page, limit = kwargs['page'], kwargs['limit']
        query = OnchainTokenBlocklist.query.order_by(OnchainTokenBlocklist.id.desc())
        if chain := kwargs.get('chain'):
            query = query.filter(
                OnchainTokenBlocklist.chain == chain,
            )
        if contract := kwargs.get('contract'):
            if contract.startswith('0x'):
                contract = OnchainAddressHelper(Chain.ERC20).normalise_address(contract)
            query = query.filter(
                OnchainTokenBlocklist.contract == contract,
            )
        pagination = query.paginate(page, limit, error_out=False)
        user_ids = [item.admin_user_id for item in pagination.items]

        users = User.query.filter(User.id.in_(user_ids)).all()
        user_email_map = {u.id: u.email for u in users}

        return dict(
            items=[
                dict(
                    id=item.id,
                    chain=item.chain.name,
                    contract=item.contract,
                    symbol=item.symbol,
                    name=item.name,
                    block_type=item.block_type.name,
                    notice_type=item.notice_type.name if item.notice_type else None,
                    spot_asset=item.spot_asset,
                    reason=item.reason,
                    admin_user=user_email_map.get(item.admin_user_id, str(item.admin_user_id)),
                    admin_user_id=item.admin_user_id,
                    created_at=item.created_at,
                ) for item in pagination.items
            ],
            total=pagination.total,
            chains=[item.name for item in Chain],
            block_types={item.name: item.value for item in OnchainTokenBlocklist.BlockType},
            notice_types={item.name: item.value for item in OnchainTokenBlocklist.NoticeType},
            assets=list_all_assets(),
        )

    @classmethod
    @ns.use_kwargs(dict(
        chain=EnumField(Chain, required=True),
        contract=fields.String(required=True),
        block_type=EnumField(OnchainTokenBlocklist.BlockType, required=True),
        notice_type=EnumField(OnchainTokenBlocklist.NoticeType, required=False),
        spot_asset=fields.String(),
        reason=fields.String(),
    ))
    def put(cls, **kwargs):
        """链上交易-代币限制管理-添加/编辑"""
        chain = kwargs['chain']
        contract = OnchainAddressHelper(chain).normalise_address(kwargs['contract'])
        block_type = kwargs['block_type']
        notice_type = kwargs.get('notice_type')
        spot_asset = kwargs.get('spot_asset') or None
        reason = kwargs.get('reason')
        if block_type in OnchainTokenBlocklist.BlockType.can_not_trade_type():
            if not notice_type:
                raise InvalidArgument(message='禁止交易时需要设置提示分类')
            if notice_type == OnchainTokenBlocklist.NoticeType.ABNORMAL:
                spot_asset = None

        if spot_asset:
            get_asset(spot_asset)  # 检查spot_asset是否有效

        token_base = get_token_base_from_coinex_wallet(chain, contract)
        if not token_base:
            raise InvalidArgument(message=f'未找到Token, chain: {chain}, contract: {contract}')

        block: OnchainTokenBlocklist = OnchainTokenBlocklist.get_or_create(chain=chain, contract=contract)
        old_data = block.to_dict(enum_to_name=True) if block.id else None
        block.symbol = token_base.symbol
        block.name = token_base.name
        block.block_type = block_type
        if block_type in OnchainTokenBlocklist.BlockType.can_not_trade_type():
            block.notice_type = notice_type
            if notice_type != OnchainTokenBlocklist.NoticeType.ABNORMAL:
                block.spot_asset = spot_asset
        block.reason = reason
        block.admin_user_id = g.user.id
        db.session_add_and_commit(block)

        AdminOperationLog.new_add_or_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSpot.OnchainTokenBlocklist,
            old_data=old_data,
            new_data=block.to_dict(enum_to_name=True),
            special_data=dict(chain=chain.name, contract=contract, symbol=block.symbol, name=block.name),
        )
        return dict()

    @classmethod
    @ns.use_kwargs(dict(
        chain=EnumField(Chain, required=True),
        contract=fields.String(required=True),
    ))
    def delete(cls, **kwargs):
        """链上交易-代币限制管理-删除"""
        chain = kwargs['chain']
        contract = OnchainAddressHelper(chain).normalise_address(kwargs['contract'])
        block: OnchainTokenBlocklist = OnchainTokenBlocklist.get_or_create(chain=chain, contract=contract)
        if not block:
            raise InvalidArgument(message=f'未找到Token, chain: {chain}, contract: {contract}')
        op_log_detail = block.to_dict(enum_to_name=True)

        db.session.delete(block)
        db.session.commit()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSpot.OnchainTokenBlocklist,
            detail=op_log_detail,
        )
        return dict()


@ns.route('/<int:user_id>/balance')
@respond_with_code
class OnchainTokenBalanceResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        contract=fields.String(required=False),
    ))
    def get(cls, user_id, **kwargs):
        """链上交易-链上资产"""
        contract: str = kwargs.get('contract')
        if contract and contract.startswith('0x'):
            contract = OnchainAddressHelper(Chain.ERC20).normalise_address(contract)

        all_balance = OnchainTokenBalance.query.filter(
            OnchainTokenBalance.user_id == user_id,
        ).all()
        token_ids = [item.token_id for item in all_balance]
        token_base_map = {
            item.id: item for item in OnchainToken.query.filter(
                OnchainToken.id.in_(token_ids),
            ).all()
        }
        token_quote_map = OnchainTokenQuoteCache().get_many(token_ids)
        data = []
        total_volume = Decimal()
        for balance in all_balance:
            balance: OnchainTokenBalance
            if balance.token_id not in token_base_map:
                raise InvalidArgument(f'miss token_id: {balance.token_id}')
            token_base: OnchainToken = token_base_map[balance.token_id]
            if contract and token_base.contract != contract:
                continue
            price = Decimal()
            if balance.token_id in token_quote_map:
                price = token_quote_map[balance.token_id]['price'] or Decimal()
            amount = decimal_add(balance.frozen, balance.available)
            volume = decimal_mul(amount, price)
            data.append(dict(
                chain=token_base.chain,
                contract=token_base.contract,
                symbol=token_base.symbol,
                name=token_base.name,
                volume=volume,
                amount=amount,
                frozen=balance.frozen,
                available=balance.available,
            ))
            total_volume = decimal_add(total_volume, volume)
        data.sort(key=lambda x: x['volume'], reverse=True)
        return dict(
            items=data,
            total_volume=total_volume,
        )
