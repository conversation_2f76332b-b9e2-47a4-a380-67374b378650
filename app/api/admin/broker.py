# -*- coding: utf-8 -*-
import json
import uuid
from collections import defaultdict
from decimal import Decimal

from sqlalchemy import func
from webargs import fields

from flask import g

from app import Language
from app.business.email import send_broker_approve_email, send_broker_auth_approve_email
from app.common import ReportType, ADMIN_EXPORT_LIMIT
from app.models import db, User
from app.models.auth import Oauth2Client
from app.models.broker import BrokerApplication, <PERSON>roker, DailyBrokerAssetReport, MonthlyBrokerAssetReport, \
    DailyBrokerUserAssetReport, MonthlyBrokerUserAssetReport
from app.models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog, OPNamespaceObjectUser
from app.exceptions import InvalidArgument
from app.api.common import Namespace, Resource, respond_with_code
from app.api.common.fields import <PERSON>itField, PageField, EnumField, PositiveDecimalField
from app.business.auth import get_admin_user_name_map
from app.utils import AWSBucketPrivate, batch_iter, now, export_xlsx, format_percent, amount_to_str

ns = Namespace("Broker-Admin")


@ns.route("")
@respond_with_code
class BrokerResource(Resource):

    export_headers = (
        {"field": "user_id", Language.ZH_HANS_CN: "用户ID"},
        {"field": "email", Language.ZH_HANS_CN: "邮箱"},
        {"field": "name", Language.ZH_HANS_CN: "经纪商名称"},
        {"field": "broker_id", Language.ZH_HANS_CN: "broker_id"},
        {"field": "rate_str", Language.ZH_HANS_CN: "返佣比例"},
        {"field": "trade_user_count", Language.ZH_HANS_CN: "总交易用户量"},
        {"field": "spot_trade_usd", Language.ZH_HANS_CN: "现货交易额（USD）"},
        {"field": "perpetual_trade_usd", Language.ZH_HANS_CN: "合约交易额（USD）"},
        {"field": "total_trade_usd", Language.ZH_HANS_CN: "总交易额（USD）"},
        {"field": "amount", Language.ZH_HANS_CN: "累计返佣（USDT）"},
        {"field": "created_at_str", Language.ZH_HANS_CN: "成为经纪商时间"},
        {"field": "updated_by", Language.ZH_HANS_CN: "操作人"},
        {"field": "is_auth", Language.ZH_HANS_CN: "开通授权"},
        {"field": "remark", Language.ZH_HANS_CN: "备注"},
    )

    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=fields.Integer,
            name=fields.String,
            status=EnumField(Broker.Status),
            page=PageField(unlimited=True),
            limit=LimitField,
            export=fields.Boolean,
        )
    )
    def get(cls, **kwargs):
        """经纪商-列表"""

        page = kwargs['page']
        limit = kwargs['limit']
        model = Broker
        query = model.query.filter(model.status == Broker.Status.VALID).order_by(model.id.desc())
        if user_id := kwargs.get("user_id"):
            query = query.filter(model.user_id == user_id)
        if name := kwargs.get("name"):
            query = query.filter(model.name.contains(name))
        if status := kwargs.get("status"):
            query = query.filter(model.status == status)

        if kwargs.get('export'):
            limit = ADMIN_EXPORT_LIMIT
            page = 1

        paginate = query.paginate(page, limit, error_out=False)
        total = paginate.total
        rows = paginate.items

        op_user_ids = list({i.updated_by for i in rows if i.updated_by})
        admin_user_name_map = get_admin_user_name_map(op_user_ids)

        user_ids = {i.user_id for i in rows}
        user_email_map = {}
        for ids_ in batch_iter(user_ids, 2000):
            chunk_users = User.query.filter(User.id.in_(ids_)).with_entities(User.id, User.email).all()
            user_email_map.update(dict(chunk_users))

        broker_mapper = defaultdict(lambda: {
            "user_list": set(),
            "amount": Decimal(),
            "spot_trade_usd": Decimal(),
            "perpetual_trade_usd": Decimal(),
            "spot_fee_usd": Decimal(),
            "perpetual_fee_usd": Decimal(),
        })

        daily_reports = DailyBrokerAssetReport.query.filter(
            DailyBrokerAssetReport.user_id.in_(user_ids)
        ).all()

        oauth_user_ids = [i.user_id for i in Oauth2Client.query.all()]

        for row in daily_reports:
            broker_mapper[row.user_id]['spot_trade_usd'] += row.spot_trade_usd
            broker_mapper[row.user_id]['perpetual_trade_usd'] += row.perpetual_trade_usd
            broker_mapper[row.user_id]['spot_fee_usd'] += row.spot_fee_usd
            broker_mapper[row.user_id]['amount'] += row.amount
            broker_mapper[row.user_id]['perpetual_fee_usd'] += row.perpetual_fee_usd
            broker_mapper[row.user_id]['user_list'].update(row.get_deal_user_ids())

        items = []
        for row in rows:
            d = row.to_dict(enum_to_name=True)
            d['created_at_str'] = d['created_at'].strftime("%Y-%m-%d %H:%M:%S")
            d['rate_str'] = format_percent(d['rate'])
            d["email"] = user_email_map.get(row.user_id)
            d["is_auth"] = True if row.user_id in oauth_user_ids else False
            d["updated_by_name"] = admin_user_name_map.get(row.updated_by)
            d["amount"] = broker_mapper[row.user_id]['amount']
            d["spot_trade_usd"] = broker_mapper[row.user_id]['spot_trade_usd']
            d["perpetual_trade_usd"] = broker_mapper[row.user_id]['perpetual_trade_usd']
            d["total_trade_usd"] = broker_mapper[row.user_id]['spot_trade_usd'] + broker_mapper[row.user_id]['perpetual_trade_usd']
            d["spot_fee_usd"] = broker_mapper[row.user_id]['spot_fee_usd']
            d["perpetual_fee_usd"] = broker_mapper[row.user_id]['perpetual_fee_usd']
            d["total_fee_usd"] = broker_mapper[row.user_id]['spot_fee_usd'] + broker_mapper[row.user_id][
                'perpetual_fee_usd']
            d["trade_user_count"] = len(broker_mapper[row.user_id]['user_list'])
            items.append(d)

        if kwargs.get('export'):
            return export_xlsx(
                filename='broker_list',
                data_list=items,
                export_headers=cls.export_headers
            )

        return dict(
            items=items,
            total=total,
            extra=dict(
                status_dict={i.name: i.value for i in model.Status},
            ),
        )

    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=fields.Integer(required=True),
            name=fields.String(required=True),
            rate=PositiveDecimalField(allow_zero=True, validate=lambda x: x % Decimal("0.01") == 0 and 0 <= x <= 1),
            remark=fields.String(allow_none=True, missing=""),
        )
    )
    def post(cls, **kwargs):
        """经纪商-添加经纪商"""
        user_id = kwargs["user_id"]
        cls.check_add(user_id)

        admin_user_id = g.user.id
        broker: Broker = Broker.query.filter(Broker.user_id == user_id).first()
        if broker and broker.status == Broker.Status.VALID:
            raise InvalidArgument(message=f"该用户已经是经纪商")
        if not broker:
            broker = Broker(user_id=user_id)
        now_ = now()
        broker.created_at = now_
        broker.broker_id = broker.generate_broker_id()
        broker.rate = kwargs["rate"]
        broker.name = kwargs["name"]
        broker.status = Broker.Status.VALID
        broker.remark = kwargs.get("remark") or ""
        broker.effected_at = now_
        broker.updated_by = admin_user_id
        db.session.add(broker)
        db.session.commit()
        send_broker_approve_email.delay(user_id)

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.Broker,
            detail=kwargs,
            target_user_id=user_id,
        )

        return broker.to_dict(enum_to_name=True)

    @classmethod
    def check_add(cls, user_id: int):
        user = User.query.filter(User.id == user_id).first()
        if not user:
            raise InvalidArgument(message="用户不存在")
        if user.is_sub_account:
            raise InvalidArgument(message="不允许添加子账户！")

    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=fields.Integer(required=True),
            name=fields.String(required=True),
            rate=PositiveDecimalField(allow_zero=True, validate=lambda x: x % Decimal("0.01") == 0 and 0 <= x <= 1),
            remark=fields.String(allow_none=True, missing=""),
        )
    )
    def put(cls, **kwargs):
        """经纪商-编辑经纪商"""
        admin_user_id = g.user.id
        user_id = kwargs["user_id"]
        broker: Broker = Broker.query.filter(
            Broker.user_id == user_id,
            Broker.status == Broker.Status.VALID,
        ).first()
        if not broker:
            raise InvalidArgument(message="经纪商不存在")
        old_data = broker.to_dict(enum_to_name=True)

        broker.remark = kwargs.get("remark") or ""
        broker.updated_by = admin_user_id
        broker.rate = kwargs["rate"]
        broker.name = kwargs["name"]
        db.session_add_and_commit(broker)

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.Broker,
            old_data=old_data,
            new_data=broker.to_dict(enum_to_name=True),
            target_user_id=broker.user_id,
        )

    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=fields.Integer(required=True),
        )
    )
    def delete(cls, **kwargs):
        """经纪商-删除经纪商"""
        user_id = kwargs["user_id"]
        broker = Broker.query.filter(
            Broker.user_id == user_id,
            Broker.status == Broker.Status.VALID,
        ).first()
        if not broker:
            raise InvalidArgument(message="经纪商不存在")
        broker.status = Broker.Status.DELETED
        oauth_row = Oauth2Client.query.filter(
            Oauth2Client.user_id == user_id,
        ).first()
        if oauth_row:
            db.session.delete(oauth_row)
        db.session.commit()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.Broker,
            detail=dict(user_id=user_id, broker_id=broker.id, name=broker.name),
            target_user_id=user_id,
        )
        return {}


@ns.route("/application-list")
@respond_with_code
class BrokerApplicationListResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=fields.Integer,
            name=fields.String,
            page=PageField(unlimited=True),
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """经纪商申请-列表"""
        model = BrokerApplication
        query = model.query.order_by(model.id.desc()).with_entities(
            model.id,
            model.created_at,
            model.applied_at,
            model.user_id,
            model.name,
            model.contact_name,
            model.contact_type,
            model.contact_info,
            model.remark,
        )
        if user_id := kwargs.get("user_id"):
            query = query.filter(model.user_id == user_id)
        if name := kwargs.get("name"):
            query = query.filter(model.name.contains(name))

        paginate = query.paginate(kwargs["page"], kwargs["limit"], error_out=False)
        total = paginate.total
        rows = paginate.items

        user_ids = {i.user_id for i in rows}
        user_email_map = {}
        for ids_ in batch_iter(user_ids, 2000):
            chunk_users = User.query.filter(User.id.in_(ids_)).with_entities(User.id, User.email).all()
            user_email_map.update(dict(chunk_users))

        items = []
        for row in rows:
            d = {
                "id": row.id,
                "created_at": row.created_at,
                "applied_at": row.applied_at,
                "user_id": row.user_id,
                "email": user_email_map.get(row.user_id, ""),
                "name": row.name,
                "contact_name": row.contact_name,
                "contact_type": row.contact_type,
                "contact_info": row.contact_info,
                "remark": row.remark,
            }
            items.append(d)

        return dict(
            items=items,
            total=total,
            extra=dict(
                contact_type_dict={i.name: i.value for i in model.ContactType},
                trade_scale_dict={i.name: i.value for i in model.TradeScale},
                user_scale_dict={i.name: i.value for i in model.UserScale},
            ),
        )


@ns.route("/application/<int:id_>")
@respond_with_code
class BrokerApplyDetailResource(Resource):
    @classmethod
    def get(cls, id_):
        """经纪商申请-详情"""
        row = BrokerApplication.query.get(id_)
        user = User.query.get(row.user_id)
        detail = row.to_dict(enum_to_name=True)
        detail["email"] = user.email

        detail["extra_data"] = json.loads(detail["extra_data"])
        image_keys = detail["extra_data"]['extra_images']
        extra_images = []
        for s3_key in image_keys:
            extra_images.append(AWSBucketPrivate.get_file_url(s3_key))
        detail["extra_img_data"] = extra_images

        return detail

    @classmethod
    @ns.use_kwargs(
        dict(
            remark=fields.String,
        )
    )
    def put(cls, id_, **kwargs):
        """经纪商申请-编辑备注"""
        row: BrokerApplication = BrokerApplication.query.get(id_)
        old_data = row.to_dict(enum_to_name=True)
        if (remark := kwargs.get("remark")) is not None:
            if len(remark) > 128:
                raise InvalidArgument("备注不能超过128个字符")
            row.remark = remark

        db.session.add(row)
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.BrokerApplication,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
            target_user_id=row.user_id,
        )


@ns.route("/auth")
@respond_with_code
class BrokerAuthResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=fields.Integer(required=True),
            image_url=fields.String(required=True),
            redirect_url=fields.String(required=True),
        )
    )
    def post(cls, **kwargs):
        """经纪商-经纪商授权"""
        user_id = kwargs["user_id"]
        broker = Broker.query.filter(
            Broker.user_id == user_id,
            Broker.status == Broker.Status.VALID,
        ).first()
        if not broker:
            raise InvalidArgument(message="经纪商不存在")
        oauth_row = Oauth2Client.query.filter(
            Oauth2Client.user_id == user_id
        ).first()
        if oauth_row:
            raise InvalidArgument(message="该第三方已授权")
        oauth_row = Oauth2Client(user_id=user_id)
        oauth_row.client_id = str(uuid.uuid4())
        oauth_row.client_secret = str(uuid.uuid4())
        oauth_row.redirect_url = kwargs['redirect_url']
        oauth_row.image_url = kwargs['image_url']
        db.session.add(oauth_row)
        db.session.commit()
        send_broker_auth_approve_email.delay(user_id,
                                             oauth_row.client_id,
                                             oauth_row.client_secret)

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.Broker,
            detail=oauth_row.to_dict(enum_to_name=True),
            target_user_id=oauth_row.user_id,
        )

    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=fields.Integer(required=True),
        )
    )
    def delete(cls, **kwargs):
        """经纪商-删除经纪商授权"""
        user_id = kwargs["user_id"]
        query = Oauth2Client.query.filter(
            Oauth2Client.user_id == user_id
        )
        row = query.first()
        if not row:
            raise InvalidArgument(message="授权记录不存在")
        old_data = row.to_dict(enum_to_name=True)
        query.delete()
        db.session.commit()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.Broker,
            detail=old_data,
            target_user_id=row.user_id,
        )


@ns.route('/records')
@respond_with_code
class BrokerRecordsListResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        user_id=fields.Integer(),
        broker_name=fields.String,
        start_date=fields.DateTime(format="%Y-%m-%d"),
        end_date=fields.DateTime(format="%Y-%m-%d"),
        page=PageField(unlimited=True),
        limit=LimitField(missing=100),
        report_type=EnumField(ReportType, required=True),
        export=fields.Boolean,
    ))
    def get(cls, **kwargs):
        """经纪商-经纪商交易记录"""
        if kwargs['report_type'] == ReportType.DAILY:
            model = DailyBrokerAssetReport
        else:
            model = MonthlyBrokerAssetReport

        report_query = model.query.order_by(
            model.date.desc()
        )

        if start_date := kwargs.get('start_date'):
            report_query = report_query.filter(
                model.date >= start_date)
        if end_date := kwargs.get('end_date'):
            report_query = report_query.filter(
                model.date <= end_date)
        if user_id := kwargs.get('user_id'):
            report_query = report_query.filter(
                model.user_id == user_id)

        records = []
        paginate = report_query.paginate(kwargs['page'], kwargs['limit'], error_out=False)
        result = paginate.items

        broker_data_map = {i.user_id: i for i in Broker.query.all()}

        for row in result:
            records.append(dict(
                date=row.date,
                user_id=row.user_id,
                name=broker_data_map[row.user_id].name if row.user_id in broker_data_map else '',
                broker_id=broker_data_map[row.user_id].broker_id if row.user_id in broker_data_map else '',
                amount=row.amount,
                spot_fee_usd=row.spot_fee_usd,
                perpetual_fee_usd=row.perpetual_fee_usd,
                total_fee_usd=row.spot_fee_usd + row.perpetual_fee_usd,
                spot_trade_usd=row.spot_trade_usd,
                perpetual_trade_usd=row.perpetual_trade_usd,
                total_trade_usd=row.spot_trade_usd + row.perpetual_trade_usd,
                deal_user_count=row.deal_user_count,
            ))

        return dict(
            items=records,
            page=paginate.page,
            total=paginate.total,
        )


@ns.route("/detail-list")
@respond_with_code
class BrokerReferralDetailsListResource(Resource):

    export_headers = (
        {"field": "user_id", Language.ZH_HANS_CN: "用户ID"},
        {"field": "name", Language.ZH_HANS_CN: "绑定经纪商"},
        {"field": "broker_id", Language.ZH_HANS_CN: "broker_id"},
        {"field": "spot_trade_usd", Language.ZH_HANS_CN: "现货交易额（USD）"},
        {"field": "perpetual_trade_usd", Language.ZH_HANS_CN: "合约交易额（USD）"},
        {"field": "total_trade_usd", Language.ZH_HANS_CN: "总交易额（USD）"},
        {"field": "spot_fee_usd", Language.ZH_HANS_CN: "现货手续费（USD）"},
        {"field": "perpetual_fee_usd", Language.ZH_HANS_CN: "合约手续费（USD）"},
        {"field": "total_fee_usd", Language.ZH_HANS_CN: "总手续费（USD）"},
        {"field": "amount", Language.ZH_HANS_CN: "返佣金额（USDT）"},
    )

    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=fields.Integer,
            broker_user_id=fields.Integer,
            status=EnumField(Broker.Status),
            page=PageField(unlimited=True),
            limit=LimitField,
            export=fields.Boolean,
        )
    )
    def get(cls, **kwargs):
        """经纪商-经纪商返佣记录"""
        page = kwargs['page']
        limit = kwargs['limit']
        query = DailyBrokerUserAssetReport.query
        if user_id := kwargs.get("user_id"):
            query = query.filter(DailyBrokerUserAssetReport.user_id == user_id)
        if broker_user_id := kwargs.get("broker_user_id"):
            query = query.filter(DailyBrokerUserAssetReport.broker_user_id == broker_user_id)
        query = query.with_entities(
            DailyBrokerUserAssetReport.broker_user_id,
            DailyBrokerUserAssetReport.user_id,
            func.sum(DailyBrokerUserAssetReport.amount).label("amount"),
            func.sum(DailyBrokerUserAssetReport.spot_fee_usd).label("spot_fee_usd"),
            func.sum(DailyBrokerUserAssetReport.perpetual_fee_usd).label("perpetual_fee_usd"),
            func.sum(DailyBrokerUserAssetReport.spot_trade_usd).label("spot_trade_usd"),
            func.sum(DailyBrokerUserAssetReport.perpetual_trade_usd).label("perpetual_trade_usd"),
        ).group_by(DailyBrokerUserAssetReport.broker_user_id,
                   DailyBrokerUserAssetReport.user_id)

        if kwargs.get('export'):
            limit = ADMIN_EXPORT_LIMIT
            page = 1

        paginate = query.paginate(page, limit, error_out=False)
        total = paginate.total
        rows = paginate.items

        broker_data_map = {i.user_id: i for i in Broker.query.all()}

        items = []
        for row in rows:

            items.append(dict(
                user_id=row.user_id,
                name=broker_data_map[row.broker_user_id].name if row.broker_user_id in broker_data_map else '',
                broker_id=broker_data_map[row.broker_user_id].broker_id if row.broker_user_id in broker_data_map else '',
                broker_user_id=row.broker_user_id,
                amount=row.amount,
                spot_fee_usd=row.spot_fee_usd,
                perpetual_fee_usd=row.perpetual_fee_usd,
                total_fee_usd=row.spot_fee_usd+row.perpetual_fee_usd,
                spot_trade_usd=row.spot_trade_usd,
                perpetual_trade_usd=row.perpetual_trade_usd,
                total_trade_usd=row.spot_trade_usd+row.perpetual_trade_usd,
            ))

        if kwargs.get('export'):
            return export_xlsx(
                filename='broker_deal_list',
                data_list=items,
                export_headers=cls.export_headers
            )

        return dict(
            items=items,
            total=total,
        )


@ns.route("/referral-detail")
@respond_with_code
class BrokerReferralUserDetailsListResource(Resource):

    export_headers = (
        {"field": "date", Language.ZH_HANS_CN: "日期"},
        {"field": "user_id", Language.ZH_HANS_CN: "用户ID"},
        {"field": "name", Language.ZH_HANS_CN: "经纪商名称"},
        {"field": "spot_trade_usd", Language.ZH_HANS_CN: "现货交易额（USD）"},
        {"field": "perpetual_trade_usd", Language.ZH_HANS_CN: "合约交易额（USD）"},
        {"field": "total_trade_usd", Language.ZH_HANS_CN: "总交易额（USD）"},
        {"field": "spot_fee_usd", Language.ZH_HANS_CN: "现货手续费（USD）"},
        {"field": "perpetual_fee_usd", Language.ZH_HANS_CN: "合约手续费（USD）"},
        {"field": "total_fee_usd", Language.ZH_HANS_CN: "总手续费（USD）"},
        {"field": "amount", Language.ZH_HANS_CN: "返佣金额（USDT）"},
    )

    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=fields.Integer,
            broker_name=fields.String,
            report_type=EnumField(ReportType, required=True),
            start_date=fields.DateTime(format="%Y-%m-%d"),
            end_date=fields.DateTime(format="%Y-%m-%d"),
            page=PageField(unlimited=True),
            limit=LimitField,
            export=fields.Boolean,
        )
    )
    def get(cls, **kwargs):
        """经纪商-用户返佣明细记录"""
        page = kwargs['page']
        limit = kwargs['limit']
        if kwargs['report_type'] is ReportType.DAILY:
            model = DailyBrokerUserAssetReport
        else:
            model = MonthlyBrokerUserAssetReport
        broker_user_ids = cls.get_broker_user_ids(kwargs.get('broker_name'))
        query = model.query
        if user_id := kwargs.get("user_id"):
            query = query.filter(model.user_id == user_id)
        if broker_user_ids:
            query = query.filter(model.broker_user_id.in_(broker_user_ids))
        if start_date := kwargs.get('start_date'):
            query = query.filter(model.date >= start_date)
        if end_date := kwargs.get('end_date'):
            query = query.filter(model.date <= end_date)
        if kwargs.get('export'):
            limit = ADMIN_EXPORT_LIMIT
            page = 1
        query = query.order_by(model.id.desc())
        paginate = query.paginate(page, limit, error_out=False)
        total = paginate.total
        rows = paginate.items

        broker_data_map = {i.user_id: i for i in Broker.query.all()}
        items = []
        for row in rows:
            items.append(dict(
                date=row.date,
                user_id=row.user_id,
                name=broker_data_map[row.broker_user_id].name if row.broker_user_id in broker_data_map else '',
                amount=row.amount,
                spot_fee_usd=amount_to_str(row.spot_fee_usd, 2),
                perpetual_fee_usd=amount_to_str(row.perpetual_fee_usd, 2),
                total_fee_usd=amount_to_str(row.spot_fee_usd+row.perpetual_fee_usd, 2),
                spot_trade_usd=amount_to_str(row.spot_trade_usd, 2),
                perpetual_trade_usd=amount_to_str(row.perpetual_trade_usd, 2),
                total_trade_usd=amount_to_str(row.spot_trade_usd+row.perpetual_trade_usd, 2),
            ))

        if kwargs.get('export'):
            return export_xlsx(
                filename='broker_referral_deals',
                data_list=items,
                export_headers=cls.export_headers
            )

        return dict(
            items=items,
            total=total,
        )

    @classmethod
    def get_broker_user_ids(cls, broker_name: str | None) -> list:
        if not broker_name:
            return []
        model = Broker
        rows = model.query.with_entities(
            model.user_id,
        ).filter(
            model.name.contains(broker_name)
        ).all()
        return [row.user_id for row in rows]
