# -*- coding: utf-8 -*-

import json
from collections import defaultdict
from datetime import datetime, timedelta
from decimal import Decimal
from enum import Enum
from typing import Dict, List
from dateutil import tz
from flask import g
from flask_babel import force_locale
from flask_restx import fields as fx_fields
from marshmallow import Schema, EXCLUDE
from pyroaring import BitMap
from sqlalchemy import func, or_, and_
from webargs import fields as wa_fields

from app.business.auth import get_admin_user_name_map, get_special_conf_create_operators
from app.business.site import BusinessSettings
from app.models import row_to_dict
from app.models.referral import DailyUserReferralSlice
from app.models.user import UserBusinessRecord, UserPreference, AppraisalHistory
from ..common import Resource, Namespace, respond_with_code
from ..common.fields import (
    EnumField,
    PageField,
    LimitField,
    FileField,
    TimestampField,
)
from app.common.countries import AREAS_MAPPING, AreaInfo, get_code_to_cn_name, \
    list_country_codes_3_to_area, list_country_codes_3_to_continent, list_continent_cn_names
from ...business import CacheLock, LockKeys, ReferralBusiness, UserPreferences
from ...business.ambassador import get_agents_statistic_data, AGENT_EXPORT_HEADERS
from ...business.email import send_update_ambassador_level_email, \
    send_ambassador_application_to_agent
from ...business.referral import AmbassadorBusiness, AmbassadorAgentBusiness, AmbassadorAgentRelation, TreeAmbHelper
from ...business.bus_referral import BusRelationUserQuerier
from ...business.user_status import AmbassadorChangeType, AmbassadorAgentChangeType
from ...common import Language, TradeBusinessType, get_country, language_cn_names, list_country_codes_3_admin, \
    ADMIN_EXPORT_LIMIT, language_name_cn_names
from ...exceptions import InvalidArgument, RecordNotFound
from ...models import (
    Ambassador,
    BusinessAmbassador,
    MonthlyAmbassadorReport,
    AmbassadorStar,
    AmbassadorApplication,
    User,
    Referral,
    db,
    AmbassadorAgent,
    AmbassadorAgentHistory,
    ReferralRate,
    AmbassadorStatistics,
    UserStatusChangeHistory,
    UserSpecialConfigChangeLog,
    AmbassadorGuide,
    AmbassadorGuideTranslation,
    MonthlyAmbassadorAgentReport, PotentialAmbassador, AmbassadorBusinessTrace, BusinessTraceChangeHistory,
    ReferralHistory, AdminUser
)
from ...models.activity import AmbassadorActivity, AmbassadorActivityContent
from ...models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog, OPNamespaceObjectUser
from ...utils import export_xlsx, query_to_page, last_month, AWSBucketPrivate, now, amount_to_str, batch_iter, \
    timestamp_to_datetime, format_percent
from ...utils.date_ import datetime_to_utc8_str, datetime_to_str, today, last_month_range, str_to_datetime

ns = Namespace('Ambassador')


@ns.route('/info')
@respond_with_code
class AmbassadorInfoResource(Resource):

    @classmethod
    def get(cls):
        """用户-大使-信息"""
        with force_locale(Language.ZH_HANS_CN.value):
            return dict(
                statuses=Ambassador.Status,
                levels={level.name: Ambassador.LEVEL_NAMES[level]
                        for level in Ambassador.Level},
                active_statuses=AmbassadorAgent.ActiveStatus,
            )


class AmbassadorCheckMixin:

    @classmethod
    def check_agent(cls, user_id: int, ambassador_user_id: int) -> None:
        cls.check_user(user_id)

        if user_id == ambassador_user_id:
            raise InvalidArgument(message="大使代理不能是自己")

        agent = AmbassadorAgentBusiness.get_agent(user_id)
        if not agent:
            raise InvalidArgument(message="大使代理不存在")

    @classmethod
    def check_user(cls, user_id: int) -> None:
        user = User.query.get(user_id)
        if not user:
            raise InvalidArgument(message="用户不存在")

        if user.is_sub_account:
            raise InvalidArgument(message="不能是子账户")


@ns.route('')
@respond_with_code
class AmbassadorListResource(AmbassadorCheckMixin, Resource):

    export_headers = (
        {"field": "user_id", Language.ZH_HANS_CN: "用户ID"},
        {"field": "email", Language.ZH_HANS_CN: "邮箱"},
        {"field": "source", Language.ZH_HANS_CN: "大使来源"},
        {"field": "level", Language.ZH_HANS_CN: "大使等级"},
        {"field": "appraisal_level", Language.ZH_HANS_CN: "考核等级"},
        {"field": "lock_level", Language.ZH_HANS_CN: "保底等级"},
        {"field": "expired_time", Language.ZH_HANS_CN: "保底等级到期时间"},
        # {"field": "agent_email", Language.ZH_HANS_CN: "代理"},
        # {"field": "referral_status", Language.ZH_HANS_CN: "代理返佣状态"},
        {"field": "cur_valid_refer_count", Language.ZH_HANS_CN: "本次在职refer人数"},
        {"field": "total_referral_count", Language.ZH_HANS_CN: "历史在职refer人数"},
        {"field": "cur_refer_user_balance_usd", Language.ZH_HANS_CN: "本次在职refer用户总资产"},
        {"field": "refer_user_balance_usd", Language.ZH_HANS_CN: "历史在职refer用户总资产"},
        {"field": "cur_refer_active_user_count", Language.ZH_HANS_CN: "本次在职refer活跃用户"},
        {"field": "refer_active_user_count", Language.ZH_HANS_CN: "历史在职refer活跃用户"},
        {"field": "cur_valid_deal_referral_count", Language.ZH_HANS_CN: "本次在职refer交易人数"},
        {"field": "total_deal_referral_count", Language.ZH_HANS_CN: "历史在职refer交易人数"},
        {"field": "cur_valid_deal_amount", Language.ZH_HANS_CN: "本次在职refer交易总额"},
        {"field": "total_deal_amount", Language.ZH_HANS_CN: "历史在职refer交易总额"},
        {"field": "last_month_deal_amount", Language.ZH_HANS_CN: "上个月被邀请人累计交易量(USD)"},
        {"field": "this_month_deal_amount", Language.ZH_HANS_CN: "本月被邀请人累计交易量(USD)"},
        {"field": "cur_valid_referral_amount", Language.ZH_HANS_CN: "本次在职累计返佣(USDT)"},
        {"field": "total_refer_amount", Language.ZH_HANS_CN: "历史在职累计返佣(USDT)"},
        {"field": "status", Language.ZH_HANS_CN: "状态"},
        {"field": "remark", Language.ZH_HANS_CN: "备注"},
        {"field": "country_cn_name", Language.ZH_HANS_CN: "地区"},
        {"field": "effected_at", Language.ZH_HANS_CN: "成为大使时间"},
        {"field": "removed_at", Language.ZH_HANS_CN: "失效时间"},
        {"field": "max_level_history", Language.ZH_HANS_CN: "历史最高等级"},
        {"field": "become_count", Language.ZH_HANS_CN: "成为大使次数"},
        {"field": "agent_amb_effected_at", Language.ZH_HANS_CN: "绑定时间"},
    )

    @classmethod
    @ns.use_kwargs(
        dict(
            status=EnumField(Ambassador.Status),
            level=EnumField(Ambassador.Level),
            type=EnumField(Ambassador.Type),
            user_id=wa_fields.Integer,
            agent_id=wa_fields.Integer,  # 查看一个代理的大使
            location_code=wa_fields.String,
            page=PageField(unlimited=True, missing=1),
            limit=LimitField(missing=50),
            export=wa_fields.Boolean,
            sort_name=EnumField(
                [
                    "created_at",
                    "total_referral_count",
                    "total_deal_amount",
                    "last_month_deal_amount",
                    "this_month_deal_amount",
                    "total_refer_amount",
                ],
                missing="created_at",
            ),
            source=EnumField(Ambassador.Source),
        )
    )
    def get(cls, **kwargs):
        """
        用户-大使-大使列表
        """
        sort_name = kwargs['sort_name']
        page = kwargs['page']
        limit = kwargs['limit']
        query = Ambassador.query.join(User).filter(
            Ambassador.user_id == User.id,
        )
        # 查找一位代理的所有大使
        if agent_id := kwargs.get('agent_id'):
            history = AmbassadorAgentHistory.query.filter(
                AmbassadorAgentHistory.user_id == agent_id,
            )
            ambassador_ids = [item.ambassador_id for item in history]
            query = query.filter(
                Ambassador.user_id.in_(ambassador_ids)
            )
        if status := kwargs.get('status'):
            query = query.filter(Ambassador.status == status)
        if type_ := kwargs.get('type'):
            query = query.filter(Ambassador.type == type_)
        if level := kwargs.get('level'):
            # 用实际等级过滤
            small_level = []
            for i in Ambassador.Level:
                small_level.append(i)
                if i == level:
                    break
            now_ = now()
            query = query.filter(
                or_(
                    and_(
                        # 没有保底等级 实际等级为考核等级
                        or_(Ambassador.expired_time.is_(None), Ambassador.expired_time <= now_),
                        Ambassador.appraisal_level == level,
                    ),
                    and_(
                        # 有保底等级 实际等级为保底等级
                        Ambassador.expired_time > now_,
                        Ambassador.lock_level == level,
                        Ambassador.appraisal_level.in_(small_level),
                    ),
                    and_(
                        # 有保底等级 实际等级为考核等级
                        Ambassador.expired_time > now_,
                        Ambassador.appraisal_level == level,
                        Ambassador.lock_level.in_(small_level),
                    ),
                )
            )
        if user_id := kwargs.get('user_id', None):
            query = query.filter(Ambassador.user_id == user_id)
        if location_code := kwargs.get('location_code'):
            query = query.filter(User.location_code == location_code)

        if source := kwargs.get('source'):
            if source == Ambassador.Source.BUSINESS:
                query = query.filter(Ambassador.type == Ambassador.Type.BUSINESS)
            else:
                # 如果大使类型是普通大使，再看该大使有没有代理，如果有代理则为大使推荐，无代理的为自助申请。
                query = query.filter(
                    Ambassador.type == Ambassador.Type.NORMAL,
                    Ambassador.source == source,
                )

        query = query.order_by(
            Ambassador.id.desc()
        )
        records: list[Ambassador] = query.all()
        total = len(records)

        amb_mapping = {row.user_id: row for row in records}
        user_ids = list(amb_mapping.keys())
        become_ambassador_count_mapping = cls._get_become_ambassador_count_map()
        users = User.query.filter(User.id.in_(user_ids)).all()

        agent_history = AmbassadorAgentHistory.query.filter(
            AmbassadorAgentHistory.ambassador_id.in_(user_ids),
        ).all()
        agent_user_ids = {i.user_id for i in agent_history}
        agent_user_email_map = dict(User.query.filter(User.id.in_(agent_user_ids)
                                                      ).with_entities(User.id, User.email).all())

        user_email_map = {u.id: u.email for u in users}
        user_location_code_map = {u.id: u.location_code for u in users}

        ambassador_agent_map = {
            a.ambassador_id: a for a in agent_history}
        amb_statistics_map = {
            i.user_id: i for i in AmbassadorStatistics.query.filter(AmbassadorStatistics.user_id.in_(user_ids)).all()
        }

        monthly_report_map = cls._get_monthly_report_map(amb_mapping)

        # 大使过期时间
        model = UserStatusChangeHistory
        c_type = AmbassadorChangeType
        amb_his_rows = model.query.filter(
            model.user_id.in_(user_ids),
            model.type.in_([model.Type.AMBASSADOR.name, model.Type.BUSINESS_USER.name]),
            model.action.in_([c_type.REMOVE.name, c_type.LEVEL_CHANGE.name, c_type.ADD.name]),
        ).order_by(model.id).all()
        amb_remove_map = {row.user_id: row.created_at for row in amb_his_rows if row.action == c_type.REMOVE.name}

        amb_level_map = cls.get_amb_level_map(amb_his_rows, user_id)

        res = []
        for item in records:
            if item.user_id not in monthly_report_map:
                monthly_report_map[item.user_id] = defaultdict(Decimal)

            str_appraisal_level = item.appraisal_level.name
            str_guarantee_level = item.lock_level.name if item.lock_level else None
            amb_statistics_info: AmbassadorStatistics = amb_statistics_map.get(item.user_id)

            agent_id = None
            agent_amb_effected_at = None
            referral_status = '--'
            relation = ambassador_agent_map.get(item.user_id)
            if relation:
                agent_id = relation.user_id
                agent_amb_effected_at = relation.effected_at
                referral_status = '生效中' if relation.status is AmbassadorAgentHistory.Status.VALID else '已失效'

            if item.type == Ambassador.Type.BUSINESS:
                source = Ambassador.Source.BUSINESS.value
            else:
                source = item.source.value
            res.append(
                dict(
                    user_id=item.user_id,
                    email=user_email_map[item.user_id],
                    level=item.level.name,
                    type=item.type.name,
                    agent_id=agent_id,
                    agent_email=agent_user_email_map.get(agent_id, ""),
                    total_referral_count=amb_statistics_info.refer_count if amb_statistics_info else 0,  # 历史总邀请人数
                    cur_valid_refer_count=amb_statistics_info.cur_valid_refer_count if amb_statistics_info else 0,  # 当前有效的邀请人数
                    total_deal_referral_count=amb_statistics_info.refer_total_deal_count if amb_statistics_info else 0,
                    cur_valid_deal_referral_count=amb_statistics_info.refer_deal_count if amb_statistics_info else 0,
                    refer_user_balance_usd=amb_statistics_info.refer_user_balance_usd if amb_statistics_info else 0,
                    cur_refer_user_balance_usd=amb_statistics_info.cur_refer_user_balance_usd if amb_statistics_info else 0,
                    refer_active_user_count=amb_statistics_info.refer_active_user_count if amb_statistics_info else 0,
                    cur_refer_active_user_count=amb_statistics_info.cur_refer_active_user_count if amb_statistics_info else 0,
                    cur_valid_deal_amount=monthly_report_map[item.user_id]["cur_valid_deal_amount"],
                    cur_valid_referral_amount=monthly_report_map[item.user_id]["cur_valid_referral_amount"],
                    total_deal_amount=monthly_report_map[item.user_id]["overall_deal_amount"],
                    last_month_deal_amount=monthly_report_map[item.user_id]["last_month_deal_amount"],
                    this_month_deal_amount=monthly_report_map[item.user_id]["this_month_deal_amount"],
                    total_refer_amount=monthly_report_map[item.user_id]["overall_referral_amount"],
                    status=item.status.name,
                    remark=item.remark,
                    lock_level=str_guarantee_level,
                    appraisal_level=str_appraisal_level,
                    expired_time=item.expired_time if item.expired_time else None,
                    created_at=item.created_at if item.created_at else None,
                    country_cn_name=(c.cn_name if (c := get_country(user_location_code_map[item.user_id])) else "其他"),
                    effected_at=item.effected_at if item.effected_at else None,
                    agent_amb_effected_at=agent_amb_effected_at if agent_amb_effected_at else None,
                    referral_status=referral_status,
                    become_count=become_ambassador_count_mapping.get(item.user_id, 0),
                    raw_source=item.source.name,
                    source=source,
                    removed_at=amb_remove_map.get(item.user_id),
                    max_level_history=amb_level_map.get(item.user_id),
                )
            )

        res.sort(key=lambda x: x[sort_name], reverse=True)

        if not kwargs.get('export'):
            items = res[(page - 1) * limit: page * limit - 1]
            ambassador_user_ids = [item['user_id'] for item in items]
            not_least_level_users = cls._get_not_least_level_users(ambassador_user_ids)
            for item in items:
                if item['user_id'] in not_least_level_users:
                    item['appraisal_level'] = '未达标'
            return dict(
                total=total,
                items=res[(page - 1) * limit: page * limit - 1],
                levels={level.name: Ambassador.LEVEL_NAMES[level] for level in Ambassador.Level},
                extra=dict(
                    sources=Ambassador.Source,
                    ambassador_type_dict=Ambassador.Type,
                    countries={code: get_country(code).cn_name
                               for code in list_country_codes_3_admin()},
                ),
            )
        else:
            ambassador_user_ids = [item['user_id'] for item in res]
            not_least_level_users = cls._get_not_least_level_users(ambassador_user_ids)
            for i in res:
                i["effected_at"] = datetime_to_utc8_str(i["effected_at"])
                i["removed_at"] = datetime_to_utc8_str(i["removed_at"]) if i["removed_at"] else ''
                i["agent_amb_effected_at"] = datetime_to_utc8_str(i["agent_amb_effected_at"]) \
                    if i["agent_amb_effected_at"] else ''
                i['expired_time'] = datetime_to_utc8_str(i['expired_time']) if i['expired_time'] else ''
                if i['user_id'] in not_least_level_users:
                    i['appraisal_level'] = '未达标'
            return export_xlsx(
                filename='coinex_ambassador_list',
                data_list=res,
                export_headers=cls.export_headers
            )

    @classmethod
    def get_amb_level_map(cls, amb_his_rows, user_id):
        c_type = AmbassadorChangeType
        LEVEL = Ambassador.Level
        level_sort_map = {
            LEVEL.SILVER.name: 0,
            LEVEL.GOLD.name: 1,
            LEVEL.DIAMOND.name: 2,
        }
        amb_level_map = {}
        type_set = {c_type.LEVEL_CHANGE.name, c_type.ADD.name}
        for row in amb_his_rows:
            if row.action in type_set:
                detail = json.loads(row.detail)
                if user_id not in amb_level_map:
                    amb_level_map[row.user_id] = detail["new_level"]
                else:
                    if level_sort_map[detail["new_level"]] > level_sort_map[amb_level_map[row.user_id]]:
                        amb_level_map[row.user_id] = detail["new_level"]
        return amb_level_map

    @classmethod
    def _get_become_ambassador_count_map(cls):
        apply_log = AmbassadorApplication.query.with_entities(
            AmbassadorApplication.user_id,
            func.count(AmbassadorApplication.user_id).label('count')
        ).filter(
            AmbassadorApplication.type == AmbassadorApplication.Type.AMBASSADOR,
            AmbassadorApplication.status == AmbassadorApplication.Status.AUDITED,
        ).group_by(
            AmbassadorApplication.user_id
        ).all()
        become_ambassador_count_mapping = dict(apply_log)  # 只统计了主动申请通过的
        return become_ambassador_count_mapping

    @classmethod
    def _get_monthly_report_map(cls, ambassador_mapping):
        user_ids = list(ambassador_mapping.keys())
        monthly_report = MonthlyAmbassadorReport.query.filter(
            MonthlyAmbassadorReport.user_id.in_(user_ids)
        ).with_entities(
            MonthlyAmbassadorReport.report_date,
            MonthlyAmbassadorReport.referral_amount,
            MonthlyAmbassadorReport.deal_amount,
            MonthlyAmbassadorReport.user_id,
            func.year(MonthlyAmbassadorReport.report_date).label('year'),
            func.month(MonthlyAmbassadorReport.report_date).label('month'),
            MonthlyAmbassadorReport.asset
        ).all()
        today = datetime.today()
        last_mon = last_month(today.year, today.month)
        monthly_report_map = dict()
        for m in monthly_report:
            if m.user_id not in monthly_report_map:
                monthly_report_map[m.user_id] = defaultdict(Decimal)
            # 推荐人累计交易量
            monthly_report_map[m.user_id]['overall_deal_amount'] += m.deal_amount
            # 推荐人上个月的交易量
            if m.year == last_mon.year and m.month == last_mon.month:
                monthly_report_map[m.user_id]['last_month_deal_amount'] += \
                    m.deal_amount
            # 推荐人本月的交易量
            if m.year == today.year and m.month == today.month:
                monthly_report_map[m.user_id]['this_month_deal_amount'] += \
                    m.deal_amount
            # 累计返佣
            monthly_report_map[m.user_id]['overall_referral_amount'] += \
                m.referral_amount

            amb = ambassador_mapping[m.user_id]
            if amb.status is Ambassador.Status.VALID:
                # 求当前在职大使交易相关数据
                effected_at = amb.effected_at
                if effected_at:
                    effected_date = effected_at.date()
                    if effected_date <= m.report_date:
                        # 在职大使累计返佣
                        monthly_report_map[m.user_id]['cur_valid_referral_amount'] += m.referral_amount
                        # 在职大使累计交易量
                        monthly_report_map[m.user_id]['cur_valid_deal_amount'] += m.deal_amount
                    elif effected_date.replace(day=1) == m.report_date and effected_date > m.report_date:
                        # 在职大使累计返佣
                        monthly_report_map[m.user_id]['cur_valid_referral_amount'] += m.referral_amount
                        # 在职大使累计交易量
                        monthly_report_map[m.user_id]['cur_valid_deal_amount'] += m.deal_amount
        return monthly_report_map

    @classmethod
    def _get_not_least_level_users(cls, user_ids):
        if not user_ids:
            return set()
        report_date = last_month(now().year, now().month)
        model = AppraisalHistory
        ret = set()
        for chunk_user_ids in batch_iter(user_ids, 2000):
            rows = model.query.with_entities(
                model.user_id,
                model.result
            ).filter(
                model.business_type == model.BusinessType.AMBASSADOR,
                model.report_date == report_date,
                model.user_id.in_(chunk_user_ids),
            ).all()
            for row in rows:
                result = json.loads(row.result)
                real_result = result.get('real_result')
                if real_result and real_result == model.ResultStatus.NOT_LEAST_LEVEL.name:
                    ret.add(row.user_id)
        return ret

    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=wa_fields.Integer(required=True),
            remark=wa_fields.String(allow_none=True),
            agent_id=wa_fields.Integer,
            lock_level=EnumField(Ambassador.Level),
            appraisal_level=EnumField(Ambassador.Level, required=True),
            expired_time=TimestampField(is_ms=True),
            source=EnumField(Ambassador.Source, allow_none=True),
        )
    )
    def post(cls, **kwargs):
        """
        用户-大使-新建大使
        """
        user_id = kwargs["user_id"]
        cls.check_user(user_id)
        if Ambassador.query.filter(Ambassador.user_id == user_id).first():
            raise InvalidArgument(message="大使已存在")
        if len({"lock_level", "expired_time"} & set(kwargs.keys())) == 1:
            raise InvalidArgument(message="保底等级和保底时间必须同时存在")
        if BusRelationUserQuerier.get_bus_ambassador(user_id, need_valid=True):
            raise InvalidArgument(message=f"用户{user_id}存在生效中的商务大使，不允许添加为普通大使")
        if TreeAmbHelper.get_ambassador(user_id, need_valid=True):
            raise InvalidArgument(message=f"用户{user_id}存在生效中的子代理，不允许添加为普通大使")

        agent_id = kwargs.get("agent_id")
        if agent_id:
            cls.check_agent(user_id=agent_id, ambassador_user_id=user_id)

        AmbassadorBusiness.become_ambassador(
            user_id,
            appraisal_level=kwargs["appraisal_level"],
            lock_level=kwargs.get("lock_level"),
            expired_time=kwargs.get("expired_time"),
            remark=kwargs.get("remark"),
            agent_user_id=agent_id,
            source=kwargs.get("source"),
            admin_user_id=g.user.id
        )

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.Ambassador,
            detail=kwargs,
            target_user_id=user_id,
        )


@ns.route('/<int:id_>')
@respond_with_code
class AmbassadorResource(AmbassadorCheckMixin, Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            remark=wa_fields.String(missing=None),
            agent_id=wa_fields.Integer(missing=None),
            lock_level=EnumField(Ambassador.Level),
            appraisal_level=EnumField(Ambassador.Level, required=True),
            type=EnumField(Ambassador.Type, required=True),
            expired_time=TimestampField(is_ms=True),
        )
    )
    def patch(cls, id_, **kwargs):
        """
        用户-大使-修改大使(此处不允许修改状态)
        """
        ambassador = Ambassador.query.filter(
            Ambassador.user_id == id_
        ).first()
        if not ambassador:
            raise InvalidArgument(message="不存在此大使")
        old_data = ambassador.to_dict(enum_to_name=True)

        old_level = ambassador.level
        if len({'lock_level', 'expired_time'} & set(kwargs.keys())) == 1:
            raise InvalidArgument(message='保底等级和保底时间必须同时存在')

        old_amb_dict = ambassador.to_dict()
        old_amb_dict["level"] = old_level

        if kwargs.get('lock_level'):
            ambassador.lock_level = kwargs['lock_level']
        if kwargs.get('expired_time'):
            ambassador.expired_time = kwargs['expired_time']
        ambassador.remark = kwargs['remark']
        ambassador.appraisal_level = kwargs['appraisal_level']
        ambassador_type = kwargs["type"]
        ambassador.type = ambassador_type
        new_level = ambassador.level

        old_agent_id = None
        agent_id = kwargs.get('agent_id')
        if agent_id:
            cls.check_agent(user_id=agent_id, ambassador_user_id=id_)
            old_agent_id = AmbassadorBusiness.add_or_update_agent_relation(user_id=id_, agent_user_id=agent_id)
        else:
            relation = AmbassadorAgentRelation.get_relation_obj(ambassador_id=id_)
            if relation:
                raise InvalidArgument(message="不可清理大使绑定关系")

        db.session.commit()
        if ambassador_type != Ambassador.Type.BUSINESS:
            send_update_ambassador_level_email.delay(id_, new_level.value, old_level.value if old_level else None)

        # 相关字段修改了 才添加变更记录
        now_ = now()
        admin_user_id = g.user.id
        if old_agent_id != agent_id:
            ReferralBusiness.add_ambassador_change_history(
                user_id=ambassador.user_id,
                change_type=AmbassadorChangeType.ADJUST_AGENT,
                new_agent_id=agent_id,
                old_agent_id=old_agent_id,
                admin_user_id=admin_user_id,
                create_time=now_,
            )

        if (old_lock_level := old_amb_dict["lock_level"]) != ambassador.lock_level:
            # 改了保底等级
            ReferralBusiness.add_ambassador_change_history(
                user_id=ambassador.user_id,
                change_type=AmbassadorChangeType.ADJUST_LOCK_LEVEL,
                new_lock_level=ambassador.lock_level,
                old_lock_level=old_lock_level,
                expired_time=ambassador.expired_time,
                admin_user_id=admin_user_id,
                create_time=now_,
            )
        elif old_amb_dict["expired_time"] != ambassador.expired_time:
            # 没改保底等级，只改了过期时间
            ReferralBusiness.add_ambassador_change_history(
                user_id=ambassador.user_id,
                change_type=AmbassadorChangeType.ADJUST_LOCK_EXPIRED_TIME,
                expired_time=ambassador.expired_time,
                admin_user_id=admin_user_id,
                create_time=now_,
            )

        if (old_level := old_amb_dict["level"]) != ambassador.level:
            ReferralBusiness.add_ambassador_change_history(
                user_id=ambassador.user_id,
                change_type=AmbassadorChangeType.LEVEL_CHANGE,
                new_level=ambassador.level,
                old_level=old_level,
                admin_user_id=admin_user_id,
                create_time=now_,
            )
        if (old_type := old_amb_dict["type"]) != ambassador_type:
            ReferralBusiness.add_ambassador_change_history(
                user_id=ambassador.user_id,
                change_type=AmbassadorChangeType.ADJUST_TYPE,
                new_type=ambassador_type,
                old_type=old_type,
                admin_user_id=admin_user_id,
                create_time=now_,
            )

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.Ambassador,
            old_data=old_data,
            new_data=ambassador.to_dict(enum_to_name=True),
            target_user_id=ambassador.user_id,
        )


@ns.route('/<int:id_>/status')
@respond_with_code
class AmbassadorStatusResource(Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            status=EnumField(Ambassador.Status, required=True),
        )
    )
    def patch(cls, id_, **kwargs):
        """
        用户-大使-修改大使生效状态
        """
        model = Ambassador
        ambassador = model.query.filter(
            model.user_id == id_
        ).first()
        if not ambassador:
            raise InvalidArgument(message="不存在此大使")
        old_data = ambassador.to_dict(enum_to_name=True)

        status = kwargs['status']
        if ambassador.status != status:
            if status == model.Status.DELETED:
                AmbassadorBusiness.make_ambassador_invalid(ambassador, admin_user_id=g.user.id)
            else:
                if BusRelationUserQuerier.get_bus_ambassador(id_, need_valid=True):
                    raise InvalidArgument(message=f"用户{id_}存在生效中的商务大使，不允许添加为普通大使")
                if TreeAmbHelper.get_ambassador(id_, need_valid=True):
                    raise InvalidArgument(message=f"用户{id_}存在生效中的子代理，不允许添加为普通大使")
                if ambassador.type != Ambassador.Type.NORMAL:
                    raise InvalidArgument(message=f"大使{id_}是商务类型，不再支持在此修改")
                AmbassadorBusiness.become_ambassador(
                    ambassador.user_id,
                    appraisal_level=ambassador.appraisal_level,
                    agent_user_id=None,  # 此种情况不涉及大使代理逻辑
                    admin_user_id=g.user.id,
                )

            AdminOperationLog.new_edit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectUser.Ambassador,
                old_data=old_data,
                new_data=ambassador.to_dict(enum_to_name=True),
                target_user_id=ambassador.user_id,
            )


@ns.route("/change-history")
@respond_with_code
class AmbassadorChangeLogResource(Resource):

    export_limit = 10 * 10000

    CHANGE_TYPE_DESC_MAP = {
        AmbassadorChangeType.ADD.name: "成为大使",
        AmbassadorChangeType.REMOVE.name: "取消大使",
        AmbassadorChangeType.LEVEL_CHANGE.name: "等级变动",
        AmbassadorChangeType.ADJUST_LOCK_LEVEL.name: "调整保底等级",
        AmbassadorChangeType.ADJUST_LOCK_EXPIRED_TIME.name: "调整保底等级时间",  # 修改保底时间也归类为调整保底等级
        AmbassadorChangeType.LOCK_LEVEL_EXPIRED.name: "保底等级到期",
        AmbassadorChangeType.ADJUST_AGENT.name: "调整大使代理",
        AmbassadorChangeType.ADJUST_TYPE.name: "调整大使类型",
    }

    AMBASSADOR_LEVEL_NAME_MAP = {
        Ambassador.Level.SILVER.name: "白银",
        Ambassador.Level.GOLD.name: "黄金",
        Ambassador.Level.DIAMOND.name: "钻石",
    }

    class AllAmbassadorType(Enum):      # 兼容历史数据
        REFERRER = "推荐大使"
        MARKET = "市场大使"
        NORMAL = "普通大使"
        BUSINESS = "商务大使"

    export_headers = (
        {"field": "created_at", Language.ZH_HANS_CN: "时间"},
        {"field": "user_id", Language.ZH_HANS_CN: "用户ID"},
        {"field": "email", Language.ZH_HANS_CN: "邮箱"},
        {"field": "refer_source", Language.ZH_HANS_CN: "大使来源"},
        {"field": "change_type", Language.ZH_HANS_CN: "变更类型"},
        {"field": "detail", Language.ZH_HANS_CN: "变更详情"},
        {"field": "admin_email", Language.ZH_HANS_CN: "操作人"},
    )

    @classmethod
    @ns.use_kwargs(
        dict(
            search_keyword=wa_fields.String,
            change_type=EnumField(AmbassadorChangeType),
            start_date=wa_fields.Date,
            end_date=wa_fields.Date,
            page=PageField,
            limit=LimitField,
            export=wa_fields.Boolean,
        )
    )
    def get(cls, **kwargs):
        """ 用户-大使-变更记录 """
        page, limit = kwargs["page"], kwargs["limit"]
        query = UserStatusChangeHistory.query.filter(
            UserStatusChangeHistory.type == UserStatusChangeHistory.Type.AMBASSADOR.name,
        ).order_by(UserStatusChangeHistory.id.desc())
        if search_keyword := kwargs.get("search_keyword"):
            search_keyword = search_keyword.strip()
            keyword_results = User.search_for_users(search_keyword)
            query = query.filter(
                UserStatusChangeHistory.user_id.in_(keyword_results),
            )
        if change_type := kwargs.get("change_type"):
            query = query.filter(
                UserStatusChangeHistory.action == change_type.name,
            )
        if start_date := kwargs.get("start_date"):
            query = query.filter(
                UserStatusChangeHistory.created_at >= start_date,
            )
        if end_date := kwargs.get("end_date"):
            query = query.filter(
                UserStatusChangeHistory.created_at <= end_date + timedelta(days=1),
            )
        if not kwargs.get('export'):
            records = query.limit(limit).offset((page - 1) * limit).all()
        else:
            records = query.limit(cls.export_limit).all()
        user_ids = {item.user_id for item in records}
        user_email_map = {}
        for batch_user_id in batch_iter(user_ids, 1000):
            user_emails = User.query.filter(User.id.in_(batch_user_id)).with_entities(User.id, User.email).all()
            user_email_map.update(dict(user_emails))
        op_user_ids = {item.admin_user_id for item in records if item.admin_user_id}
        name_map = get_admin_user_name_map(op_user_ids)
        res = []
        for item in records:  # type: UserStatusChangeHistory
            details = json.loads(item.detail)
            if new_level := details.get("new_level"):
                new_level = cls.AMBASSADOR_LEVEL_NAME_MAP[details["new_level"]]
            if old_level := details.get("old_level"):
                old_level = cls.AMBASSADOR_LEVEL_NAME_MAP[details["old_level"]]
            if new_lock_level := details.get("new_lock_level"):
                new_lock_level = cls.AMBASSADOR_LEVEL_NAME_MAP[details["new_lock_level"]]
            if old_lock_level := details.get("old_lock_level"):
                old_lock_level = cls.AMBASSADOR_LEVEL_NAME_MAP[details["old_lock_level"]]
            if expired_time := details.get("expired_time"):
                expired_time = timestamp_to_datetime(
                    int(expired_time), tz.gettz("UTC+8")).strftime("%Y-%m-%d %H:%M:%S")
            exp_time_str = expired_time if expired_time else "永不过期"
            if new_type := details.get("new_type"):
                new_type = cls.AllAmbassadorType[new_type].value
            else:
                new_type = Ambassador.Type.NORMAL.value
            new_type = str(new_type).rstrip("大使")
            if old_type := details.get("old_type"):
                old_type = cls.AllAmbassadorType[old_type].value
            else:
                old_type = Ambassador.Type.NORMAL.value
            old_type = str(old_type).rstrip("大使")

            detail_string = ""
            if item.action == AmbassadorChangeType.ADD.name:
                detail_string = f"成为 {new_type}{new_level}大使"
            elif item.action == AmbassadorChangeType.REMOVE.name:
                detail_string = f"取消 {old_type}{old_level}大使"
            elif item.action == AmbassadorChangeType.LEVEL_CHANGE.name:
                detail_string = f"{old_type}{old_level} 大使变为 {new_type}{new_level}大使"
            elif item.action == AmbassadorChangeType.ADJUST_LOCK_LEVEL.name:
                if not old_lock_level and new_lock_level:
                    detail_string = f"设置保底等级{new_lock_level}，到期时间为{exp_time_str}"
                elif old_lock_level and new_lock_level:
                    detail_string = f"保底等级从{old_lock_level} 调整到 {new_lock_level}, 到期时间为{exp_time_str}"
                elif old_lock_level and not new_lock_level:
                    detail_string = f"取消保底等级{old_lock_level}"
            elif item.action == AmbassadorChangeType.ADJUST_LOCK_EXPIRED_TIME.name:
                detail_string = f"调整保底等级到期时间，到期时间为{exp_time_str}"
            elif item.action == AmbassadorChangeType.ADJUST_AGENT.name:
                old_agent_id, new_agent_id = details.get("old_agent_id"), details.get("new_agent_id")
                if old_agent_id is None and new_agent_id is not None:
                    detail_string = f"绑定大使代理{new_agent_id}"
                elif old_agent_id is not None and new_agent_id is None:
                    detail_string = f"删除大使代理{old_agent_id}"
                elif old_agent_id is not None and new_agent_id is not None:
                    detail_string = f"大使代理从{old_agent_id}修改为{new_agent_id}"
            elif item.action == AmbassadorChangeType.LOCK_LEVEL_EXPIRED.name:
                detail_string = f"{old_lock_level}保底等级到期"
            elif item.action == AmbassadorChangeType.ADJUST_TYPE.name:
                detail_string = f"{old_type}大使 调整为 {new_type}大使"

            res.append(
                dict(
                    created_at=item.created_at,
                    user_id=item.user_id,
                    email=user_email_map.get(item.user_id),
                    change_type=cls.CHANGE_TYPE_DESC_MAP[item.action],
                    raw_detail=details,
                    detail=detail_string,
                    admin_user_id=item.admin_user_id,
                    admin_email=name_map.get(item.admin_user_id) or "系统",
                    refer_source=details.get('refer_source')
                )
            )
        if not kwargs.get('export'):
            return dict(
                items=res,
                # total=total,
                change_types=cls.CHANGE_TYPE_DESC_MAP,
            )
        else:
            for i in res:
                i["created_at"] = datetime_to_utc8_str(i["created_at"])
            return export_xlsx(
                filename='coinex_ambassador_change_history',
                data_list=res,
                export_headers=cls.export_headers
            )


@ns.route('/referral')
@respond_with_code
class AmbassadorReferralResource(Resource):

    export_headers = (
        {"field": "user_id", Language.ZH_HANS_CN: "用户ID"},
        {"field": "email", Language.ZH_HANS_CN: "邮箱"},
        {"field": "report_date", Language.ZH_HANS_CN: "时间"},
        {"field": "delta_referral_count", Language.ZH_HANS_CN: "当月新增推荐人数"},
        {"field": "referral_count", Language.ZH_HANS_CN: "推荐人数"},
        {"field": "delta_referral_trade_count", Language.ZH_HANS_CN: "当月新交易用户"},
        {"field": "deposit_rate", Language.ZH_HANS_CN: "充值率"},
        {"field": "trade_rate", Language.ZH_HANS_CN: "交易率"},
        {"field": "delta_referral_amount", Language.ZH_HANS_CN: "当月新用户交易量（USD）"},
        {"field": "deal_amount", Language.ZH_HANS_CN: "被邀请人累计交易量(USD)"},
        {"field": "deal_user_count", Language.ZH_HANS_CN: "被邀请人累计交易用户数"},
        {"field": "referral_amount", Language.ZH_HANS_CN: "返佣金额(USDT)"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        user_id=wa_fields.Integer,
        start_date=wa_fields.DateTime("%Y-%m"),
        end_date=wa_fields.DateTime("%Y-%m"),
        page=PageField(unlimited=True, missing=1),
        limit=LimitField(missing=50),
        export=wa_fields.Boolean,
    ))
    def get(cls, **kwargs):
        """用户-大使-大使推荐列表"""
        query = MonthlyAmbassadorReport.query.order_by(
            MonthlyAmbassadorReport.report_date.desc(),
            MonthlyAmbassadorReport.user_id,
        )
        page, limit = kwargs['page'], kwargs['limit']
        if user_id := kwargs.get('user_id'):
            query = query.filter(MonthlyAmbassadorReport.user_id == user_id)
        else:
            if start_date := kwargs.get('start_date'):
                query = query.filter(
                    MonthlyAmbassadorReport.report_date >= start_date)
            if end_date := kwargs.get('end_date'):
                query = query.filter(
                    MonthlyAmbassadorReport.report_date <= end_date)

        export = kwargs.get("export")
        if export:
            records = query.limit(ADMIN_EXPORT_LIMIT).all()
            total = len(records)
        else:
            page_rows = query.paginate(page, limit, error_out=False)
            total = page_rows.total
            records = page_rows.items

        user_ids = {r.user_id for r in records}
        user_email_map = {}
        for batch_user_ids in batch_iter(user_ids, 1000):
            users = (
                User.query.filter(User.id.in_(batch_user_ids))
                .with_entities(
                    User.email,
                    User.id,
                )
                .all()
            )
            user_email_map.update({u.id: u.email for u in users})

        res = []
        for item in records:
            res.append(dict(
                user_id=item.user_id,
                email=user_email_map[item.user_id],
                report_date=item.report_date,
                deal_user_count=item.deal_user_count,
                deposit_rate=item.deposit_rate,
                trade_rate=item.trade_rate,
                delta_referral_amount=item.delta_referral_amount,
                deal_amount=item.deal_amount,
                referral_count=item.referral_count,
                delta_referral_count=item.delta_referral_count,
                delta_referral_trade_count=item.delta_referral_trade_count,
                referral_amount=item.referral_amount,
            ))
        if export:
            for i in res:
                i["report_date"] = i["report_date"].strftime("%Y-%m")
                i["deposit_rate"] = f"{amount_to_str(i['deposit_rate'] * 100)}%"
                i["trade_rate"] = f"{amount_to_str(i['trade_rate'] * 100)}%"
            return export_xlsx(
                filename="ambassador_monthly_referral_list",
                data_list=res,
                export_headers=cls.export_headers,
            )

        return dict(
            total=total,
            items=res,
        )


class AmbassadorStarMixin:
    continent_map = {e.name: e.value for e in AmbassadorStar.Continent}

    @classmethod
    def check_continent(cls, continent):
        row = AmbassadorStar.query.filter(AmbassadorStar.continent == continent.name).first()
        if row:
            raise InvalidArgument(message=f'{cls.continent_map[row.continent]} 已被 {row.user_id} 选择')


@ns.route('/stars')
@respond_with_code
class AmbassadorStarListResource(AmbassadorStarMixin, Resource):

    marshal_fields = {
        'id': fx_fields.Integer,
        'user_id': fx_fields.Integer,
        'name': fx_fields.String,
        'avatar': FileField(attribute='file_id'),
        'sort_id': fx_fields.Integer,
        'intro_cn': fx_fields.String,
        'intro_tc': fx_fields.String,
        'intro_en': fx_fields.String,
        'continent': fx_fields.String,
        'country': fx_fields.String,
    }

    @classmethod
    @ns.use_kwargs(dict(
        user_id=wa_fields.Raw,
        page=PageField(unlimited=True),
        limit=LimitField,
    ))
    def get(cls, **kwargs):
        """用户-大使-大使风采列表"""
        query = AmbassadorStar.query.order_by(
            AmbassadorStar.sort_id
        )
        if user_id := kwargs.get('user_id'):
            query = query.filter(AmbassadorStar.user_id == user_id)
        ret = query_to_page(
            query, kwargs['page'], kwargs['limit'], cls.marshal_fields)
        
        country_continent_map = list_country_codes_3_to_continent()

        continent_countries_map = defaultdict(list)

        for code, continent in country_continent_map.items():
            continent_countries_map[continent].append(code)
        Continent = AmbassadorStar.Continent
        ret['extra'] = dict(continents=Continent,
                            continent_countries=continent_countries_map,
                            continent_cn_names=list_continent_cn_names(),
                            country_cn_names=get_code_to_cn_name(),
                            continent_codes={
                                Continent.AFRICA.name: 'AF',
                                Continent.ASIA.name: 'AS',
                                Continent.EUROPE.name: 'EU',
                                Continent.NORTH_AMERICA.name: 'NA',
                                Continent.OCEANIA.name: 'OC',
                                Continent.SOUTH_AMERICA.name: 'SA',

                            })
        return ret

    @classmethod
    @ns.use_kwargs(dict(
        user_id=wa_fields.Integer(required=True),
        name=wa_fields.String(required=True),
        intro_cn=wa_fields.String(required=True),
        intro_en=wa_fields.String(required=True),
        intro_tc=wa_fields.String(required=True),
        file_id=wa_fields.Integer(required=True),
        continent=EnumField(AmbassadorStar.Continent),
        country=wa_fields.String,
    ))
    def post(cls, **kwargs):
        """用户-大使-新建大使风采"""
        def total_star():
            return AmbassadorStar.query.count()

        with CacheLock(LockKeys.ambassador_star()):
            user_id, \
            name, intro_cn, \
            intro_tc, intro_en, \
            file_id = kwargs['user_id'], kwargs['name'], \
                      kwargs['intro_cn'], kwargs['intro_tc'], \
                      kwargs['intro_en'], kwargs['file_id']

            if not all([user_id, name, intro_cn, intro_tc, intro_en, file_id]):
                raise InvalidArgument(message='参数不为空')

            if AmbassadorStar.query.filter(
                    AmbassadorStar.user_id == user_id
            ).first():
                raise InvalidArgument(message='用户 {} 已经是大使风采'.format(user_id))

            star = AmbassadorStar(
                user_id=user_id,
                name=name,
                intro_en=intro_en,
                intro_cn=intro_cn,
                intro_tc=intro_tc,
                file_id=file_id,
                sort_id=total_star() + 1,
            )
            if continent := kwargs.get('continent'):
                # cls.check_continent(continent)
                star.continent = continent.name
            if country := kwargs.get('country'):
                star.country = country
            db.session.add(star)
            db.session.commit()

            AdminOperationLog.new_add(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectUser.AmbassadorStar,
                detail=kwargs,
                target_user_id=user_id,
            )


@ns.route('/star/<int:id_>')
@respond_with_code
class AmbassadorStarResource(AmbassadorStarMixin, Resource):

    @classmethod
    @ns.use_kwargs(dict(
        name=wa_fields.String(required=True),
        intro_cn=wa_fields.String(required=True),
        intro_en=wa_fields.String(required=True),
        intro_tc=wa_fields.String(required=True),
        file_id=wa_fields.Integer,
        continent=EnumField(AmbassadorStar.Continent),
        country=wa_fields.String,
    ))
    def patch(cls, id_, **kwargs):
        """用户-大使-编辑大使风采"""
        with CacheLock(LockKeys.ambassador_star()):
            name, intro_cn, \
            intro_tc, intro_en, \
            file_id = kwargs['name'], \
                      kwargs['intro_cn'], kwargs['intro_tc'], \
                      kwargs['intro_en'], kwargs.get('file_id')
            star = AmbassadorStar.query.get(id_)
            old_data = star.to_dict(enum_to_name=True)
            star.name = name
            star.intro_en = intro_en
            star.intro_cn = intro_cn
            star.intro_tc = intro_tc
            if file_id:
                star.file_id = file_id
            if continent := kwargs.get('continent'):
                # cls.check_continent(continent)
                star.continent = continent.name
            if country := kwargs.get('country'):
                star.country = country
            db.session.commit()

            AdminOperationLog.new_edit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectUser.AmbassadorStar,
                old_data=old_data,
                new_data=star.to_dict(enum_to_name=True),
                target_user_id=star.user_id,
            )

    @classmethod
    def delete(cls, id_):
        """用户-大使-删除大使风采"""
        def _reorder_all():
            query = AmbassadorStar.query.order_by(
                AmbassadorStar.sort_id
            )
            sort_id = 1
            for item in query:
                if item.sort_id != sort_id:
                    item.sort_id = sort_id
                sort_id += 1
            db.session.commit()

        with CacheLock(LockKeys.ambassador_star()):
            star = AmbassadorStar.query.get(id_)
            user_id = star.user_id
            data = dict(id=id_, user_id=user_id, name=star.name)
            db.session.delete(star)
            db.session.commit()
            _reorder_all()

            AdminOperationLog.new_delete(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectUser.AmbassadorStar,
                detail=data,
                target_user_id=user_id,
            )


@ns.route('/star-order/<int:id_>')
@respond_with_code
class AmbassadorStarOrderResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        up=wa_fields.Boolean(required=True)
    ))
    def put(cls, id_, **kwargs):
        """用户-大使-修改大使风采排序"""
        def get_by_sort_id(sort_id):
            return AmbassadorStar.query.filter(
                AmbassadorStar.sort_id == sort_id
            ).first()

        star = AmbassadorStar.query.get(id_)
        old_data = star.to_dict(enum_to_name=True)
        up = kwargs['up']
        if up:
            if star.sort_id == 1:
                raise InvalidArgument(message='已经是第一')
            record = get_by_sort_id(star.sort_id - 1)
        else:
            last_record: AmbassadorStar = AmbassadorStar.query.order_by(
                AmbassadorStar.sort_id.desc()
            ).first()
            if star.sort_id == last_record.sort_id:
                raise InvalidArgument(message='已经是最后')
            record = get_by_sort_id(star.sort_id + 1)
        star.sort_id, record.sort_id = \
            record.sort_id, star.sort_id
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.AmbassadorStar,
            old_data=old_data,
            new_data=star.to_dict(enum_to_name=True),
            target_user_id=star.user_id,
        )


@ns.route('/applications')
@respond_with_code
class AmbassadorApplicationListResource(Resource):
    status_map = {
        AmbassadorApplication.Status.CREATED.value: "待审核",
        AmbassadorApplication.Status.REJECTED.value: "审核不通过",
        AmbassadorApplication.Status.AUDITED.value: "审核通过",
        AmbassadorApplication.Status.DELETED.value: "已删除",
    }

    export_headers = (
        {"field": "application_id", Language.ZH_HANS_CN: "流水ID"},
        {"field": "user_id", Language.ZH_HANS_CN: "申请人ID"},
        {"field": "email", Language.ZH_HANS_CN: "邮箱"},
        {"field": "name", Language.ZH_HANS_CN: "姓名"},
        {"field": "agent_id", Language.ZH_HANS_CN: "大使代理"},
        {"field": "referrer_user_id", Language.ZH_HANS_CN: "注册推荐人"},
        {"field": "country_cn_name", Language.ZH_HANS_CN: "地区"},
        {"field": "telegram", Language.ZH_HANS_CN: "电报"},
        {"field": "mobile_num", Language.ZH_HANS_CN: "手机号"},
        {"field": "applied_at", Language.ZH_HANS_CN: "申请时间"},
        {"field": "application_count", Language.ZH_HANS_CN: "申请次数"},
        {"field": "last_invalid_reason", Language.ZH_HANS_CN: "上次失效原因"},
        {"field": "potential_type", Language.ZH_HANS_CN: "高潜线索类型"},
        {"field": "updated_at", Language.ZH_HANS_CN: "更新时间"},
        {"field": "status", Language.ZH_HANS_CN: "状态"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        status=wa_fields.String(missing=None),
        location_code=wa_fields.String,
        refer_keyword=wa_fields.String,
        ambassador_keyword=wa_fields.String,
        keyword=wa_fields.String,
        page=PageField(missing=1),
        limit=LimitField(missing=50),
        export=wa_fields.Boolean,
    ))
    def get(cls, **kwargs):
        """
        大使申请列表
        """
        model = AmbassadorApplication
        ambassador_query = model.query.join(User).filter(
            model.user_id == User.id,
            model.type == model.Type.AMBASSADOR,
        ).order_by(model.id.desc())

        count_query = ambassador_query.group_by(model.user_id).with_entities(
            AmbassadorApplication.user_id,
            func.count(AmbassadorApplication.id).label('application_count')
        )
        count_map = dict(count_query)

        page, limit = kwargs['page'], kwargs['limit']
        user_ids = []
        if keyword := kwargs.get('keyword', '').strip():
            keyword_results = User.search_for_users(keyword)
            user_ids.extend(keyword_results)
            ambassador_query = ambassador_query.filter(model.user_id.in_(keyword_results))

        if location_code := kwargs.get('location_code'):
            ambassador_query = ambassador_query.filter(User.location_code == location_code)

        if ambassador_keyword := kwargs.get('ambassador_keyword', '').strip():
            # 邀请成为大使的人（与邀请注册区分）
            ambassador_user_ids = []
            ambassador_keyword_results = User.search_for_users(ambassador_keyword)
            ambassador_user_ids.extend(ambassador_keyword_results)
            ambassador_refer_query = Referral.query.filter(Referral.user_id.in_(ambassador_user_ids))
            ambassador_refer_code_list = [i.code for i in ambassador_refer_query]
            ambassador_query = ambassador_query.filter(model.referral_code.in_(ambassador_refer_code_list))

        if refer_keyword := kwargs.get('refer_keyword', '').strip():
            refer_user_ids = []
            refer_keyword_results = User.search_for_users(refer_keyword)
            refer_user_ids.extend(refer_keyword_results)
            refer_query = ReferralHistory.query.filter(ReferralHistory.referrer_id.in_(refer_user_ids))
            referree_user_ids = [i.referree_id for i in refer_query]
            ambassador_query = ambassador_query.filter(model.user_id.in_(referree_user_ids))

        if status := kwargs.get('status'):
            ambassador_query = ambassador_query.filter(
                model.status == status
            )

        user_count = ambassador_query.with_entities(
            func.count(model.user_id.distinct())
        ).scalar() or 0

        export = kwargs.get("export")
        if export:
            records = ambassador_query.limit(ADMIN_EXPORT_LIMIT).all()
            total = len(records)
        else:
            page_rows = ambassador_query.paginate(page, limit, error_out=False)
            total = page_rows.total
            records = page_rows.items

        user_ids.extend([item.user_id for item in records])
        user_ids = list(set(user_ids))
        user_email_map = {}
        user_location_code_map = {}
        for batch_user_ids in batch_iter(user_ids, 1000):
            users = (
                User.query.filter(User.id.in_(batch_user_ids))
                    .with_entities(
                    User.email,
                    User.id,
                    User.location_code
                )
                    .all()
            )
            user_email_map.update({u.id: u.email for u in users})
            user_location_code_map.update({u.id: u.location_code for u in users})
        refer_codes = [item.referral_code for item in records if item.referral_code]
        referrals = Referral.query.filter(
            Referral.code.in_(refer_codes)
        ).all()
        referral_code_map = {r.code: r.user_id for r in referrals}

        referral_users = ReferralHistory.query.filter(
            ReferralHistory.referree_id.in_(user_ids)
        ).all()
        referral_user_map = {r.referree_id: r.referrer_id for r in referral_users}
        potentials = PotentialAmbassador.query.filter(
            PotentialAmbassador.user_id.in_(user_ids),
        ).all()
        potentials = {row.user_id: row for row in potentials}
        res = []
        for item in records:
            if item.user_id in potentials:
                potential_type = potentials[item.user_id].source.value
            else:
                potential_type = '/'
            res.append(dict(
                application_id=item.id,
                head_url=AWSBucketPrivate.get_file_url(item.head) if item.head else '',
                user_id=item.user_id,
                potential_type=potential_type,
                agent_id=(referral_code_map.get(item.referral_code)
                          if item.referral_code else None),
                referrer_user_id=referral_user_map.get(item.user_id, None),
                email=user_email_map[item.user_id],
                name=item.name,
                applied_at=item.created_at,
                application_count=count_map.get(item.user_id, 0),
                updated_at=item.updated_at,
                status=item.status.value,
                last_invalid_reason=item.last_invalid_reason.name if item.last_invalid_reason else '',
                telegram=item.telegram,
                mobile_num=item.mobile_num,
                country_cn_name=(c.cn_name if (c := get_country(user_location_code_map[item.user_id])) else '其他')
            ))
        if export:
            for i in res:
                i["status"] = cls.status_map[i["status"]]
                i["last_invalid_reason"] = model.LastInvalidReason[i["last_invalid_reason"]].value if i["last_invalid_reason"] else '-'
                i["applied_at"] = datetime_to_utc8_str(i["applied_at"])
                i["updated_at"] = datetime_to_utc8_str(i["updated_at"])
                i["application_count"] = count_map.get(i["user_id"], 0)
            return export_xlsx(
                filename="ambassador_applications_list",
                data_list=res,
                export_headers=cls.export_headers,
            )

        return dict(
            user_count=user_count,
            records=res,
            status_options=cls.status_map,
            last_invalid_reason_dict=model.LastInvalidReason,
            total=total,
            countries={code: get_country(code).cn_name
                       for code in list_country_codes_3_admin()}
        )


@ns.route('/application/<int:id_>')
@respond_with_code
class AmbassadorApplicationResource(AmbassadorCheckMixin, Resource):

    @classmethod
    def get(cls, id_):
        """
        大使申请详情
        """
        application = AmbassadorApplication.query.filter(
            AmbassadorApplication.id == id_
        ).first()
        application_dict = application.to_dict()
        if head := application_dict.get('head'):
            application_dict['head_url'] = AWSBucketPrivate.get_file_url(head)
        else:
            application_dict['head_url'] = None
        return dict(
            application=application_dict,
            community_scale_options=AmbassadorApplication.CommunityScale,
            reason_options=AmbassadorApplication.Reason
        )

    @classmethod
    def _handle_special_ambassador(cls, user_id: int):
        """
        处理特殊大使
        """
        now_ = now()
        
        start_ts = int(datetime(2022, 7, 21).timestamp())
        end_ts = int(datetime(2022, 10, 22).timestamp())
        current_ts = int(now_.timestamp())
        
        if not start_ts <= current_ts <= end_ts:
            return
        referral_rate = ReferralRate.get_or_create(user_id=user_id,
                                                   business=TradeBusinessType.PERPETUAL)
        referral_rate.rate = Decimal('0.6')
        referral_rate.expired_at = now_ + timedelta(days=90)
        referral_rate.remark = 'from referral code: "futures"'
        referral_rate.status = ReferralRate.Status.VALID
        db.session.add(referral_rate)
        db.session.commit()
        UserSpecialConfigChangeLog.add(
            user_id=referral_rate.user_id,
            config_type=UserSpecialConfigChangeLog.SpecialConfigType.REFERRAL_RATE,
            op_type=UserSpecialConfigChangeLog.OpType.CREATE,
            admin_user_id=g.user.id,
            change_detail=referral_rate.record_detail,
            change_remark='from referral code: "futures"',
            op_id=referral_rate.id
        )

    @classmethod
    @ns.use_kwargs(dict(
        result=wa_fields.String(required=True),
        record_id=wa_fields.Integer(required=True),
        agent_id=wa_fields.Integer(allow_none=True),
    ))
    def put(cls, id_, **kwargs):
        """
        大使申请审批
        """
        cls.check_user(user_id=id_)
        agent_id = kwargs.get('agent_id')
        # 检查用户状态
        if agent_id:
            cls.check_agent(user_id=agent_id, ambassador_user_id=id_)
        application = AmbassadorApplication.query.filter(
            AmbassadorApplication.id == kwargs.get('record_id'),
        ).first()
        status_map = {status.value: status for status in AmbassadorApplication.Status}
        result = kwargs.get('result')

        application.status = status_map[kwargs['result']]

        AdminOperationLog.new_audit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.AmbassadorApplication,
            detail=dict(id=application.id, user_id=application.user_id, status=application.status.name),
            target_user_id=application.user_id,
        )

        if result != AmbassadorApplication.Status.AUDITED.value:
            db.session.commit()
            if result == AmbassadorApplication.Status.REJECTED.value and agent_id:
                send_ambassador_application_to_agent.delay('ambassador_application_reject',
                                                           id_, agent_id,
                                                           application.telegram)
            return

        if BusRelationUserQuerier.get_bus_ambassador(id_, need_valid=True):
            raise InvalidArgument(message=f"用户{id_}存在生效中的商务大使，不允许添加为普通大使")
        if TreeAmbHelper.get_ambassador(id_, need_valid=True):
            raise InvalidArgument(message=f"用户{id_}存在生效中的子代理，不允许添加为普通大使")
        # KOL特殊处理备注
        ref_row = Referral.query.filter(Referral.code.in_(["mg2sa", "xprogram"])).with_entities(Referral.id).all()
        remark = None
        if ref_row:
            ref_history = ReferralHistory.query.filter(
                ReferralHistory.referree_id == application.user_id,
                ReferralHistory.referral_id.in_([i.id for i in ref_row]),
            ).first()
            remark = "渠道合作kol转化" if ref_history else None
        AmbassadorBusiness.become_ambassador(id_, remark=remark, agent_user_id=agent_id, admin_user_id=g.user.id)
        if agent_id:
            if agent_id == BusinessSettings.special_ambassador_agent_user_id:
                cls._handle_special_ambassador(id_)
            created_at = datetime_to_str(application.created_at)
            send_ambassador_application_to_agent.delay('ambassador_application_approve',
                                                       id_, agent_id,
                                                       application.telegram,
                                                       created_at)


@ns.route('/applications/batch-audit')
@respond_with_code
class ApplicationsBatchAuditResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        ids=wa_fields.List(wa_fields.Integer, required=True)
    ))
    def post(cls, **kwargs):
        """大使申请审批-批量通过"""

        application_list = AmbassadorApplication.query.filter(
            AmbassadorApplication.id.in_(kwargs['ids']),
        ).all()

        for application in application_list:
            if application.status != AmbassadorApplication.Status.CREATED:
                raise InvalidArgument(message=f'{application.id} 状态异常')

        user_ids = {i.user_id for i in application_list}
        ambassador = Ambassador.query.filter(
            Ambassador.user_id.in_(user_ids),
            Ambassador.status == Ambassador.Status.VALID
        ).first()
        if ambassador:
            raise InvalidArgument(message=f"用户ID为{ambassador.user_id}的大使已存在")

        refer_codes = [i.referral_code for i in application_list if i.referral_code]
        referrals = Referral.query.filter(Referral.code.in_(refer_codes)).all()
        referral_code_map = {r.code: r.user_id for r in referrals}
        for agent_id_ in referral_code_map.values():
            agent = AmbassadorAgentBusiness.get_agent(agent_id_)
            if not agent:
                raise InvalidArgument(message=f"用户ID为{agent.user_id}的大使代理不存在")

        db.session.rollback()
        application_list = AmbassadorApplication.query.filter(
                AmbassadorApplication.id.in_(kwargs['ids']),
                AmbassadorApplication.status == AmbassadorApplication.Status.CREATED,
        ).all()

        ref_row = Referral.query.filter(Referral.code.in_(["mg2sa", "xprogram"])).with_entities(Referral.id).all()
        kol_user_ids = set()
        if ref_row:
            ref_history = ReferralHistory.query.filter(
                ReferralHistory.referral_id.in_([i.id for i in ref_row]),
            ).all()
            kol_user_ids = {i.referree_id for i in ref_history}
        for application in application_list:
            user_id = application.user_id
            application.status = AmbassadorApplication.Status.AUDITED
            agent_id = referral_code_map.get(application.referral_code)
            # KOL特殊处理备注
            remark = "渠道合作kol转化" if application.user_id in kol_user_ids else None

            AmbassadorBusiness.become_ambassador(user_id, remark=remark, agent_user_id=agent_id, admin_user_id=g.user.id)
            if agent_id:
                created_at = datetime_to_str(application.created_at)
                send_ambassador_application_to_agent.delay(
                    'ambassador_application_approve',
                    user_id, agent_id,
                    application.telegram,
                    created_at)

            AdminOperationLog.new_audit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectUser.AmbassadorApplication,
                detail=dict(id=application.id, user_id=user_id, status=application.status),
                target_user_id=application.user_id,
            )
        db.session.commit()


@ns.route('/applications/batch-reject')
@respond_with_code
class ApplicationsBatchDeleteResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        ids=wa_fields.List(wa_fields.Integer, required=True)
    ))
    def post(cls, **kwargs):
        """大使申请审批-批量拒绝"""
        application_list = AmbassadorApplication.query.filter(
            AmbassadorApplication.id.in_(kwargs['ids']),
        ).all()

        for application in application_list:
            if application.status != AmbassadorApplication.Status.CREATED:
                raise InvalidArgument(message=f'{application.id} 状态异常')

        user_ids = {i.user_id for i in application_list}
        ambassador = Ambassador.query.filter(
            Ambassador.user_id.in_(user_ids),
            Ambassador.status == Ambassador.Status.VALID
        ).first()
        if ambassador:
            raise InvalidArgument(message=f"用户ID为{ambassador.user_id}的大使已存在")

        refer_codes = [i.referral_code for i in application_list if i.referral_code]
        referrals = Referral.query.filter(Referral.code.in_(refer_codes)).all()
        referral_code_map = {r.code: r.user_id for r in referrals}
        for agent_id_ in referral_code_map.values():
            agent = AmbassadorAgentBusiness.get_agent(agent_id_)
            if not agent:
                raise InvalidArgument(message=f"用户ID为{agent.user_id}的大使代理不存在")
            if agent.status == AmbassadorAgent.Status.DELETED:
                raise InvalidArgument(message=f"用户ID为{agent.user_id}的大使代理已失效")

        db.session.rollback()
        application_list = AmbassadorApplication.query.filter(
                AmbassadorApplication.id.in_(kwargs['ids']),
        ).all()
        for application in application_list:
            application.status = AmbassadorApplication.Status.REJECTED
            user_id = application.user_id
            agent_id = referral_code_map.get(application.referral_code)
            if agent_id:
                send_ambassador_application_to_agent.delay(
                    'ambassador_application_reject',
                    user_id, agent_id,
                    application.telegram)

            AdminOperationLog.new_audit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectUser.AmbassadorApplication,
                detail=dict(id=application.id, user_id=user_id, status=application.status),
                target_user_id=application.user_id,
            )
        db.session.commit()


class CheckMixin:

    @classmethod
    def check(cls, user_id: int, is_appraisal: bool, referral_rate: Decimal) -> None:
        user = User.query.get(user_id)
        if not user:
            raise InvalidArgument(message=f"用户{user_id}不存在")

        if user.is_sub_account:
            raise InvalidArgument(message="不能是子账户")

        if not Decimal() <= referral_rate < Decimal("1"):
            raise InvalidArgument(message="返佣比例必须大于等于0小于1")

        if is_appraisal:
            ambassador = AmbassadorBusiness.get_ambassador(user_id)
            if ambassador:
                raise InvalidArgument(message='当该用户是大使时，无法设置大使代理需考核')

    @classmethod
    def check_status(cls, user_id: int, status: AmbassadorAgent.Status) -> None:
        if status is AmbassadorAgent.Status.DELETED:
            ambassador = AmbassadorBusiness.get_ambassador(user_id)
            if ambassador and ambassador.type != Ambassador.Type.BUSINESS:
                raise InvalidArgument(message='当该用户是大使且不是商务大使时，无法设置大使代理失效')


@ns.route('/agents')
@respond_with_code
class AmbassadorAgentListResource(CheckMixin, Resource):

    @classmethod
    @ns.use_kwargs(
        dict(
            status=EnumField(AmbassadorAgent.Status),
            is_appraisal=wa_fields.Boolean,
            is_ambassador=wa_fields.Boolean,
            user_id=wa_fields.Integer,
            page=PageField(missing=1),
            limit=LimitField(missing=50),
            export=wa_fields.Integer,
        )
    )
    def get(cls, **kwargs):
        """
        大使代理列表
        """
        agent_query = AmbassadorAgent.query
        page, limit = kwargs['page'], kwargs['limit']

        if kwargs.get("user_id"):
            agent_query = agent_query.filter(AmbassadorAgent.user_id == kwargs.get("user_id"))
        if (is_appraisal := kwargs.get("is_appraisal")) is not None:
            agent_query = agent_query.filter(AmbassadorAgent.is_appraisal == is_appraisal)
        if (is_ambassador := kwargs.get("is_ambassador")) is not None:
            sub_query = Ambassador.query.with_entities(
                Ambassador.user_id
            ).filter(Ambassador.status == Ambassador.Status.VALID).subquery()
            if is_ambassador:
                agent_query = agent_query.filter(AmbassadorAgent.user_id.in_(sub_query))
            else:
                agent_query = agent_query.filter(AmbassadorAgent.user_id.notin_(sub_query))
        if status := kwargs.get("status"):
            agent_query = agent_query.filter(AmbassadorAgent.status == status)

        records = agent_query.all()
        total = len(records)

        if not kwargs.get("export"):
            records = records[(page-1)*limit: page*limit]
        res = get_agents_statistic_data(records, kwargs.get("export"))
        if kwargs.get('export'):
            return export_xlsx(
                filename='ambassador_agent_list',
                data_list=res,
                export_headers=AGENT_EXPORT_HEADERS
            )

        return dict(total=total, items=res)

    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=wa_fields.Integer,
            real_referral_rate=wa_fields.Decimal(validate=lambda x: Decimal() <= x < Decimal("1")),
            is_appraisal=wa_fields.Boolean(default=False),
            remark=wa_fields.String(allow_none=True),
        )
    )
    def post(cls, **kwargs):
        """
        新增大使代理
        """
        cls.check(kwargs['user_id'], kwargs['is_appraisal'], kwargs['real_referral_rate'])
        AmbassadorAgentBusiness.become_ambassador_agent(
            kwargs["user_id"],
            referral_rate=kwargs.get("real_referral_rate"),
            is_appraisal=kwargs.get("is_appraisal", False),
            remark=kwargs.get("remark"),
            admin_user_id=g.user.id,
        )

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.Agent,
            detail=kwargs,
            target_user_id=kwargs['user_id'],
        )


@ns.route('/agent/<int:id_>')
@respond_with_code
class AmbassadorAgentResource(CheckMixin, Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            real_referral_rate=wa_fields.Decimal(validate=lambda x: Decimal() <= x < Decimal("1")),
            is_appraisal=wa_fields.Boolean(default=False),
            remark=wa_fields.String(allow_none=True),
        )
    )
    def put(cls, id_, **kwargs):
        """
        编辑大使代理
        """
        cls.check(id_, kwargs.get("is_appraisal"), kwargs.get("real_referral_rate"))
        old_agent = AmbassadorAgentBusiness.get_agent(id_)
        old_data = old_agent.to_dict(enum_to_name=True) if old_agent else {}
        AmbassadorAgentBusiness.update_ambassador_agent(
            user_id=id_,
            referral_rate=kwargs['real_referral_rate'],
            is_appraisal=kwargs.get("is_appraisal", False),
            remark=kwargs.get("remark"),
            admin_user_id=g.user.id,
        )

        new_agent = AmbassadorAgentBusiness.get_agent(id_)
        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.Agent,
            old_data=old_data,
            new_data=new_agent.to_dict(enum_to_name=True) if new_agent else {},
            target_user_id=id_,
        )


@ns.route('/agent/<int:id_>/status')
@respond_with_code
class AmbassadorAgentStatusResource(CheckMixin, Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            status=EnumField(AmbassadorAgent.Status, required=True),
        )
    )
    def patch(cls, id_, **kwargs):
        """
        编辑大使代理状态
        """
        cls.check_status(id_, status=kwargs.get('status'))
        old_agent = AmbassadorAgentBusiness.get_agent(id_)
        old_data = old_agent.to_dict(enum_to_name=True) if old_agent else {}
        AmbassadorAgentBusiness.change_ambassador_agent_status(
            user_id=id_,
            status=kwargs["status"],
            admin_user_id=g.user.id,
        )

        new_agent = AmbassadorAgentBusiness.get_agent(id_)
        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.Agent,
            old_data=old_data,
            new_data=new_agent.to_dict(enum_to_name=True) if new_agent else {},
            target_user_id=id_,
        )


@ns.route('/agent/change-history')
@respond_with_code
class AmbassadorAgentChangeLogResource(Resource):

    ROLES = {
        UserStatusChangeHistory.Type.AMBASSADOR_AGENT.name: '大使内推',
        }
    CHANGE_TYPES = {
                AmbassadorAgentChangeType.ADD.name: '增加',
                AmbassadorAgentChangeType.REMOVE.name: '删除',
                AmbassadorAgentChangeType.EDIT.name: '修改'
            }

    @classmethod
    @ns.use_kwargs(
        dict(
            search_keyword=wa_fields.String,
            change_type=EnumField(AmbassadorAgentChangeType),
            start_date=wa_fields.Date,
            end_date=wa_fields.Date,
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 用户-大使代理-变更记录 """
        page, limit = kwargs["page"], kwargs["limit"]
        query = UserStatusChangeHistory.query.order_by(UserStatusChangeHistory.id.desc())
        if search_keyword := kwargs.get("search_keyword"):
            search_keyword = search_keyword.strip()
            keyword_results = User.search_for_users(search_keyword)
            query = query.filter(
                UserStatusChangeHistory.user_id.in_(keyword_results),
            )
        if change_type := kwargs.get("change_type"):
            query = query.filter(
                UserStatusChangeHistory.action == change_type.name,
            )

        query = query.filter(
            UserStatusChangeHistory.type.in_(
                (UserStatusChangeHistory.Type.AMBASSADOR_AGENT.name,)
            )
        )
        if start_date := kwargs.get("start_date"):
            query = query.filter(
                UserStatusChangeHistory.created_at >= start_date,
            )
        if end_date := kwargs.get("end_date"):
            query = query.filter(
                UserStatusChangeHistory.created_at <= end_date + timedelta(days=1),
            )
        query = query.order_by(UserStatusChangeHistory.id)
        records = query.paginate(page, limit, error_out=False)
        user_ids = {item.user_id for item in records.items}
        user_ids |= {item.admin_user_id for item in records.items if item.admin_user_id}
        user_email_map = {}
        for batch_user_id in batch_iter(user_ids, 1000):
            user_emails = User.query.filter(User.id.in_(batch_user_id)).with_entities(User.id, User.email).all()
            user_email_map.update(dict(user_emails))
        name_map = get_admin_user_name_map(user_ids)
        res = []
        for item in records.items:
            item: UserStatusChangeHistory
            detail = json.loads(item.detail)
            remark = detail.get("remark", "--")
            referral_rate = detail.get("referral_rate", "0.05")
            is_appraisal = detail.get("is_appraisal", False)
            if detail['status'] == AmbassadorAgent.Status.VALID.name:
                status_str = '生效'
            else:
                status_str = '失效'
            detail_str = f'返佣比例为：{referral_rate}，是否考核：{is_appraisal}，{status_str}'

            res.append(dict(
                created_at=item.created_at,
                user_id=item.user_id,
                email=user_email_map[item.user_id],
                role=cls.ROLES[item.type],
                change_type=cls.CHANGE_TYPES[item.action],
                detail=detail_str,
                remark=remark,
                admin_user_id=item.admin_user_id,
                admin_email=name_map.get(item.admin_user_id)
            ))
        return dict(
            items=res,
            change_types=cls.CHANGE_TYPES,
            roles=cls.ROLES
        )


@ns.route("/agent/referral")
@respond_with_code
class AmbassadorAgentReferralResource(Resource):

    export_headers = (
        {"field": "user_id", Language.ZH_HANS_CN: "用户ID"},
        {"field": "email", Language.ZH_HANS_CN: "邮箱"},
        {"field": "report_date", Language.ZH_HANS_CN: "时间"},
        {"field": "delta_referral_ambassador_count", Language.ZH_HANS_CN: "推荐大使数"},
        {"field": "delta_referral_deal_ambassador_count", Language.ZH_HANS_CN: "有交易大使数"},
        {"field": "delta_referral_user_count", Language.ZH_HANS_CN: "大使推荐用户数"},
        {"field": "delta_deal_user_count", Language.ZH_HANS_CN: "大使推荐交易用户数"},
        {"field": "delta_deal_amount", Language.ZH_HANS_CN: "被邀请人累计交易量(USD)"},
        {"field": "referral_amount", Language.ZH_HANS_CN: "返佣金额(USDT)"},
    )

    @classmethod
    @ns.use_kwargs(
        dict(
            user_id=wa_fields.Integer,
            start_date=wa_fields.DateTime("%Y-%m"),
            end_date=wa_fields.DateTime("%Y-%m"),
            page=PageField(unlimited=True, missing=1),
            limit=LimitField(missing=50),
            export=wa_fields.Boolean,
        )
    )
    def get(cls, **kwargs):
        """ 用户-CoinEX代理-代理推荐列表 """
        query = MonthlyAmbassadorAgentReport.query.order_by(
            MonthlyAmbassadorAgentReport.report_date.desc(),
            MonthlyAmbassadorAgentReport.user_id,
        )
        page, limit = kwargs["page"], kwargs["limit"]
        if user_id := kwargs.get("user_id"):
            query = query.filter(MonthlyAmbassadorAgentReport.user_id == user_id)
        if start_date := kwargs.get("start_date"):
            query = query.filter(MonthlyAmbassadorAgentReport.report_date >= start_date)
        if end_date := kwargs.get("end_date"):
            query = query.filter(MonthlyAmbassadorAgentReport.report_date <= end_date)

        export = kwargs.get("export")
        if export:
            records = query.limit(ADMIN_EXPORT_LIMIT).all()
            total = len(records)
        else:
            page_rows = query.paginate(page, limit, error_out=False)
            total = page_rows.total
            records = page_rows.items

        user_ids = {r.user_id for r in records}
        user_email_map = {}
        for batch_user_ids in batch_iter(user_ids, 1000):
            users = (
                User.query.filter(User.id.in_(batch_user_ids))
                .with_entities(
                    User.email,
                    User.id,
                )
                .all()
            )
            user_email_map.update({u.id: u.email for u in users})

        res = []
        for item in records:
            res_item = item.to_dict()
            res_item["email"] = user_email_map.get(item.user_id, "")
            res.append(res_item)
        if export:
            for i in res:
                i["report_date"] = i["report_date"].strftime("%Y-%m")
            return export_xlsx(
                filename="ambassador_agent_monthly_referral_list",
                data_list=res,
                export_headers=cls.export_headers,
            )

        return dict(
            total=total,
            items=res,
        )


@ns.route('/rate')
@respond_with_code
class SpecialReferralRateResource(Resource):
    """
    特殊返佣比例
    """
    business_type_map = {
        TradeBusinessType.PERPETUAL.value: "合约",
        TradeBusinessType.SPOT.value: "现货"
    }
    
    export_headers = (
        {"field": "user_id", Language.ZH_HANS_CN: "用户ID"},
        {"field": "email", Language.ZH_HANS_CN: "邮箱"},
        {"field": "business_type", Language.ZH_HANS_CN: "业务"},
        {"field": "rate", Language.ZH_HANS_CN: "返佣比例"},
        {"field": "expired_at", Language.ZH_HANS_CN: "到期时间"},
        {"field": "updated_at", Language.ZH_HANS_CN: "最近修改"},
        {"field": "remark", Language.ZH_HANS_CN: "备注"},
        {"field": "operator", Language.ZH_HANS_CN: "操作人"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        business_type=wa_fields.String,
        start_date=wa_fields.DateTime("%Y-%m-%d"),
        end_date=wa_fields.DateTime("%Y-%m-%d"),
        keyword=wa_fields.String,
        page=PageField(missing=1),
        limit=LimitField(missing=50),
        export=wa_fields.Boolean(missing=False),
    ))
    def get(cls, **kwargs):
        """
        特殊返佣比例列表
        """
        rate_query = ReferralRate.query.filter(
            ReferralRate.status == ReferralRate.Status.VALID
        )
        page, limit = kwargs['page'], kwargs['limit']
        if business_type := kwargs.get('business_type'):
            rate_query = rate_query.filter(
                ReferralRate.business == business_type
            )
        if start_date := kwargs.get('start_date'):
            rate_query = rate_query.filter(
                ReferralRate.expired_at >= start_date
            )
        if end_date := kwargs.get('end_date'):
            rate_query = rate_query.filter(
                ReferralRate.expired_at <= end_date
            )

        if keyword := kwargs.get('keyword', '').strip():
            keyword_results = User.search_for_users(keyword)
            rate_query = rate_query.filter(
                ReferralRate.user_id.in_(keyword_results))
        rate_query = rate_query.order_by(
            ReferralRate.id.desc()
        )
        if kwargs['export']:
            items = list(rate_query.limit(ADMIN_EXPORT_LIMIT).all())
        else:
            records = rate_query.paginate(page, limit, error_out=False)
            items = records.items
            total = records.total
        user_ids = [item.user_id for item in items]
        users = User.query.filter(
            User.id.in_(user_ids)).with_entities(
            User.email,
            User.id
        ).all()
        user_email_map = {u.id: u.email for u in users}
        record_ids = [i.id for i in items]
        operator_id_dict, operator_name_dict = get_special_conf_create_operators(
            record_ids, UserSpecialConfigChangeLog.SpecialConfigType.REFERRAL_RATE)
        res = []
        for item in items:
            res.append(dict(
                user_id=item.user_id,
                email=user_email_map[item.user_id],
                business_type=item.business.value,
                rate=item.rate,
                expired_at=item.expired_at,
                updated_at=item.updated_at,
                remark=item.remark,
                operator=operator_name_dict.get(item.id),
                operator_id=operator_id_dict.get(item.id)
            ))
        
        if kwargs['export']:
            for item in res:
                item["business_type"] = cls.business_type_map.get(item["business_type"], "未知")
                item["rate"] = format_percent(item["rate"])
                item["expired_at"] = datetime_to_utc8_str(item['expired_at']) if item["expired_at"] else '永不过期'
                item['updated_at'] = datetime_to_utc8_str(item['updated_at'])
            return export_xlsx(
                filename='user_report',
                data_list=res,
                export_headers=cls.export_headers
            )
        return dict(
            total=total,
            items=res
        )

    @classmethod
    @ns.use_kwargs(dict(
        user_id=wa_fields.Integer(required=True),
        business_data=wa_fields.List(wa_fields.Dict, required=True),
        expired_at=TimestampField(is_ms=True, allow_none=True),
        remark=wa_fields.String(allow_none=True)
    ))
    def post(cls, **kwargs):
        """
        添加特殊返佣比例
        """
        cls._validate(kwargs)
        biz_msg = []
        for biz in kwargs['business_data']:
            if biz['business_type'] == 'perpetual':
                type_ = TradeBusinessType.PERPETUAL
                display_msg = '合约'
            else:
                type_ = TradeBusinessType.SPOT
                display_msg = '现货'
            referral_rate: ReferralRate = ReferralRate.get_or_create(user_id=kwargs['user_id'], business=type_)
            if referral_rate.id and referral_rate.status is ReferralRate.Status.VALID:
                if not referral_rate.expired_at:
                    biz_msg.append(display_msg)
                    continue
                if referral_rate.expired_at < now():
                    biz_msg.append(display_msg)
                    continue
            referral_rate.rate = biz['rate']
            referral_rate.expired_at = kwargs['expired_at']
            referral_rate.remark = kwargs.get('remark')
            referral_rate.status = ReferralRate.Status.VALID
            db.session.add(referral_rate)
            db.session.commit()
            UserSpecialConfigChangeLog.add(
                user_id=referral_rate.user_id,
                config_type=UserSpecialConfigChangeLog.SpecialConfigType.REFERRAL_RATE,
                op_type=UserSpecialConfigChangeLog.OpType.CREATE,
                admin_user_id=g.user.id,
                change_detail=referral_rate.record_detail,
                change_remark=kwargs.get('remark'),
                op_id=referral_rate.id
            )

            AdminOperationLog.new_add(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectUser.SpecialAmbassadorRate,
                detail=referral_rate.to_dict(enum_to_name=True),
                target_user_id=referral_rate.user_id,
            )
        if biz_msg:
            return dict(tips=f'用户ID存在已配置{"+".join(biz_msg)}数据')
        return dict()

    @classmethod
    def _validate(cls, params):
        for biz in params['business_data']:
            if not biz['business_type'] or not biz['rate']:
                raise InvalidArgument(message='业务或返佣比例必须配置！')
            biz['rate'] = Decimal(biz['rate'])
            if not Decimal() < biz['rate'] < Decimal('1'):
                raise InvalidArgument(message='返佣比例必须在（0 ～ 1）！')
        if not User.query.get(params['user_id']):
            raise InvalidArgument(message="不存在此用户")

    @classmethod
    @ns.use_kwargs(dict(
        user_id=wa_fields.Integer(required=True),
        business_type=wa_fields.String(required=True),
        rate=wa_fields.Decimal(required=True),
        expired_at=TimestampField(is_ms=True, allow_none=True),
        remark=wa_fields.String(allow_none=True)
    ))
    def put(cls, **kwargs):
        """
        修改特殊返佣比例
        """
        referral_rate = ReferralRate.query.filter(
            ReferralRate.user_id == kwargs['user_id'],
            ReferralRate.business == kwargs['business_type'],
            ReferralRate.status == ReferralRate.Status.VALID
        ).first()

        if not referral_rate:
            raise InvalidArgument(message="不存在此用户的费率")
        old_data = referral_rate.to_dict(enum_to_name=True)

        referral_rate.rate = kwargs['rate'],
        referral_rate.expired_at = kwargs['expired_at']
        referral_rate.remark = kwargs.get('remark')
        db.session.commit()
        UserSpecialConfigChangeLog.add(
            user_id=referral_rate.user_id,
            config_type=UserSpecialConfigChangeLog.SpecialConfigType.REFERRAL_RATE,
            op_type=UserSpecialConfigChangeLog.OpType.UPDATE,
            admin_user_id=g.user.id,
            change_detail=referral_rate.record_detail,
            change_remark=kwargs.get('remark'),
            op_id=referral_rate.id
        )

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.SpecialAmbassadorRate,
            old_data=old_data,
            new_data=referral_rate.to_dict(enum_to_name=True),
            target_user_id=referral_rate.user_id,
        )

    @classmethod
    @ns.use_kwargs(dict(
        user_id=wa_fields.Integer(required=True),
        business_type=wa_fields.String(required=True),
    ))
    def delete(cls, **kwargs):
        """
        删除特殊返佣比例
        """
        referral_rate = ReferralRate.query.filter(
            ReferralRate.user_id == kwargs['user_id'],
            ReferralRate.business == kwargs['business_type']
        ).first()
        if not referral_rate:
            raise InvalidArgument(message="不存在此用户的费率")
        referral_rate.status = ReferralRate.Status.DELETED
        db.session.commit()
        UserSpecialConfigChangeLog.add(
            user_id=referral_rate.user_id,
            config_type=UserSpecialConfigChangeLog.SpecialConfigType.REFERRAL_RATE,
            op_type=UserSpecialConfigChangeLog.OpType.DELETE,
            admin_user_id=g.user.id,
            change_detail=referral_rate.record_detail,
            op_id=referral_rate.id
        )

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.SpecialAmbassadorRate,
            detail=dict(id=referral_rate.id, business=referral_rate.business.name, rate=referral_rate.rate),
            target_user_id=referral_rate.user_id,
        )


@ns.route('/rate/batch-import')
@respond_with_code
class BatchSpecialReferralRateResource(Resource):
    business_mapping = {
        '现货': TradeBusinessType.SPOT,
        '合约': TradeBusinessType.PERPETUAL,
    }

    @classmethod
    @ns.use_kwargs(dict(
        data=wa_fields.List(wa_fields.Dict, required=True),
    ))
    def post(cls, **kwargs):
        necessary_checks = []
        business_checks = []
        rate_checks = []
        email_checks = []
        unexpired_checks = []
        data = [[
            d['邮箱'].strip().lower(),
            d['业务'].strip(),
            str(d['返佣比例']).strip(),
            d['到期时间'].strip(),
            d['备注'].strip(),
        ] for d in kwargs['data']]
        emails = [d['邮箱'].strip().lower() for d in kwargs['data']]
        user_emails = cls._get_user_emails(emails)
        for idx, item in enumerate(data, start=1):
            # ["邮箱", "业务", "返佣比例", "到期时间", "备注"]
            email, business, rate, expired_at, remark = item
            if not email or not business or not rate:
                necessary_checks.append((idx, item))
                continue
            if business not in cls.business_mapping:
                business_checks.append((idx, item))
                continue
            rate = Decimal(rate)
            if not Decimal() < rate < Decimal('1'):
                rate_checks.append((idx, item))
                continue
            email = email.lower()
            user_id = user_emails.get(email)
            if not user_id:
                email_checks.append(item)
                continue
            expired_at = str_to_datetime(expired_at) if expired_at else None
            referral_rate: ReferralRate = ReferralRate.get_or_create(
                user_id=user_id,
                business=cls.business_mapping[business]
            )
            if referral_rate.id and referral_rate.status is ReferralRate.Status.VALID:
                if not referral_rate.expired_at:
                    unexpired_checks.append(item)
                    continue
                if referral_rate.expired_at < now():
                    unexpired_checks.append(item)
                    continue
            referral_rate.rate = rate
            referral_rate.expired_at = expired_at
            referral_rate.remark = remark
            referral_rate.status = ReferralRate.Status.VALID
            db.session.add(referral_rate)
            db.session.commit()
            UserSpecialConfigChangeLog.add(
                user_id=referral_rate.user_id,
                config_type=UserSpecialConfigChangeLog.SpecialConfigType.REFERRAL_RATE,
                op_type=UserSpecialConfigChangeLog.OpType.CREATE,
                admin_user_id=g.user.id,
                change_detail=referral_rate.record_detail,
                change_remark=kwargs.get('remark'),
                op_id=referral_rate.id
            )
        msg = []
        for idx, _ in necessary_checks:
            msg.append(f'表格{idx}行，必填字段未填写，上传已剔除不完整数据行；')
        for idx, _ in business_checks:
            msg.append(f'表格{idx}行，业务字段不正确；')
        for idx, _ in rate_checks:
            msg.append(f'表格{idx}行，返佣比例不正确，需填写 0 ～ 1，上传已剔除错误行；')
        if email_checks:
            msg_emails = list(set([x[0] for x in email_checks]))
            msg.append(f'文件中{"、".join(msg_emails)}邮箱数据不存在，数据已剔除以上几行上传；')
        if unexpired_checks:
            msg_emails = list(set([x[0] for x in unexpired_checks]))
            msg.append(f'文件中{"、".join(msg_emails)}邮箱数据存在未到期记录，请删除原数据后重新上传；')
        if msg:
            return dict(tips='\n'.join(msg))
        return dict()

    @classmethod
    def _get_user_emails(cls, emails):
        rows = User.query.with_entities(
            User.email,
            User.id,
        ).filter(
            User.email.in_(emails)
        ).all()
        return dict(rows)


@ns.route("/guides")
@respond_with_code
class AmbassadorGuideManageResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            page=PageField,
            limit=LimitField,
        )
    )
    def get(cls, **kwargs):
        """ 用户-CoinEx大使-大使攻略-列表 """
        guide_query = AmbassadorGuide.query.filter(
            AmbassadorGuide.status == AmbassadorGuide.Status.VALID,
        ).order_by(AmbassadorGuide.rank.desc())
        pagination = guide_query.paginate(kwargs["page"], kwargs["limit"], error_out=False)
        guide_ids = [i.id for i in pagination.items]
        tran_rows = AmbassadorGuideTranslation.query.filter(AmbassadorGuideTranslation.guide_id.in_(guide_ids)).all()
        guide_tran_dict = defaultdict(list)
        for i in tran_rows:
            guide_tran_dict[i.guide_id].append(dict(title=i.title, url=i.url, lang=i.lang, guide_id=i.guide_id))

        table_items = []
        for i in pagination.items:
            translations = guide_tran_dict[i.id]
            missing_langs = {lang.name for lang in AmbassadorGuideTranslation.AVAILABLE_LANGUAGES} - {
                d["lang"] for d in translations
            }
            for missing_lang in missing_langs:
                translations.append(dict(title="", url="", lang=missing_lang, guide_id=i.id))
            translation_map = {i["lang"]: i for i in translations}
            zh_cn_tran = translation_map[Language.ZH_HANS_CN.name]
            mapping = dict(
                id=i.id,
                rank=i.rank,
                created_at=i.created_at,
                title=zh_cn_tran["title"],
                url=zh_cn_tran["url"],
                remark=i.remark,
                status=i.status.name,
                translations=translations,
            )
            table_items.append(mapping)

        return dict(
            total=pagination.total,
            items=table_items,
            extra=dict(
                languages=[v.name for v in AmbassadorGuideTranslation.AVAILABLE_LANGUAGES],
                language_trans={
                    lang.name: cn_name
                    for lang, cn_name in language_cn_names().items()
                    if lang in AmbassadorGuideTranslation.AVAILABLE_LANGUAGES
                },
                status_dict={
                    AmbassadorGuide.Status.VALID.name: "开启",
                    AmbassadorGuide.Status.DELETED.name: "关闭",
                },
            ),
        )

    class DetailTransSchema(Schema):
        lang = EnumField(Language, required=True)
        title = wa_fields.String(required=True)
        url = wa_fields.String(required=True)
        guide_id = wa_fields.Integer()  # when post: None

        class Meta:
            UNKNOWN = EXCLUDE

    @classmethod
    def _check_and_format_detail(cls, details: List[Dict]) -> List[Dict]:
        detail_map = {i["lang"]: i for i in details}
        required_languages = [Language.EN_US, Language.ZH_HANS_CN]
        for lang in required_languages:
            detail = detail_map.get(lang)
            if not detail or not detail["title"] or not detail["url"]:
                raise InvalidArgument(message="语言{}必填".format(lang))

        default_detail: Dict = detail_map[Language.EN_US]
        support_languages = set(AmbassadorGuideTranslation.AVAILABLE_LANGUAGES)
        param_languages = set()
        not_exist_languages = []
        for detail in details:
            if detail["lang"] not in support_languages:
                not_exist_languages.append(detail["lang"])
            param_languages.add(detail["lang"])
        if not_exist_languages:
            raise InvalidArgument(message="{}不在提供的语言列表中".format(not_exist_languages))

        for detail in details:
            # 补全
            if not detail["title"]:
                detail["title"] = default_detail["title"]
            if not detail["url"]:
                detail["url"] = default_detail["url"]

        if missing_languages := support_languages - param_languages:
            for missing_lang in missing_languages:
                # 补全缺失的语言
                missing_detail = default_detail.fromkeys(["title", "url"])
                missing_detail["lang"] = missing_lang
                details.append(missing_detail)

        return details

    @classmethod
    @ns.use_kwargs(
        dict(
            translations=wa_fields.Nested(DetailTransSchema, many=True, required=True),
            remark=wa_fields.String(required=False, missing=""),
        )
    )
    def post(cls, **kwargs):
        """ 用户-CoinEx大使-大使攻略-添加攻略 """
        detail_trans = cls._check_and_format_detail(kwargs["translations"])
        cur_max_rank_cursor = AmbassadorGuide.query.order_by(AmbassadorGuide.rank.desc()).first()
        if cur_max_rank_cursor:
            next_rank = cur_max_rank_cursor.rank + 1
        else:
            next_rank = 1
        detail_trans_dict = {v["lang"]: v for v in detail_trans}
        cn_detail = detail_trans_dict[Language.ZH_HANS_CN]
        new_guide = AmbassadorGuide(
            title=cn_detail["title"],
            rank=next_rank,
            remark=kwargs.get("remark", ""),
        )
        db.session_add_and_commit(new_guide)

        trans_objs = [
            AmbassadorGuideTranslation(
                guide_id=new_guide.id,
                lang=lang.name,
                title=detail["title"],
                url=detail["url"],
            )
            for lang, detail in detail_trans_dict.items()
        ]
        db.session.add_all(trans_objs)
        db.session.commit()

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.AmbassadorGuide,
            detail=kwargs,
        )
        return new_guide.to_dict()

    @classmethod
    @ns.use_kwargs(
        dict(
            id=wa_fields.Integer(required=True),
            translations=wa_fields.Nested(DetailTransSchema, many=True, required=True),
            remark=wa_fields.String(required=False, missing=""),
            status=EnumField(AmbassadorGuide.Status, required=True),
        )
    )
    def put(cls, **kwargs):
        """ 用户-CoinEx大使-大使攻略-编辑攻略 """
        guide = AmbassadorGuide.query.filter(AmbassadorGuide.id == kwargs["id"]).first()
        if not guide:
            raise InvalidArgument(message="攻略不存在")
        old_data = guide.to_dict(enum_to_name=True)

        detail_trans = cls._check_and_format_detail(kwargs["translations"])
        detail_trans_dict = {v["lang"]: v for v in detail_trans}

        guide.title = detail_trans_dict[Language.ZH_HANS_CN]["title"]
        guide.remark = kwargs.get("remark", "")
        guide.status = kwargs["status"]

        trans = AmbassadorGuideTranslation.query.filter(
            AmbassadorGuideTranslation.guide_id == guide.id,
        ).all()
        db_trans_dict = {v.lang: v for v in trans}
        # update
        for lang, detail in detail_trans_dict.items():
            lang = lang.name
            if lang in db_trans_dict:
                old_tran = db_trans_dict[lang].to_dict(enum_to_name=True)
                db_trans_dict[lang].title = detail["title"]
                db_trans_dict[lang].url = detail["url"]

                AdminOperationLog.new_edit(
                    user_id=g.user.id,
                    ns_obj=OPNamespaceObjectUser.AmbassadorGuide,
                    old_data=old_tran,
                    new_data=db_trans_dict[lang].to_dict(enum_to_name=True),
                    special_data=dict(guide_id=guide.id, lang=lang),
                )
        # insert
        trans_objs = [
            AmbassadorGuideTranslation(
                guide_id=guide.id,
                lang=lang.name,
                title=detail["title"],
                url=detail["url"],
            )
            for lang, detail in detail_trans_dict.items()
            if lang.name not in db_trans_dict
        ]
        db.session.add_all(trans_objs)
        db.session.commit()

        for tran in trans_objs:
            AdminOperationLog.new_add(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectUser.AmbassadorGuide,
                detail=tran.to_dict(enum_to_name=True),
            )

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.AmbassadorGuide,
            old_data=old_data,
            new_data=guide.to_dict(enum_to_name=True),
        )

    @classmethod
    @ns.use_kwargs(
        dict(
            id=wa_fields.Integer(required=True),
            change=EnumField(["UP", "DOWN"], required=True),
        )
    )
    def patch(cls, **kwargs):
        """ 用户-CoinEx大使-大使攻略-修改排序 """
        guide = AmbassadorGuide.query.filter(AmbassadorGuide.id == kwargs["id"]).first()
        if not guide:
            raise InvalidArgument(message="攻略不存在")
        old_data = guide.to_dict(enum_to_name=True)

        param_change = kwargs["change"]
        old_rank = guide.rank
        # rank越大排序越前
        if param_change == "DOWN":
            new_rank = guide.rank - 1
            # rank最小为1, new_rank=0时已经是第一了不用处理
            if new_rank > 0:
                change_obj = AmbassadorGuide.query.filter(AmbassadorGuide.rank == new_rank).first()
                guide.rank = new_rank
                change_obj.rank = old_rank
                db.session.commit()
        elif param_change == "UP":
            new_rank = guide.rank + 1
            last_id = AmbassadorGuide.query.order_by(AmbassadorGuide.rank.desc()).first().id
            if last_id != guide.id:
                change_obj = AmbassadorGuide.query.filter(AmbassadorGuide.rank == new_rank).first()
                guide.rank = new_rank
                change_obj.rank = old_rank
                db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.AmbassadorGuide,
            old_data=old_data,
            new_data=guide.to_dict(enum_to_name=True),
        )

    @classmethod
    @ns.use_kwargs(
        dict(
            id=wa_fields.Integer(required=True),
        )
    )
    def delete(cls, **kwargs):
        """ 用户-CoinEx大使-大使攻略-删除攻略 """
        guide = AmbassadorGuide.query.get(kwargs["id"])
        guide.status = AmbassadorGuide.Status.DELETED
        db.session.commit()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.AmbassadorGuide,
            detail=guide.to_dict(enum_to_name=True),
        )


@ns.route("/potential")
@respond_with_code
class PotentialAmbassadorResource(Resource):

    export_headers = (
        {"field": "user_id", Language.ZH_HANS_CN: "用户ID"},
        {"field": "email", Language.ZH_HANS_CN: "邮箱"},
        {"field": "created_at", Language.ZH_HANS_CN: "加入线索库时间"},
        {"field": "location", Language.ZH_HANS_CN: "国家"},
        {"field": "area", Language.ZH_HANS_CN: "地区"},
        {"field": "source", Language.ZH_HANS_CN: "线索类型"},
        {"field": "status", Language.ZH_HANS_CN: "转化状态"},
        {"field": "ambassador_status", Language.ZH_HANS_CN: "大使状态"}
    )

    @classmethod
    @ns.use_kwargs(dict(
        user_id=wa_fields.Integer(missing=0),
        location=wa_fields.String(missing=""),
        area=EnumField(AreaInfo, missing=""),
        source=EnumField(PotentialAmbassador.Source, missing=""),
        status=EnumField(PotentialAmbassador.Status, missing=""),
        ambassador_status=EnumField(Ambassador.Status, missing=""),
        page=PageField,
        limit=LimitField,
        export=wa_fields.Boolean(missing=False)
    ))
    def get(cls, **kwargs):
        """大使-高潜线索库"""
        page, limit = kwargs["page"], kwargs['limit']
        query = PotentialAmbassador.query.select_from(PotentialAmbassador).join(
            User, PotentialAmbassador.user_id == User.id, isouter=True
        ).join(
            Ambassador, PotentialAmbassador.user_id == Ambassador.user_id, isouter=True
        )
        if user_id := kwargs["user_id"]:
            query = query.filter(
                PotentialAmbassador.user_id == user_id
            )
        if location := kwargs["location"]:
            query = query.filter(
                User.location_code == location
            )
        if area := kwargs["area"]:
            area_location_codes = [i.info.iso_3 for i in AREAS_MAPPING[area]]
            query = query.filter(
                User.location_code.in_(area_location_codes)
            )
        if source := kwargs['source']:
            query = query.filter(
                PotentialAmbassador.source == source
            )
        if status := kwargs['status']:
            query = query.filter(
                PotentialAmbassador.status == status
            )
        if ambassador_status := kwargs['ambassador_status']:
            query = query.filter(
                Ambassador.status == ambassador_status
            )
        query = query.with_entities(
            PotentialAmbassador.user_id,
            PotentialAmbassador.created_at,
            User.email,
            User.location_code,
            PotentialAmbassador.status,
            PotentialAmbassador.source,
            Ambassador.status.label("ambassador_status")
        )
        countries = {code: get_country(code).cn_name for code in list_country_codes_3_admin()}
        areas_mapping = {code: area.value for code, area in list_country_codes_3_to_area().items()}
        if kwargs['export']:
            items = query.limit(ADMIN_EXPORT_LIMIT).all()
            user_ids = [i.user_id for i in items]
            # 查询商务大使的状态
            bussiness_ambassador_status_mapper = {
                i.user_id: i.status for i in BusinessAmbassador.query.filter(
                BusinessAmbassador.user_id.in_(user_ids)
            ).with_entities(
                BusinessAmbassador.user_id,
                BusinessAmbassador.status,
            ).all()}
            records = cls.get_export_data(items, countries, areas_mapping, bussiness_ambassador_status_mapper)
            return export_xlsx(
                filename='potential-ambassador',
                data_list=records,
                export_headers=cls.export_headers
            )
        paginate = query.paginate(page, limit)
        user_ids = [i.user_id for i in paginate.items]
        # 查询商务大使的状态
        bussiness_ambassador_status_mapper = {
            i.user_id: i.status for i in BusinessAmbassador.query.filter(
            BusinessAmbassador.user_id.in_(user_ids)
        ).with_entities(
            BusinessAmbassador.user_id,
            BusinessAmbassador.status,
        ).all()}
        items = []
        for i in paginate.items:
            ambassador_status = i.ambassador_status.name if i.ambassador_status else None
            if not ambassador_status:
                ambassador_status_mapper = {
                    BusinessAmbassador.Status.VALID: "VALID",
                    BusinessAmbassador.Status.DELETED: "DELETED"
                }
                status = bussiness_ambassador_status_mapper.get(i.user_id)
                ambassador_status = ambassador_status_mapper.get(status) if status else "/"
            items.append({
                "user_id": i.user_id,
                "created_at": i.created_at,
                "email": i.email,
                "location": i.location_code,
                "area": areas_mapping.get(i.location_code),
                "source": i.source,
                "status": i.status,
                "ambassador_status": ambassador_status
            })
        return dict(
            items=items,
            total=paginate.total,
            status={i.name: i.value for i in PotentialAmbassador.Status},
            ambassador_status={i.name: i.value for i in Ambassador.Status},
            sources={i.name: i.value for i in PotentialAmbassador.Source},
            countries=countries,
            areas={i.name: i.value for i in AreaInfo},
        )

    @classmethod
    def get_export_data(cls, items, countries, areas_mapping, bussiness_ambassador_status_mapper):
        ambassador_status_mapper = {
            Ambassador.Status.VALID: "生效中",
            Ambassador.Status.DELETED: "已失效",
            BusinessAmbassador.Status.VALID: "生效中",
            BusinessAmbassador.Status.DELETED: "已失效"
        }
        records = []
        for item in items:
            if item.ambassador_status:
                ambassador_status = ambassador_status_mapper[item.ambassador_status]
            else:
                status = bussiness_ambassador_status_mapper.get(item.user_id)
                ambassador_status = ambassador_status_mapper.get(status) if status else "/"
            records.append({
                "user_id": item.user_id,
                "created_at": item.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                "email": item.email,
                "location": countries.get(item.location_code, "/"),
                "area": areas_mapping.get(item.location_code, "/"),
                "source": item.source.value,
                "status": item.status.value,
                "ambassador_status": ambassador_status
            })
        return records


@ns.route("/business/trace")
@respond_with_code
class BusinessAmbassadorTraceResource(Resource):

    export_headers = (
        {"field": "user_id", Language.ZH_HANS_CN: "用户ID"},
        {"field": "email", Language.ZH_HANS_CN: "邮箱"},
        {"field": "created_at", Language.ZH_HANS_CN: "加入线索库时间"},
        {"field": "location", Language.ZH_HANS_CN: "国家"},
        {"field": "area", Language.ZH_HANS_CN: "地区"},
        {"field": "lang", Language.ZH_HANS_CN: "语言"},
        {"field": "mobile", Language.ZH_HANS_CN: "手机号"},
        {"field": "telegram", Language.ZH_HANS_CN: "电报号"},
        {"field": "refer_count", Language.ZH_HANS_CN: "refer人数"},
        {"field": "refer_trade_count", Language.ZH_HANS_CN: "refer交易人数"},
        {"field": "refer_amount_usd", Language.ZH_HANS_CN: "累计返佣市值"},
        {"field": "last_month_refer_count", Language.ZH_HANS_CN: "上月refer人数"},
        {"field": "last_month_refer_trade_count", Language.ZH_HANS_CN: "上月refer交易人数"},
        {"field": "last_month_refer_amount_usd", Language.ZH_HANS_CN: "上月返佣市值"},
        {"field": "source", Language.ZH_HANS_CN: "线索类型"},
        {"field": "status", Language.ZH_HANS_CN: "转化状态"},
        {"field": "ambassador_status", Language.ZH_HANS_CN: "大使状态"},
        {"field": "trace_user_email", Language.ZH_HANS_CN: "跟进人"},
        {"field": "remark", Language.ZH_HANS_CN: "备注"},
    )

    ORDER_BYS = {
        "created_at": "加入线索库时间",
        "refer_count": "refer人数",
        "refer_trade_count": "refer交易人数",
        "refer_amount_usd": "累计返佣市值",
        "last_month_refer_count": "上月refer人数",
        "last_month_refer_trade_count": "上月refer交易人数",
        "last_month_refer_amount_usd": "上月返佣市值",
    }

    @classmethod
    @ns.use_kwargs(dict(
        user_id=wa_fields.Integer,
        location=wa_fields.String,
        area=EnumField(AreaInfo, missing=""),
        language=EnumField(Language, missing=""),
        source=EnumField(PotentialAmbassador.Source, missing=""),
        status=EnumField(AmbassadorBusinessTrace.Status, missing=""),
        ambassador_status=EnumField(Ambassador.Status, missing=""),
        trace_user_id=wa_fields.Integer,
        order_by=EnumField(list(ORDER_BYS), missing="created_at"),
        page=PageField,
        limit=LimitField,
        export=wa_fields.Boolean(missing=False)
    ))
    def get(cls, **kwargs):
        """大使-商务线索库"""
        page, limit = kwargs['page'], kwargs['limit']
        order_by = kwargs['order_by'] or "created_at"
        export = kwargs['export']
        query = AmbassadorBusinessTrace.query.select_from(AmbassadorBusinessTrace).join(
            PotentialAmbassador, AmbassadorBusinessTrace.potential_id == PotentialAmbassador.id,
        ).join(
            User, AmbassadorBusinessTrace.user_id == User.id
        ).join(
            Ambassador, AmbassadorBusinessTrace.user_id == Ambassador.user_id, isouter=True
        )
        if user_id := kwargs.get("user_id"):
            query = query.filter(
                AmbassadorBusinessTrace.user_id == user_id
            )
        if location := kwargs.get("location"):
            query = query.filter(
                User.location_code == location
            )
        if area := kwargs.get("area"):
            area_location_codes = [i.info.iso_3 for i in AREAS_MAPPING[area]]
            query = query.filter(
                User.location_code.in_(area_location_codes)
            )
        if source := kwargs.get("source"):
            query = query.filter(
                PotentialAmbassador.source == source
            )
        if status := kwargs.get("status"):
            query = query.filter(
                AmbassadorBusinessTrace.status == status
            )
        if ambassador_status := kwargs.get("ambassador_status"):
            query = query.filter(
                Ambassador.status == ambassador_status
            )
        if language := kwargs.get("language"):
            query = query.join(
                UserPreference,
                UserPreference.user_id == AmbassadorBusinessTrace.user_id,
                isouter=True
            ).filter(
                UserPreference.key == UserPreferences.language.name,
                UserPreference.value == language.value
            )
        if trace_user_id := kwargs.get("trace_user_id"):
            query = query.filter(
                AmbassadorBusinessTrace.trace_user_id == trace_user_id
            )

        query = query.with_entities(
            AmbassadorBusinessTrace.id,
            AmbassadorBusinessTrace.user_id,
            User.email,
            AmbassadorBusinessTrace.created_at,
            User.location_code,
            AmbassadorBusinessTrace.mobile,
            AmbassadorBusinessTrace.telegram,
            PotentialAmbassador.source,
            AmbassadorBusinessTrace.status,
            AmbassadorBusinessTrace.trace_user_id,
            AmbassadorBusinessTrace.remark,
            Ambassador.status.label("ambassador_status")
        )
        if order_by == "created_at" and (not export):
            query = query.order_by(AmbassadorBusinessTrace.created_at.desc())
            paginate = query.paginate(page, limit)
            query_items = paginate.items
            total = paginate.total
            memory_paginate = False
        else:
            query_items = query.all()
            total = len(query_items)
            memory_paginate = True

        user_ids = [i.user_id for i in query_items]
        trace_ids = [i.id for i in query_items]

        histories = BusinessTraceChangeHistory.query.filter(
            BusinessTraceChangeHistory.business_trade_id.in_(trace_ids)
        ).order_by(
            BusinessTraceChangeHistory.id
        ).all()
        change_type_mapper = defaultdict(lambda: defaultdict(list))
        trace_user_set = set()
        for history in histories:
            change_type_mapper[history.business_trade_id][history.type].append(history.value)
            if history.type == BusinessTraceChangeHistory.Type.USER:
                trace_user_set.add(int(history.value))
        trace_user_name_mapper = {
            i.user_id: i.name for i in AdminUser.query.filter(AdminUser.user_id.in_(trace_user_set)).all()
        }
        lang_name_mapper = language_cn_names()
        countries = {code: get_country(code).cn_name for code in list_country_codes_3_admin()}
        areas_mapping = {code: area.value for code, area in list_country_codes_3_to_area().items()}
        refer_data = cls._refer_info_data(user_ids)
        # 查询商务大使的状态
        bussiness_ambassador_status_mapper = {
            i.user_id: i.status for i in BusinessAmbassador.query.filter(
                BusinessAmbassador.user_id.in_(user_ids)
            ).with_entities(
                BusinessAmbassador.user_id,
                BusinessAmbassador.status,
            ).all()
        }
        if kwargs['export']:
            records = cls.get_export_data(
                query_items,
                countries,
                areas_mapping,
                refer_data,
                trace_user_name_mapper,
                lang_name_mapper,
                bussiness_ambassador_status_mapper
            )
            return export_xlsx(
                filename='business-trace',
                data_list=records,
                export_headers=cls.export_headers
            )
        items = []
        for item in query_items:
            user_id = item.user_id
            if item.ambassador_status:
                ambassador_status = item.ambassador_status
            else:
                ambassador_status = bussiness_ambassador_status_mapper.get(user_id, None)
            items.append({
                "id": item.id,
                "email": item.email,
                "user_id": user_id,
                "created_at": item.created_at,
                "location": item.location_code,
                "area": areas_mapping.get(item.location_code),
                "lang": lang_name_mapper.get(UserPreferences(user_id).language),
                "mobile": item.mobile,
                "telegram": item.telegram,
                "status": item.status,
                "source": item.source,
                "ambassador_status": ambassador_status.name if ambassador_status else "/",
                "trace_user_id": item.trace_user_id,
                "trace_user_email": trace_user_name_mapper.get(item.trace_user_id),
                "remark": item.remark,
                "trace_user_history": [
                    trace_user_name_mapper.get(int(id_)) for id_ in change_type_mapper[item.id][BusinessTraceChangeHistory.Type.USER]
                ],
                "status_history": change_type_mapper[item.id][BusinessTraceChangeHistory.Type.STATUS],
                "remark_history": change_type_mapper[item.id][BusinessTraceChangeHistory.Type.REMARK],
                **refer_data[user_id]
            })
        if memory_paginate:
            items.sort(key=lambda x: x[order_by], reverse=True)
            items = items[(page - 1) * limit: page * limit]
        return dict(
            items=items,
            total=total,
            status={i.name: i.value for i in AmbassadorBusinessTrace.Status},
            ambassador_status={i.name: i.value for i in Ambassador.Status},
            sources={i.name: i.value for i in PotentialAmbassador.Source},
            languages=language_name_cn_names(),
            countries=countries,
            areas={i.name: i.value for i in AreaInfo},
            order_bys=cls.ORDER_BYS,
        )

    @classmethod
    def get_export_data(cls, items, countries, areas_mapping, refer_data,
                         trace_user_name_mapper, lang_name_mapper, bussiness_ambassador_status_mapper):
        ambassador_status_mapper = {
            Ambassador.Status.VALID: "生效中",
            Ambassador.Status.DELETED: "已失效",
            BusinessAmbassador.Status.VALID: "生效中",
            BusinessAmbassador.Status.DELETED: "已失效",
        }
        records = []
        for item in items:
            user_id = item.user_id
            refer_dict = refer_data[user_id]
            if item.ambassador_status:
                ambassador_status = ambassador_status_mapper[item.ambassador_status]
            else:
                ambassador_status = ambassador_status_mapper.get(bussiness_ambassador_status_mapper.get(user_id, None))
            records.append({
                "email": item.email,
                "user_id": user_id,
                "created_at": item.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                "location": countries.get(item.location_code, "/"),
                "area": areas_mapping.get(item.location_code, "/"),
                "lang": lang_name_mapper.get(UserPreferences(user_id).language, "/"),
                "mobile": item.mobile if item.mobile else "/",
                "telegram": item.telegram if item.telegram else "/",
                "status": item.status.value,
                "source": item.source.value,
                "ambassador_status": ambassador_status if ambassador_status else "/",
                "trace_user_email": trace_user_name_mapper.get(item.trace_user_id),
                "remark": item.remark,
                "refer_trade_count": amount_to_str(refer_dict.get("refer_trade_count", 0)),
                "refer_count": amount_to_str(refer_dict.get("refer_count", 0)),
                "refer_amount_usd": amount_to_str(refer_dict.get("refer_amount_usd", Decimal())),
                "last_month_refer_trade_count": amount_to_str(refer_dict.get("last_month_refer_trade_count", 0)),
                "last_month_refer_count": amount_to_str(refer_dict.get("last_month_refer_count", 0)),
                "last_month_refer_amount_usd": amount_to_str(refer_dict.get("last_month_refer_amount_usd", Decimal()))
            })
        return records

    @classmethod
    def _refer_info_data(cls, user_ids: List[int]):
        # 需要查询历史所有的邀请关系
        refer_query = ReferralHistory.query.filter(
            ReferralHistory.referrer_id.in_(user_ids),
        ).with_entities(
            ReferralHistory.referrer_id,
            ReferralHistory.referree_id,
            ReferralHistory.created_at,
        ).all()
        referree_mapper = {ee: er for er, ee, _ in refer_query}

        last_month_start_dt, last_month_end_dt = last_month_range(today())
        last_month_referrees = {i[1]for i in refer_query if last_month_start_dt <= i[2].date() <= last_month_end_dt}
        refer_is_trade_mapper = cls._get_user_is_trade_mapper(list(referree_mapper.keys()))
        refer_trade_count_mapper = defaultdict(int)
        last_month_refer_trade_count_mapper = defaultdict(int)
        last_month_refee_map = defaultdict(set)
        for ee, er in referree_mapper.items():
            if not refer_is_trade_mapper[ee]:
                continue
            refer_trade_count_mapper[er] += 1
            if ee in last_month_referrees:
                last_month_refer_trade_count_mapper[er] += 1
                last_month_refee_map[er].add(ee)

        summary_query = DailyUserReferralSlice.query.filter(
            DailyUserReferralSlice.user_id.in_(user_ids),
            DailyUserReferralSlice.referral_id.is_(None),
        ).group_by(
            DailyUserReferralSlice.user_id
        ).with_entities(
            DailyUserReferralSlice.user_id,
            func.sum(DailyUserReferralSlice.amount_usd).label("amount_usd"),
            func.sum(DailyUserReferralSlice.referral_count).label("referral_count")
        ).all()
        summary_mapper = {i.user_id: {
            "refer_count": i.referral_count,
            "amount_usd": i.amount_usd
        } for i in summary_query}

        last_month_summary_query = DailyUserReferralSlice.query.filter(
            DailyUserReferralSlice.user_id.in_(user_ids),
            DailyUserReferralSlice.date >= last_month_start_dt,
            DailyUserReferralSlice.date <= last_month_end_dt,
            DailyUserReferralSlice.referral_id.is_(None),
        ).group_by(
            DailyUserReferralSlice.user_id
        ).with_entities(
            DailyUserReferralSlice.user_id,
            func.sum(DailyUserReferralSlice.amount_usd).label("amount_usd"),
            func.sum(DailyUserReferralSlice.referral_count).label("referral_count")
        ).all()
        last_month_summary_mapper = {i.user_id: {
            "refer_count": i.referral_count,
            "amount_usd": i.amount_usd
        } for i in last_month_summary_query}

        result = {}
        for user_id in user_ids:
            summary = summary_mapper.get(user_id)
            last_month_summary = last_month_summary_mapper.get(user_id)
            result[user_id] = {
                "refer_trade_count": refer_trade_count_mapper.get(user_id, 0),
                "refer_count": summary["refer_count"] if summary else 0,
                "refer_amount_usd": summary["amount_usd"] if summary else 0,
                "last_month_refer_trade_count": last_month_refer_trade_count_mapper[user_id],
                "last_month_refer_count": len(last_month_refee_map[user_id]),
                "last_month_refer_amount_usd": last_month_summary["amount_usd"] if last_month_summary else 0,
            }
        return result

    @classmethod
    def _get_user_is_trade_mapper(cls, user_ids: List[int]):
        max_date = UserBusinessRecord.query.with_entities(
            func.max(UserBusinessRecord.report_at)
        ).scalar() or now().date()
        query = UserBusinessRecord.query.filter(
            UserBusinessRecord.report_at == max_date,
            UserBusinessRecord.business != UserBusinessRecord.Business.DEPOSIT
        ).with_entities(
            UserBusinessRecord.history_user_bit_map
        ).all()
        trade_user_set = set()
        for bitmap, in query:
            trade_user_set |= set(BitMap.deserialize(bitmap))
        return {
            user_id: user_id in trade_user_set for user_id in user_ids
        }


@ns.route("/business/batch/trace")
@respond_with_code
class BusinessTraceDetailResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        user_ids=wa_fields.List(wa_fields.Integer, required=True),
        trace_user_id=wa_fields.Integer(required=True)
    ))
    def post(cls, **kwargs):
        """商务线索库-批量分配跟进人"""
        user_ids, trace_user_id = kwargs["user_ids"], kwargs["trace_user_id"]
        traces = AmbassadorBusinessTrace.query.filter(
            AmbassadorBusinessTrace.user_id.in_(user_ids)
        ).all()
        trace_history = []
        for trace in traces:  # type: AmbassadorBusinessTrace
            if trace.status not in (AmbassadorBusinessTrace.Status.CREATED, AmbassadorBusinessTrace.Status.PROGRESS):
                continue
            if trace.status == AmbassadorBusinessTrace.Status.CREATED:
                trace.status = AmbassadorBusinessTrace.Status.PROGRESS
                trace_history.append(BusinessTraceChangeHistory(
                    business_trade_id=trace.id,
                    type=BusinessTraceChangeHistory.Type.STATUS,
                    value=trace.status.value
                ))
            if trace.trace_user_id != trace_user_id:
                trace.trace_user_id = trace_user_id
                trace_history.append(BusinessTraceChangeHistory(
                    business_trade_id=trace.id,
                    type=BusinessTraceChangeHistory.Type.USER,
                    value=trace.trace_user_id
                ))
        db.session.add_all(trace_history)
        db.session.commit()

        for row in trace_history:
            AdminOperationLog.new_add(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectUser.UserAmbassadorTrace,
                detail=row.to_dict(enum_to_name=True),
            )

    @classmethod
    @ns.use_kwargs(dict(
        trace_id=wa_fields.Integer(required=True),
        remark=wa_fields.String(required=True)
    ))
    def put(cls, **kwargs):
        """商务线索库-修改备注"""
        trace_id, remark = kwargs["trace_id"], kwargs["remark"]
        trace = AmbassadorBusinessTrace.query.get(trace_id)
        if not trace:
            raise InvalidArgument
        old_data = trace.to_dict(enum_to_name=True)
        if trace.remark == remark:
            return
        trace.remark = remark
        db.session.add(BusinessTraceChangeHistory(
            business_trade_id=trace.id,
            type=BusinessTraceChangeHistory.Type.REMARK,
            value=trace.remark
        ))
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.UserAmbassadorTrace,
            old_data=old_data,
            new_data=trace.to_dict(enum_to_name=True),
            target_user_id=trace.user_id,
        )

    @classmethod
    @ns.use_kwargs(dict(
        trace_id=wa_fields.Integer(required=True)
    ))
    def delete(cls, **kwargs):
        """商务线索库-终止跟进"""
        trace = AmbassadorBusinessTrace.query.get(kwargs["trace_id"])
        if not trace:
            raise InvalidArgument
        if trace.status not in (AmbassadorBusinessTrace.Status.CREATED, AmbassadorBusinessTrace.Status.PROGRESS):
            raise InvalidArgument
        trace.status = AmbassadorBusinessTrace.Status.FAILED
        db.session.add(BusinessTraceChangeHistory(
            business_trade_id=trace.id,
            type=BusinessTraceChangeHistory.Type.STATUS,
            value=trace.status.value
        ))
        db.session.commit()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.UserAmbassadorTrace,
            detail=trace.to_dict(enum_to_name=True),
        )


@ns.route('/activities')
@respond_with_code
class AmbassadorActivitiesResource(Resource):
    model = AmbassadorActivity

    @classmethod
    @ns.use_kwargs(dict(
        name=wa_fields.String,
        status=EnumField(model.Status),
        page=PageField,
        limit=LimitField
    ))
    def get(cls, **kwargs):
        """用户-大使体系-大使活动列表"""
        page = kwargs['page']
        limit = kwargs['limit']

        _now = now()
        model = cls.model
        query = model.query
        if status := kwargs.get('status'):
            query = query.filter(model.status == status)
        if name := kwargs.get('name'):
            query = query.filter(model.name.contains(name))

        records = query.order_by(model.id.desc()).paginate(page, limit)
        items = []
        for item in records.items:
            i_dict = item.to_dict(enum_to_name=True)
            items.append(i_dict)

        return dict(
            total=records.total,
            items=items,
            extra=dict(
                statuses=model.Status,
            )
        )

    @classmethod
    @ns.use_kwargs(dict(
        name=wa_fields.String(required=True),
        url=wa_fields.String(required=True),
    ))
    def post(cls, **kwargs):
        """用户-大使体系-大使活动创建"""
        model = cls.model
        row = db.session_add_and_commit(
            model(
                name=kwargs['name'],
                url=kwargs['url'],
                status=model.Status.CLOSE,
            )
        )

        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.UserAmbassadorActivity,
            detail=kwargs,
        )
        return row


@ns.route('/activity/<int:id_>')
@respond_with_code
class AmbassadorActivityResource(Resource):
    model = AmbassadorActivity

    @classmethod
    def get(cls, id_):
        """用户-大使体系-大使活动详情"""
        lang_names = language_cn_names()
        extra = dict(
            languages={e.name: lang_names[e] for e in Language},
            statuses=cls.model.Status
        )
        if not id_:
            return dict(
                extra=extra
            )

        row = cls.model.query.get(id_)
        if row is None:
            raise RecordNotFound
        result = row.to_dict(enum_to_name=True)
        result.update(
            extra=extra
        )
        return result

    @classmethod
    @ns.use_kwargs(dict(
        name=wa_fields.String(required=True),
        url=wa_fields.String(required=True),
    ))
    def put(cls, id_, **kwargs):
        """用户-大使体系-大使活动编辑"""
        row = cls.model.query.get(id_)
        if row is None:
            raise RecordNotFound
        old_data = row.to_dict(enum_to_name=True)

        row.name = kwargs['name']
        row.url = kwargs['url']
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.UserAmbassadorActivity,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
        )
        return row

    @classmethod
    def patch(cls, id_):
        """用户-大使体系-大使活动状态编辑"""
        row = cls.model.query.get(id_)
        if row is None:
            raise RecordNotFound
        old_data = row.to_dict(enum_to_name=True)

        if row.status is cls.model.Status.CLOSE:
            row.status = cls.model.Status.OPEN
        else:
            row.status = cls.model.Status.CLOSE
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectUser.UserAmbassadorActivity,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
        )
        return row


@ns.route('/activity/<int:id_>/langs/<lang>')
@respond_with_code
class AmbassadorActivityContentResource(Resource):
    model = AmbassadorActivityContent

    @classmethod
    def get(cls, id_, lang):
        """用户-大使体系-获取单条内容"""
        row = cls.get_row(id_, lang)
        if row is None:
            raise RecordNotFound
        return dict(content=row.content)

    @classmethod
    @ns.use_kwargs(dict(
        content=wa_fields.String(required=True)
    ))
    def put(cls, id_, lang, **kwargs):
        """用户-大使体系-大使活动内容编辑"""
        row = cls.get_row(id_, lang)
        model = cls.model
        if row is None:
            row = db.session_add_and_commit(model(
                activity_id=id_,
                lang=getattr(Language, lang),
                content=kwargs['content']
            ))

            AdminOperationLog.new_add(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectUser.UserAmbassadorActivity,
                detail=row.to_dict(enum_to_name=True),
            )
        else:
            old_data = row_to_dict(row, enum_to_name=True)
            row.content = kwargs['content']
            db.session.commit()

            AdminOperationLog.new_edit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectUser.UserAmbassadorActivity,
                old_data=old_data,
                new_data=row_to_dict(row, enum_to_name=True),
                special_data=dict(lang=lang),
            )

        return dict(content=row.content)

    @classmethod
    def get_row(cls, id_, lang):
        if not isinstance((lang := getattr(Language, lang, '')), Language):
            raise InvalidArgument

        model = cls.model
        return model.query.filter(
            model.activity_id == id_,
            model.lang == lang
        ).first()
