import copy

from flask import g
from sqlalchemy import func, or_
from webargs import fields

from app import Language
from app.api.common import (Namespace, Resource, respond_with_code)
from app.api.common.fields import EnumField, TimestampField
from app.business.p2p.utils import P2pUtils
from app.business.auth import get_admin_user_name_map
from app.caches.operation import P2pActivityBannerCache
from app.common import language_cn_names
from app.exceptions import InvalidArgument, RecordNotFound
from app.models import db, P2pActivity, P2pActivityBanner
from app.models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog, OPNamespaceObjectOperation
from app.utils import now, AWSBucketPublic
from app.utils.helper import Struct

ns = Namespace('Operation p2p')


class P2pActivityMixin:
    model = P2pActivity
    content_model = P2pActivityBanner

    @classmethod
    def fmt_platform(cls, platform: P2pActivity.Platform):
        if platform == P2pActivity.Platform.ALL:
            return [p.name for p in P2pActivity.Platform if p != P2pActivity.Platform.ALL]
        return [platform.name]

    @classmethod
    def validate(cls, params, id_):
        new_params = copy.deepcopy(params)
        platform_list = params['platform']
        if len(platform_list) > 1:
            new_params['platform'] = P2pActivity.Platform.ALL
        else:
            new_params['platform'] = platform_list[0]
        # 修改不检查时间
        if id_:
            return new_params
        if params['start_at'] > params['end_at']:
            raise InvalidArgument(message='开始时间不能大于结束时间')

        if params['start_at'] < now():
            raise InvalidArgument(message='开始时间不能小于当前时间')
        return new_params

    @classmethod
    def _get_row(cls, id_):
        row = cls.model.query.get(id_)
        if row is None:
            raise RecordNotFound
        return row

    @classmethod
    def _get_rows(cls, id_: int):
        model = cls.content_model
        return model.query.filter(
            model.owner_id == id_,
        ).all()


@ns.route("")
@respond_with_code
class P2pActivityListResource(Resource, P2pActivityMixin):
    @classmethod
    @ns.use_kwargs(dict(
        name=fields.String,
        platform=EnumField(P2pActivity.Platform),
        status=EnumField(P2pActivity.Status),
        display_to_merchant=fields.Boolean(missing=None),
        content_type=EnumField(P2pActivity.ContentType),
        page=fields.Integer(missing=1),
        limit=fields.Integer(missing=50)
    ))
    def get(cls, **kwargs):
        """运营-WEB-p2p主页运营位"""
        _now = now()
        params = Struct(**kwargs)
        query = cls.model.query.filter(
            cls.model.status != P2pActivity.Status.DELETED
        )
        if name := params.name:
            query = query.filter(cls.model.name.contains(name))
        if platform := params.platform:
            query = query.filter(or_(cls.model.platform == platform, cls.model.platform == P2pActivity.Platform.ALL))
        if status := params.status:
            query = query.filter(cls.model.status == status)
        if content_type := params.content_type:
            query = query.filter(cls.model.content_type == content_type)
        if (display_to_merchant := params.display_to_merchant) is not None:
            query = query.filter(cls.model.display_to_merchant == display_to_merchant)
        records = query.order_by(cls.model.sort_id.desc(), cls.model.id.desc()).paginate(params.page, params.limit)
        rows = records.items
        updated_user_ids = {item.updated_by for item in rows}
        name_map = get_admin_user_name_map(updated_user_ids)
        items = []
        for row in rows:
            row_dict = row.to_dict(enum_to_name=True)
            row_dict['platform'] = cls.fmt_platform(row.platform)
            row_dict["updated_user_email"] = name_map.get(row.updated_by)
            fiats = row.fiats
            row_dict['fiats_str'] = "全部法币" if not fiats else fiats.replace(",", "，")
            items.append(row_dict)
        return dict(
            total=records.total,
            items=items,
            extra=dict(
                statues={k.name: k.value for k in cls.model.Status if k != cls.model.Status.DELETED},
                platforms={i.name: i.value for i in cls.model.Platform if i != cls.model.Platform.ALL},
                content_types=cls.model.ContentType
            )
        )


@ns.route('/<int:id_>')
@respond_with_code
class P2pActivityDetailResource(Resource, P2pActivityMixin):

    @classmethod
    def get(cls, id_):
        """运营-WEB-p2p主页运营位详情"""
        lang_names = language_cn_names()
        p2p_fiats = P2pUtils.get_all_p2p_fiats()
        extra = dict(
            show_types=cls.model.ShowType,
            platforms={i.name: i.value for i in cls.model.Platform if i != cls.model.Platform.ALL},
            content_types=cls.model.ContentType,
            languages={v.name: lang_names[v] for v in Language},
            fiats=p2p_fiats
        )
        if id_ == 0:
            return dict(extra=extra)
        if (row := cls.model.query.get(id_)) is None:
            raise RecordNotFound

        _now = now()
        row_dict = row.to_dict(enum_to_name=True)
        row_dict['fiats'] = row.fiats.split(',') if row.fiats else []
        row_dict.update(extra=extra)
        row_dict['platform'] = cls.fmt_platform(row.platform)
        return row_dict

    @classmethod
    def _update_lang_data(cls, id_, lang_data: dict):
        rows = cls._get_rows(id_)
        update_langs = []
        for lang, file_data in lang_data.items():
            daylight_file_key = file_data.get("daylight_file_key", "")
            night_file_key = file_data.get("night_file_key", "")
            content = file_data.get("content", "")
            if not ((daylight_file_key and night_file_key) or content):
                continue
            lang_banner = cls.content_model.get_or_create(
                owner_id=id_,
                lang=lang
            )
            db.session.add(lang_banner)
            lang_banner.daylight_file_key = daylight_file_key or ""
            lang_banner.night_file_key = night_file_key or ""
            lang_banner.content = content or ""
            update_langs.append(Language[lang])
        delete_langs = {i.lang for i in rows} - set(update_langs)
        cls.content_model.query.filter(
            cls.content_model.lang.in_(delete_langs)
        ).delete()

    @classmethod
    @ns.use_kwargs(dict(
        name=fields.String(required=True),
        platform=fields.List(EnumField(P2pActivity.Platform), required=True),
        content_type=EnumField(P2pActivity.ContentType, required=True),
        display_to_merchant=fields.Boolean(required=True),
        fiats=fields.List(fields.String, missing=[]),
        start_at=TimestampField(required=True),
        end_at=TimestampField(required=True),
        jump_id=fields.Integer(allow_none=True),
        lang_data=fields.Dict(keys=EnumField([i.name for i in Language]), values=fields.Dict, required=True)
    ))
    def put(cls, id_, **kwargs):
        """运营-WEB-新建/编辑p2p主页运营位"""
        kwargs = cls.validate(kwargs, id_)
        lang_data = kwargs.pop('lang_data')
        fiats = kwargs.pop("fiats")
        fiats_str = ",".join(fiats) if fiats else ""
        old_data = None
        if id_ == 0:
            row = cls.model(
                **kwargs,
                updated_by=g.user.id,
            )
            sort_id = (cls.model.query.with_entities(
                func.max(cls.model.sort_id).label('max_sort_id')
            ).scalar() or 0) + 1
            row.sort_id = sort_id
            row.fiats = fiats_str
            row.status = P2pActivity.Status.PENDING
        else:
            row = cls._get_row(id_)
            old_data = row.to_dict(enum_to_name=True)
            row.name = kwargs['name']
            row.platform = kwargs['platform']
            row.display_to_merchant = kwargs['display_to_merchant']
            row.content_type = kwargs['content_type']
            row.start_at = kwargs['start_at']
            row.end_at = kwargs['end_at']
            row.fiats = fiats_str
            row.jump_id = kwargs['jump_id']
        row.updated_by = g.user.id
        db.session.add(row)
        db.session.flush()
        cls._update_lang_data(row.id, lang_data)
        db.session.commit()
        P2pActivityBannerCache.reload()

        AdminOperationLog.new_add_or_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.P2pActivity,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
        )

        return row.to_dict(enum_to_name=True)

    @classmethod
    def delete(cls, id_):
        """运营-WEB-删除p2p主页运营位"""
        row = cls._get_row(id_)
        origin_status = row.status
        row.status = P2pActivity.Status.DELETED
        row.updated_by = g.user.id
        db.session.commit()
        if origin_status == P2pActivity.Status.ONLINE:
            P2pActivityBannerCache.reload()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.P2pActivity,
            detail=row.to_dict(enum_to_name=True),
        )


@ns.route('/<int:id_>/offline')
@respond_with_code
class P2pActivityOfflineResource(Resource, P2pActivityMixin):

    @classmethod
    def patch(cls, id_):
        """运营-WEB-下架p2p主页运营位"""
        row = cls._get_row(id_)
        old_data = row.to_dict(enum_to_name=True)
        row.end_at = now()
        row.updated_by = g.user.id
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.P2pActivity,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
        )


@ns.route('/<int:id_>/sort_id')
@respond_with_code
class P2pActivitySortIDResource(Resource):

    @classmethod
    def post(cls, id_):
        """运营-WEB-p2p主页运营位上移"""
        row: P2pActivity = P2pActivity.query.get(id_)
        if row is None:
            raise RecordNotFound
        other: P2pActivity = P2pActivity.query \
            .filter(P2pActivity.sort_id > row.sort_id) \
            .order_by(P2pActivity.sort_id.asc()).first()
        if other is not None:
            row_old_data = row.to_dict(enum_to_name=True)
            other_old_data = other.to_dict(enum_to_name=True)
            row.sort_id, other.sort_id = other.sort_id, row.sort_id
            db.session.commit()

            AdminOperationLog.new_edit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectOperation.P2pActivity,
                old_data=row_old_data,
                new_data=row.to_dict(enum_to_name=True),
            )
            AdminOperationLog.new_edit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectOperation.P2pActivity,
                old_data=other_old_data,
                new_data=other.to_dict(enum_to_name=True),
            )
        return {}


@ns.route('/<int:id_>/langs')
@respond_with_code
class NewInsetConfigContentResource(Resource, P2pActivityMixin):
    model = P2pActivityBanner

    @classmethod
    def get(cls, id_):
        """运营-WEB-p2p主页运营位详情"""
        rows = cls._get_rows(id_)
        data = {}
        if rows is None:
            return data
        for row in rows:
            data[row.lang.name] = {
                'daylight_file_key': row.daylight_file_key,
                'night_file_key': row.night_file_key,
                'daylight_file_url': AWSBucketPublic.get_file_url(row.daylight_file_key) if row.daylight_file_key else "",
                'night_file_url': AWSBucketPublic.get_file_url(row.night_file_key) if row.night_file_key else "",
                'content': row.content,
            }
        return data
