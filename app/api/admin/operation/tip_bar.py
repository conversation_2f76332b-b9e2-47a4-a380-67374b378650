# -*- coding: utf-8 -*-
from collections import defaultdict

from flask import g

import json
from functools import partial

from app.api.common import Namespace, Resource, respond_with_code
from app.api.common.fields import TimestampField, EnumField
from app.assets import list_all_assets
from app.business import BusinessSettings
from app.business.auth import get_admin_user_name_map
from app.business.push_statistic import UserTagGroupBiz, TipBarUserParser
from app.caches import MarketCache, PerpetualMarketCache
from app.caches.copy_trading import CopyTradingMarketsCache
from app.common import language_cn_names, Enum
from app.common.onchain import Chain
from app.exceptions import InvalidArgument, OperationDenied, RecordNotFound
from app.models import db, TipBar, TipBarContent, AssetMaintainRelation
from app.models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog, OPNamespaceObjectOperation, OPNamespaceObjectSystem
from app.models.onchain import OnchainToken
from app.utils import now
from app.utils.date_ import datetime_to_str
from webargs import fields

from app.utils.helper import Struct

ns = Namespace("Operation - TipBar")


class TipBarMixin:
    @classmethod
    def get_status_values(cls, start_t, end_t, now_t):
        if start_t > now_t:
            status = "pending"
        elif end_t < now_t:
            status = "offline"
        else:
            status = "online"
        return status

    @classmethod
    def validate_params_and_business(cls, params, id_=None):
        cls.validate_params(params)
        cls.validate_business(params, id_)

    @classmethod
    def validate_params(cls, params):
        if params["started_at"] > params["ended_at"]:
            raise InvalidArgument(message="结束时间小于开始时间")
        if params["ended_at"] < now():
            raise InvalidArgument(message="结束时间小于当前时间")

    @classmethod
    def validate_business(cls, params, id_):
        start, end = params["started_at"], params["ended_at"]

        rows = TipBar.query.filter(
            TipBar.status.in_(
                [TipBar.Status.DRAFT, TipBar.Status.CREATED, TipBar.Status.AUDITED]
            ),
            TipBar.ended_at >= max(start, now()),  # 过滤掉已过期的活动
            TipBar.started_at <= end,  # 过滤掉 开始时间小于本次结束时间 的活动
        ).with_entities(
            TipBar.trigger_pages,
            TipBar.id,
            TipBar.started_at,
            TipBar.ended_at,
        )
        # 过滤自身活动
        if id_:
            rows = rows.filter(TipBar.id != id_)
        if (platform := params["platform"]) != TipBar.Platform.ALL:
            rows = rows.filter(TipBar.platform.in_([TipBar.Platform.ALL, platform]))
        params_page_set = {i["trigger_page"] for i in params["trigger_pages"]}
        spot_params_set = [i["trigger_page_params"] for i in params["trigger_pages"] if i["trigger_page"] == 'SPOT_MARKET']
        perpetual_page_set = [i["trigger_page_params"] for i in params["trigger_pages"] if i["trigger_page"] == 'PERPETUAL_MARKET']
        params_spot_market_set = set()
        params_perpetual_market_set = set()
        for item in params["trigger_pages"]:
            if item["trigger_page"] == "SPOT_MARKET":
                params_spot_market_set.update(item["trigger_page_params"])
            elif item["trigger_page"] == "PERPETUAL_MARKET":
                params_perpetual_market_set.update(item["trigger_page_params"])
        for row in rows:
            trigger_pages = json.loads(row["trigger_pages"])
            if not trigger_pages:
                continue
            row_page_set = {i.get("trigger_page") for i in trigger_pages}
            spot_row_params_set = [i.get("trigger_page_params") for i in trigger_pages if i.get("trigger_page") == 'SPOT_MARKET']
            perpetual_row_params_set = [i.get("trigger_page_params") for i in trigger_pages if i.get("trigger_page") == 'PERPETUAL_MARKET']
            row_spot_market_set = set()
            row_perpetual_market_set = set()
            for page in trigger_pages:
                if page["trigger_page"] == "SPOT_MARKET":
                    row_spot_market_set.update(page["trigger_page_params"])
                elif page["trigger_page"] == "PERPETUAL_MARKET":
                    row_perpetual_market_set.update(page["trigger_page_params"])
            # 全部页包含所有页,相同类型市场不能重复
            trigger_all = TipBar.TriggerPage.ALL.name in params_page_set or TipBar.TriggerPage.ALL.name in row_page_set

            empty_spot_params = [] in spot_params_set and spot_row_params_set
            empty_spot_row = [] in spot_row_params_set and spot_params_set

            empty_perpetual_page = [] in perpetual_page_set and perpetual_row_params_set
            empty_perpetual_row = [] in perpetual_row_params_set and perpetual_page_set

            match_spot_market = params_spot_market_set & row_spot_market_set
            match_perpetual_market = params_perpetual_market_set & row_perpetual_market_set

            if (
                    trigger_all
                    or empty_spot_params
                    or empty_spot_row
                    or empty_perpetual_page
                    or empty_perpetual_row
                    or match_spot_market
                    or match_perpetual_market
            ):
                dt_format = partial(
                    datetime_to_str, offset_minutes=60 * 8
                )  # to utc+8 str
                raise InvalidArgument(
                    message=f"提示条已存在; id: {row.id} 开始时间 {dt_format(row.started_at)} "
                    f"结束时间 {dt_format(row.ended_at)}; 请修改 提示时间 不与之重叠"
                )

    @classmethod
    def trim_trigger_pages(cls, trigger_pages: list):
        pages = []
        for trigger_page_map in trigger_pages:
            trigger_page = trigger_page_map["trigger_page"]
            param_type = trigger_page_map["param_type"]
            page_op = trigger_page_map["page_op"]
            trigger_page_params = trigger_page_map["trigger_page_params"]
            if trigger_page == cls.model.TriggerPage.ASSET_DATA.name:
                param_type = cls.model.TriggerPageParamType.ASSET.name
            page = {
                "trigger_page": trigger_page,
                "param_type": param_type,
                "page_op": page_op,
                "trigger_page_params": trigger_page_params,
            }
            if trigger_page_params_chain := trigger_page_map.get("trigger_page_params_chain"):
                page["trigger_page_params_chain"] = trigger_page_params_chain
            pages.append(page)

        pages = json.dumps(pages)
        return pages

    @classmethod
    def get_markets(cls):
        perpetual_markets = PerpetualMarketCache().get_market_list()
        copy_trading_markets = list(CopyTradingMarketsCache().read())
        markets_detail = MarketCache.online_markets_detail()
        spot_markets = list(markets_detail.keys())
        return spot_markets, perpetual_markets, copy_trading_markets

    @classmethod
    def get_onchain_tokens(cls):
        rows = OnchainToken.query.with_entities(
            OnchainToken.id,
            OnchainToken.chain,
            OnchainToken.contract,
            OnchainToken.symbol,
        ).all()
        res = defaultdict(list)
        for r in rows:
            r: OnchainToken
            res[r.chain.get_display_name()].append(dict(
                token_id=r.id,
                contract=r.contract,
                symbol=r.symbol,
            ))
        return res

    @classmethod
    def _get_auto_maintains(cls, ids: list) -> set:
        model = AssetMaintainRelation
        rows = model.query.with_entities(
            model.maintain_id,
            model.business_id,
        ).filter(
            model.business_id.in_(ids)
        ).all()
        return {row.business_id for row in rows}


@ns.route("")
@respond_with_code
class TipBarsResource(TipBarMixin, Resource):
    model = TipBar

    class DisplayStatus(Enum):
        DRAFT = "未提交"
        CREATED = "待审核"
        REJECTED = "未通过"
        PENDING = "待通知"
        RUNNING = "通知中"
        FINISHED = "已结束"
        FAILED = "失败"

    deserialize_status_mapping = {
        DisplayStatus.DRAFT: model.Status.DRAFT,
        DisplayStatus.CREATED: model.Status.CREATED,
        DisplayStatus.REJECTED: model.Status.REJECTED,
        DisplayStatus.PENDING: model.Status.AUDITED,
        DisplayStatus.RUNNING: model.Status.AUDITED,
        DisplayStatus.FINISHED: model.Status.FINISHED,
        DisplayStatus.FAILED: model.Status.FAILED,
    }
    serialize_status_mapping = {
        model.Status.DRAFT: DisplayStatus.DRAFT,
        model.Status.CREATED: DisplayStatus.CREATED,
        model.Status.REJECTED: DisplayStatus.REJECTED,
        model.Status.AUDITED: DisplayStatus.PENDING,
        model.Status.FINISHED: DisplayStatus.FINISHED,
        model.Status.FAILED: DisplayStatus.FAILED,
    }

    @classmethod
    @ns.use_kwargs(
        dict(
            name=fields.String,
            type=EnumField(model.Type),
            platform=EnumField(model.Platform),
            trigger_page=EnumField(model.TriggerPage),
            status=EnumField(DisplayStatus),
            start_time=TimestampField(),
            end_time=TimestampField(),
            page=fields.Integer(missing=1),
            limit=fields.Integer(missing=50),
        )
    )
    def get(cls, **kwargs):
        """触达管理-提示条-列表"""
        params = Struct(**kwargs)
        query = cls.get_query_by(params)

        records = query.paginate(params.page, params.limit)
        items = records.items
        create_user_ids = {item.created_by for item in items}
        name_map = get_admin_user_name_map(create_user_ids)

        now_ = now()
        model = cls.model
        bars = []
        auto_maintains = cls._get_auto_maintains([row.id for row in items])
        for row in items:
            row_dict = row.to_dict(enum_to_name=True)
            row_dict["trigger_pages"] = row.get_trigger_pages()
            row_dict["created_user_email"] = name_map.get(row.created_by) or "-"
            row_dict["status"] = row.status.value

            status = cls.serialize_status_mapping[row.status]
            row_dict["status"] = status.name
            can_del, can_stop = True, False
            if row.status is model.Status.FINISHED:
                can_del = False
            elif row.status is model.Status.FAILED:
                can_del = False
            elif row.status is model.Status.AUDITED:
                if row.started_at <= now_:
                    row_dict["status"] = cls.DisplayStatus.RUNNING.name
                    can_stop = True
                    can_del = False
                else:
                    row_dict["status"] = cls.DisplayStatus.PENDING.name

            row_dict["can_del"] = can_del
            row_dict["can_stop"] = can_stop
            row_dict["from_maintain"] = row.id in auto_maintains
            bars.append(row_dict)

        return dict(
            total=records.total,
            items=bars,
            extra=dict(
                platforms=model.Platform,
                trigger_pages=model.TriggerPage,
                statuses=cls.DisplayStatus,
                types=model.Type,
                whitelist_assets=BusinessSettings.asset_volatility_tb_whitelist,
                assets=list_all_assets(),
            ),
        )

    @classmethod
    def get_query_by(cls, params):
        model = cls.model
        query = model.query.filter(model.status != model.Status.DELETED).order_by(
            model.id.desc()
        )

        if name := params.name:
            query = query.filter(model.name.contains(name))
        if type_ := params.type:
            query = query.filter(model.type == type_)
        if platform := params.platform:
            query = query.filter(model.platform == platform)
        if trigger_page := params.trigger_page:
            query = query.filter(model.trigger_pages.contains(trigger_page.name))
        if status := params.status:
            status = cls.deserialize_status_mapping.get(status)
            if status:
                query = query.filter(model.status == status)
                if params.status == cls.DisplayStatus.PENDING:
                    query = query.filter(model.started_at > now())
                elif params.status == cls.DisplayStatus.RUNNING:
                    query = query.filter(model.started_at < now())
        if params.start_time:
            query = query.filter(model.started_at >= params.start_time)
        if params.end_time:
            query = query.filter(model.ended_at <= params.end_time)

        return query

    @classmethod
    @ns.use_kwargs(
        dict(
            name=fields.String(required=True),
            type=EnumField(model.Type, required=True),
            started_at=TimestampField(required=True),
            ended_at=TimestampField(required=True),
            platform=EnumField(model.Platform, required=True),
            trigger_pages=fields.List(fields.Dict(required=True), required=True),
            filter_type=EnumField(model.FilterType, enum_by_value=True, required=True),
            groups=fields.List(fields.Integer),
            whitelist_enabled=fields.Boolean(missing=False),
            user_whitelist=fields.String(required=False),
            jump_page_enabled=fields.Boolean(missing=False),
            jump_type=EnumField(model.JumpType),
            jump_id=fields.Integer,
        )
    )
    def post(cls, **kwargs):
        """运营-提示条管理-新增提示条"""
        model = cls.model
        cls.validate_params_and_business(kwargs)

        row = model(
            name=kwargs["name"],
            type=kwargs["type"],
            started_at=kwargs["started_at"],
            ended_at=kwargs["ended_at"],
            platform=kwargs["platform"],
            trigger_pages=cls.trim_trigger_pages(kwargs["trigger_pages"]),
            filter_type=kwargs["filter_type"],
            whitelist_enabled=kwargs["whitelist_enabled"],
            user_whitelist=kwargs.get("user_whitelist") or "",
            jump_page_enabled=kwargs["jump_page_enabled"],
            jump_type=kwargs.get("jump_type"),
            jump_id=kwargs.get("jump_id"),
            remark=kwargs.get("remark", ""),
            created_by=g.user.id,
        )
        filter_type = row.filter_type = kwargs["filter_type"]
        if filter_type == model.FilterType.FILTERS:
            if not (groups := kwargs.get("groups")):
                raise InvalidArgument
            groups = UserTagGroupBiz.filter_tag_group_ids(ids=groups)
            row.groups = json.dumps(groups)
            _, users = TipBarUserParser(row).parse()
            row.users = json.dumps(list(users))
            row.target_user_number = len(users)

        obj = db.session_add_and_commit(row)

        kwargs['id'] = obj.id
        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.TipBar,
            detail=kwargs,
        )
        return {"id": obj.id}


@ns.route("/<int:id_>")
@respond_with_code
class TipBarResource(TipBarMixin, Resource):
    model = TipBar

    FILTER_TYPES = {
        model.FilterType.FILTERS.value: "定向用户",
        model.FilterType.NONE.value: "全部用户",
    }

    extra = dict(
        trigger_pages=model.TriggerPage,
        param_types=model.TriggerPageParamType,
        page_ops=model.TriggerPageOp,
        platforms=model.Platform,
        filter_types=FILTER_TYPES,
        jump_types=model.JumpType,
        statuses=model.Status,
        types={v.name: v.value for v in model.TYPE_DISPLAY},
        langs={
            x.name: y
            for x, y in language_cn_names().items()
            if x in TipBar.AVAILABLE_LANGS
        },
    )

    @classmethod
    def get(cls, id_):
        """运营-提示条管理-提示条详情"""
        spot_markets, perpetual_markets, copy_trading_markets = cls.get_markets()
        extra = {
            "assets": list_all_assets(),
            "markets": spot_markets,
            "perpetual_markets": perpetual_markets,
            "copy_trading_markets": copy_trading_markets,
            "onchain_chains": Chain.get_display_name_list(),
            "onchain_tokens": cls.get_onchain_tokens(),
            **cls.extra,
        }
        if id_ == 0:
            return dict(extra=extra)

        row = TipBar.query.get(id_)
        if not row:
            raise InvalidArgument
        auto_maintains = cls._get_auto_maintains([row.id])

        rows = TipBarContent.query.filter(TipBarContent.tip_bar_id == id_).all()
        rows = {x.lang.name: x for x in rows}
        contents = {}
        has_content = False
        for lang in TipBar.AVAILABLE_LANGS:
            lang = lang.name
            r = rows.get(lang)
            if r:
                contents[lang] = dict(
                    lang=lang,
                    content=r.content,
                )
                has_content = True
            else:
                contents[lang] = dict(
                    lang=lang,
                    content="",
                )
        user_ids = {row.created_by}
        if row.audited_by:
            user_ids.add(row.audited_by)
        name_map = get_admin_user_name_map(user_ids)
        created_user_email = name_map.get(row.created_by) or '-'
        audited_user_email = name_map.get(row.audited_by) or '-'

        extra.update(tag_groups=UserTagGroupBiz.get_tag_group_info(row.get_groups()))
        return dict(
            record=dict(
                id=row.id,
                name=row.name,
                type=row.type.name,
                started_at=row.started_at,
                ended_at=row.ended_at,
                audited_at=row.audited_at,
                created_at=row.created_at,
                platform=row.platform.name,
                trigger_pages=row.get_trigger_pages(),
                jump_page_enabled=row.jump_page_enabled,
                jump_type=row.jump_type.name if row.jump_type else row.jump_type,
                jump_id=row.jump_id,
                whitelist_enabled=row.whitelist_enabled,
                user_whitelist=row.user_whitelist,
                auditor_remark=row.auditor_remark,
                filter_type=row.filter_type,
                filters=json.loads(row.filters) if row.filters else None,
                target_user_number=(
                    "-"
                    if row.filter_type == TipBar.FilterType.NONE
                    else row.target_user_number
                ),
                status=row.status.name,
                has_content=has_content,
                from_maintain=id_ in auto_maintains,
                created_by=row.created_by,
                audited_by=row.audited_by,
                created_user_email=created_user_email,
                audited_user_email=audited_user_email,
            ),
            contents=contents,
            extra=extra,
        )

    @classmethod
    @ns.use_kwargs(
        dict(
            name=fields.String(required=True),
            type=EnumField(model.Type, required=True),
            started_at=TimestampField(required=True),
            ended_at=TimestampField(required=True),
            platform=EnumField(model.Platform, required=True),
            trigger_pages=fields.List(fields.Dict(required=True), required=True),
            filter_type=EnumField(model.FilterType, enum_by_value=True, required=True),
            groups=fields.List(fields.Integer),
            whitelist_enabled=fields.Boolean(missing=False),
            user_whitelist=fields.String(required=False),
            jump_page_enabled=fields.Boolean(missing=False),
            jump_type=EnumField(model.JumpType),
            jump_id=fields.Integer,
        )
    )
    def patch(cls, id_, **kwargs):
        """运营-提示条管理-编辑提示条"""
        row = TipBar.query.get(id_)
        if not row:
            raise InvalidArgument
        old_data = row.to_dict(enum_to_name=True)

        if row.status is cls.model.Status.FINISHED:
            raise OperationDenied(message="cannot edit a finished item")

        if row.status is cls.model.Status.AUDITED:
            if row.started_at <= now():
                raise OperationDenied(message="cannot edit a running item")

        cls.validate_params_and_business(kwargs, id_)
        row.name = kwargs["name"]
        row.type = kwargs["type"]
        row.started_at = kwargs["started_at"]
        row.ended_at = kwargs["ended_at"]
        row.platform = kwargs["platform"]
        row.whitelist_enabled = kwargs["whitelist_enabled"]
        row.user_whitelist = kwargs.get("user_whitelist") or ""
        row.jump_page_enabled = kwargs["jump_page_enabled"]
        row.jump_type = kwargs.get("jump_type")
        row.jump_id = kwargs.get("jump_id")
        row.trigger_pages = cls.trim_trigger_pages(kwargs["trigger_pages"])
        if row.status in [cls.model.Status.FAILED,
                          cls.model.Status.AUDITED,
                          cls.model.Status.REJECTED]:
            # 失败，审核，拒绝状态下重新编辑后变为待审核
            row.status = cls.model.Status.CREATED
        if filter_type := kwargs.get("filter_type"):
            row.filter_type = filter_type
            row.users = None
            row.target_user_number = 0
            if filter_type == TipBar.FilterType.FILTERS:
                if not (groups := kwargs.get("groups")):
                    raise InvalidArgument
                groups = UserTagGroupBiz.filter_tag_group_ids(ids=groups)
                row.groups = json.dumps(groups)
                _, users = TipBarUserParser(row).parse()
                row.users = json.dumps(list(users))
                row.target_user_number = len(users)
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.TipBar,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
        )
        return {}

    @classmethod
    def delete(cls, id_):
        """运营-提示条管理-删除提示条"""
        row = TipBar.query.get(id_)
        if not row:
            raise InvalidArgument
        row.status = TipBar.Status.DELETED
        db.session.commit()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.TipBar,
            detail=dict(id=id_, name=row.name),
        )
        return {}


@ns.route("/<int:id_>/offline")
@respond_with_code
class TipBarOfflineResource(Resource):
    @classmethod
    def put(cls, id_):
        """运营-提示条管理-停止提示条"""
        row = TipBar.query.get(id_)
        if not row:
            raise InvalidArgument
        row.ended_at = now()
        db.session.commit()

        AdminOperationLog.new_stop(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.TipBar,
            detail=dict(id=id_, name=row.name),
        )
        return {}


@ns.route("/<int:id_>/langs/<lang>")
@respond_with_code
class TipBarContentResource(Resource):
    @classmethod
    @ns.use_kwargs(
        dict(
            content=fields.String(missing=""),
        )
    )
    def put(cls, id_, lang, **kwargs):
        """运营-提示条管理-编辑提示条内容"""
        row = TipBar.query.get(id_)
        if not row:
            raise InvalidArgument
        if lang not in [e.name for e in TipBar.AVAILABLE_LANGS]:
            raise InvalidArgument

        row = TipBarContent.query.filter(
            TipBarContent.tip_bar_id == id_, TipBarContent.lang == lang
        ).first()
        if row:
            old_data = row.to_dict(enum_to_name=True)
            row.content = kwargs["content"]

            AdminOperationLog.new_edit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectOperation.TipBarContent,
                old_data=old_data,
                new_data=row.to_dict(enum_to_name=True),
                special_data=dict(tip_bar_id=id_, lang=lang),
            )
        else:
            row = TipBarContent(
                tip_bar_id=id_,
                lang=lang,
                content=kwargs["content"],
            )
            db.session.add(row)

            AdminOperationLog.new_add(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectOperation.TipBarContent,
                detail=row.to_dict(enum_to_name=True),
            )
        db.session.commit()


@ns.route("/<int:id_>/created")
@respond_with_code
class TipBarCreated(Resource):
    model = TipBar

    @classmethod
    def patch(cls, id_):
        """运营-触达管理-提示条-提交审核"""
        row = cls.model.query.get(id_)
        if row is None:
            raise RecordNotFound
        old_data = row.to_dict(enum_to_name=True)

        if row.status is not cls.model.Status.DRAFT:
            raise OperationDenied(message="only draft status item can be edit")

        if TipBarContent.query.filter(TipBarContent.tip_bar_id == id_).count() == 0:
            raise OperationDenied(message="提示内容未配置,不可提交审核")
        row.status = cls.model.Status.CREATED
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.TipBar,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
        )

        return row


@ns.route("/<int:id_>/audit")
@respond_with_code
class TipBarAudit(Resource):
    model = TipBar

    @classmethod
    @ns.use_kwargs(
        dict(
            status=EnumField(model.Status, required=True),
            auditor_remark=fields.String,
        )
    )
    def patch(cls, id_, **kwargs):
        """运营-触达管理-提示条-审核"""
        row = cls.model.query.get(id_)
        if row is None:
            raise RecordNotFound

        params = Struct(**kwargs)
        if params.status not in (cls.model.Status.AUDITED, cls.model.Status.REJECTED):
            raise OperationDenied(message=f"invalid status {params.status!r}")

        if row.status is not cls.model.Status.CREATED:
            raise OperationDenied(message="only created status item can be audit")

        user_id: int = g.user.id
        row.status = params.status
        row.audited_by = user_id
        row.audited_at = now()
        row.auditor_remark = params.auditor_remark or ""
        db.session.commit()

        row_dict = row.to_dict(enum_to_name=True)
        if row.status is cls.model.Status.AUDITED:
            if row.started_at <= now():
                row_dict["status"] = "RUNNING"
            else:
                row_dict["status"] = "PENDING"

        row_dict["trigger_pages"] = row.get_trigger_pages()

        kwargs['id'] = id_
        AdminOperationLog.new_audit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.TipBar,
            detail=kwargs,
        )
        return row_dict


@ns.route("/<int:id_>/offline")
@respond_with_code
class TipBarOffline(Resource):
    model = TipBar

    @classmethod
    def patch(cls, id_):
        """运营-触达管理-提示条-通知中提前结束"""
        row = cls.model.query.get(id_)
        if row is None:
            raise RecordNotFound

        if row.status is not cls.model.Status.AUDITED:
            raise OperationDenied(message="only running status item can be stopped")
        now_ = now()
        if row.started_at > now_:
            raise OperationDenied(message="only running status item can be stopped")

        row.status = cls.model.Status.FINISHED
        row.ended_at = now_
        db.session.commit()

        row_dict = row.to_dict(enum_to_name=True)
        row_dict["trigger_pages"] = row.get_trigger_pages()

        AdminOperationLog.new_stop(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.TipBar,
            detail=dict(id=id_, name=row.name),
        )
        return row_dict


@ns.route("/whitelist")
@respond_with_code
class TipBarWhitelist(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        value=fields.List(fields.String, required=True)
    ))
    def patch(cls, **kwargs):
        """运营-触达管理-提示条-白名单设置"""
        value = kwargs['value']
        field = 'asset_volatility_tb_whitelist'
        old_value = getattr(BusinessSettings, field)
        try:
            setattr(BusinessSettings, field, value)
        except (AttributeError, ValueError) as e:
            raise InvalidArgument(message=f'{e!r}')
        new_value = getattr(BusinessSettings, field)

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectSystem.BusinessSettings,
            old_data={field: old_value},
            new_data={field: new_value},
        )
