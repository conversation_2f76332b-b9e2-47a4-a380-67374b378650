# -*- coding: utf-8 -*-
from flask import g

import json

from app.api.common import Namespace, Resource, respond_with_code
from app.api.common.fields import TimestampField, EnumField
from app.assets import list_all_assets
from app.business.auth import get_admin_user_name_map
from app.business.operation import TipBarHelper
from app.common import language_cn_names, Enum
from app.exceptions import InvalidArgument, OperationDenied, RecordNotFound
from app.models import db, TipBar, AssetMaintain, AssetMaintainContent, OperationTemplate, OperationTemplateContent
from app.models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog, OPNamespaceObjectOperation
from app.utils import now
from webargs import fields

from app.utils.helper import Struct

ns = Namespace("Operation - AssetMaintain")


class AssetMaintainMixin:
    @classmethod
    def get_status_values(cls, start_t, end_t, now_t):
        if start_t > now_t:
            status = "pending"
        elif end_t < now_t:
            status = "offline"
        else:
            status = "online"
        return status

    @classmethod
    def validate_params_and_business(cls, params, id_=None):
        cls.validate_params(params)
        cls.validate_business(params, id_)

    @classmethod
    def validate_params(cls, params):
        if params["started_at"] > params["ended_at"]:
            raise InvalidArgument(message="结束时间小于开始时间")
        if params["ended_at"] < now():
            raise InvalidArgument(message="结束时间小于当前时间")

    @classmethod
    def validate_business(cls, params, id_):
        start, end = params["started_at"], params["ended_at"]
        asset = params['asset']
        maintain_type = params['maintain_type']
        tag_pages = params.get('tag_pages') or []
        if maintain_type and not tag_pages:
            raise InvalidArgument(message='标签配置功能需同时配置：维护类型 + 标签配置')
        if not maintain_type and tag_pages:
            raise InvalidArgument(message='标签配置功能需同时配置：维护类型 + 标签配置')
        tip_pages = params.get('tip_pages') or []
        if TipBar.TriggerPage.ALL.name in tip_pages:
            tip_pages.remove(TipBar.TriggerPage.ALL.name)
        if TipBar.TriggerPage.ONCHAIN.name in tip_pages:
            tip_pages.remove(TipBar.TriggerPage.ONCHAIN.name)

        model = AssetMaintain
        query = model.query.filter(
            model.status.in_(
                [model.Status.DRAFT, model.Status.CREATED, model.Status.AUDITED]
            ),
            model.ended_at >= max(start, now()),  # 过滤掉已过期的活动
            model.started_at <= end,  # 过滤掉 开始时间小于本次结束时间 的活动
            model.asset == asset,  # 一个币种只能配置一条
        ).with_entities(
            model.id,
        )
        # 过滤自身活动
        if id_:
            query = query.filter(model.id != id_)
        row = query.first()
        if row:
            raise InvalidArgument(
                message=f"{asset} 相同币种只能配置一条记录！"
            )

    @classmethod
    def dumps_pages(cls, pages: list | None) -> str:
        if pages:
            return json.dumps(pages)
        return ''


@ns.route("")
@respond_with_code
class AssetMaintainResource(AssetMaintainMixin, Resource):
    model = AssetMaintain

    class DisplayStatus(Enum):
        DRAFT = "未提交"
        CREATED = "待审核"
        REJECTED = "未通过"
        PENDING = "待通知"
        RUNNING = "通知中"
        FINISHED = "已结束"

    deserialize_status_mapping = {
        DisplayStatus.DRAFT: model.Status.DRAFT,
        DisplayStatus.CREATED: model.Status.CREATED,
        DisplayStatus.REJECTED: model.Status.REJECTED,
        DisplayStatus.PENDING: model.Status.AUDITED,
        DisplayStatus.RUNNING: model.Status.AUDITED,
        DisplayStatus.FINISHED: model.Status.FINISHED,
    }
    serialize_status_mapping = {
        model.Status.DRAFT: DisplayStatus.DRAFT,
        model.Status.CREATED: DisplayStatus.CREATED,
        model.Status.REJECTED: DisplayStatus.REJECTED,
        model.Status.AUDITED: DisplayStatus.PENDING,
        model.Status.FINISHED: DisplayStatus.FINISHED,
    }

    @classmethod
    @ns.use_kwargs(
        dict(
            asset=fields.String,
            platform=EnumField(model.Platform),
            maintain_type=EnumField(model.MaintainType),
            status=EnumField(DisplayStatus),
            start_time=TimestampField(),
            end_time=TimestampField(),
            page=fields.Integer(missing=1),
            limit=fields.Integer(missing=50),
        )
    )
    def get(cls, **kwargs):
        """触达管理-币种维护-列表"""
        params = Struct(**kwargs)
        query = cls.get_query_by(params)

        records = query.paginate(params.page, params.limit)
        items = records.items
        create_user_ids = {item.created_by for item in items}
        name_map = get_admin_user_name_map(create_user_ids)

        now_ = now()
        model = cls.model
        ret = []
        for row in items:
            row_dict = row.to_dict(enum_to_name=True)
            row_dict["created_user_email"] = name_map.get(row.created_by) or "-"
            row_dict["status"] = row.status.value
            row_dict["tag_pages"] = row.get_tag_pages()
            row_dict["tip_pages"] = row.get_tip_pages()
            features = []
            if row_dict["tag_pages"] and row_dict['maintain_type']:
                features.append(model.Feature.TAG.value)
            if row_dict["tip_pages"]:
                features.append(model.Feature.TIP_BAR.value)
            row_dict['features'] = '\n'.join(features)
            status = cls.serialize_status_mapping[row.status]
            row_dict["status"] = status.name
            can_del, can_stop = True, False
            if row.status is model.Status.FINISHED:
                can_del = False
            elif row.status is model.Status.AUDITED:
                if row.started_at <= now_:
                    row_dict["status"] = cls.DisplayStatus.RUNNING.name
                    can_stop = True
                    can_del = False
                else:
                    row_dict["status"] = cls.DisplayStatus.PENDING.name

            row_dict["can_del"] = can_del
            row_dict["can_stop"] = can_stop
            ret.append(row_dict)

        return dict(
            total=records.total,
            items=ret,
            extra=dict(
                platforms=model.Platform,
                maintain_types=model.MaintainType,
                statuses=cls.DisplayStatus,
                assets=list_all_assets(),
            ),
        )

    @classmethod
    def get_query_by(cls, params):
        model = cls.model
        query = model.query.filter(model.status != model.Status.DELETED).order_by(
            model.id.desc()
        )
        if asset := params.asset:
            query = query.filter(model.asset == asset)
        if maintain_type := params.maintain_type:
            query = query.filter(model.maintain_type == maintain_type)
        if platform := params.platform:
            query = query.filter(model.platform == platform)
        if status := params.status:
            status = cls.deserialize_status_mapping.get(status)
            if status:
                query = query.filter(model.status == status)
                if params.status == cls.DisplayStatus.PENDING:
                    query = query.filter(model.started_at > now())
                elif params.status == cls.DisplayStatus.RUNNING:
                    query = query.filter(model.started_at < now())
        if params.start_time:
            query = query.filter(model.started_at >= params.start_time)
        if params.end_time:
            query = query.filter(model.ended_at <= params.end_time)

        return query

    @classmethod
    @ns.use_kwargs(
        dict(
            assets=fields.List(fields.String, required=True),
            platform=EnumField(model.Platform, required=True),
            started_at=TimestampField(required=True),
            ended_at=TimestampField(required=True),
            jump_page_enabled=fields.Boolean(missing=False),
            jump_type=EnumField(model.JumpType),
            jump_id=fields.Integer,

            maintain_type=EnumField(model.MaintainType, required=True, allow_none=True),
            tag_pages=fields.List(fields.String),
            tip_pages=fields.List(fields.String),
        )
    )
    def post(cls, **kwargs):
        """运营-币种维护-新增"""
        model = cls.model
        assets = kwargs['assets']
        if len(assets) == 0:
            raise InvalidArgument(message="必须选择一个币种")
        for _asset in assets:
            validate_data = kwargs | dict(asset=_asset)
            cls.validate_params_and_business(validate_data)
        last_id = 0
        for _asset in assets:
            row = model(
                asset=_asset,
                started_at=kwargs["started_at"],
                ended_at=kwargs["ended_at"],
                platform=kwargs["platform"],
                jump_page_enabled=kwargs["jump_page_enabled"],
                jump_type=kwargs.get("jump_type"),
                jump_id=kwargs.get("jump_id"),

                maintain_type=kwargs.get('maintain_type'),
                tag_pages=cls.dumps_pages(kwargs.get('tag_pages')),
                tip_pages=cls.dumps_pages(kwargs.get('tip_pages')),
                created_by=g.user.id,
            )
            obj = db.session_add_and_commit(row)
            kwargs['id'] = obj.id
            kwargs['asset'] = _asset
            AdminOperationLog.new_add(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectOperation.AssetMaintain,
                detail=kwargs,
            )
            last_id = obj.id
        return dict(id=last_id)


@ns.route("/<int:id_>")
@respond_with_code
class AssetMaintainDetailResource(AssetMaintainMixin, Resource):
    model = AssetMaintain

    extra = dict(
        platforms=model.Platform,
        maintain_types=model.MaintainType,
        tag_pages=model.TagPage,
        tip_pages={i.name: i.value for i in TipBar.TriggerPage if i != TipBar.TriggerPage.ONCHAIN},
        jump_types=model.JumpType,
        statuses=model.Status,
        langs={
            x.name: y
            for x, y in language_cn_names().items()
            if x in TipBar.AVAILABLE_LANGS
        },
    )

    @classmethod
    def get(cls, id_):
        """运营-币种维护-详情"""
        extra = {
            "assets": list_all_assets(),
            **cls.extra,
        }
        if id_ == 0:
            return dict(extra=extra)

        model = cls.model
        c_model = AssetMaintainContent
        row = model.query.get(id_)
        if not row:
            raise InvalidArgument
        rows = c_model.query.filter(c_model.owner_id == id_).all()
        rows = {x.lang.name: x for x in rows}
        contents = {}
        has_content = False
        for lang in TipBar.AVAILABLE_LANGS:
            lang = lang.name
            r = rows.get(lang)
            if r:
                contents[lang] = dict(
                    lang=lang,
                    content=r.content,
                )
                has_content = True
            else:
                contents[lang] = dict(
                    lang=lang,
                    content="",
                )
        user_ids = {row.created_by}
        if row.audited_by:
            user_ids.add(row.audited_by)
        name_map = get_admin_user_name_map(user_ids)
        created_user_email = name_map.get(row.created_by) or '-'
        audited_user_email = name_map.get(row.audited_by) or '-'
        return dict(
            record=dict(
                id=row.id,
                asset=row.asset,
                started_at=row.started_at,
                ended_at=row.ended_at,
                audited_at=row.audited_at,
                created_at=row.created_at,
                platform=row.platform.name,
                maintain_type=row.maintain_type.name if row.maintain_type else None,
                tag_pages=row.get_tag_pages(),
                tip_pages=row.get_tip_pages(),
                jump_page_enabled=row.jump_page_enabled,
                jump_type=row.jump_type.name if row.jump_type else row.jump_type,
                jump_id=row.jump_id,
                auditor_remark=row.auditor_remark,
                status=row.status.name,
                has_content=has_content,
                created_by=row.created_by,
                audited_by=row.audited_by,
                created_user_email=created_user_email,
                audited_user_email=audited_user_email,
            ),
            contents=contents,
            extra=extra,
        )

    @classmethod
    @ns.use_kwargs(
        dict(
            asset=fields.String(required=True),
            platform=EnumField(model.Platform, required=True),
            started_at=TimestampField(required=True),
            ended_at=TimestampField(required=True),
            jump_page_enabled=fields.Boolean(missing=False),
            jump_type=EnumField(model.JumpType),
            jump_id=fields.Integer,

            maintain_type=EnumField(model.MaintainType, required=True, allow_none=True),
            tag_pages=fields.List(fields.String),
            tip_pages=fields.List(fields.String),
        )
    )
    def patch(cls, id_, **kwargs):
        """运营-币种维护-编辑"""
        model = cls.model
        row = model.query.get(id_)
        if not row:
            raise InvalidArgument
        old_data = row.to_dict(enum_to_name=True)

        if row.status is cls.model.Status.FINISHED:
            raise OperationDenied(message="cannot edit a finished item")

        if row.status is cls.model.Status.AUDITED:
            if row.started_at <= now():
                raise OperationDenied(message="cannot edit a running item")

        cls.validate_params_and_business(kwargs, id_)
        row.asset = kwargs["asset"]
        row.started_at = kwargs["started_at"]
        row.ended_at = kwargs["ended_at"]
        row.platform = kwargs["platform"]
        row.jump_page_enabled = kwargs["jump_page_enabled"]
        row.jump_type = kwargs.get("jump_type")
        row.jump_id = kwargs.get("jump_id")
        row.maintain_type = kwargs.get("maintain_type")
        row.tag_pages = cls.dumps_pages(kwargs.get('tag_pages'))
        row.tip_pages = cls.dumps_pages(kwargs.get('tip_pages'))
        if row.status in [
            cls.model.Status.AUDITED,
            cls.model.Status.REJECTED
        ]:
            # 失败，审核，拒绝状态下重新编辑后变为待审核
            row.status = cls.model.Status.CREATED
        db.session.commit()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.AssetMaintain,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
        )
        return {}

    @classmethod
    def delete(cls, id_):
        """运营-币种维护-删除"""
        model = cls.model
        row = model.query.get(id_)
        if not row:
            raise InvalidArgument
        row.status = model.Status.DELETED
        db.session.commit()

        AdminOperationLog.new_delete(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.TipBar,
            detail=dict(id=id_, name=row.asset),
        )
        TipBarHelper.auto_delete_tip_bars(row)
        return {}


@ns.route("/<int:id_>/langs/<lang>")
@respond_with_code
class AssetMaintainContentResource(AssetMaintainMixin, Resource):
    model = AssetMaintain

    @classmethod
    @ns.use_kwargs(
        dict(
            content=fields.String(missing=""),
        )
    )
    def put(cls, id_, lang, **kwargs):
        """运营-币种维护-编辑内容"""
        model = cls.model
        row = model.query.get(id_)
        if not row:
            raise InvalidArgument
        if lang not in [e.name for e in TipBar.AVAILABLE_LANGS]:
            raise InvalidArgument

        c_model = AssetMaintainContent
        c_row = c_model.query.filter(
            c_model.owner_id == id_, c_model.lang == lang
        ).first()
        if c_row:
            old_data = c_row.to_dict(enum_to_name=True)
            c_row.content = kwargs["content"]

            AdminOperationLog.new_edit(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectOperation.AssetMaintainContent,
                old_data=old_data,
                new_data=c_row.to_dict(enum_to_name=True),
                special_data=dict(tip_bar_id=id_, lang=lang),
            )
        else:
            c_row = c_model(
                owner_id=id_,
                lang=lang,
                content=kwargs["content"],
            )
            db.session.add(c_row)

            AdminOperationLog.new_add(
                user_id=g.user.id,
                ns_obj=OPNamespaceObjectOperation.AssetMaintainContent,
                detail=c_row.to_dict(enum_to_name=True),
            )
        db.session.commit()


@ns.route("/batch-asset-maintain-content")
@respond_with_code
class BatchAssetMaintainContentResource(AssetMaintainMixin, Resource):
    model = AssetMaintain

    @classmethod
    @ns.use_kwargs(
        dict(
            ids=fields.List(fields.Integer, required=True),
            template_id=fields.Integer(required=True),
            remark=fields.String(),
        )
    )
    def post(cls, **kwargs):
        """运营-币种维护-批量应用模板"""
        ids = kwargs["ids"]
        template_id = kwargs["template_id"]
        remark = kwargs.get("remark", "")
        _c_model = OperationTemplate

        _c_obj = _c_model.query.filter(
            _c_model.id == template_id,
            _c_model.status == _c_model.Status.VALID,
            # 币种维护使用tip_bar的模板
            _c_model.business == _c_model.Business.TIP_BAR
        ).first()
        if not _c_obj:
            raise InvalidArgument(message=f"模板{template_id}不存在")
        # 校验传入的id
        q = AssetMaintain.query.filter(
            AssetMaintain.id.in_(ids),
            AssetMaintain.status.in_([
                AssetMaintain.Status.CREATED,
                AssetMaintain.Status.DRAFT,
                ]
            )
        ).with_entities(AssetMaintain.id, AssetMaintain.status).all()
        id_status_map = {v.id: v.status for v in q}
        if set(ids) != set(id_status_map.keys()):
            raise InvalidArgument(message=f"传入的配置信息不为待审核和未提交，请检查")
        template_query = OperationTemplateContent.query.filter(
            OperationTemplateContent.template_id == template_id,
            OperationTemplateContent.lang.in_(TipBar.AVAILABLE_LANGS)
        ).all()
        template_map = {
            v.lang: v.content
            for v in template_query
        }
        AssetMaintainContent.query.filter(
            AssetMaintainContent.owner_id.in_(ids)
        ).delete()
        for lang in TipBar.AVAILABLE_LANGS:
            if lang in template_map:
                for _id in ids:
                    db.session.add(
                        AssetMaintainContent(
                            owner_id=_id,
                            lang=lang,
                            content=template_map[lang])
                    )
        db.session.commit()
        details = {
            "ids": ids,
            "template_id": template_id,
            "remark": remark,
            "batch_contents": {lang.value: v for lang, v in template_map.items()}
        }
        AdminOperationLog.new_add(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.BatchAssetMaintainContent,
            detail=details,
        )
        return {}


@ns.route("/<int:id_>/created")
@respond_with_code
class AssetMaintainCreated(Resource):
    model = AssetMaintain

    @classmethod
    def patch(cls, id_):
        """运营-触达管理-币种维护-提交审核"""
        row = cls.model.query.get(id_)
        if row is None:
            raise RecordNotFound
        old_data = row.to_dict(enum_to_name=True)

        if row.status is not cls.model.Status.DRAFT:
            raise OperationDenied(message="only draft status item can be edit")

        # c_model = AssetMaintainContent
        # if c_model.query.filter(c_model.owner_id == id_).count() == 0:
        #     raise OperationDenied(message="提示内容未配置,不可提交审核")
        row.status = cls.model.Status.CREATED
        db.session.commit()
        row_dict = row.to_dict(enum_to_name=True)
        row_dict["tag_pages"] = row.get_tag_pages()
        row_dict["tip_pages"] = row.get_tip_pages()

        AdminOperationLog.new_edit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.AssetMaintain,
            old_data=old_data,
            new_data=row.to_dict(enum_to_name=True),
            special_data=dict(asset=row.asset),
        )
        return row_dict


@ns.route("/<int:id_>/audit")
@respond_with_code
class AssetMaintainAudit(AssetMaintainMixin, Resource):
    model = AssetMaintain

    @classmethod
    @ns.use_kwargs(
        dict(
            status=EnumField(model.Status, required=True),
            auditor_remark=fields.String,
        )
    )
    def patch(cls, id_, **kwargs):
        """运营-触达管理-币种维护-审核"""
        row = cls.model.query.get(id_)
        if row is None:
            raise RecordNotFound

        params = Struct(**kwargs)
        if params.status not in (cls.model.Status.AUDITED, cls.model.Status.REJECTED):
            raise OperationDenied(message=f"invalid status {params.status!r}")

        if row.status is not cls.model.Status.CREATED:
            raise OperationDenied(message="only created status item can be audit")

        user_id: int = g.user.id
        if g.user.id == row.created_by:
            raise OperationDenied(message='内容审核人不得与内容创建人重复！')

        row.status = params.status
        row.audited_by = user_id
        row.audited_at = now()
        row.auditor_remark = params.auditor_remark or ""
        db.session.commit()

        row_dict = row.to_dict(enum_to_name=True)
        if row.status is cls.model.Status.AUDITED:
            if row.started_at <= now():
                row_dict["status"] = "RUNNING"
            else:
                row_dict["status"] = "PENDING"

        row_dict["tag_pages"] = row.get_tag_pages()
        row_dict["tip_pages"] = row.get_tip_pages()

        kwargs['id'] = id_
        AdminOperationLog.new_audit(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.AssetMaintain,
            detail=kwargs,
        )
        if row.status is cls.model.Status.AUDITED:
            TipBarHelper.auto_gen_tip_bars(row)
        return row_dict


@ns.route("/<int:id_>/offline")
@respond_with_code
class AssetMaintainOfflineResource(AssetMaintainMixin, Resource):
    model = AssetMaintain

    @classmethod
    def patch(cls, id_):
        """运营-触达管理-币种维护-下架"""
        row = cls.model.query.get(id_)
        if row is None:
            raise RecordNotFound

        if row.status is not cls.model.Status.AUDITED:
            raise OperationDenied(message="only running status item can be stopped")
        now_ = now()
        if row.started_at > now_:
            raise OperationDenied(message="only running status item can be stopped")

        row.status = cls.model.Status.FINISHED
        row.ended_at = now_
        db.session.commit()

        row_dict = row.to_dict(enum_to_name=True)
        row_dict["tag_pages"] = row.get_tag_pages()
        row_dict["tip_pages"] = row.get_tip_pages()

        AdminOperationLog.new_stop(
            user_id=g.user.id,
            ns_obj=OPNamespaceObjectOperation.AssetMaintain,
            detail=dict(id=id_, name=row.asset),
        )
        TipBarHelper.auto_offline_tip_bars(row)
        return row_dict
