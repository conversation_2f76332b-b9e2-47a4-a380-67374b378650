
from webargs import fields

from app import Language
from app.api.common import Namespace, respond_with_code, Resource
from app.api.common.fields import PageField, LimitField, DateField, EnumField
from app.business.report.refer import get_refer_page_data
from app.common import ReportType, ADMIN_EXPORT_LIMIT
from app.models import DailyReferTypeReport, MonthlyReferTypeReport, \
    DailyReferReport, MonthlyReferReport, AssetPrice, DailyNormalReferReport, MonthlyNormalReferReport, \
    User, DailyUserReferralDetailReport, MonthlyUserReferralDetailReport
from app.utils import export_xlsx, batch_iter, format_percent

ns = Namespace('Report - Refer')


@ns.route('/type')
@respond_with_code
class ReferTypeReport(Resource):

    export_headers_referral = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "referrer_count", Language.ZH_HANS_CN: "邀请人"},
        {"field": "new_referrer_count", Language.ZH_HANS_CN: "新增邀请人"},
        {"field": "total_invitee", Language.ZH_HANS_CN: "refer总人数"},
        {"field": "invitee_count", Language.ZH_HANS_CN: "refer注册用户"},
        {"field": "trade_count", Language.ZH_HANS_CN: "refer交易人数"},
        {"field": "new_trade_count", Language.ZH_HANS_CN: "新增refer交易人数"},
        {"field": "trade_usd", Language.ZH_HANS_CN: "refer交易总额（USD）"},
        {"field": "fee_usd", Language.ZH_HANS_CN: "refer贡献手续费（USD）"},
        {"field": "refer_count", Language.ZH_HANS_CN: "返佣人数"},
        {"field": "refer_amount", Language.ZH_HANS_CN: "返佣数量（CET）"},
        {"field": "refer_usd", Language.ZH_HANS_CN: "返佣金额（USD）"},
        {"field": "refer_rate", Language.ZH_HANS_CN: "返佣比例"},
    )

    export_headers_ambassador = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "total_invitee", Language.ZH_HANS_CN: "refer总人数"},
        {"field": "refer_user_balance_usd", Language.ZH_HANS_CN: "refer用户总资产"},
        {"field": "refer_active_user_count", Language.ZH_HANS_CN: "refer活跃用户"},
        {"field": "invitee_count", Language.ZH_HANS_CN: "refer注册人数"},
        {"field": "trade_count", Language.ZH_HANS_CN: "refer交易人数"},
        {"field": "new_trade_count", Language.ZH_HANS_CN: "新增refer交易人数"},
        {"field": "trade_usd", Language.ZH_HANS_CN: "refer交易总额（USD）"},
        {"field": "trade_spot_usd", Language.ZH_HANS_CN: "refer现货交易额（USD）"},
        {"field": "trade_perpetual_usd", Language.ZH_HANS_CN: "refer合约交易额（USD）"},
        {"field": "fee_usd", Language.ZH_HANS_CN: "refer贡献手续费（USD）"},
        {"field": "refer_count", Language.ZH_HANS_CN: "返佣人数"},
        {"field": "refer_amount", Language.ZH_HANS_CN: "返佣数量（USDT）"},
        {"field": "refer_usd", Language.ZH_HANS_CN: "返佣金额（USD）"},
        {"field": "refer_rate", Language.ZH_HANS_CN: "返佣比例"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        report_type=fields.String,
        type=fields.String,
        start_date=DateField(to_date=True),
        end_date=DateField(to_date=True),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50),
        export=fields.Boolean(missing=False),
    ))
    def get(cls, **kwargs):
        """报表-返佣报表-类型返佣"""

        page = kwargs['page']
        limit = kwargs['limit']
        type_ = kwargs['type']

        attr_columns = [
            "report_date",
            "type",
            "asset",
            "trade_count",
            "new_trade_count",
            "trade_usd",
            "trade_spot_usd",
            "trade_perpetual_usd",
            "fee_usd",
            "refer_count",
            "refer_amount",
            "refer_usd",
            "refer_rate",
            "referrer_count",
            "new_referrer_count",
            "invitee_count",
            "total_invitee",
            "invitee_percent",
            "trade_percent",
            "new_trade_percent",
            "type_user_count",
            "refer_user_balance_usd",
            "refer_active_user_count",
        ]

        if kwargs['report_type'] == ReportType.DAILY.value:
            model = DailyReferTypeReport
        else:
            model = MonthlyReferTypeReport
        query = model.query.filter(model.type == model.Type(type_)).order_by(
            model.report_date.desc()
        ).with_entities(*[getattr(model, i) for i in attr_columns])

        if start_date := kwargs.get('start_date'):
            query = query.filter(model.report_date >= start_date)
        if end_date := kwargs.get('end_date'):
            query = query.filter(model.report_date < end_date)

        if kwargs['export']:
            items = list(query.limit(ADMIN_EXPORT_LIMIT).all())
        else:
            pagination = query.paginate(page, limit, error_out=False)
            items = list(pagination.items)
            total = pagination.total
            page = pagination.page

        if kwargs['export']:
            data_list = [dict(i) for i in items]
            for item in data_list:
                item["refer_rate"] = format_percent(item["refer_rate"])
            if type_ == model.Type.REFERRAL.value:
                headers = cls.export_headers_referral
            elif type_ == model.Type.AMBASSADOR.value:
                headers = cls.export_headers_ambassador
            return export_xlsx(
                filename=f'user_{type_}_report',
                data_list=data_list,
                export_headers=headers
            )
        return dict(
            records=[{i: getattr(item, i) for i in attr_columns} for item in items],
            total=total,
            page=page,
        )


@ns.route('/proportion')
@respond_with_code
class ReferProportionReport(Resource):
    @classmethod
    @ns.use_kwargs(dict(
        report_type=fields.String,
        start_date=DateField(to_date=True),
        end_date=DateField(to_date=True),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50)
    ))
    def get(cls, **kwargs):
        """报表-返佣报表-返佣占比"""

        page = kwargs['page']
        limit = kwargs['limit']

        if kwargs['report_type'] == ReportType.DAILY.value:
            model = DailyReferReport
        else:
            model = MonthlyReferReport
        query = model.query.order_by(model.report_date.desc())
        if start_date := kwargs.get('start_date'):
            query = query.filter(model.report_date >= start_date)
        if end_date := kwargs.get('end_date'):
            query = query.filter(model.report_date < end_date)
        pagination = query.paginate(page, limit, error_out=False)

        records = []
        for item in pagination.items:
            price_map = AssetPrice.get_close_price_map(item.report_date)
            refer_usd = item.refer_amount * price_map['CET']
            ambassador_usd = item.ambassador_amount * price_map['USDT']
            ambassador_agent_usd = item.ambassador_agent_amount * price_map['USDT']
            total_usd = refer_usd + ambassador_usd + ambassador_agent_usd
            record = {
                'report_date': item.report_date,
                'total_usd': total_usd,
                'refer_usd': refer_usd,
                'refer_rate': refer_usd / total_usd if total_usd else 0,
                'ambassador_usd': ambassador_usd,
                'ambassador_rate': ambassador_usd / total_usd if total_usd else 0,
                'ambassador_agent_usd': ambassador_agent_usd,
                'ambassador_agent_rate': ambassador_agent_usd / total_usd if total_usd else 0,
            }
            records.append(record)

        return dict(
            records=records,
            total=pagination.total,
            page=pagination.page,
        )


@ns.route('/normal_refer')
@respond_with_code
class NormalReferReport(Resource):
    export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "referrer_count", Language.ZH_HANS_CN: "邀请人"},
        {"field": "new_referrer_count", Language.ZH_HANS_CN: "新增邀请人"},
        {"field": "new_referrer_rate", Language.ZH_HANS_CN: "新增邀请人占比"},
        {"field": "invitee_count", Language.ZH_HANS_CN: "refer注册用户"},
        {"field": "invitee_rate", Language.ZH_HANS_CN: "refer注册用户占比"},
        {"field": "trade_invitee_count", Language.ZH_HANS_CN: "refer交易用户"},
        {"field": "trade_invitee_rate", Language.ZH_HANS_CN: "refer交易用户占比"},
        {"field": "new_trade_invitee_count", Language.ZH_HANS_CN: "新增refer交易用户"},
        {"field": "new_trade_invitee_rate", Language.ZH_HANS_CN: "新增refer交易用户占比"},
        {"field": "invitee_trade_usd", Language.ZH_HANS_CN: "refer交易额(USD)"},
        {"field": "invitee_trade_rate", Language.ZH_HANS_CN: "refer交易额占比"},
        {"field": "invitee_fee_usd", Language.ZH_HANS_CN: "refer交易手续费(USD)"},
        {"field": "invitee_fee_rate", Language.ZH_HANS_CN: "refer交易手续费占比"}
    )

    @classmethod
    @ns.use_kwargs(dict(
        report_type=fields.String(required=True, validate=lambda x: x in ["DAILY", "MONTHLY"]),
        start_date=DateField(to_date=True),
        end_date=DateField(to_date=True),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50),
        export=fields.Boolean(missing=False),
    ))
    def get(cls, **kwargs):
        """报表-普通邀请报表"""
        page = kwargs['page']
        limit = kwargs['limit']

        model_map = {
            'DAILY': DailyNormalReferReport,
            'MONTHLY': MonthlyNormalReferReport,
        }

        model = model_map[kwargs['report_type']]
        query = model.query.order_by(model.report_date.desc())
        if start_date := kwargs.get('start_date'):
            query = query.filter(model.report_date >= start_date)
        if end_date := kwargs.get('end_date'):
            query = query.filter(model.report_date < end_date)

        if kwargs['export']:
            return export_xlsx(
                filename='normal_refer_report',
                data_list=[cls.add_rate_field(i) for i in query],
                export_headers=cls.export_headers
            )
        else:
            pagination = query.paginate(page, limit, error_out=False)
            records = [cls.add_rate_field(i) for i in pagination.items]
            return dict(
                records=records,
                total=pagination.total,
                page=pagination.page,
            )

    @classmethod
    def add_rate_field(cls, row):

        def safe_percent_format(a, b):
            return f'{a / b * 100 if b else 0:.2f}%'

        rate_dict = {
            # 新增邀请人占比
            "new_referrer_rate": safe_percent_format(row.new_referrer_count, row.referrer_count),
            # refer 注册用户占比
            "invitee_rate": safe_percent_format(row.invitee_count, row.increase_user_count),
            # refer交易用户占比
            "trade_invitee_rate": safe_percent_format(row.trade_invitee_count, row.trade_user_count),
            # 新增refer交易用户占比
            "new_trade_invitee_rate": safe_percent_format(row.new_trade_invitee_count, row.increase_trade_user_count),
            # refer交易额占比
            "invitee_trade_rate": safe_percent_format(row.invitee_trade_usd, row.site_trade_usd),
            # refer交易手续费占比
            "invitee_fee_rate": safe_percent_format(row.invitee_fee_usd, row.site_fee_usd),
        }
        record = row.to_dict()
        record.update(rate_dict)
        return record


@ns.route('/normal-user-refer-report')
@respond_with_code
class UserReferralDetailReportResource(Resource):
    export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "email", Language.ZH_HANS_CN: "邮箱"},
        {"field": "refer_count", Language.ZH_HANS_CN: "推荐人数"},
        {"field": "deposit_count", Language.ZH_HANS_CN: "被邀请人充值人数"},
        {"field": "deal_count", Language.ZH_HANS_CN: "被邀请人交易人数"},
        {"field": "spot_trade_usd", Language.ZH_HANS_CN: "被邀请人币币交易量（USD）"},
        {"field": "perpetual_trade_usd", Language.ZH_HANS_CN: "被邀请人合约交易量（USD）"},
        {"field": "spot_fee_usd", Language.ZH_HANS_CN: "被邀请人币币手续费（USD）"},
        {"field": "perpetual_fee_usd", Language.ZH_HANS_CN: "被邀请人合约手续费（USD）"},
        {"field": "refer_total_amount", Language.ZH_HANS_CN: "返佣数量（CET）"},
        {"field": "refer_total_usd", Language.ZH_HANS_CN: "返佣金额（USD）"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        report_type=EnumField(ReportType, enum_by_value=True, required=True),
        start_date=fields.DateTime(format="%Y-%m-%d"),
        end_date=fields.DateTime(format="%Y-%m-%d"),
        user_id=fields.Integer,
        page=PageField(unlimited=True),
        limit=LimitField(missing=50),
        export=fields.Boolean,
    ))
    def get(cls, **kwargs):
        """ 用户-邀请返佣-普通返佣记录 """
        if kwargs["report_type"] == ReportType.DAILY:
            query_model = DailyUserReferralDetailReport
            is_month = False
        else:
            query_model = MonthlyUserReferralDetailReport
            is_month = True
        query = cls.get_filter_query(query_model, kwargs)

        if kwargs.get("export"):
            export_rows = query.limit(ADMIN_EXPORT_LIMIT).all()
            return export_xlsx(
                filename='normal_user_refer_detail_list',
                data_list=cls.format_items(export_rows, is_export=True, is_month=is_month),
                export_headers=cls.export_headers,
            )

        count, total_data, page_rows = get_refer_page_data(
            kwargs, query, DailyUserReferralDetailReport, cls.get_filter_query
        )
        return dict(
            count=count,
            total_data=total_data,
            items=cls.format_items(page_rows, is_export=False, is_month=is_month)
        )

    @classmethod
    def format_items(cls, items: list, is_export: bool = False, is_month: bool = False) -> list:
        user_ids = {i.user_id for i in items}
        user_email_map = {}
        for ids_ in batch_iter(user_ids, 1000):
            chunk_users = User.query.filter(User.id.in_(ids_)).with_entities(User.id, User.email).all()
            user_email_map.update(dict(chunk_users))

        dt_fmt = "%Y-%m" if is_month else "%Y-%m-%d"
        records = []
        for item in items:
            record = {
                'report_date': item.report_date.strftime(dt_fmt) if is_export else item.report_date,
                'user_id': item.user_id,
                'email': user_email_map.get(item.user_id, ""),
                'refer_count': item.refer_count,
                'deal_count': item.deal_count,
                'deposit_count': item.deposit_count,
                'spot_trade_usd': item.spot_trade_usd,
                'perpetual_trade_usd': item.perpetual_trade_usd,
                'spot_fee_usd': item.spot_fee_usd,
                'perpetual_fee_usd': item.perpetual_fee_usd,
                'refer_total_amount': item.refer_total_amount,
                'refer_total_usd': item.refer_total_usd,
            }
            records.append(record)
        return records

    @classmethod
    def get_filter_query(cls, query_model, kwargs):
        query = query_model.query.order_by(query_model.report_date.desc())
        if start_date := kwargs.get("start_date"):
            query = query.filter(query_model.report_date >= start_date)
        if end_date := kwargs.get("end_date"):
            query = query.filter(query_model.report_date <= end_date)
        if user_id := kwargs.get("user_id"):
            query = query.filter(query_model.user_id == user_id)
        return query
