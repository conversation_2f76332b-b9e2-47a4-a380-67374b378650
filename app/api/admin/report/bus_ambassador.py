from collections import defaultdict
import datetime
import json
from decimal import Decimal

from enum import Enum

from flask import g
from flask_restx import fields as fx_fields, marshal
from sqlalchemy import tuple_

from app.api.common import Namespace, Resource, \
    respond_with_code
from app.api.common.fields import EnumField, PageField, LimitField
from app.business.report.refer import get_refer_page_data, modify_end_date_kwarg
from app.business.auth import is_super_user, get_admin_user_name_map
from app.business.user import UserRepository
from app.common import ReportType, ADMIN_EXPORT_LIMIT
from marshmallow import fields as mm_fields

from app.models import (
    DailyBusinessAmbassadorReferralReport,
    DailyTreeAmbassadorReferralReport,
    DailyBusinessAgentReferralReport,
    DailyBusinessUserReferralReport,
    DailyBusinessTeamReferralReport,
    DailyBusinessPrReferralReport,
    DailyBusinessAmbassadorTotalReferralReport,
    MonthlyBusinessAmbassadorReferralReport,
    MonthlyTreeAmbassadorReferralReport,
    MonthlyBusinessAgentReferralReport,
    MonthlyBusinessUserReferralReport,
    MonthlyBusinessTeamReferralReport,
    MonthlyBusinessPrReferralReport,
    MonthlyBusinessAmbassadorTotalReferralReport,
    BusinessAmbassadorPermission,
    BusinessUser,
    BusinessAmbassador,
    TreeAmbassador,
    TreeAmbassadorHierarchy,
    Language,
    User, BusinessTeam
)
from app.models.daily import DailyBusinessAmbassadorDetailReferralReport
from app.models.monthly import MonthlyBusinessAmbassadorDetailReferralReport
from app.models.quarterly import QuarterlyBusinessAmbassadorTotalReferralReport
from app.models.weekly import WeeklyBusinessAmbassadorTotalReferralReport
from app.utils import export_xlsx, amount_to_str, format_percent, batch_iter
from app.utils.date_ import cur_quarter, cur_week


ns = Namespace('Report - Business - Ambassador')
url_prefix = '/bus-amb'


def get_visible_bus_users(user_id: int) -> list[BusinessUser]:
    # 商务在列表里默认能看到自己邀请的大使，默认能新增大使，能看到自己邀请大使的详情并对自己邀请大使相关内容进行编辑
    # 组长在列表默认能看到本组商务邀请的大使，默认能新增大使，能看到本组商务邀请大使的详情并对本组商务邀请大使相关内容进行编辑
    # 可以又是组长又是商务
    from app.api.admin.bus_ambassador import BusTeamsResource

    bus_users = []
    leader_own_teams = BusTeamsResource.get_teams_by_leader(user_id)
    if leader_own_teams:
        # 是组长
        bus_users = BusinessUser.query.filter(
            BusinessUser.team_id.in_([i.id for i in leader_own_teams]),
        ).order_by(BusinessUser.id.desc()).all()
    # 判断是普通商务
    cur_bus_user = BusinessUser.query.filter(
        BusinessUser.user_id == user_id
    ).first()
    if cur_bus_user and user_id not in {i.user_id for i in bus_users}:
        bus_users.append(cur_bus_user)
    return bus_users


def get_visible_amb_ids(user_id: int, team_id: int = None) -> list[int]:
    bus_users = get_visible_bus_users(user_id)
    if not bus_users:
        return []

    bus_ids = {item.user_id for item in bus_users}
    if team_id:
        q_bus_users = BusinessUser.query.filter(
            BusinessUser.team_id == team_id,
        ).order_by(BusinessUser.id.desc()).all()
        bus_ids = bus_ids & {item.user_id for item in q_bus_users}
    bus_amb_rows = BusinessAmbassador.query.filter(
        BusinessAmbassador.bus_user_id.in_(bus_ids),
    ).order_by(BusinessAmbassador.id.desc()).with_entities(
        BusinessAmbassador.user_id,
        BusinessAmbassador.type,
    ).all()
    visible_amb_ids = [i.user_id for i in bus_amb_rows]
    root_ids = [r.user_id for r in bus_amb_rows if r.type == BusinessAmbassador.Type.TREE_ROOT]
    if root_ids:
        tree_amb_rows = TreeAmbassador.query.filter(
            TreeAmbassador.root_id.in_(root_ids),
        ).with_entities(
            TreeAmbassador.user_id,
        ).all()
        visible_amb_ids.extend([i.user_id for i in tree_amb_rows])
    return visible_amb_ids


@ns.route('/ambassador-referral-report')
@respond_with_code
class BusinessAmbassadorReferralReportResource(Resource):
    export_fields = {
        'user_id': fx_fields.Integer,
        'email': fx_fields.String(attribute=lambda x: x.user.email),
        'bus_user_id': fx_fields.Integer,
        'bus_user_name': fx_fields.String,
        'report_date': fx_fields.Date,
        'deal_count': fx_fields.Integer,
        'deposit_count': fx_fields.Integer,
        'refer_count': fx_fields.Integer,
        'perpetual_fee_usd': fx_fields.Float,
        'spot_fee_usd': fx_fields.Float,
        'perpetual_trade_usd': fx_fields.Float,
        'spot_trade_usd': fx_fields.Float,
        'refer_total_amount': fx_fields.Float,
    }

    export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "email", Language.ZH_HANS_CN: "邮箱"},
        {"field": "bus_user_name", Language.ZH_HANS_CN: "所属商务"},
        {"field": "refer_count", Language.ZH_HANS_CN: "推荐人数"},
        {"field": "deposit_count", Language.ZH_HANS_CN: "被邀请人充值人数"},
        {"field": "deal_count", Language.ZH_HANS_CN: "被邀请人交易人数"},
        {"field": "spot_trade_usd", Language.ZH_HANS_CN: "被邀请人币币交易量（USD）"},
        {"field": "perpetual_trade_usd", Language.ZH_HANS_CN: "被邀请人合约交易量（USD）"},
        {"field": "spot_fee_usd", Language.ZH_HANS_CN: "被邀请人币币手续费（USD）"},
        {"field": "perpetual_fee_usd", Language.ZH_HANS_CN: "被邀请人合约手续费（USD）"},
        {"field": "refer_total_amount", Language.ZH_HANS_CN: "大使返佣（USDT）"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        report_type=EnumField(ReportType, enum_by_value=True, required=True),
        start_date=mm_fields.DateTime(format="%Y-%m-%d"),
        end_date=mm_fields.DateTime(format="%Y-%m-%d"),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50),
        keyword=mm_fields.String(),
        bus_user_id=mm_fields.Integer(),
        export=mm_fields.Boolean,
    ))
    def get(cls, **kwargs):
        """
        用户-商务大使体系-商务大使返佣记录
        """
        report_type = kwargs["report_type"]
        if report_type == ReportType.DAILY:
            query_model = DailyBusinessAmbassadorReferralReport
        else:
            query_model = MonthlyBusinessAmbassadorReferralReport
        query = cls.get_filter_query(query_model, kwargs)

        if kwargs.get("export"):
            data_list = marshal(query.limit(ADMIN_EXPORT_LIMIT).all(), cls.export_fields)
            cls.fill_bus_user_info(data_list)
            return export_xlsx(
                filename='coinex_business_ambassador_detail_list',
                data_list=data_list,
                export_headers=cls.export_headers
            )

        count, total_data, page_rows = get_refer_page_data(
            kwargs, query, DailyBusinessAmbassadorReferralReport, cls.get_filter_query
        )
        items = marshal(page_rows, cls.export_fields)
        cls.fill_bus_user_info(items)
        return dict(
            count=count,
            total_data=total_data,
            items=items,
        )

    @classmethod
    def fill_bus_user_info(cls, items: list[dict]) -> list[dict]:
        amb_ids = {i['user_id'] for i in items}
        amb_rows = BusinessAmbassador.query.filter(
            BusinessAmbassador.user_id.in_(amb_ids),
        ).with_entities(
            BusinessAmbassador.user_id,
            BusinessAmbassador.bus_user_id,
        ).all()
        amb_id_bus_id_map = dict(amb_rows)
        miss_amb_ids = set(amb_ids) - set(amb_id_bus_id_map)
        if miss_amb_ids:
            tree_amb_rows = TreeAmbassador.query.filter(
                TreeAmbassador.user_id.in_(miss_amb_ids),
            ).with_entities(
                TreeAmbassador.user_id,
                TreeAmbassador.root_id,
            ).all()
            amb_id_root_id_map = dict(tree_amb_rows)
            root_amb_rows = BusinessAmbassador.query.filter(
                BusinessAmbassador.user_id.in_(list(amb_id_root_id_map.values())),
            ).with_entities(
                BusinessAmbassador.user_id,
                BusinessAmbassador.bus_user_id,
            ).all()
            root_id_bus_id_map = dict(root_amb_rows)
        else:
            amb_id_root_id_map = {}
            root_id_bus_id_map = {}

        bus_ids = {v for v in amb_id_bus_id_map.values() if v} | {v for v in root_id_bus_id_map.values() if v}
        bus_user_email_map = {}
        for ch_bus_ids in batch_iter(bus_ids, 1000):
            ch_user_rows = User.query.filter(
                User.id.in_(ch_bus_ids)
            ).with_entities(User.id, User.email).all()
            bus_user_email_map.update(dict(ch_user_rows))
        for i in items:
            if i['user_id'] in amb_id_bus_id_map:
                bus_user_id = amb_id_bus_id_map.get(i['user_id'])
            elif i['user_id'] in amb_id_root_id_map:
                bus_user_id = root_id_bus_id_map.get(amb_id_root_id_map[i['user_id']])
            else:
                bus_user_id = None
            i['bus_user_id'] = bus_user_id
            i['bus_user_name'] = bus_user_email_map.get(i['bus_user_id'], '')
        return items

    @classmethod
    def get_filter_query(cls, query_model, kwargs):
        _permission = kwargs.get('permission', BusinessAmbassadorPermission.Type.BUS_AMB_REPORT)
        permission_limit = BusinessAmbassadorPermission.is_limit_visible(
            g.user.id,
            _permission
        )
        visible_amb_ids = get_visible_amb_ids(g.user.id, kwargs.get("team_id"))
        limit_visible = permission_limit and not is_super_user(g.user.id)

        query = query_model.query.order_by(
            query_model.report_date.desc()
        )
        if limit_visible:
            query = query.filter(
                query_model.user_id.in_(visible_amb_ids)
            )
        if kwargs.get("bus_user_id"):
            filter_amb_rows = BusinessAmbassador.query.filter(
                BusinessAmbassador.bus_user_id == kwargs['bus_user_id'],
            ).with_entities(
                BusinessAmbassador.user_id,
                BusinessAmbassador.type,
            ).all()
            filter_amb_ids = {i.user_id for i in filter_amb_rows}
            q_root_ids = [i.user_id for i in filter_amb_rows if i.type == BusinessAmbassador.Type.TREE_ROOT]
            if q_root_ids:
                q_tree_amb_rows = TreeAmbassador.query.filter(
                    TreeAmbassador.root_id.in_(q_root_ids),
                ).with_entities(
                    TreeAmbassador.user_id,
                ).all()
                filter_amb_ids.update([i.user_id for i in q_tree_amb_rows])
            query = query.filter(
                query_model.user_id.in_(filter_amb_ids)
            )
        if kwargs.get("start_date"):
            query = query.filter(
                query_model.report_date >= kwargs["start_date"]
            )
        if kwargs.get("end_date"):
            query = query.filter(
                query_model.report_date <= kwargs["end_date"]
            )

        if keyword := kwargs.get("keyword"):
            keyword_results = User.search_for_users(keyword)
            query = query.filter(
                query_model.user_id.in_(keyword_results)
            )
        return query


@ns.route('/tree-amb-referral-report')
@respond_with_code
class TreeAmbassadorReferralReportResource(BusinessAmbassadorReferralReportResource):
    export_fields = {
        'user_id': fx_fields.Integer,
        'email': fx_fields.String,
        'bus_user_id': fx_fields.Integer,
        'bus_user_name': fx_fields.String,
        'tree_height': fx_fields.String,
        'report_date': fx_fields.Date,
        'deal_count': fx_fields.Integer,
        'deposit_count': fx_fields.Integer,
        'refer_count': fx_fields.Integer,
        'perpetual_fee_usd': fx_fields.Float,
        'spot_fee_usd': fx_fields.Float,
        'perpetual_trade_usd': fx_fields.Float,
        'spot_trade_usd': fx_fields.Float,
        'refer_total_amount': fx_fields.Float,
        'parent_refer_list': fx_fields.String,
    }

    export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "email", Language.ZH_HANS_CN: "子代理"},
        {"field": "bus_user_name", Language.ZH_HANS_CN: "所属商务"},
        {"field": "tree_height", Language.ZH_HANS_CN: "代理级别"},
        {"field": "refer_count", Language.ZH_HANS_CN: "推荐人数"},
        {"field": "deal_count", Language.ZH_HANS_CN: "被邀请人交易人数"},
        {"field": "spot_trade_usd", Language.ZH_HANS_CN: "被邀请人币币交易量（USD）"},
        {"field": "perpetual_trade_usd", Language.ZH_HANS_CN: "被邀请人合约交易量（USD）"},
        {"field": "spot_fee_usd", Language.ZH_HANS_CN: "被邀请人币币手续费（USD）"},
        {"field": "perpetual_fee_usd", Language.ZH_HANS_CN: "被邀请人合约手续费（USD）"},
        {"field": "refer_total_amount", Language.ZH_HANS_CN: "自己收到返佣（USDT）"},
        {"field": "parent_refer_list", Language.ZH_HANS_CN: "返佣链路（USDT）"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        report_type=EnumField(ReportType, enum_by_value=True, required=True),
        start_date=mm_fields.DateTime(format="%Y-%m-%d"),
        end_date=mm_fields.DateTime(format="%Y-%m-%d"),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50),
        keyword=mm_fields.String(),
        parent_id=mm_fields.Integer(),
        bus_user_id=mm_fields.Integer(),
        tree_height=mm_fields.Integer(),
        team_id=mm_fields.Integer(),
        export=mm_fields.Boolean,
    ))
    def get(cls, **kwargs):
        """用户-商务大使体系-子代理返佣记录"""
        page, limit = kwargs["page"], kwargs["limit"]
        report_type = kwargs["report_type"]
        if report_type == ReportType.DAILY:
            query_model = DailyTreeAmbassadorReferralReport
            base_data_model = DailyBusinessAmbassadorReferralReport
        else:
            query_model = MonthlyTreeAmbassadorReferralReport
            base_data_model = MonthlyBusinessAmbassadorReferralReport

        kwargs['permission'] = BusinessAmbassadorPermission.Type.TREE_AMB_REPORT
        query = cls.get_tree_filter_query(query_model, kwargs)
        if kwargs.get('start_date') or kwargs.get('keyword') or kwargs.get('bus_user_id') or kwargs.get('tree_height'):
            is_need_total = True
            t_rows = query.all()
        else:
            is_need_total = False
            t_rows = query.paginate(page, limit, error_out=False).items
        base_keys = [(i.report_date, i.user_id) for i in t_rows]
        base_data_query = base_data_model.query.filter(
            tuple_(base_data_model.report_date, base_data_model.user_id).in_(base_keys)
        ).order_by(base_data_model.report_date.desc())
        if kwargs.get("export"):
            data_list = marshal(base_data_query.limit(ADMIN_EXPORT_LIMIT).all(), cls.export_fields)
            cls.fill_info(t_rows, data_list)
            for d in data_list:
                d["tree_height"] = f'{d["tree_height"]}级'
                d["parent_refer_list"] = "\n".join(
                    [f'{p["level_str"]} {p["email"]} {amount_to_str(p["amount"])}' for p in d['parent_refer_list']]
                )
            return export_xlsx(
                filename='coinex_child_ambassador_referral_report_list',
                data_list=data_list,
                export_headers=cls.export_headers
            )

        kwargs['is_need_total'] = is_need_total
        count, total_data, page_rows = get_refer_page_data(
            kwargs, base_data_query, DailyBusinessAmbassadorReferralReport, cls.get_filter_query
        )
        items = marshal(page_rows, cls.export_fields)
        _, parent_total_amount = cls.fill_info(t_rows, items)
        total_data['parent_total_amount'] = parent_total_amount if is_need_total else '-'
        return dict(
            count=count,
            total_data=total_data,
            items=items,
        )

    @classmethod
    def fill_info(cls, t_rows: list, items: list[dict]):
        amb_ids = {i['user_id'] for i in items}
        amb_rows = TreeAmbassador.query.filter(
            TreeAmbassador.user_id.in_(amb_ids),
        ).with_entities(
            TreeAmbassador.user_id,
            TreeAmbassador.root_id,
        ).all()
        amb_id_root_id_map = dict(amb_rows)
        root_amb_rows = BusinessAmbassador.query.filter(
            BusinessAmbassador.user_id.in_(list(amb_id_root_id_map.values())),
        ).with_entities(
            BusinessAmbassador.user_id,
            BusinessAmbassador.bus_user_id,
        ).all()
        root_id_bus_id_map = dict(root_amb_rows)

        t_row_map = {}
        p_ids = set()
        for t in t_rows:
            t_row_map[(t.report_date.strftime("%Y-%m-%d"), t.user_id)] = t
            for parent_d in t.parent_refer_data:
                p_ids.add(parent_d['user_id'])

        q_email_ids = set(amb_id_root_id_map) | set(root_id_bus_id_map.values()) | p_ids
        user_email_map = {}
        for ch_bus_ids in batch_iter(q_email_ids, 1000):
            ch_user_rows = User.query.filter(
                User.id.in_(ch_bus_ids)
            ).with_entities(User.id, User.email).all()
            user_email_map.update(dict(ch_user_rows))

        parent_total_amount = 0
        for i in items:
            key_ = (i['report_date'], i['user_id'])
            t_row = t_row_map.get(key_)
            root_id = amb_id_root_id_map.get(i['user_id'])
            bus_user_id = root_id_bus_id_map.get(root_id)
            i['bus_user_id'] = bus_user_id
            i['bus_user_name'] = user_email_map.get(i['bus_user_id'], '')
            i['email'] = user_email_map.get(i['user_id'], '')
            if t_row:
                tree_height = t_row.tree_height
                parent_refer_list = []
                for parent_d in t_row.parent_refer_data:
                    _d = dict(parent_d)
                    _d['email'] = user_email_map.get(_d['user_id'], _d['user_id'])
                    if _d['tree_height']:
                        level_str = f"{_d['tree_height']}级代理"
                    else:
                        level_str = '总代理'
                    _d['level_str'] = level_str
                    parent_refer_list.append(_d)
                    parent_total_amount += Decimal(_d['amount'])
            else:
                tree_height = -1
                parent_refer_list = []
            i['tree_height'] = tree_height
            i['parent_refer_list'] = parent_refer_list
        return items, parent_total_amount

    @classmethod
    def get_tree_filter_query(cls, query_model, kwargs):
        query = TreeAmbassadorReferralReportResource.get_filter_query(query_model, kwargs)
        if parent_id := kwargs.get("parent_id"):
            q_child_ids = [i['user_id'] for i in TreeAmbassadorHierarchy.get_child_info(parent_id)]
            query = query.filter(query_model.user_id.in_(q_child_ids))
        if tree_height := kwargs.get("tree_height"):
            query = query.filter(query_model.tree_height == tree_height)
        return query


@ns.route('/share-referral-report')
@respond_with_code
class BusinessShareReferralReportResource(Resource):
    export_fields = {
        'target_user_id': fx_fields.Integer,  # 商务代理ID
        'bus_amb_id': fx_fields.Integer,  # 关联商务大使ID
        'target_user_email': fx_fields.String(attribute=lambda x: x.target_user.email),  # 商务代理邮箱
        'bus_amb_email': fx_fields.String(attribute=lambda x: x.bus_amb.email),  # 关联商务大使邮箱
        'report_date': fx_fields.Date,
        'deal_count': fx_fields.Integer,
        'deposit_count': fx_fields.Integer,
        'refer_count': fx_fields.Integer,
        'perpetual_fee_usd': fx_fields.Float,
        'spot_fee_usd': fx_fields.Float,
        'perpetual_trade_usd': fx_fields.Float,
        'spot_trade_usd': fx_fields.Float,
        'refer_total_amount': fx_fields.Float,
    }

    export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "target_user_email", Language.ZH_HANS_CN: "商务代理"},
        {"field": "bus_amb_email", Language.ZH_HANS_CN: "关联大使"},
        {"field": "deal_count", Language.ZH_HANS_CN: "被邀请人交易人数"},
        {"field": "spot_trade_usd", Language.ZH_HANS_CN: "被邀请人币币交易量（USD）"},
        {"field": "perpetual_trade_usd", Language.ZH_HANS_CN: "被邀请人合约交易量（USD）"},
        {"field": "spot_fee_usd", Language.ZH_HANS_CN: "被邀请人币币手续费（USD）"},
        {"field": "perpetual_fee_usd", Language.ZH_HANS_CN: "被邀请人合约手续费（USD）"},
        {"field": "refer_total_amount", Language.ZH_HANS_CN: "商务代理返佣（USDT）"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        report_type=EnumField(ReportType, enum_by_value=True, required=True),
        start_date=mm_fields.DateTime(format="%Y-%m-%d"),
        end_date=mm_fields.DateTime(format="%Y-%m-%d"),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50),
        target_user_keyword=mm_fields.String(),  # 商务代理搜索
        bus_amb_keyword=mm_fields.String(),  # 关联大使搜索
        export=mm_fields.Boolean,
    ))
    def get(cls, **kwargs):
        """
        用户-商务大使体系-商务代理返佣记录
        """
        report_type = kwargs["report_type"]
        if report_type == ReportType.DAILY:
            query_model = DailyBusinessAgentReferralReport
        else:
            query_model = MonthlyBusinessAgentReferralReport
        query = cls.get_filter_query(query_model, kwargs)

        if kwargs.get("export"):
            return export_xlsx(
                filename='coinex_business_share_detail_list',
                data_list=marshal(query.limit(ADMIN_EXPORT_LIMIT).all(), cls.export_fields),
                export_headers=cls.export_headers
            )

        count, total_data, page_rows = get_refer_page_data(
            kwargs, query, DailyBusinessAgentReferralReport, cls.get_filter_query
        )
        return dict(
            count=count,
            total_data=total_data,
            items=marshal(page_rows, cls.export_fields)
        )

    @classmethod
    def get_filter_query(cls, query_model, kwargs):
        permission_limit = BusinessAmbassadorPermission.is_limit_visible(
            g.user.id,
            BusinessAmbassadorPermission.Type.BUS_AGENT_REPORT
        )
        visible_amb_ids = get_visible_amb_ids(g.user.id)
        limit_visible = permission_limit and not is_super_user(g.user.id)

        query = query_model.query.order_by(
            query_model.report_date.desc()
        )
        if limit_visible:
            query = query.filter(
                query_model.bus_amb_id.in_(visible_amb_ids)
            )
        if kwargs.get("start_date"):
            query = query.filter(
                query_model.report_date >= kwargs["start_date"]
            )
        if kwargs.get("end_date"):
            query = query.filter(
                query_model.report_date <= kwargs["end_date"]
            )

        if target_user_keyword := kwargs.get("target_user_keyword"):
            keyword_results = User.search_for_users(target_user_keyword)
            query = query.filter(
                query_model.target_user_id.in_(keyword_results)
            )
        if bus_amb_keyword := kwargs.get("bus_amb_keyword"):
            keyword_results = User.search_for_users(bus_amb_keyword)
            query = query.filter(
                query_model.bus_amb_id.in_(keyword_results)
            )
        return query


@ns.route('/team-referral-report')
@respond_with_code
class BusinessUserReferralReportResource(Resource):
    export_fields = {
        'user_id': fx_fields.Integer,
        'name': fx_fields.Raw,
        'report_date': fx_fields.Date,
        'refer_ambassador_count': fx_fields.Integer,
        'tree_ambassador_count': fx_fields.Integer,
        'refer_count': fx_fields.Integer,
        'deal_count': fx_fields.Integer,
        'total_trade_usd': fx_fields.Float,
        'total_fee_usd': fx_fields.Float,
        'refer_total_amount': fx_fields.Float,
    }

    user_export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "name", Language.ZH_HANS_CN: "商务"},
        {"field": "refer_ambassador_count", Language.ZH_HANS_CN: "直属代理数"},
        {"field": "tree_ambassador_count", Language.ZH_HANS_CN: "子代理数"},
        {"field": "refer_count", Language.ZH_HANS_CN: "代理推荐用户数"},
        {"field": "deal_count", Language.ZH_HANS_CN: "代理推荐交易用户数"},
        {"field": "total_trade_usd", Language.ZH_HANS_CN: "代理推荐用户累计交易额（USD）"},
        {"field": "total_fee_usd", Language.ZH_HANS_CN: "代理推荐用户累计手续费（USD）"},
        {"field": "refer_total_amount", Language.ZH_HANS_CN: "商务返佣（USDT）"},
    )
    team_export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "name", Language.ZH_HANS_CN: "组长"},
        {"field": "refer_ambassador_count", Language.ZH_HANS_CN: "本组直属代理数"},
        {"field": "tree_ambassador_count", Language.ZH_HANS_CN: "本组子代理数"},
        {"field": "refer_count", Language.ZH_HANS_CN: "本组代理推荐用户数"},
        {"field": "deal_count", Language.ZH_HANS_CN: "本组代理推荐交易用户数"},
        {"field": "total_trade_usd", Language.ZH_HANS_CN: "本组代理推荐用户累计交易额（USD）"},
        {"field": "total_fee_usd", Language.ZH_HANS_CN: "本组代理推荐用户累计手续费（USD）"},
        {"field": "refer_total_amount", Language.ZH_HANS_CN: "组长返佣（USDT）"},
    )
    pr_export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "name", Language.ZH_HANS_CN: "邮箱"},
        {"field": "refer_ambassador_count", Language.ZH_HANS_CN: "本组直属代理数"},
        {"field": "tree_ambassador_count", Language.ZH_HANS_CN: "本组子代理数"},
        {"field": "refer_count", Language.ZH_HANS_CN: "本组代理推荐用户数"},
        {"field": "deal_count", Language.ZH_HANS_CN: "本组代理推荐交易用户数"},
        {"field": "total_trade_usd", Language.ZH_HANS_CN: "本组代理推荐用户累计交易额（USD）"},
        {"field": "total_fee_usd", Language.ZH_HANS_CN: "本组代理推荐用户累计手续费（USD）"},
        {"field": "refer_total_amount", Language.ZH_HANS_CN: "统筹账号返佣（USDT）"},
    )

    class BusinessUserReportType(Enum):
        USER = 'user'
        TEAM = 'team'
        PR = 'pr'

    @classmethod
    @ns.use_kwargs(dict(
        report_type=EnumField(BusinessUserReportType, enum_by_value=True, required=True),
        start_date=mm_fields.DateTime(format="%Y-%m-%d"),
        end_date=mm_fields.DateTime(format="%Y-%m-%d"),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50),
        keyword=mm_fields.String(),
        export=mm_fields.Boolean,
    ))
    def get(cls, **kwargs):
        """
        用户-商务大使体系-商务返佣记录
        """
        report_type = kwargs["report_type"]
        if report_type == cls.BusinessUserReportType.USER:
            query_model = MonthlyBusinessUserReferralReport
            daily_model = DailyBusinessUserReferralReport
            export_headers = cls.user_export_headers
            filename = 'coinex_business_user_detail_list'
        elif report_type == cls.BusinessUserReportType.PR:
            query_model = MonthlyBusinessPrReferralReport
            daily_model = DailyBusinessPrReferralReport
            export_headers = cls.pr_export_headers
            filename = 'coinex_business_pr_detail_list'
        else:
            query_model = MonthlyBusinessTeamReferralReport
            daily_model = DailyBusinessTeamReferralReport
            export_headers = cls.team_export_headers
            filename = 'coinex_business_team_detail_list'
        query = cls.get_filter_query(query_model, kwargs)

        if kwargs.get("export"):
            data_list = cls.add_admin_name(query.limit(ADMIN_EXPORT_LIMIT).all())
            return export_xlsx(
                filename=filename,
                data_list=marshal(data_list, cls.export_fields),
                export_headers=export_headers
            )

        count, total_data, page_rows = cls.get_refer_page_data(
            kwargs, query, daily_model
        )

        return dict(
            count=count,
            total_data=total_data,
            items=marshal(cls.add_admin_name(page_rows), cls.export_fields)
        )

    @classmethod
    def add_admin_name(cls, rows):
        bus_user_id = [i.user_id for i in rows]
        bus_user_name_map = get_admin_user_name_map(bus_user_id)
        new_page_rows = []
        for item in rows:
            tmp = item.to_dict()
            tmp["name"] = bus_user_name_map.get(item.user_id)
            new_page_rows.append(tmp)
        return new_page_rows

    @classmethod
    def get_refer_page_data(cls, kwargs, query, day_model):
        total_data = {
            "refer_ambassador_count": "--",
            "tree_ambassador_count": "--",
            "refer_count": "--",
            "deal_count": "--",
            "deposit_count": "--",
            "total_trade_usd": "--",
            "total_fee_usd": "--",
            "refer_total_amount": "--",
        }
        page, limit = kwargs["page"], kwargs["limit"]
        keys = kwargs.keys() - {"report_type", "page", "limit", "export"}
        if keys and all([bool(kwargs.get(i)) for i in keys]):
            modify_end_date_kwarg(kwargs)
            deal_count, deposit_count = set(), set()
            for row in cls.get_filter_query(day_model, kwargs).all():
                deal_count.update(json.loads(row.deal_user_list))
                deposit_count.update(json.loads(row.deposit_user_list))
            rows = query.all()
            for field in total_data.keys():
                if field not in ["deal_count", "deposit_count"]:
                    total_data[field] = amount_to_str(sum([getattr(row, field, 0) for row in rows]), 2)
            total_data["deal_count"] = len(deal_count)
            total_data["deposit_count"] = len(deposit_count)
            count = len(rows)
            page_rows = rows[(page - 1) * limit: page * limit]
        else:
            page_rows = query.paginate(page, limit, error_out=False).items
            count = query.count()
        return count, total_data, page_rows

    @classmethod
    def get_filter_query(cls, query_model, kwargs):
        query = query_model.query.order_by(
            query_model.report_date.desc()
        )
        if kwargs.get("start_date"):
            query = query.filter(
                query_model.report_date >= kwargs["start_date"]
            )
        if kwargs.get("end_date"):
            query = query.filter(
                query_model.report_date <= kwargs["end_date"]
            )

        if keyword := kwargs.get("keyword"):
            keyword_results = User.search_for_users(keyword)
            query = query.filter(
                query_model.user_id.in_(keyword_results)
            )
        return query


class BusinessAmbReportMixin:

    export_fields = {
        'report_date': fx_fields.Date,
        'bus_user_count': fx_fields.Integer,
        'bus_amb_count': fx_fields.Integer,
        'new_bus_amb_count': fx_fields.Integer,
        'refer_count': fx_fields.Integer,
        'refer_user_balance_usd': fx_fields.Float,
        'refer_active_user_count': fx_fields.Integer,
        'new_refer_count': fx_fields.Integer,
        'invitee_percent': fx_fields.Float,
        'deal_count': fx_fields.Integer,
        'trade_percent': fx_fields.Float,
        'new_deal_count': fx_fields.Integer,
        'new_trade_percent': fx_fields.Float,
        'deal_usd': fx_fields.Float,
        'fee_usd': fx_fields.Float,
        'refer_amb_count': fx_fields.Integer,
        'refer_amb_amount': fx_fields.Float,
        'refer_agent_count': fx_fields.Integer,
        'refer_agent_amount': fx_fields.Float,
        'average_refer_rate': fx_fields.Float,
        'refer_bus_count': fx_fields.Integer,
        'refer_bus_amount': fx_fields.Float,
        'team_id': fx_fields.Integer,
    }

    export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "business_team", Language.ZH_HANS_CN: "团队"},
        {"field": "bus_user_count", Language.ZH_HANS_CN: "商务数量"},
        {"field": "bus_amb_count", Language.ZH_HANS_CN: "商务大使数量"},
        {"field": "new_bus_amb_count", Language.ZH_HANS_CN: "新增大使"},
        {"field": "refer_count", Language.ZH_HANS_CN: "refer总人数"},
        {"field": "refer_user_balance_usd", Language.ZH_HANS_CN: "refer用户总资产"},
        {"field": "refer_active_user_count", Language.ZH_HANS_CN: "refer活跃用户"},
        {"field": "new_refer_count", Language.ZH_HANS_CN: "refer注册人数"},
        {"field": "invitee_percent", Language.ZH_HANS_CN: "refer注册人数(占比)"},
        {"field": "deal_count", Language.ZH_HANS_CN: "refer交易人数"},
        {"field": "trade_percent", Language.ZH_HANS_CN: "refer交易人数(占比)"},
        {"field": "new_deal_count", Language.ZH_HANS_CN: "新增refer交易人数"},
        {"field": "new_trade_percent", Language.ZH_HANS_CN: "新增refer交易人数(占比)"},
        {"field": "deal_usd", Language.ZH_HANS_CN: "refer交易总额(USD)"},
        {"field": "fee_usd", Language.ZH_HANS_CN: "refer贡献手续费(USD)"},
        {"field": "refer_amb_count", Language.ZH_HANS_CN: "收到返佣大使"},
        {"field": "refer_amb_amount", Language.ZH_HANS_CN: "大使返佣金额"},
        {"field": "refer_agent_count", Language.ZH_HANS_CN: "收到返佣商务代理"},
        {"field": "refer_agent_amount", Language.ZH_HANS_CN: "商务代理返佣金额"},
        {"field": "average_refer_rate", Language.ZH_HANS_CN: "平均返佣比例"},
        {"field": "refer_bus_count", Language.ZH_HANS_CN: "收到返佣商务"},
        {"field": "refer_bus_amount", Language.ZH_HANS_CN: "商务返佣金额"},
    )


@ns.route('/ambassador-report')
@respond_with_code
class BusinessAmbassadorReportResource(Resource, BusinessAmbReportMixin):
    
    @classmethod
    @ns.use_kwargs(dict(
        report_type=EnumField(ReportType, enum_by_value=True, required=True),
        start_date=mm_fields.DateTime(format="%Y-%m-%d"),
        end_date=mm_fields.DateTime(format="%Y-%m-%d"),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50),
        export=mm_fields.Boolean,
        team_id=mm_fields.Integer(required=True)
    ))
    def get(cls, **kwargs):
        """
        报表-返佣报表-商务大使报表
        """
        page = kwargs['page']
        limit = kwargs['limit']
        team_id = kwargs['team_id']
        report_type = kwargs["report_type"]
        if report_type == ReportType.DAILY:
            query_model = DailyBusinessAmbassadorTotalReferralReport
        elif report_type == ReportType.WEEKLY:
            query_model = WeeklyBusinessAmbassadorTotalReferralReport
        elif report_type == ReportType.MONTHLY:
            query_model = MonthlyBusinessAmbassadorTotalReferralReport
        elif report_type == ReportType.QUARTERLY:
            query_model = QuarterlyBusinessAmbassadorTotalReferralReport
        else:
            raise ValueError(f"Invalid report type: {report_type}")

        query = query_model.query.filter(
            query_model.team_id == team_id
        ).order_by(
            query_model.report_date.desc()
        )
        if kwargs.get("start_date"):
            query = query.filter(
                query_model.report_date >= kwargs["start_date"]
            )
        if kwargs.get("end_date"):
            query = query.filter(
                query_model.report_date <= kwargs["end_date"]
            )
        teams = BusinessTeam.query.all()
        business_team_dic = {i.id: i.name for i in teams}
        business_team_dic.update({0: 'ALL'})
        if kwargs.get("export"):
            data_list = marshal(query.limit(ADMIN_EXPORT_LIMIT).all(), cls.export_fields)
            for item in data_list:
                item['invitee_percent'] = format_percent(item['invitee_percent'])
                item['trade_percent'] = format_percent(item['trade_percent'])
                item['new_trade_percent'] = format_percent(item['new_trade_percent'])
                item['business_team'] = business_team_dic[item['team_id']]
            return export_xlsx(
                filename='coinex_business_ambassador_total_list',
                data_list=data_list,
                export_headers=cls.export_headers
            )

        ret = query.paginate(page, limit, error_out=False)
        pagination = ret.items
        total = ret.total
        business_teams = [{'id': i.id, 'name': i.name} for i in teams]
        business_teams.insert(0, {'id': 0, 'name': 'ALL'})
        data_list = marshal(pagination, cls.export_fields)
        for item in data_list:
            item['business_team'] = business_team_dic[item['team_id']]
        return dict(
            total=total,
            items=data_list,
            business_teams=business_teams
        )


@ns.route('/ambassador-report-detail')
@respond_with_code
class AmbassadorReportDetailResource(Resource, BusinessAmbReportMixin):

    @classmethod
    @ns.use_kwargs(
        dict(
            report_type=EnumField(ReportType, enum_by_value=True, required=True),
            sort_field=mm_fields.String(required=True),
            report_date=mm_fields.DateTime(format="%Y-%m-%d", required=True),
            export=mm_fields.Boolean,
        )
    )
    def get(cls, **kwargs):
        """
        报表-返佣报表-商务大使报表详情
        """
        report_type = kwargs['report_type']
        sort_field = kwargs['sort_field']
        report_date = kwargs['report_date']

        if report_type == ReportType.DAILY:
            query_model = DailyBusinessAmbassadorTotalReferralReport
        elif report_type == ReportType.MONTHLY:
            query_model = MonthlyBusinessAmbassadorTotalReferralReport
            report_date = datetime.date(report_date.year, report_date.month, 1)
        elif report_type == ReportType.WEEKLY:
            query_model = WeeklyBusinessAmbassadorTotalReferralReport
            report_date = cur_week(report_date)
        elif report_type == ReportType.QUARTERLY:
            query_model = QuarterlyBusinessAmbassadorTotalReferralReport
            report_date = cur_quarter(report_date.year, report_date.month)
        else:
            raise ValueError(f"Invalid report type: {report_type}")
        query = query_model.query.filter(
            query_model.report_date == report_date
        ).all()
        records = marshal(query, cls.export_fields)

        teams = BusinessTeam.query.all()
        business_team_dic = {i.id: i.name for i in teams}
        business_team_dic.update({0: 'ALL'})

        records.sort(key=lambda x: x[sort_field], reverse=True)
        records.sort(key=lambda x: x['team_id'] == 0, reverse=True)
        for item in records:
            item['invitee_percent'] = format_percent(item['invitee_percent'])
            item['trade_percent'] = format_percent(item['trade_percent'])
            item['new_trade_percent'] = format_percent(item['new_trade_percent'])
            item['business_team'] = business_team_dic[item['team_id']]
        if kwargs.get('export'):
            return export_xlsx(
                filename='coinex_business_ambassador_total_list',
                data_list=records,
                export_headers=cls.export_headers
            )
        return dict(
            items=records
            )
            

@ns.route('/ambassador-detail-referral-report')
@respond_with_code
class BusinessAmbassadorDetailReferralReportResource(Resource):
    
    export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "referree_id", Language.ZH_HANS_CN: "用户ID"},
        {"field": "user_email", Language.ZH_HANS_CN: "代理邮箱"},
        {"field": "bus_name", Language.ZH_HANS_CN: "商务"},
        {"field": "bus_team", Language.ZH_HANS_CN: "团队"},
        {"field": "agent_type", Language.ZH_HANS_CN: "代理类型"},
        {"field": "spot_trade_usd", Language.ZH_HANS_CN: "币币交易额（USD）"},
        {"field": "perpetual_trade_usd", Language.ZH_HANS_CN: "合约交易额（USD）"},
        {"field": "total_trade_usd", Language.ZH_HANS_CN: "总交易额（USD）"},
        {"field": "spot_fee_usd", Language.ZH_HANS_CN: "币币手续费（USD）"},
        {"field": "perpetual_fee_usd", Language.ZH_HANS_CN: "合约手续费（USD）"},
        {"field": "total_fee_usd", Language.ZH_HANS_CN: "总手续费（USD）"},
        {"field": "refer_total_amount", Language.ZH_HANS_CN: "返佣金额（USDT）"},
    )
    
    class BusAmbType(Enum):
        NORMAL = "普通代理"
        BROKER = "Broker代理"
        TREE_ROOT = "总代理"
        CHILD = "子代理"
    
    @classmethod
    @ns.use_kwargs(dict(
        report_type=EnumField(ReportType, enum_by_value=True, required=True),
        start_date=mm_fields.DateTime(format="%Y-%m-%d"),
        end_date=mm_fields.DateTime(format="%Y-%m-%d"),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50),
        referree_id=mm_fields.Integer(),
        user_id=mm_fields.Integer(),
        bus_team_id=mm_fields.Integer(),
        bus_user_id=mm_fields.Integer(),
        agent_type=EnumField(BusAmbType, enum_by_value=False),
        export=mm_fields.Boolean,
    ))
    def get(cls, **kwargs):
        """
        报表-返佣报表-代理返佣明细报表
        """
        
        report_type = kwargs['report_type']
        if report_type == ReportType.DAILY:
            query_model = DailyBusinessAmbassadorDetailReferralReport
        elif report_type == ReportType.MONTHLY:
            query_model = MonthlyBusinessAmbassadorDetailReferralReport
        else:
            raise ValueError(f"Invalid report type: {report_type}")
        team_dic = cls.get_team_dic()
        
        business_users, team_business_dic, bus_id_to_team_dic = cls.get_business_users()
        bus_amb_type_dic, bus_id_to_amb_dic, amb_bus_id_dic, user_id_to_agent_type_dic = cls.get_bus_ambs()
        tree_amb_height_dic, root_child_dic, child_root_dic = cls.get_tree_ambs()
        query = cls.get_filter_query(query_model, kwargs)
        query = cls.query_with_user_id(query, query_model, kwargs, business_users, team_business_dic, 
                                       bus_id_to_amb_dic, bus_amb_type_dic, root_child_dic)
        if kwargs.get("export"):
            recs = query.limit(ADMIN_EXPORT_LIMIT).all()
            res = cls.fmt_data(recs, team_dic, bus_id_to_team_dic, tree_amb_height_dic,
                               amb_bus_id_dic, child_root_dic, user_id_to_agent_type_dic)
            return export_xlsx(
                filename='coinex_ambassador_referree_list',
                data_list=res,
                export_headers=cls.export_headers
            )
        
        ret = query.paginate(kwargs["page"], kwargs["limit"], error_out=False)
        items = ret.items
        res = cls.fmt_data(items, team_dic, bus_id_to_team_dic, tree_amb_height_dic,
                           amb_bus_id_dic, child_root_dic, user_id_to_agent_type_dic)
        extras = dict(
            team_dic=team_dic,
            agent_type_dic=cls.BusAmbType
        )
        return dict(
            total=ret.total,
            items=res,
            extras=extras,
        )
    @classmethod
    def get_team_dic(cls):
        teams = BusinessTeam.query.filter(
            BusinessTeam.status == BusinessTeam.Status.VALID,
        ).all()
        return {i.id: i.name for i in teams}
    
    @classmethod
    def get_business_users(cls):
        bus_user_recs = BusinessUser.query.filter(
            BusinessUser.status == BusinessUser.Status.VALID,
        ).with_entities(
            BusinessUser.user_id,
            BusinessUser.team_id,
        ).all()
        business_users = set()
        team_business_dic = defaultdict(set)
        bus_id_to_team_dic = dict()
        for rec in bus_user_recs:
            business_users.add(rec.user_id)
            team_business_dic[rec.team_id].add(rec.user_id)
            bus_id_to_team_dic[rec.user_id] = rec.team_id
        return business_users, team_business_dic, bus_id_to_team_dic

    @classmethod
    def get_bus_ambs(cls):
        bus_amb_recs = BusinessAmbassador.query.filter(
            BusinessAmbassador.status == BusinessAmbassador.Status.VALID,
        ).with_entities(
            BusinessAmbassador.user_id,
            BusinessAmbassador.type,
            BusinessAmbassador.bus_user_id,
        ).all()

        bus_amb_type_dic = defaultdict(set)
        bus_id_to_amb_dic = defaultdict(set)
        amb_bus_id_dic = dict()
        user_id_to_agent_type_dic = dict()
        for rec in bus_amb_recs:

            type_ = getattr(cls.BusAmbType, rec.type.name.upper())
            bus_amb_type_dic[type_].add(rec.user_id)
            bus_id_to_amb_dic[rec.bus_user_id].add(rec.user_id)
            amb_bus_id_dic[rec.user_id] = rec.bus_user_id
            user_id_to_agent_type_dic[rec.user_id] = type_
        return bus_amb_type_dic, bus_id_to_amb_dic, amb_bus_id_dic, user_id_to_agent_type_dic
    
    @classmethod
    def get_tree_ambs(cls):
        tree_amb_recs = TreeAmbassador.query.filter(
            TreeAmbassador.status == TreeAmbassador.Status.VALID,
        ).with_entities(
            TreeAmbassador.user_id,
            TreeAmbassador.tree_height,
            TreeAmbassador.root_id,
        ).all()

        tree_amb_height_dic = dict()
        root_child_dic = defaultdict(set)  
        child_root_dic = dict()
        for rec in tree_amb_recs:

            tree_amb_height_dic[rec.user_id] = rec.tree_height
            root_child_dic[rec.root_id].add(rec.user_id)
            child_root_dic[rec.user_id] = rec.root_id
        return tree_amb_height_dic, root_child_dic, child_root_dic
    
    @classmethod
    def get_filter_query(cls, query_model, kwargs):
        query = query_model.query.order_by(
            query_model.report_date.desc())
        if kwargs.get("start_date"):
            query = query.filter(
                query_model.report_date >= kwargs["start_date"])
        if kwargs.get("end_date"):
            query = query.filter(
                query_model.report_date <= kwargs["end_date"])

        if referree_id := kwargs.get("referree_id"):
            query = query.filter(
                query_model.referree_id == referree_id
            )
        return query

    @classmethod
    def query_with_user_id(cls, query, query_model, kwargs, business_users, 
                           team_business_dic, bus_id_to_amb_dic, 
                           bus_amb_type_dic, root_child_dic):
        user_ids = cls.get_user_ids(kwargs, business_users, team_business_dic, 
                                    bus_id_to_amb_dic, bus_amb_type_dic, 
                                    root_child_dic)
        if user_ids is None:
            return query
        return query.filter(
            query_model.user_id.in_(user_ids)
        )
        
    @classmethod
    def get_user_ids(cls, kwargs, all_business_users, all_team_business_dic, 
                     all_bus_id_to_amb_dic, all_bus_amb_type_dic, all_root_child_dic):
        bus_team_id = kwargs.get("bus_team_id")
        bus_user_id = kwargs.get("bus_user_id")
        amb_user_id = kwargs.get("user_id")
        agent_type = kwargs.get("agent_type")
        if not any([bus_team_id, bus_user_id, amb_user_id, agent_type]):
            return None
        
        if bus_team_id:
            business_users = set(all_team_business_dic[bus_team_id])
        else:
            business_users = all_business_users
        if bus_user_id:
            business_users &= {bus_user_id}
        
        if not business_users:
            return set()
        bus_amb_user_ids = set()
        for bus_id in business_users:
            bus_amb_user_ids.update(all_bus_id_to_amb_dic[bus_id])
        user_ids = set()
        if agent_type:
            if agent_type != cls.BusAmbType.CHILD:
                agent_type_ids = all_bus_amb_type_dic[agent_type]
                user_ids = bus_amb_user_ids & agent_type_ids
            else:
                all_root_ids = all_bus_amb_type_dic[cls.BusAmbType.TREE_ROOT]
                root_ids = all_root_ids & bus_amb_user_ids
                for root_id in root_ids:
                    user_ids.update(all_root_child_dic[root_id])
        else:
            user_ids = bus_amb_user_ids
            all_root_ids = all_bus_amb_type_dic[cls.BusAmbType.TREE_ROOT]
            root_ids = all_root_ids & bus_amb_user_ids
            for root_id in root_ids:
                user_ids.update(all_root_child_dic[root_id])
        if amb_user_id:
            user_ids &= {amb_user_id}
        return user_ids
            
    @classmethod
    def fmt_data(cls, recs, team_dic, bus_id_to_team_dic, tree_amb_height_dic, 
                 amb_bus_id_dic, child_root_dic, user_id_to_agent_type_dic):
        res = []
        if not recs:
            return res
        user_ids = {i.user_id for i in recs}
        user_to_bus_id_dic = cls.get_user_to_bus_id_dic(user_ids, amb_bus_id_dic, child_root_dic)
        bus_ids = set(user_to_bus_id_dic.values())
        bus_id_to_name_dic = UserRepository.get_id_to_name_dic(bus_ids)
        user_id_email_dic = UserRepository.get_users_id_email_map(user_ids)
        res = []
        for rec in recs:
            item = rec.to_dict()
            item['report_date'] = item['report_date'].strftime('%Y-%m-%d')
            item['user_email'] = user_id_email_dic.get(rec.user_id, '')
            bus_id = user_to_bus_id_dic.get(rec.user_id, None)
            item['bus_user_id'] = bus_id
            item['bus_name'] = bus_id_to_name_dic.get(bus_id, '')
            team_id = bus_id_to_team_dic.get(bus_id, None)
            item['bus_team'] = team_dic.get(team_id, '')
            a_type = user_id_to_agent_type_dic.get(rec.user_id, None)
            if a_type == cls.BusAmbType.CHILD or rec.user_id in child_root_dic:
                tree_height = tree_amb_height_dic.get(rec.user_id, 0)
                agent_type = f'{tree_height}级代理'
            else:
                agent_type = a_type.value if a_type else ''
            item['agent_type'] = agent_type
            item['spot_trade_usd'] = amount_to_str(rec.spot_trade_usd, 2)
            item['perpetual_trade_usd'] = amount_to_str(rec.perpetual_trade_usd, 2)
            item['spot_fee_usd'] = amount_to_str(rec.spot_fee_usd, 2)
            item['perpetual_fee_usd'] = amount_to_str(rec.perpetual_fee_usd, 2)
            item['total_trade_usd'] = amount_to_str(rec.spot_trade_usd + rec.perpetual_trade_usd, 2)
            item['total_fee_usd'] = amount_to_str(rec.spot_fee_usd + rec.perpetual_fee_usd, 2)
            item['refer_total_amount'] = amount_to_str(rec.refer_total_amount, 2)
            res.append(item)
        return res
    
    @classmethod
    def get_user_to_bus_id_dic(cls, user_ids, amb_bus_id_dic, child_root_dic):
        res = dict()
        for user_id in user_ids:
            if user_id in amb_bus_id_dic:
                bus_id = amb_bus_id_dic[user_id]
                res[user_id] = bus_id
            else:
                root_id = child_root_dic[user_id]
                res[user_id] = amb_bus_id_dic[root_id]
        return res
        