# -*- coding: utf-8 -*-
from decimal import Decimal

from flask_restx import fields as fx_fields, marshal

from app.api.common import Namespace, Resource, \
    respond_with_code
from app.api.common.fields import EnumField, PageField, LimitField
from app.business.report.refer import get_refer_page_data
from app.business.user import UserRepository
from app.common import ReportType, ADMIN_EXPORT_LIMIT
from marshmallow import fields as mm_fields

from app.models import DailyAmbassadorReferralReport, \
    MonthlyAmbassadorReferralReport, DailyAmbassadorReferralDetailReport, \
    MonthlyAmbassadorReferralDetailReport, Language, User
from app.models.daily import DailyAmbassadorReferreeReport
from app.models.monthly import MonthlyAmbassadorReferreeReport
from app.utils import export_xlsx, format_percent, amount_to_str
from app.utils.helper import Struct

ns = Namespace('Report - Ambassador')


@ns.route('/report')
@respond_with_code
class AmbassadorReportResource(Resource):
    export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "ambassador_agent_count", Language.ZH_HANS_CN: "代理数量"},
        {"field": "ambassador_count", Language.ZH_HANS_CN: "大使数量"},
        {"field": "only_agent_count", Language.ZH_HANS_CN: "纯代理数量"},
        {"field": "new_ambassador_count", Language.ZH_HANS_CN: "新增大使"},
        {"field": "ambassador_refer_user_count", Language.ZH_HANS_CN: "refer总人数"},
        {"field": "refer_user_balance_usd", Language.ZH_HANS_CN: "refer用户总资产"},
        {"field": "refer_active_user_count", Language.ZH_HANS_CN: "refer活跃用户"},
        {"field": "refer_count", Language.ZH_HANS_CN: "refer注册人数"},
        {"field": "new_user_percent", Language.ZH_HANS_CN: "refer注册人数(占比)"},
        {"field": "refer_deal_count", Language.ZH_HANS_CN: "refer交易人数"},
        {"field": "trade_percent", Language.ZH_HANS_CN: "refer交易人数(占比)"},
        {"field": "new_trade_user_count", Language.ZH_HANS_CN: "新增refer交易人数"},
        {"field": "new_trade_percent", Language.ZH_HANS_CN: "新增refer交易人数(占比)"},
        {"field": "refer_deal_amount", Language.ZH_HANS_CN: "refer交易总额(USD)"},
        {"field": "refer_fee_amount", Language.ZH_HANS_CN: "refer贡献手续费"},
        {"field": "fee_percent", Language.ZH_HANS_CN: "净手续费占比"},
        {"field": "refer_ambassador_count", Language.ZH_HANS_CN: "收到返佣大使"},
        {"field": "ambassador_referral_amount", Language.ZH_HANS_CN: "大使返佣(USD)"},
        {"field": "refer_ambassador_agent_count", Language.ZH_HANS_CN: "收到返佣代理"},
        {"field": "ambassador_agent_referral_amount", Language.ZH_HANS_CN: "代理返佣(USD)"},
        {"field": "ambassador_referral_rate", Language.ZH_HANS_CN: "大使平均返佣比例"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        report_type=EnumField(ReportType, enum_by_value=True, required=True),
        start_date=mm_fields.DateTime(format="%Y-%m-%d"),
        end_date=mm_fields.DateTime(format="%Y-%m-%d"),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50),
        export=mm_fields.Boolean(missing=False),

    ))
    def get(cls, **kwargs):
        """
        报表-大使报表
        """
        params = Struct(**kwargs)

        if params.report_type == ReportType.DAILY:
            query = DailyAmbassadorReferralReport.query.order_by(
                DailyAmbassadorReferralReport.report_date.desc())
            if params.start_date:
                query = query.filter(
                    DailyAmbassadorReferralReport.report_date >= params.start_date)
            if params.end_date:
                query = query.filter(
                    DailyAmbassadorReferralReport.report_date <= params.end_date)
        else:
            query = MonthlyAmbassadorReferralReport.query.order_by(
                MonthlyAmbassadorReferralReport.report_date.desc())
            if params.start_date:
                query = query.filter(
                    MonthlyAmbassadorReferralReport.report_date >= params.start_date)
            if params.end_date:
                query = query.filter(
                    MonthlyAmbassadorReferralReport.report_date <= params.end_date)

        if params.export:
            return export_xlsx(
                filename='ambassador',
                data_list=cls.get_export_data(query.limit(ADMIN_EXPORT_LIMIT).all()),
                export_headers=cls.export_headers
            )

        records = query.paginate(params.page, params.limit, error_out=False)
        items = []
        for record in records.items:
            row_dict = record.to_dict(enum_to_name=True)
            if record.new_trade_user_site_count:
                ratio = Decimal(record.new_trade_user_count / record.new_trade_user_site_count)
            else:
                ratio = 0
            row_dict['new_trade_percent'] = ratio
            items.append(row_dict)
        return dict(
            total=query.count(),
            items=items
        )

    @classmethod
    def get_export_data(cls, records):
        export_data = []
        for row in records:
            record = row.to_dict()
            if row.new_trade_user_site_count:
                ratio = Decimal(row.new_trade_user_count / row.new_trade_user_site_count)
            else:
                ratio = 0
            record['new_trade_percent'] = format_percent(ratio)
            record['new_user_percent'] = format_percent(record['new_user_percent'])
            record['refer_deal_amount'] = amount_to_str(record['refer_deal_amount'], 2)
            record['refer_fee_amount'] = amount_to_str(record['refer_fee_amount'], 2)
            record['refer_user_balance_usd'] = amount_to_str(record['refer_user_balance_usd'], 2)
            record['fee_percent'] = format_percent(record['fee_percent'])
            record['ambassador_referral_amount'] = amount_to_str(record['ambassador_referral_amount'], 2)
            record['ambassador_agent_referral_amount'] = amount_to_str(record['ambassador_agent_referral_amount'], 2)
            record['ambassador_referral_rate'] = format_percent(record['ambassador_referral_rate'])
            record['trade_percent'] = format_percent(record['trade_percent'])

            export_data.append(record)
        return export_data


@ns.route('/referral-report')
@respond_with_code
class AmbassadorReferralReportResource(Resource):
    
    export_fields = {
        'user_id': fx_fields.Integer,
        'email': fx_fields.String(attribute=lambda x: x.user.email),
        'report_date': fx_fields.Date,
        'deal_count': fx_fields.Integer,
        'deposit_count': fx_fields.Integer,
        'refer_count': fx_fields.Integer,
        'perpetual_fee_usd': fx_fields.Float,
        'spot_fee_usd': fx_fields.Float,
        'perpetual_trade_usd': fx_fields.Float,
        'spot_trade_usd': fx_fields.Float,
        'refer_total_amount': fx_fields.Float,
    }

    export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "email", Language.ZH_HANS_CN: "邮箱"},
        {"field": "refer_count", Language.ZH_HANS_CN: "推荐人数"},
        {"field": "deposit_count", Language.ZH_HANS_CN: "被邀请人充值人数"},
        {"field": "deal_count", Language.ZH_HANS_CN: "被邀请人交易人数"},
        {"field": "spot_trade_usd", Language.ZH_HANS_CN: "被邀请人币币交易量（USD）"},
        {"field": "perpetual_trade_usd", Language.ZH_HANS_CN: "被邀请人合约交易量（USD）"},
        {"field": "spot_fee_usd", Language.ZH_HANS_CN: "被邀请人币币手续费（USD）"},
        {"field": "perpetual_fee_usd", Language.ZH_HANS_CN: "被邀请人合约手续费（USD）"},
        {"field": "refer_total_amount", Language.ZH_HANS_CN: "大使返佣（USDT）"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        report_type=EnumField(ReportType, enum_by_value=True, required=True),
        start_date=mm_fields.DateTime(format="%Y-%m-%d"),
        end_date=mm_fields.DateTime(format="%Y-%m-%d"),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50),
        keyword=mm_fields.String(),
        export=mm_fields.Boolean,
    ))
    def get(cls, **kwargs):
        """
        用户-CoinEx大使-大使返佣报表
        """
        report_type = kwargs["report_type"]
        if report_type == ReportType.DAILY:
            query_model = DailyAmbassadorReferralDetailReport
        else:
            query_model = MonthlyAmbassadorReferralDetailReport
        query = cls.get_filter_query(query_model, kwargs)

        if kwargs.get("export"):
            return export_xlsx(
                filename='coinex_ambassador_detail_list',
                data_list=marshal(query.limit(ADMIN_EXPORT_LIMIT).all(), cls.export_fields),
                export_headers=cls.export_headers
            )

        count, total_data, page_rows = get_refer_page_data(
            kwargs, query, DailyAmbassadorReferralDetailReport, cls.get_filter_query
        )
        return dict(
            count=count,
            total_data=total_data,
            items=marshal(page_rows, cls.export_fields)
        )

    @classmethod
    def get_filter_query(cls, query_model, kwargs):
        query = query_model.query.order_by(
            query_model.report_date.desc())
        if kwargs.get("start_date"):
            query = query.filter(
                query_model.report_date >= kwargs["start_date"])
        if kwargs.get("end_date"):
            query = query.filter(
                query_model.report_date <= kwargs["end_date"])

        if keyword := kwargs.get("keyword"):
            keyword_results = User.search_for_users(keyword)
            query = query.filter(
                query_model.user_id.in_(keyword_results)
            )
        return query


@ns.route('/referral-detail-report')
@respond_with_code
class AmbassadorReferralDetailReportResource(Resource):

    export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "referree_id", Language.ZH_HANS_CN: "用户ID"},
        {"field": "user_email", Language.ZH_HANS_CN: "大使邮箱"},
        {"field": "spot_trade_usd", Language.ZH_HANS_CN: "币币交易额（USD）"},
        {"field": "perpetual_trade_usd", Language.ZH_HANS_CN: "合约交易额（USD）"},
        {"field": "total_trade_usd", Language.ZH_HANS_CN: "总交易额（USD）"},
        {"field": "spot_fee_usd", Language.ZH_HANS_CN: "币币手续费（USD）"},
        {"field": "perpetual_fee_usd", Language.ZH_HANS_CN: "合约手续费（USD）"},
        {"field": "total_fee_usd", Language.ZH_HANS_CN: "总手续费（USD）"},
        {"field": "refer_total_amount", Language.ZH_HANS_CN: "返佣金额（USDT）"},
    )
    
    @classmethod
    @ns.use_kwargs(dict(
        report_type=EnumField(ReportType, enum_by_value=True, required=True),
        start_date=mm_fields.DateTime(format="%Y-%m-%d"),
        end_date=mm_fields.DateTime(format="%Y-%m-%d"),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50),
        user_id=mm_fields.Integer(),
        referree_id=mm_fields.Integer(),
        export=mm_fields.Boolean,
    ))  
    def get(cls, **kwargs):
        """
        用户-CoinEx大使-被邀请人返佣报表
        """
        report_type = kwargs["report_type"]
        if report_type == ReportType.DAILY:
            query_model = DailyAmbassadorReferreeReport
        else:
            query_model = MonthlyAmbassadorReferreeReport
        query = cls.get_filter_query(query_model, kwargs)
        if kwargs.get("export"):
            res = cls.fmt_data(query.limit(ADMIN_EXPORT_LIMIT).all())
            return export_xlsx(
                filename='coinex_ambassador_referree_list',
                data_list=res,
                export_headers=cls.export_headers
            )
        
        ret = query.paginate(kwargs["page"], kwargs["limit"], error_out=False)
        items = ret.items
        res = cls.fmt_data(items)
        return dict(
            total=ret.total,
            items=res
        )
    
    @classmethod
    def fmt_data(cls, items):
        res = []
        user_ids = {i.user_id for i in items}
        user_emails = UserRepository.get_users_id_email_map(user_ids)
        for item in items:
            d = item.to_dict()
            d['user_email'] = user_emails[item.user_id]
            spot_trade_usd = d['spot_trade_usd']
            perpetual_trade_usd = d['perpetual_trade_usd']
            d['spot_trade_usd'] = amount_to_str(spot_trade_usd, 2)
            d['perpetual_trade_usd'] = amount_to_str(perpetual_trade_usd, 2)
            d['total_trade_usd'] = amount_to_str(spot_trade_usd + perpetual_trade_usd, 2)
            spot_fee_usd = d['spot_fee_usd']
            perpetual_fee_usd = d['perpetual_fee_usd']
            d['spot_fee_usd'] = amount_to_str(spot_fee_usd, 2)
            d['perpetual_fee_usd'] = amount_to_str(perpetual_fee_usd, 2)
            d['total_fee_usd'] = amount_to_str(spot_fee_usd + perpetual_fee_usd, 2)
            d['refer_total_amount'] = amount_to_str(d['refer_total_amount'], 2)
            res.append(d)
        return res
    
    @classmethod
    def get_filter_query(cls, query_model, kwargs):
        query = query_model.query.order_by(
            query_model.report_date.desc())
        if kwargs.get("start_date"):
            query = query.filter(
                query_model.report_date >= kwargs["start_date"])
        if kwargs.get("end_date"):
            query = query.filter(
                query_model.report_date <= kwargs["end_date"])

        if user_id := kwargs.get("user_id"):
            query = query.filter(
                query_model.user_id == user_id
            )
        if referree_id := kwargs.get("referree_id"):
            query = query.filter(
                query_model.referree_id == referree_id
            )
        return query