# -*- coding: utf-8 -*-
from collections import defaultdict
from datetime import date
from decimal import Decimal

from marshmallow import fields as mm_fields
from pyroaring import BitMap
from sqlalchemy import func

from app import Language
from app.api.common import Namespace, respond_with_code, Resource
from app.api.common.fields import EnumField, PageField, LimitField, DateField
from app.business.investment import calc_month_report_rate
from app.business.prices import PriceManager
from app.common import ReportType, ADMIN_EXPORT_LIMIT
from app.models.daily import (
    DailySiteInvestmentReport, DailyInvestmentReport,
)
from app.models.monthly import (
    MonthlySiteInvestmentReport, MonthlyInvestmentReport,
)
from app.models.equity_center import UserDailyIncEquityHistory
from app.utils import quantize_amount, export_xlsx, amount_to_str, format_percent
from app.utils.date_ import next_month
from app.utils.helper import Struct

ns = Namespace('Report - Invest')


class InvestmentReportBase:
    """理财报表基类，提供公共方法"""

    @classmethod
    def get_invest_increase_data(cls, report_type, kwargs):
        """获取指定日期和币种的理财加息权益数据（活期补贴）"""
        model = UserDailyIncEquityHistory

        q = (
            model.query.filter(
                model.status == model.Status.FINISHED,
            )
            .group_by(
                model.report_date,
                model.asset,
            )
            .with_entities(
                model.report_date,
                model.asset,
                func.sum(model.interest_amount).label("total_interest"),
            )
        )
        if start_date := kwargs.get("start_date"):
            q = q.filter(model.report_date >= start_date.date())
        if end_date := kwargs.get("end_date"):
            q = q.filter(model.report_date <= end_date.date())
        if asset := kwargs.get("asset"):
            q = q.filter(model.asset == asset)

        rows = q.all()
        inc_data = defaultdict(lambda: defaultdict(Decimal))
        prices = PriceManager.assets_to_usd()
        for row in rows:
            report_date = row.report_date if report_type == ReportType.DAILY else cls.get_month_date(row.report_date)
            inc_data[report_date][row.asset] += row.total_interest * prices.get(row.asset, Decimal())
        return inc_data

    @classmethod
    def get_month_date(tmp_date):
        # 获取一个月的第一天
        month_date = date.replace(day=1)
        return month_date


@ns.route('/site-investment-report')
@respond_with_code
class SiteInvestmentReportResource(Resource, InvestmentReportBase):
    export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "investment_user_count", Language.ZH_HANS_CN: "理财人数"},
        {"field": "interest_user_count", Language.ZH_HANS_CN: "理财收益人数"},
        {"field": "increase_investment_user", Language.ZH_HANS_CN: "新增理财人数"},
        {"field": "increase_interest_user", Language.ZH_HANS_CN: "新增理财收益人数"},
        {"field": "usd", Language.ZH_HANS_CN: "理财市值(USD)"},
        {"field": "investment_interest_usd", Language.ZH_HANS_CN: "理财总收益(USD)"},
        {"field": "interest_base_usd", Language.ZH_HANS_CN: "基础收益(USD)"},
        {"field": "interest_ladder_usd", Language.ZH_HANS_CN: "阶梯补贴(USD)"},
        {"field": "interest_fixed_usd", Language.ZH_HANS_CN: "固定补贴(USD)"},
        {"field": "invest_increase_usd", Language.ZH_HANS_CN: "活期补贴(USD)"},
        {"field": "year_income_rate", Language.ZH_HANS_CN: "年化收益率"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        report_type=EnumField(ReportType, enum_by_value=True, required=True),
        start_date=mm_fields.DateTime(format="%Y-%m-%d"),
        end_date=mm_fields.DateTime(format="%Y-%m-%d"),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50),
        export=mm_fields.Boolean(missing=False),

    ))
    def get(cls, **kwargs):
        """报表-理财报表-全站理财报表"""
        params = Struct(**kwargs)
        model = MonthlySiteInvestmentReport if params.report_type == ReportType.MONTHLY else DailySiteInvestmentReport
        q = model.query.order_by(
            model.report_date.desc()
        )
        if params.start_date:
            q = q.filter(model.report_date >= params.start_date.date())
        if params.end_date:
            q = q.filter(model.report_date <= params.end_date.date())
        total = 0
        if kwargs['export']:
            items_ = q.limit(ADMIN_EXPORT_LIMIT).all()
        else:
            pagination = q.paginate(params.page, params.limit, error_out=False)
            items_ = pagination.items
            total = pagination.total

        inc_data = cls.get_invest_increase_data(params.report_type, kwargs)

        items = []
        for v in items_:
            # 获取活期补贴数据
            inc_date_data = inc_data.get(v.report_date, {})
            item_data = dict(
                report_date=v.report_date,
                usd=v.usd,
                investment_interest_usd=v.investment_interest_usd,
                increase_investment_user=v.increase_investment_user,
                increase_interest_user=v.increase_interest_user,
                investment_user_count=v.investment_user_count,
                interest_user_count=v.interest_user_count,
                year_income_rate=cls.calc_income_rate(v, params.report_type),
                interest_base_usd=v.base_interest_usd or 0,
                interest_ladder_usd=v.ladder_interest_usd or 0,
                interest_fixed_usd=v.fixed_interest_usd or 0,
                # 添加活期补贴数据
                invest_increase_usd=sum(inc_date_data.values(), Decimal()),
            )
            items.append(item_data)

        if params.export:
            return export_xlsx(
                filename='investment_site_report',
                data_list=cls.get_export_data(items),
                export_headers=cls.export_headers
            )

        return dict(
            total=total,
            items=items,
        )

    @classmethod
    def get_export_data(cls, records):
        for record in records:
            record['usd'] = amount_to_str(record['usd'], 2)
            record['investment_interest_usd'] = amount_to_str(record['investment_interest_usd'], 2)
            record['year_income_rate'] = format_percent(record['year_income_rate'], 2)
        return records

    @classmethod
    def calc_income_rate(cls, row, report_type: ReportType):
        if report_type == ReportType.DAILY:
            return quantize_amount(row.investment_interest_usd / row.usd * 365, 4) if row.usd else 0
        else:
            return calc_month_report_rate(row)

    
@ns.route('/asset-detail')
@respond_with_code
class DailyInvestmentAssetDetailResource(Resource):
    @classmethod
    @ns.use_kwargs(dict(
        report_date=DateField(to_date=True, missing=''),
        order=mm_fields.String(missing='usd'),
        report_type=EnumField(ReportType, required=True),
    ))
    def get(cls, **kwargs):
        """报表-理财报表-全站理财详情"""

        order = kwargs['order']
        report_date = kwargs['report_date']

        if kwargs['report_type'] == ReportType.DAILY:
            model = DailyInvestmentReport
        else:
            model = MonthlyInvestmentReport

        query = model.query
        if not report_date:
            last_item = query.order_by(
                model.report_date.desc()).limit(1).first()
            report_date = last_item.report_date.strftime(
                '%Y-%m-%d') if last_item else None
        if report_date:
            if kwargs['report_type'] == ReportType.MONTHLY:
                report_date = date(report_date.year, report_date.month, 1)
            query = query.filter(
                model.report_date == report_date)

        if order:
            query = query.order_by(getattr(model, order).desc())
        query = query.filter(
            model.report_date == report_date)

        total_usd = 0
        total_interest_usd = 0
        increase_user_bitmap = BitMap()
        increase_interest_user_bitmap = BitMap()
        site_cur_user_bitmap = BitMap()
        site_cur_interest_user_bitmap = BitMap()
        records = []

        if kwargs['report_type'] == ReportType.DAILY:
            statistic_query = DailyInvestmentReport.query.filter(
                DailyInvestmentReport.report_date == report_date)
        else:
            statistic_query = DailyInvestmentReport.query.filter(
                DailyInvestmentReport.report_date >= report_date,
                DailyInvestmentReport.report_date < next_month(report_date.year, report_date.month),
            )
            
        interest_fields = DailyInvestmentReport.interest_fields()
        total_interest_map = defaultdict(Decimal)

        for statistic in statistic_query.all():
            total_usd += statistic.usd
            total_interest_usd += statistic.investment_interest_usd
            increase_user_bitmap.update(BitMap.deserialize(statistic.increase_user_bitmap))
            increase_interest_user_bitmap.update(BitMap.deserialize(statistic.increase_interest_user_bitmap))
            site_cur_user_bitmap.update(BitMap.deserialize(statistic.site_cur_user_bitmap))
            site_cur_interest_user_bitmap.update(BitMap.deserialize(statistic.site_cur_interest_user_bitmap))
            for field in interest_fields:
                total_interest_map[field] += getattr(statistic, field) or 0

        for item in query.all():
            report_date = item.report_date.strftime('%Y-%m-%d') \
                if kwargs['report_type'] == ReportType.DAILY else item.report_date.strftime('%Y-%m')
            record = {
                "report_date": report_date,
                "asset": item.asset,
                "investment_user_count": item.user_count,
                "interest_user_count": item.interest_user_count,
                "increase_investment_user": item.increase_investment_user,
                "increase_interest_user": item.increase_interest_user,
                "investment_interest_usd": amount_to_str(item.investment_interest_usd, 2),
                "amount": "{} {}".format(amount_to_str(item.amount, 8), item.asset),
                "usd": amount_to_str(item.usd, 2),
                "investment_interest_amount": "{} {}".format(amount_to_str(item.investment_interest_amount, 8), item.asset),
                "investment_interest_rate": 0 if kwargs["report_type"] == ReportType.MONTHLY else quantize_amount(item.day_rate * 365, 4),
                **{k: amount_to_str(getattr(item, k) or 0, 2) for k in interest_fields},
            }
            records.append(record)
            

        total_record = {
            'report_date': '合计',
            'asset': 'ALL',
            'usd': amount_to_str(total_usd, 2),
            'investment_interest_usd': amount_to_str(total_interest_usd, 2),
            'amount': '-',
            'investment_interest_amount': '-',
            'investment_interest_rate': '-',
            'investment_user_count': len(site_cur_user_bitmap),
            'interest_user_count': len(site_cur_interest_user_bitmap),
            'increase_investment_user': len(increase_user_bitmap),
            'increase_interest_user': len(increase_interest_user_bitmap),
            **{k: amount_to_str(v, 2) for k, v in total_interest_map.items()},
        }
        records.insert(0, total_record)

        return dict(
            records=records
        )


@ns.route('/asset-investment-report')
@respond_with_code
class AssetInvestmentReportResource(Resource, InvestmentReportBase):
    export_headers = (
        {"field": "report_date", Language.ZH_HANS_CN: "日期"},
        {"field": "asset", Language.ZH_HANS_CN: "币种"},
        {"field": "investment_user_count", Language.ZH_HANS_CN: "理财人数"},
        {"field": "increase_investment_user", Language.ZH_HANS_CN: "新增理财人数"},
        {"field": "amount", Language.ZH_HANS_CN: "理财数量"},
        {"field": "usd", Language.ZH_HANS_CN: "理财市值(USD)"},
        {"field": "investment_interest_amount", Language.ZH_HANS_CN: "理财收益"},
        {"field": "investment_interest_usd", Language.ZH_HANS_CN: "理财收益(USD)"},
        {"field": "interest_base_usd", Language.ZH_HANS_CN: "基础收益(USD)"},
        {"field": "interest_ladder_usd", Language.ZH_HANS_CN: "阶梯补贴(USD)"},
        {"field": "interest_fixed_usd", Language.ZH_HANS_CN: "固定补贴(USD)"},
        {"field": "invest_increase_usd", Language.ZH_HANS_CN: "活期补贴(USD)"},
        {"field": "investment_interest_rate", Language.ZH_HANS_CN: "收益率(当日年化)"},
    )

    @classmethod
    @ns.use_kwargs(dict(
        report_type=EnumField(ReportType, enum_by_value=True, required=True),
        asset=mm_fields.String(),
        start_date=mm_fields.DateTime(format="%Y-%m-%d"),
        end_date=mm_fields.DateTime(format="%Y-%m-%d"),
        page=PageField(unlimited=True),
        limit=LimitField(missing=50),
        export=mm_fields.Boolean(missing=False),

    ))
    def get(cls, **kwargs):
        """报表-法币报表-币种法币报表"""
        params = Struct(**kwargs)
        assets = [
            v.asset for v in
            DailyInvestmentReport.query.with_entities(
                DailyInvestmentReport.asset.distinct().label('asset')
            )
        ]
        model = DailyInvestmentReport if params.report_type == ReportType.DAILY else MonthlyInvestmentReport
        q = model.query.order_by(
            model.report_date.desc()
        )
        if params.asset:
            q = q.filter(
                model.asset == params.asset
            )
        if params.start_date:
            q = q.filter(
                model.report_date >= params.start_date.date()
            )
        if params.end_date:
            q = q.filter(
                model.report_date <= params.end_date.date()
            )
        total = 0
        if kwargs['export']:
            items_ = q.limit(ADMIN_EXPORT_LIMIT).all()
        else:
            pagination = q.paginate(params.page, params.limit, error_out=False)
            items_ = pagination.items
            total = pagination.total
            
        exclude_fields = [
            "increase_user_bitmap",
            "increase_interest_user_bitmap",
            "site_history_user_bitmap",
            "site_cur_user_bitmap",
            "site_history_interest_user_bitmap",
            "site_cur_interest_user_bitmap",
        ]
        inc_data = cls.get_invest_increase_data(params.report_type, kwargs)
        
        items = []
        for row in items_:
            # 获取活期补贴数据
            inc_date_data = inc_data.get(row.report_date, {})

            item = row.to_dict()
            for field in exclude_fields:
                item.pop(field, None)
            if params.report_type == ReportType.DAILY:
                item["investment_interest_rate"] = quantize_amount(item["day_rate"] * 365, 4)
            else:
                item["investment_interest_rate"] = calc_month_report_rate(row)
            # 添加活期补贴数据
            item["invest_increase_usd"] = inc_date_data.get(row.asset, 0)

            items.append(item)
            
        if params.export:
            return export_xlsx(
                filename='investment_asset_report',
                data_list=cls.get_export_data(items),
                export_headers=cls.export_headers
            )
        return dict(
            total=total,
            items=items,
            assets=assets
        )

    @classmethod
    def get_export_data(cls, records):
        for record in records:
            record['usd'] = amount_to_str(record['usd'], 2)
            record['investment_interest_usd'] = amount_to_str(record['investment_interest_usd'], 2)
            record['investment_interest_rate'] = format_percent(record['investment_interest_rate'], 2)
        return records
