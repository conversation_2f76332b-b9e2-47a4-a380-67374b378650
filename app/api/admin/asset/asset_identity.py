# -*- coding: utf-8 -*-
from ...common.fields import Enum<PERSON>ield
from ....models.asset import AssetIdentity
from ...common import Resource, Namespace, respond_with_code


ns = Namespace('Asset - Asset Identity')
url_prefix = '/asset-identity'


@ns.route('')
@respond_with_code
class AssetIdentityResource(Resource):

    @classmethod
    @ns.use_kwargs(dict(
        status=EnumField(AssetIdentity.Status, missing=AssetIdentity.Status.ONLINE, default=AssetIdentity.Status.ONLINE),
    ))
    def get(cls, **kwargs):
        """币种列表"""
        status = kwargs['status']
        query = AssetIdentity.query
        if status is not None:
            query = query.filter(AssetIdentity.status == status)

        identities = query.order_by(AssetIdentity.code).all()
        return [{
            'id': identity.id,
            'code': identity.code,
            'status': identity.status.name,
        } for identity in identities]


