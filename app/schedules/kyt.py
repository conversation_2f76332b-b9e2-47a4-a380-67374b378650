from celery.schedules import crontab

from app.business.kyt import DepositRiskAssessmentManager, KytUsageQuotasMonitor, KYTAbnormalMonitor, \
    WithdrawalPreScreeningManager

from app.common import CeleryQueues
from app.business import lock_call
from app.utils import scheduled, route_module_to_celery_queue

route_module_to_celery_queue(__name__, CeleryQueues.WALLET)

RAM = DepositRiskAssessmentManager


@scheduled(crontab(minute="*/10"))
@lock_call()
def process_deposit_risk_assessment_reqs():
    RAM.process_assessment_requests()


@scheduled(crontab(minute="*/1"))
@lock_call()
def process_deposit_risk_assessment_reqs_quickly():
    RAM.process_assessment_requests_quickly()


@scheduled(crontab(minute="*/1"))
@lock_call()
def process_deposit_risk_assessments():
    RAM.process_assessment_applications()


@scheduled(crontab(minute='2', hour='*/6'))
@lock_call()
def kyt_usage_quotas_monitor():
    KytUsageQuotasMonitor.check_send_alerts()


@scheduled(crontab(minute='*/10'))
@lock_call()
def kyt_abnormal_monitor():
    KYTAbnormalMonitor.check_send_alerts()


@scheduled(crontab(day_of_week='1', hour='2', minute='30'))
@lock_call()
def db_table_records_monitor():
    RAM.check_db_records_alert()
    WithdrawalPreScreeningManager.check_db_records_alert()