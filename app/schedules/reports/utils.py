import datetime
from typing import Callable

from dateutil.relativedelta import relativedelta
from flask import current_app

from app.models import SubAccountAssetTransfer, RedPacketHistory, Deposit, db, SubAccount
from app.business.user import filter_active_users
from app.business.user_tag.helper import get_disabled_user_ids
from app.utils import last_month, next_month
from app.utils import today


def get_monthly_report_date(monthly_model, daily_model, include_curr_month=False):
    today_ = today()
    last_record = monthly_model.query.order_by(
        monthly_model.report_date.desc()
    ).first()

    if last_record:
        if include_curr_month:
            # 每个月1号需要更新上个月的月报，否则会漏掉最后一天
            today_ -= datetime.timedelta(days=1)
            cur_month = datetime.date(today_.year, today_.month, 1)
            if last_record.report_date == cur_month:
                start_month = cur_month
            elif last_record.report_date < cur_month:
                start_month = last_record.report_date + relativedelta(months=1)
            else:
                start_month = None
        else:
            if last_record.report_date < last_month(today_.year, today_.month):
                start_month = last_record.report_date + relativedelta(months=1)
            else:
                start_month = None

    else:
        first_record = daily_model.query.order_by(
            daily_model.report_date
        ).first()

        if first_record:
            date = first_record.report_date
            start_month = datetime.date(date.year, date.month, 1)
        else:
            start_month = None

    return start_month


def check_report_date_exists(report_model, report_date):
    q = report_model.query.filter(
        report_model.report_date == report_date
    ).first()
    if q:
        return True
    return False


def get_active_user_set(start_date, end_date):
    # 子账号划转
    sub_transfer_data = SubAccountAssetTransfer.query.filter(
        SubAccountAssetTransfer.created_at >= start_date,
        SubAccountAssetTransfer.created_at < end_date,
    ).with_entities(SubAccountAssetTransfer.target).all()
    # 红包记录
    red_packet_data = RedPacketHistory.query.filter(
        RedPacketHistory.effective_at >= start_date,
        RedPacketHistory.effective_at < end_date,
    ).with_entities(RedPacketHistory.user_id).all()
    # 充值
    deposit_data = Deposit.query.filter(
        Deposit.created_at >= start_date,
        Deposit.created_at < end_date,
    ).with_entities(Deposit.user_id).all()
    active_user_ids = set()
    active_user_ids.update([i.target for i in sub_transfer_data])
    active_user_ids.update([i.user_id for i in red_packet_data if i.user_id])
    active_user_ids.update([i.user_id for i in deposit_data])
    return active_user_ids


def get_report_active_user_ids_in_range(
    start_date: datetime.date,
    end_date: datetime.date,
    realtime_start_date: datetime.date = None,
    realtime_end_date: datetime.date = None,
    filter_user_ids: set[int] = None,
) -> set[int]:
    """返回用于报表逻辑的活跃用户"""
    daily_active_users = filter_active_users(start_date, end_date)
    daily_active_users = set(daily_active_users) | get_active_user_set(realtime_start_date or start_date, realtime_end_date or end_date)
    sub_user_ids = {r.user_id for r in SubAccount.query.with_entities(SubAccount.user_id).all()}
    disabled_user_ids = get_disabled_user_ids()
    exclude_user_ids = disabled_user_ids | sub_user_ids
    daily_active_users -= exclude_user_ids
    if filter_user_ids:
        daily_active_users = daily_active_users & filter_user_ids
    return daily_active_users


def update_monthly_data_accumulated_by(
        model: db.Model,
        update_func: Callable,
        start_month: datetime.date = None,
        filters: list = None
):
    """以月报最后时间，累计更新月报表"""
    today_ = today()
    cur_month = datetime.date(today_.year, today_.month, 1)

    if not start_month:
        if filters:
            last_record = model.query.filter(
                *filters
            ).order_by(model.report_date.desc()).first()
        else:
            last_record = model.query.order_by(model.report_date.desc()).first()
        start_month = last_record.report_date if last_record else last_month(cur_month.year, cur_month.month)
    try:
        while start_month <= cur_month:
            end_month = next_month(start_month.year, start_month.month)
            update_func(start_month, end_month)
            start_month = end_month
    except Exception as ex:
        db.session.rollback()
        current_app.logger.exception(ex)

# TODO: 当前月更新上一月月报表，每月实际执行一次业务逻辑。
