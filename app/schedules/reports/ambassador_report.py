import datetime
import io
import json
from collections import defaultdict
from itertools import chain
from decimal import Decimal, ROUND_HALF_EVEN
from dateutil.tz import UTC
from typing import Tuple, Dict, Optional, Iterable
from dateutil.relativedelta import relativedelta
from flask import current_app
from flask.templating import render_template_string
from flask_babel import force_locale, gettext as _
from sqlalchemy import func, distinct
from openpyxl import Workbook

from app.business.summary import (
    get_period_spot_trade_mapping, get_period_perp_trade_mapping,
    check_data_ready, get_period_increase_trade_users, get_first_trade_users
)
from app.common.constants import ReportType
from app.config import config
from app.business import (
    db, route_module_to_celery_queue, CeleryQueues, scheduled,
    crontab, Deposit, lock_call, AssetPrice, UserPreferences
)
from app.business.email import send_business_ambassador_referral_detail_email, EmailSender
from app.business.alert import send_alert_notice
from app.models import (
    DailyAmbassadorReferralDetailReport,
    DailyBusinessAmbassadorReferralReport,
    DailyBusinessAmbassadorDetailReferralReport,
    DailyBusinessAgentReferralReport,
    DailyBusinessUserReferralReport,
    DailyBusinessTeamReferralReport,
    DailyBusinessPrReferralReport,
    DailyBusinessAmbassadorTotalReferralReport,
    MonthlyBusinessAmbassadorReferralReport,
    MonthlyBusinessAgentReferralReport,
    MonthlyBusinessUserReferralReport,
    MonthlyBusinessTeamReferralReport,
    MonthlyBusinessPrReferralReport,
    MonthlyBusinessAmbassadorTotalReferralReport,
    Ambassador,
    BusinessAmbassador,
    TreeAmbassador,
    IndirectReferralAssetDetail,
    IndirectReferralAssetHistory,
    DailyTreeAmbassadorReferralReport, MonthlyTreeAmbassadorReferralReport,
    BusinessAmbassadorShareUser,
    BusinessUser,
    BusinessTeam,
    ReferralHistory,
    UserTradeSummary,
    ReferralAssetHistory,
    BusinessReferralAssetDetail,
    BusinessUserReferralAssetHistory,
    BusinessAgentReferralAssetHistory,
    MonthlyAmbassadorReferralDetailReport,
    User,
    UserExtra,
    DailyUserReport,
    MonthlyUserReport,
    AmbassadorAgent,
    AmbassadorAgentHistory,
    AmbassadorStatistics,
    DailyAmbassadorReferralReport,
    ReferralCodeAssetDetail,
    MonthlyReferralCodeAssetDetail,
    AppraisalHistory,
    ReferralAssetDetail,
    DailyUserReferralDetailReport,
    MonthlyUserReferralDetailReport, DailySpotTradeReport, DailyPerpetualTradeReport, BusinessAmbassadorStatistics,
    BusinessAmbassadorReferralDetailReport,
)
from app.models.broker import DailyBrokerAssetReport, DailyBrokerUserAssetReport
from app.caches.report import WeeklyAmbassadorReportCache, MonthlyAmbassadorReportCache, \
    BusMonthlyAmbassadorReportCache, BusWeeklyAmbassadorReportCache, BusinessAmbassadorDetailReportCache
from app.common.countries import get_country
from app.models.daily import DailyAmbassadorReferreeReport
from app.models.monthly import MonthlyAmbassadorReferreeReport, MonthlyBusinessAmbassadorDetailReferralReport
from app.models.quarterly import QuarterlyBusinessAmbassadorTotalReferralReport, QuarterlyUserReport
from app.models.weekly import WeeklyBusinessAmbassadorTotalReferralReport, WeeklyUserReport
from app.business.statistic import get_main_user_balance_usd_map
from app.schedules.reports.utils import get_monthly_report_date, get_report_active_user_ids_in_range
from app.utils import next_month, quantize_amount, batch_iter, str_to_datetime, today, last_month, AmountType, \
    amount_to_str, AWSBucketTmp, hide_email, ExcelExporter, upload_file
from app.utils.parser import JsonEncoder
from app.utils.date_ import date_to_datetime, datetime_to_str, yesterday

route_module_to_celery_queue(__name__, CeleryQueues.REPORT)


def update_daily_ambassador_detail_report(start_date, end_date):
    if not check_data_ready(start_date):
        current_app.logger.warning("{} update_daily_ambassador_detail_report-UserTradeSummary 数据未就绪".format(start_date))
        return

    ambassador_user = Ambassador.query.filter(
        Ambassador.status == Ambassador.Status.VALID,
    ).with_entities(
        Ambassador.user_id
    ).all()
    result = {i.user_id: {
        'refer_user_list': set(),
        'deposit_user_list': set(),
        'deal_user_list': set(),
        'refer_count': int(),
        'deposit_count': int(),
        'deal_count': int(),
        'spot_trade_usd': Decimal(),
        'spot_fee_usd': Decimal(),
        'perpetual_trade_usd': Decimal(),
        'perpetual_fee_usd': Decimal(),
        'refer_total_amount': Decimal(),
    } for i in ambassador_user}
    referree_result = defaultdict(lambda: defaultdict(lambda: {
        'spot_trade_usd': Decimal(),
        'spot_fee_usd': Decimal(),
        'perpetual_trade_usd': Decimal(),
        'perpetual_fee_usd': Decimal(),
        'refer_total_amount': Decimal(),
    }))
    
    ambassador_user_ids = {i.user_id for i in ambassador_user}
    referral_data = []
    cur_day_referral_data = []
    for chunk_user_ids in batch_iter(ambassador_user_ids, 200):
        chunk_referral_data = ReferralHistory.query.filter(
            ReferralHistory.created_at < end_date,
            ReferralHistory.status == ReferralHistory.Status.VALID,
            ReferralHistory.referrer_id.in_(chunk_user_ids),
        ).with_entities(
            ReferralHistory.referrer_id,
            ReferralHistory.referree_id,
        ).all()
        referral_data.extend(chunk_referral_data)

        chunk_cur_day_referral_data = ReferralHistory.query.filter(
            ReferralHistory.created_at < end_date,
            ReferralHistory.created_at >= start_date,
            ReferralHistory.status == ReferralHistory.Status.VALID,
            ReferralHistory.referrer_id.in_(chunk_user_ids),
        ).with_entities(
            ReferralHistory.referrer_id,
            ReferralHistory.referree_id,
        ).all()
        cur_day_referral_data.extend(chunk_cur_day_referral_data)

    for item in cur_day_referral_data:
        user_id = item.referrer_id
        result[user_id]['refer_user_list'].add(item.referree_id)
    referral_map = {i.referree_id: i.referrer_id for i in referral_data}
    referrer_ids = list({i.referrer_id for i in referral_data})
    referree_ids = [i.referree_id for i in referral_data]  # 被邀请人

    spot_data, perp_data = {}, {}
    referral_asset_data = []
    referral_asset_detail_data = []
    deposit_user_data = []
    for chunk_user_ids in batch_iter(referrer_ids, 200):
        chunk_referral_asset_data = ReferralAssetHistory.query.filter(
            ReferralAssetHistory.date == start_date,
            ReferralAssetHistory.user_id.in_(chunk_user_ids),
            ReferralAssetHistory.type == ReferralAssetHistory.Type.AMBASSADOR,
            ReferralAssetHistory.status == ReferralAssetHistory.Status.FINISHED,
        ).all()
        referral_asset_data.extend(chunk_referral_asset_data)
        chunk_referral_asset_detail_data = ReferralAssetDetail.query.filter(
            ReferralAssetDetail.date == start_date,
            ReferralAssetDetail.user_id.in_(chunk_user_ids),
            ReferralAssetDetail.type == ReferralAssetHistory.Type.AMBASSADOR,
        ).all()
        referral_asset_detail_data.extend(chunk_referral_asset_detail_data)
    for chunk_user_ids in batch_iter(referree_ids, 200):
        spot_data.update(get_period_spot_trade_mapping(start_date, end_date, user_ids=chunk_user_ids))
        perp_data.update(get_period_perp_trade_mapping(start_date, end_date, user_ids=chunk_user_ids))

        chunk_deposit_user_data = Deposit.query.filter(
            Deposit.created_at >= start_date,
            Deposit.created_at < end_date,
            Deposit.type == Deposit.Type.ON_CHAIN,
            Deposit.user_id.in_(chunk_user_ids),
        ).with_entities(
            Deposit.user_id.distinct().label('user_id'),
        ).all()
        deposit_user_data.extend(chunk_deposit_user_data)

    for user_id, amount_usd in spot_data.items():
        referral_user_id = referral_map[user_id]
        result[referral_user_id]['spot_trade_usd'] += amount_usd
        result[referral_user_id]['deal_user_list'].add(user_id)
        referree_result[referral_user_id][user_id]['spot_trade_usd'] += amount_usd
    for user_id, amount_usd in perp_data.items():
        referral_user_id = referral_map[user_id]
        result[referral_user_id]['perpetual_trade_usd'] += amount_usd
        result[referral_user_id]['deal_user_list'].add(user_id)
        referree_result[referral_user_id][user_id]['perpetual_trade_usd'] += amount_usd
    fee_rows_mapping = _get_spot_perp_fee_records(start_date, end_date)

    _update_fees(result, referrer_ids, referree_ids, fee_rows_mapping)
    _update_referree_data(referree_result, referrer_ids, referree_ids, fee_rows_mapping)
    for item in referral_asset_data:
        result[item.user_id]['refer_total_amount'] = item.amount
    for item in referral_asset_detail_data:
        referree_result[item.user_id][item.referree_id]['refer_total_amount'] += (item.spot_amount + item.perpetual_amount)
    for item in deposit_user_data:
        user_id = referral_map[item.user_id]
        result[user_id]['deposit_user_list'].add(item.user_id)

    for user_id, user_data in result.items():
        row = DailyAmbassadorReferralDetailReport.get_or_create(
            report_date=start_date,
            user_id=user_id)
        row.refer_count = len(user_data['refer_user_list'])
        row.deal_count = len(user_data['deal_user_list'])
        row.deposit_count = len(user_data['deposit_user_list'])
        row.refer_user_list = json.dumps(list(user_data['refer_user_list']))
        row.deal_user_list = json.dumps(list(user_data['deal_user_list']))
        row.deposit_user_list = json.dumps(list(user_data['deposit_user_list']))
        row.spot_trade_usd = user_data['spot_trade_usd']
        row.spot_fee_usd = user_data['spot_fee_usd']
        row.perpetual_trade_usd = user_data['perpetual_trade_usd']
        row.perpetual_fee_usd = user_data['perpetual_fee_usd']
        row.refer_total_amount = user_data['refer_total_amount']
        db.session.add(row)
    db.session.commit()
    for user_id, user_data in referree_result.items():
        for referree_id, referree_data in user_data.items():
            row = DailyAmbassadorReferreeReport.get_or_create(
                report_date=start_date,
                user_id=user_id,
                referree_id=referree_id)
            row.spot_trade_usd = referree_data['spot_trade_usd']
            row.spot_fee_usd = referree_data['spot_fee_usd']
            row.perpetual_trade_usd = referree_data['perpetual_trade_usd']
            row.perpetual_fee_usd = referree_data['perpetual_fee_usd']
            row.refer_total_amount = referree_data['refer_total_amount']
            db.session.add(row)
        db.session.commit()


def _update_referree_data(result, referrer_ids, referree_ids, fee_rows_mapping):
    for referrer_id in referrer_ids:
        fee_rows = fee_rows_mapping.get(referrer_id)
        if not fee_rows:
            continue
        for fee_row in fee_rows:
            referree_id = fee_row.referree_id
            if referree_id not in referree_ids:
                continue
            
            result[referrer_id][referree_id]['refer_total_amount'] += fee_row.spot_fee_usd
            result[referrer_id][referree_id]['perpetual_fee_usd'] += fee_row.perpetual_fee_usd



def update_monthly_ambassador_detail_report(start_date, end_date):
    daily_ambassador_data = DailyAmbassadorReferralDetailReport.query.filter(
        DailyAmbassadorReferralDetailReport.report_date >= start_date,
        DailyAmbassadorReferralDetailReport.report_date < end_date,
    ).all()
    ambassador_map = defaultdict(lambda: {
        'refer_user_list': set(),
        'deposit_user_list': set(),
        'deal_user_list': set(),
        'refer_count': int(),
        'deposit_count': int(),
        'deal_count': int(),
        'spot_trade_usd': Decimal(),
        'spot_fee_usd': Decimal(),
        'perpetual_trade_usd': Decimal(),
        'perpetual_fee_usd': Decimal(),
        'refer_total_amount': Decimal(),
    })
    for item in daily_ambassador_data:
        user_id = item.user_id
        ambassador_map[user_id]['spot_trade_usd'] += item.spot_trade_usd
        ambassador_map[user_id]['spot_fee_usd'] += item.spot_fee_usd
        ambassador_map[user_id]['perpetual_trade_usd'] += item.perpetual_trade_usd
        ambassador_map[user_id]['perpetual_fee_usd'] += item.perpetual_fee_usd
        ambassador_map[user_id]['refer_total_amount'] += item.refer_total_amount
        ambassador_map[user_id]['refer_user_list'].update(json.loads(item.refer_user_list))
        ambassador_map[user_id]['deposit_user_list'].update(json.loads(item.deposit_user_list))
        ambassador_map[user_id]['deal_user_list'].update(json.loads(item.deal_user_list))
    for user_id, user_data in ambassador_map.items():
        row = MonthlyAmbassadorReferralDetailReport.get_or_create(
            report_date=start_date,
            user_id=user_id)
        row.refer_count = len(user_data['refer_user_list'])
        row.deal_count = len(user_data['deal_user_list'])
        row.deposit_count = len(user_data['deposit_user_list'])
        row.spot_trade_usd = user_data['spot_trade_usd']
        row.spot_fee_usd = user_data['spot_fee_usd']
        row.perpetual_trade_usd = user_data['perpetual_trade_usd']
        row.perpetual_fee_usd = user_data['perpetual_fee_usd']
        row.refer_total_amount = user_data['refer_total_amount']
        db.session.add(row)
    db.session.commit()


@scheduled(crontab(minute=15, hour='2-4'))
@lock_call()
def update_daily_ambassador_detail_report_schedule():
    today = datetime.datetime.utcnow().date()
    last_record = DailyAmbassadorReferralDetailReport.query.order_by(
        DailyAmbassadorReferralDetailReport.report_date.desc()
    ).first()
    if last_record:
        start_date = last_record.report_date + datetime.timedelta(days=1)
    else:
        start_date = datetime.date(2021, 2, 1)
    while start_date < today:
        end_date = start_date + datetime.timedelta(days=1)
        update_daily_ambassador_detail_report(start_date, end_date)
        start_date += datetime.timedelta(days=1)


@scheduled(crontab(minute=29, hour='4-6', day_of_month=1))
@lock_call()
def update_monthly_ambassador_referree_report_schedule():
    cur_year_num = datetime.date.today().year
    cur_month_num = datetime.date.today().month
    cur_month = datetime.date(cur_year_num, cur_month_num, 1)
    start_month = get_monthly_report_date(
        MonthlyAmbassadorReferreeReport, DailyAmbassadorReferreeReport)
    if not start_month:
        return
    while start_month < cur_month:
        end_month = next_month(start_month.year, start_month.month)
        update_monthly_ambassador_referree_report(start_month, end_month)
        start_month = end_month
        
        
def update_monthly_ambassador_referree_report(start_month, end_month):
    
    def daily_data_ready(end_date):
        report_date = end_date - datetime.timedelta(days=1)
        rec = DailyAmbassadorReferreeReport.query.filter(
            DailyAmbassadorReferreeReport.report_date == report_date,
        ).first()
        return bool(rec)  
    
    d = end_month - datetime.timedelta(days=1)
    if not daily_data_ready(d):
        current_app.logger.warning(f"DailyAmbassadorReferreeReport 日报数据未就绪，无法生成月报")
        return
    
    daily_ambassador_data = DailyAmbassadorReferreeReport.query.filter(
        DailyAmbassadorReferreeReport.report_date >= start_month,
        DailyAmbassadorReferreeReport.report_date < end_month,
    ).all()
    referree_result = defaultdict(lambda: defaultdict(lambda: {
        'spot_trade_usd': Decimal(),
        'spot_fee_usd': Decimal(),
        'perpetual_trade_usd': Decimal(),
        'perpetual_fee_usd': Decimal(),
        'refer_total_amount': Decimal(),
    }))
    for item in daily_ambassador_data:
        user_id = item.user_id
        referree_result[user_id][item.referree_id]['spot_trade_usd'] += item.spot_trade_usd
        referree_result[user_id][item.referree_id]['spot_fee_usd'] += item.spot_fee_usd
        referree_result[user_id][item.referree_id]['perpetual_trade_usd'] += item.perpetual_trade_usd
        referree_result[user_id][item.referree_id]['perpetual_fee_usd'] += item.perpetual_fee_usd
        referree_result[user_id][item.referree_id]['refer_total_amount'] += item.refer_total_amount
    for user_id, user_data in referree_result.items():
        for referree_id, referree_data in user_data.items():
            row = MonthlyAmbassadorReferreeReport.get_or_create(
                report_date=start_month, user_id=user_id, referree_id=referree_id)
            row.spot_trade_usd = referree_data['spot_trade_usd']
            row.spot_fee_usd = referree_data['spot_fee_usd']
            row.perpetual_trade_usd = referree_data['perpetual_trade_usd']
            row.perpetual_fee_usd = referree_data['perpetual_fee_usd']
            row.refer_total_amount = referree_data['refer_total_amount']
            db.session.add(row)
        db.session.commit()
        

@scheduled(crontab(minute=20, hour='4-6', day_of_month=1))
@lock_call()
def update_monthly_ambassador_detail_report_schedule():
    cur_year_num = datetime.date.today().year
    cur_month_num = datetime.date.today().month
    cur_month = datetime.date(cur_year_num, cur_month_num, 1)
    start_month = get_monthly_report_date(
        MonthlyAmbassadorReferralDetailReport, DailyAmbassadorReferralDetailReport)
    if not start_month:
        return
    while start_month < cur_month:
        end_month = next_month(start_month.year, start_month.month)
        update_monthly_ambassador_detail_report(start_month, end_month)
        start_month = end_month
           

def update_daily_user_refer_detail_report(start_date, end_date):
    if not check_data_ready(start_date):
        current_app.logger.warning(f"update_daily_user_refer_detail_report-UserTradeSummary {start_date} 数据未就绪")
        return

    referral_asset_details = ReferralAssetDetail.query.filter(
        ReferralAssetDetail.date == start_date,
        ReferralAssetDetail.type == ReferralAssetHistory.Type.REFERRAL,
        ReferralAssetDetail.user_id != ReferralAssetDetail.referree_id,  # 排除被邀请用户的分成返佣记录
    ).with_entities(ReferralAssetDetail.user_id).all()
    normal_re_user_ids = {i.user_id for i in referral_asset_details}

    user_result_map = {i: {
        'refer_user_list': set(),
        'deposit_user_list': set(),
        'deal_user_list': set(),
        'refer_count': int(),
        'deposit_count': int(),
        'deal_count': int(),
        'spot_trade_usd': Decimal(),
        'spot_fee_usd': Decimal(),
        'perpetual_trade_usd': Decimal(),
        'perpetual_fee_usd': Decimal(),
        'refer_total_amount': Decimal(),
        'refer_total_usd': Decimal(),
    } for i in normal_re_user_ids}

    referral_data = []
    cur_day_referral_data = []
    for chunk_user_ids in batch_iter(normal_re_user_ids, 2000):
        chunk_referral_data = ReferralHistory.query.filter(
            ReferralHistory.created_at < end_date,
            ReferralHistory.status == ReferralHistory.Status.VALID,
            ReferralHistory.referrer_id.in_(chunk_user_ids),
        ).with_entities(
            ReferralHistory.created_at,
            ReferralHistory.referrer_id,
            ReferralHistory.referree_id,
        ).all()
        referral_data.extend(chunk_referral_data)
        cur_day_referral_data.extend([i for i in chunk_referral_data if i.created_at.date() >= start_date])

    for item in cur_day_referral_data:
        user_id = item.referrer_id
        user_result_map[user_id]['refer_user_list'].add(item.referree_id)
    referral_map = {i.referree_id: i.referrer_id for i in referral_data}
    referrer_ids = list({i.referrer_id for i in referral_data})
    referree_ids = [i.referree_id for i in referral_data]  # 被邀请人

    spot_data, perp_data = {}, {}
    referral_asset_data = []
    deposit_user_data = []
    for chunk_user_ids in batch_iter(referrer_ids, 2000):
        chunk_referral_asset_data = ReferralAssetHistory.query.filter(
            ReferralAssetHistory.date == start_date,
            ReferralAssetHistory.user_id.in_(chunk_user_ids),
            ReferralAssetHistory.type == ReferralAssetHistory.Type.REFERRAL,
            ReferralAssetHistory.status == ReferralAssetHistory.Status.FINISHED,
        ).all()
        referral_asset_data.extend(chunk_referral_asset_data)
    for chunk_user_ids in batch_iter(referree_ids, 2000):
        spot_data.update(get_period_spot_trade_mapping(start_date, end_date, user_ids=chunk_user_ids))
        perp_data.update(get_period_perp_trade_mapping(start_date, end_date, user_ids=chunk_user_ids))

        chunk_deposit_user_data = Deposit.query.filter(
            Deposit.created_at >= start_date,
            Deposit.created_at < end_date,
            Deposit.type == Deposit.Type.ON_CHAIN,
            Deposit.user_id.in_(chunk_user_ids),
        ).with_entities(
            Deposit.user_id.distinct().label('user_id'),
        ).all()
        deposit_user_data.extend(chunk_deposit_user_data)

    for user_id, amount_usd in spot_data.items():
        referral_user_id = referral_map[user_id]
        user_res = user_result_map[referral_user_id]
        user_res['spot_trade_usd'] += amount_usd
        user_res['deal_user_list'].add(user_id)
    for user_id, amount_usd in perp_data.items():
        referral_user_id = referral_map[user_id]
        user_res = user_result_map[referral_user_id]
        user_res['perpetual_trade_usd'] += amount_usd
        user_res['deal_user_list'].add(user_id)
    fee_rows_mapping = _get_spot_perp_fee_records(start_date, end_date)
    _update_fees(user_result_map, referrer_ids, referree_ids, fee_rows_mapping)

    price_map = AssetPrice.get_close_price_map(start_date)
    cet_price = price_map.get("CET", Decimal())
    for item in referral_asset_data:
        user_result_map[item.user_id]['refer_total_amount'] = item.amount
        user_result_map[item.user_id]['refer_total_usd'] = item.amount * cet_price
    for item in deposit_user_data:
        user_id = referral_map[item.user_id]
        user_result_map[user_id]['deposit_user_list'].add(item.user_id)

    add_nums = 0
    for user_id, user_data in user_result_map.items():
        row = DailyUserReferralDetailReport.get_or_create(report_date=start_date, user_id=user_id)
        row.refer_count = len(user_data['refer_user_list'])
        row.deal_count = len(user_data['deal_user_list'])
        row.deposit_count = len(user_data['deposit_user_list'])
        row.deposit_user_list = json.dumps(list(user_data['deposit_user_list']))
        row.refer_user_list = json.dumps(list(user_data['refer_user_list']))
        row.deal_user_list = json.dumps(list(user_data['deal_user_list']))
        row.refer_total_amount = user_data['refer_total_amount']
        row.refer_total_usd = quantize_amount(user_data['refer_total_usd'], 2)
        row.spot_trade_usd = user_data['spot_trade_usd']
        row.spot_fee_usd = user_data['spot_fee_usd']
        row.perpetual_trade_usd = user_data['perpetual_trade_usd']
        row.perpetual_fee_usd = user_data['perpetual_fee_usd']
        db.session.add(row)
        add_nums += 1
        if add_nums % 1000 == 0:
            db.session.commit()
    db.session.commit()


def update_monthly_user_refer_detail_report(start_date, end_date):
    daily_report_rows = DailyUserReferralDetailReport.query.filter(
        DailyUserReferralDetailReport.report_date >= start_date,
        DailyUserReferralDetailReport.report_date < end_date,
    ).all()
    user_result_map = defaultdict(lambda: {
        'refer_user_list': set(),
        'deposit_user_list': set(),
        'deal_user_list': set(),
        'refer_count': int(),
        'deposit_count': int(),
        'deal_count': int(),
        'spot_trade_usd': Decimal(),
        'spot_fee_usd': Decimal(),
        'perpetual_trade_usd': Decimal(),
        'perpetual_fee_usd': Decimal(),
        'refer_total_amount': Decimal(),
        'refer_total_usd': Decimal(),
    })
    for item in daily_report_rows:
        user_id = item.user_id
        user_result_map[user_id]['spot_trade_usd'] += item.spot_trade_usd
        user_result_map[user_id]['spot_fee_usd'] += item.spot_fee_usd
        user_result_map[user_id]['perpetual_trade_usd'] += item.perpetual_trade_usd
        user_result_map[user_id]['perpetual_fee_usd'] += item.perpetual_fee_usd
        user_result_map[user_id]['refer_total_amount'] += item.refer_total_amount
        user_result_map[user_id]['refer_total_usd'] += item.refer_total_usd
        user_result_map[user_id]['refer_user_list'].update(json.loads(item.refer_user_list))
        user_result_map[user_id]['deposit_user_list'].update(json.loads(item.deposit_user_list))
        user_result_map[user_id]['deal_user_list'].update(json.loads(item.deal_user_list))

    add_nums = 0
    for user_id, user_data in user_result_map.items():
        row = MonthlyUserReferralDetailReport.get_or_create(report_date=start_date, user_id=user_id)
        row.refer_count = len(user_data['refer_user_list'])
        row.deal_count = len(user_data['deal_user_list'])
        row.deposit_count = len(user_data['deposit_user_list'])
        row.refer_total_amount = user_data['refer_total_amount']
        row.refer_total_usd = quantize_amount(user_data['refer_total_usd'], 2)
        row.spot_trade_usd = user_data['spot_trade_usd']
        row.spot_fee_usd = user_data['spot_fee_usd']
        row.perpetual_trade_usd = user_data['perpetual_trade_usd']
        row.perpetual_fee_usd = user_data['perpetual_fee_usd']
        db.session.add(row)
        add_nums += 1
        if add_nums % 1000 == 0:
            db.session.commit()
    db.session.commit()


@scheduled(crontab(minute=20, hour='2-4'))
@lock_call()
def update_daily_user_refer_detail_report_schedule():
    """ 普通用户返佣记录-日报 """
    today_ = today()
    last_record = DailyUserReferralDetailReport.query.order_by(
        DailyUserReferralDetailReport.report_date.desc()
    ).first()
    if last_record:
        start_date = last_record.report_date - datetime.timedelta(days=1)
    else:
        start_date = datetime.date(2023, 1, 1)
    while start_date < today_:
        end_date = start_date + datetime.timedelta(days=1)
        update_daily_user_refer_detail_report(start_date, end_date)
        start_date += datetime.timedelta(days=1)


@scheduled(crontab(minute=25, hour='4-6', day_of_month=1))
@lock_call()
def update_monthly_user_refer_detail_report_schedule():
    """ 普通用户返佣记录-月报 """
    today_ = today()
    cur_year_num = today_.year
    cur_month_num = today_.month
    cur_month = datetime.date(cur_year_num, cur_month_num, 1)
    start_month = get_monthly_report_date(MonthlyUserReferralDetailReport, DailyUserReferralDetailReport)
    if not start_month:
        return
    while start_month < cur_month:
        end_month = next_month(start_month.year, start_month.month)
        update_monthly_user_refer_detail_report(start_month, end_month)
        start_month = end_month


class AmbassadorWeeklyReportHelper:
    """ 大使周报数据, 每周一发送上周的周报到slack """

    cache = WeeklyAmbassadorReportCache
    name = "CoinEx平台大使周报"

    def __init__(self, report_date: datetime.date):
        self.report_date = report_date  # 发送周报日期，通常是周一
        self.report_start = report_date - datetime.timedelta(days=7)  # 上周一（闭区间）
        self.report_end = report_date  # 本周一（开区间）

        data = self.cache().read()
        cache_data = json.loads(data) if data else None
        self.prev_cache_data: Optional[Dict] = cache_data

    @classmethod
    def calc_percent(cls, num: AmountType, total: AmountType) -> str:
        if total == 0:
            return "-"
        if num == 0:
            return "0%"
        p = quantize_amount(Decimal(num) / abs(Decimal(total)) * Decimal("100"), 2)
        return f"{p}%"

    @classmethod
    def calc_incr_percent(cls, num1: AmountType, num2: AmountType) -> str:
        if num2 == 0:
            return "-"
        if num1 == 0:
            return "0%"
        # 环比增长 =（本周数量 - 上周数量）/ 上周数量 * 100%
        p = quantize_amount((Decimal(num1) - Decimal(num2)) / abs(Decimal(num2)) * Decimal("100"), 2)
        return f"{p}%"

    @staticmethod
    def get_user_location_code_map(user_ids: Iterable[int]) -> Dict[int, str]:
        user_location_map = {}
        for ids_ in batch_iter(user_ids, 1000):
            chunk_rows = User.query.filter(User.id.in_(ids_)).with_entities(User.id, User.location_code).all()
            user_location_map.update(dict(chunk_rows))
        return user_location_map

    def build_data(self) -> Tuple[Dict, Dict]:
        # 1、大使数量数据
        ambassador_rows = Ambassador.query.filter(
            Ambassador.status == Ambassador.Status.VALID,
            Ambassador.effected_at < self.report_end,
        ).all()
        group_by_level = defaultdict(list)
        for row in ambassador_rows:
            group_by_level[row.level].append(row)
        diamond_count = len(group_by_level[Ambassador.Level.DIAMOND])
        gold_count = len(group_by_level[Ambassador.Level.GOLD])
        silver_count = len(group_by_level[Ambassador.Level.SILVER])
        ambassador_user_ids = [i.user_id for i in ambassador_rows]
        total_ambassador_count = len(ambassador_user_ids)

        agent_rows = (
            AmbassadorAgent.query.filter(
                AmbassadorAgent.status == AmbassadorAgent.Status.VALID,
                AmbassadorAgent.effected_at < self.report_end,
            )
            .with_entities(AmbassadorAgent.user_id)
            .all()
        )
        agent_user_ids = [i.user_id for i in agent_rows]
        total_agent_count = len(set(agent_user_ids) - set(ambassador_user_ids))

        new_ambassador_count = 0
        new_ambassador_refer_count = 0
        new_agent_refer_ambassador_count = 0
        new_application_ambassador_count = 0
        new_agent_count = 0
        new_amb_user_ids = set()
        pre_ambassador_user_ids = []
        if self.prev_cache_data:
            # 有上周数据，计算新增的大使和代理
            pre_ambassador_user_ids = self.prev_cache_data["ambassador_user_ids"]
            new_amb_user_ids = set(ambassador_user_ids) - set(pre_ambassador_user_ids)
            amb_agent_dict = {}  # { ambassador_id: agent_id }
            if new_amb_user_ids:
                for chunk_amb_user_ids in batch_iter(new_amb_user_ids, 200):
                    agent_history_rows = AmbassadorAgentHistory.query.filter(
                        AmbassadorAgentHistory.status == AmbassadorAgentHistory.Status.VALID,
                        AmbassadorAgentHistory.ambassador_id.in_(chunk_amb_user_ids),
                    ).all()
                    amb_agent_dict.update({i.ambassador_id: i.user_id for i in agent_history_rows})

            new_agent_ids = set(agent_user_ids) - set(self.prev_cache_data["agent_user_ids"]) - set(ambassador_user_ids)
            new_agent_count = len(new_agent_ids)
            new_ambassador_count = len(new_amb_user_ids)
            # 记录下具体的id
            current_app.logger.warning(f"amb_report new_amb_user_ids: {new_amb_user_ids}")
            current_app.logger.warning(f"amb_report new_agent_ids: {new_agent_ids}")
            for new_amb_id in new_amb_user_ids:
                agent_id = amb_agent_dict.get(new_amb_id)
                if agent_id is None:
                    # 无代理，自主申请
                    new_application_ambassador_count += 1
                else:
                    if agent_id in ambassador_user_ids:
                        new_ambassador_refer_count += 1
                    else:
                        new_agent_refer_ambassador_count += 1

        # 2、大使邀请数据
        # 新注册用户
        new_user_count = self.get_new_user_count()
        # 总用户数
        total_user_count = self.get_total_user_count()
        # 总交易用户数
        total_deal_user_count = self.get_total_deal_user_count()

        # 新增的交易用户
        new_deal_user_count = 0
        if self.prev_cache_data:
            new_deal_user_count = total_deal_user_count - self.prev_cache_data["total_deal_user_count"]

        # 大使邀请的新注册用户
        amb_daily_refer_report = DailyAmbassadorReferralReport.query.filter(
            DailyAmbassadorReferralReport.report_date >= self.report_start,
            DailyAmbassadorReferralReport.report_date < self.report_end,
        ).with_entities(
            func.sum(DailyAmbassadorReferralReport.refer_count).label('new_refer_count'),
            func.sum(DailyAmbassadorReferralReport.refer_deal_amount).label('new_deal_amount'),
        ).first()
        amb_new_refer_user_count = int(amb_daily_refer_report.new_refer_count or 0)
        amb_refer_deal_amount = quantize_amount(amb_daily_refer_report.new_deal_amount or 0, 2)

        amb_total_refer_user_count = 0
        amb_total_deal_user_count = 0
        for chunk_amb_user_ids in batch_iter(ambassador_user_ids, 200):
            chunk_amb_statistics_summary = (
                AmbassadorStatistics.query.filter(
                    AmbassadorStatistics.user_id.in_(chunk_amb_user_ids),
                )
                .with_entities(
                    func.sum(AmbassadorStatistics.cur_valid_refer_count),
                    func.sum(AmbassadorStatistics.refer_deal_count),
                )
                .first()
            )
            amb_total_refer_user_count += int(chunk_amb_statistics_summary[0] or 0)
            amb_total_deal_user_count += int(chunk_amb_statistics_summary[1] or 0)
        amb_new_deal_user_count = 0
        if self.prev_cache_data:
            amb_new_deal_user_count = amb_total_deal_user_count - self.prev_cache_data["amb_total_deal_user_count"]

        # 3、大使国家分布 TOP N 的数据
        num = 5
        all_user_ids = set(new_amb_user_ids) | set(ambassador_user_ids) | set(agent_user_ids)
        all_user_country_map, country_ambassador_str, country_new_ambassador_str = self.get_country_amb_str(
            all_user_ids, ambassador_user_ids, new_amb_user_ids, num, total_ambassador_count)

        country_agent_count_map = defaultdict(int)
        for user_id in set(agent_user_ids) - set(ambassador_user_ids):
            country = all_user_country_map.get(user_id, "其他")
            country_agent_count_map[country] += 1
        country_agents = list(sorted(country_agent_count_map.items(), key=lambda x: x[1], reverse=True))[:num]
        country_agent_str = "\n  ".join(
            [
                f"（{idx}）{i[0]}：{i[1]}（占比 {self.calc_percent(i[1], total_agent_count)}）"
                for idx, i in enumerate(country_agents, 1)
            ]
        )

        def get_prev(key_, default_val):
            if self.prev_cache_data:
                return self.prev_cache_data.get(key_, default_val)
            return default_val

        pre_diamond_count = int(get_prev('diamond_count', 0))
        pre_gold_count = int(get_prev('gold_count', 0))
        pre_silver_count = int(get_prev('silver_count', 0))

        # 4. 交易量
        site_trade_usd = self.get_side_trade(self.report_start, self.report_end)

        template_var_dict = dict(
            # 大使数量-统计信息
            new_ambassador_count=new_ambassador_count,  # 新增大使数
            new_ambassador_refer_count=new_ambassador_refer_count,  # 大使邀请
            new_agent_refer_ambassador_count=new_agent_refer_ambassador_count,  # 代理邀请-新增大使数
            new_application_ambassador_count=new_application_ambassador_count,  # 自主申请-新增大使数
            new_agent_count=new_agent_count,  # 新增代理数，不包括大使
            total_ambassador_count=total_ambassador_count,  # 累计大使数
            total_agent_count=total_agent_count,  # 累计代理数，不包括大使
            ambassador_user_ids=ambassador_user_ids,  # 用于计算新增大使数
            agent_user_ids=agent_user_ids,  # 用于计算新增代理数
            new_amb_inc_p=self.calc_incr_percent(new_ambassador_count, get_prev("new_ambassador_count", 0)),
            new_amb_ref_inc_p=self.calc_incr_percent(
                new_ambassador_refer_count, get_prev("new_ambassador_refer_count", 0)
            ),
            new_agent_amb_inc_p=self.calc_incr_percent(
                new_agent_refer_ambassador_count, get_prev("new_agent_refer_ambassador_count", 0)
            ),
            new_appl_amb_inc_p=self.calc_incr_percent(
                new_application_ambassador_count, get_prev("new_application_ambassador_count", 0)
            ),
            new_agent_inc_p=self.calc_incr_percent(new_agent_count, get_prev("new_agent_count", 0)),
            # 大使邀请-统计信息
            amb_new_refer_user_count=amb_new_refer_user_count,  # 大使邀请新增注册用户
            amb_new_deal_user_count=amb_new_deal_user_count,  # 大使邀请新增交易用户
            amb_refer_deal_amount=amb_refer_deal_amount,  # 大使邀请交易量
            amb_total_refer_user_count=amb_total_refer_user_count,  # 大使邀请总注册用户
            amb_total_deal_user_count=amb_total_deal_user_count,  # 大使邀请总交易用户
            new_ref_inc_p=self.calc_incr_percent(amb_new_refer_user_count, get_prev("amb_new_refer_user_count", 0)),
            new_deal_inc_p=self.calc_incr_percent(amb_new_deal_user_count, get_prev("amb_new_deal_user_count", 0)),
            total_ref_inc_p=self.calc_incr_percent(amb_total_refer_user_count, get_prev("amb_total_refer_user_count", 0)),
            total_deal_inc_p=self.calc_incr_percent(amb_refer_deal_amount, get_prev("amb_refer_deal_amount", 0)),
            deal_amount_p=self.calc_incr_percent(amb_total_deal_user_count, get_prev("amb_total_deal_user_count", 0)),
            new_ref_p=self.calc_percent(amb_new_refer_user_count, new_user_count),
            new_deal_p=self.calc_percent(amb_new_deal_user_count, new_deal_user_count),
            total_ref_p=self.calc_percent(amb_total_refer_user_count, total_user_count),
            total_deal_p=self.calc_percent(amb_total_deal_user_count, total_deal_user_count),
            total_deal_amount_p=self.calc_percent(amb_refer_deal_amount, site_trade_usd),
            # 国家分布 TOP N 信息
            country_new_ambassador_str=country_new_ambassador_str or "-",  # 平台大使新增来源
            country_ambassador_str=country_ambassador_str or "-",  # 平台大使总数
            country_agent_str=country_agent_str or "-",  # 大使代理（不包括大使）总数
            # 大使月度考核排名
            diamond_count=diamond_count,
            diamond_inc_count=diamond_count - pre_diamond_count,
            diamond_inc_p=self.calc_percent(diamond_count, total_ambassador_count),
            gold_count=gold_count,
            gold_inc_count=gold_count - pre_gold_count,
            gold_inc_p=self.calc_percent(gold_count, total_ambassador_count),
            silver_count=silver_count,
            silver_inc_count=silver_count - pre_silver_count,
            silver_inc_p=self.calc_percent(silver_count, total_ambassador_count)
        )
        cache_data_dict = dict(
            new_ambassador_count=new_ambassador_count,
            new_ambassador_refer_count=new_ambassador_refer_count,
            new_agent_refer_ambassador_count=new_agent_refer_ambassador_count,
            new_application_ambassador_count=new_application_ambassador_count,
            new_agent_count=new_agent_count,
            ambassador_user_ids=ambassador_user_ids,
            pre_ambassador_user_ids=pre_ambassador_user_ids,
            agent_user_ids=agent_user_ids,
            #
            amb_new_refer_user_count=amb_new_refer_user_count,
            amb_new_deal_user_count=amb_new_deal_user_count,
            amb_total_refer_user_count=amb_total_refer_user_count,
            amb_total_deal_user_count=amb_total_deal_user_count,
            total_deal_user_count=total_deal_user_count,
            amb_refer_deal_amount=amb_refer_deal_amount,
            #
            diamond_count=diamond_count,
            gold_count=gold_count,
            silver_count=silver_count,
        )
        return template_var_dict, cache_data_dict

    def get_country_amb_str(self, all_user_ids, ambassador_user_ids, new_amb_user_ids, num, total_ambassador_count):
        all_user_location_map = self.get_user_location_code_map(all_user_ids)
        all_user_country_map = {}
        for user_id in all_user_ids:
            location_code = all_user_location_map.get(user_id)
            country = "其他"
            if location_code and (c := get_country(location_code)):
                country = c.cn_name
            all_user_country_map[user_id] = country
        country_new_amb_count_map = defaultdict(int)
        for user_id in new_amb_user_ids:
            country = all_user_country_map.get(user_id, "其他")
            country_new_amb_count_map[country] += 1
        country_new_ambassadors = list(sorted(country_new_amb_count_map.items(), key=lambda x: x[1], reverse=True))[
                                  :num]
        country_new_ambassador_str = "\n  ".join(
            [f"（{idx}）{i[0]}：{i[1]}" for idx, i in enumerate(country_new_ambassadors, 1)])
        country_amb_count_map = defaultdict(int)
        for user_id in ambassador_user_ids:
            country = all_user_country_map.get(user_id, "其他")
            country_amb_count_map[country] += 1
        country_ambassadors = list(sorted(country_amb_count_map.items(), key=lambda x: x[1], reverse=True))[:num]
        country_ambassador_str = "\n  ".join(
            [
                f"（{idx}）{i[0]}：{i[1]}（占比 {self.calc_percent(i[1], total_ambassador_count)}）"
                for idx, i in enumerate(country_ambassadors, 1)
            ]
        )
        return all_user_country_map, country_ambassador_str, country_new_ambassador_str

    def get_total_deal_user_count(self):
        total_deal_user_count = UserTradeSummary.query.filter(
            UserTradeSummary.report_date < self.report_end,
        ).with_entities(
            func.count(distinct(UserTradeSummary.user_id)),
        ).scalar() or 0
        return total_deal_user_count

    def get_total_user_count(self):
        total_user_count = User.query.filter(User.created_at < self.report_end).count()
        return total_user_count

    def get_new_user_count(self):
        new_user_count = User.query.filter(
            User.created_at >= self.report_start,
            User.created_at < self.report_end,
            User.user_type != User.UserType.SUB_ACCOUNT,
        ).count()
        return new_user_count

    def format_content(self, var_dict: Dict, title: str, monthly: bool = False) -> str:
        end_date_str = (self.report_end - datetime.timedelta(days=1)).strftime('%Y/%m/%d')
        contents = [
            f"{title}：{self.report_start.strftime('%Y/%m/%d')} - {end_date_str}",
            f"",
            f"一、大使数目",
            f"1. 新增平台大使：{var_dict['new_ambassador_count']}（{var_dict['new_amb_inc_p']}）",
            f"  - 大使邀请：{var_dict['new_ambassador_refer_count']}（{var_dict['new_amb_ref_inc_p']}）",
            f"  - 大使代理邀请（不包括大使）：{var_dict['new_agent_refer_ambassador_count']}（{var_dict['new_agent_amb_inc_p']}）",
            f"  - 自主申请：{var_dict['new_application_ambassador_count']}（{var_dict['new_appl_amb_inc_p']}）",
            f"2. 累计平台大使：{var_dict['total_ambassador_count']}",
            f"",
            f"二、大使邀请",
            f"1. 新增注册用户：{var_dict['amb_new_refer_user_count']}（{var_dict['new_ref_inc_p']}；"
            f"占比 {var_dict['new_ref_p']}）",
            f"2. 新增交易用户：{var_dict['amb_new_deal_user_count']}（{var_dict['new_deal_inc_p']}；"
            f"占比 {var_dict['new_deal_p']}）",
            f"",
            f"三、国家分布Top 5",
            f"1. 平台大使新增来源",
            f"  {var_dict['country_new_ambassador_str']}",
            f"2. 平台大使总数",
            f"  {var_dict['country_ambassador_str']}",
            f"四、交易量",
            f"1. 平台大使邀请用户交易量：{var_dict['amb_refer_deal_amount']} USD（环比 {var_dict['deal_amount_p']}；"
            f"占比 {var_dict['total_deal_amount_p']}）",
            ""
        ]
        if monthly:
            contents += [
                f"五、本月大使考核",
                f"- 钻石大使：{var_dict['diamond_count']}（环比上月增长{var_dict['diamond_inc_count']}名，"
                f"占比{var_dict['diamond_inc_p']}）",
                f"- 黄金大使：{var_dict['gold_count']}（环比上月增长{var_dict['gold_inc_count']}名，"
                f"占比{var_dict['gold_inc_p']}）",
                f"- 白银大使：{var_dict['silver_count']}（环比上月增长{var_dict['silver_inc_count']}名，"
                f"占比{var_dict['silver_inc_p']}）",
                f""
            ]
        contents.append(f"--------------------------------------------------------------------")
        return "\n".join(contents)

    def get_side_trade(self, st, et):
        spot_trade = self.get_site_trade_usd(DailySpotTradeReport, st, et)
        perp_trade = self.get_site_trade_usd(DailyPerpetualTradeReport, st, et)
        # 全站交易额计算双边要 * 2
        site_trade_usd = (spot_trade + perp_trade) * 2
        return site_trade_usd

    def get_site_trade_usd(self, model, st, et):
        trade_usd = model.query.filter(
            model.report_date >= st,
            model.report_date < et,
        ).with_entities(
            func.sum(model.trade_usd)
        ).scalar()
        return trade_usd

    def send_report(self, update_cache: bool = True) -> bool:
        if self.prev_cache_data:
            last_report_date = self.prev_cache_data.get("last_report_date")
            if last_report_date and self.report_date <= str_to_datetime(last_report_date).date():
                return False

        template_var_dict, cache_data_dict = self.build_data()
        content = self.format_content(template_var_dict, self.name)
        send_alert_notice(content, config["ADMIN_CONTACTS"].get("ambassador_notice"))

        if update_cache:
            new_cache_data = {
                "last_report_date": self.report_date.strftime("%Y-%m-%d"),
                **cache_data_dict,
            }
            self.cache().set(json.dumps(new_cache_data, cls=JsonEncoder))

        return True


class MonthlyReportBase(AmbassadorWeeklyReportHelper):
    cache = None
    name = ""

    def __init__(self, report_date: datetime.date):
        self.report_date = report_date  # 发送周报日期，通常是每月一号，写入缓存
        self.report_start = last_month(report_date.year, report_date.month)  # 上个月一号（闭区间）
        self.report_end = datetime.date(report_date.year, report_date.month, 1)  # 这个月一号（开区间）

        data = self.cache().read()
        cache_data = json.loads(data) if data else None
        self.prev_cache_data: Optional[Dict] = cache_data

    def send_report(self, update_cache: bool = True) -> bool:
        if self.prev_cache_data:
            last_report_date = self.prev_cache_data.get("last_report_date")
            if last_report_date and self.report_date <= str_to_datetime(last_report_date).date():
                return False

        template_var_dict, cache_data_dict = self.build_data()
        content = self.format_content(template_var_dict, title=self.name, monthly=True)
        send_alert_notice(content, config["ADMIN_CONTACTS"].get("ambassador_notice"))

        if update_cache:
            new_cache_data = {
                "last_report_date": self.report_date.strftime("%Y-%m-%d"),
                **cache_data_dict,
            }
            self.cache().set(json.dumps(new_cache_data, cls=JsonEncoder))

        return True


class AmbassadorMonthlyReportHelper(MonthlyReportBase):
    """ 大使月报数据, 每月1号发送上月的周报到微信群 """
    cache = MonthlyAmbassadorReportCache
    name = "CoinEx平台大使月报"
    is_bus = False


class BusinessAmbWeeklyReportHelper(AmbassadorWeeklyReportHelper):
    """ 商务大使周报数据, 每周一发送上周的周报到slack """
    cache = BusWeeklyAmbassadorReportCache
    name = "CoinEx商务大使周报"

    def build_data(self) -> Tuple[Dict, Dict]:
        # 1、商务大使数量
        b_model = BusinessAmbassador
        ambassador_rows = b_model.query.filter(
            b_model.status == b_model.Status.VALID,
            b_model.effected_at < self.report_end,
        ).all()
        ambassador_user_ids = [i.user_id for i in ambassador_rows]
        total_ambassador_count = len(ambassador_user_ids)

        new_ambassador_count = 0
        new_amb_user_ids = set()
        if self.prev_cache_data:
            # 有上周数据，计算新增的商务大使
            new_amb_user_ids = set(ambassador_user_ids) - set(self.prev_cache_data["ambassador_user_ids"])
            new_ambassador_count = len(new_amb_user_ids)
            # 记录下具体的id
            current_app.logger.warning(f"bus_amb_report new_amb_user_ids: {new_amb_user_ids}")

        # 2、大使邀请数据
        # 新注册用户
        new_user_count = self.get_new_user_count()
        # 总用户数
        total_user_count = self.get_total_user_count()
        # 总交易用户数
        total_deal_user_count = self.get_total_deal_user_count()

        # 新增的交易用户
        new_deal_user_count = 0
        if self.prev_cache_data:
            new_deal_user_count = total_deal_user_count - self.prev_cache_data["total_deal_user_count"]

        # 大使邀请的新增用户
        daily_model = DailyBusinessAmbassadorTotalReferralReport
        amb_daily_refer_report = daily_model.query.filter(
            daily_model.report_date >= self.report_start,
            daily_model.report_date < self.report_end,
            daily_model.team_id == 0,
        ).with_entities(
            func.sum(daily_model.new_refer_count).label('new_refer_count'),
            func.sum(daily_model.new_deal_count).label('new_deal_count'),
            func.sum(daily_model.deal_usd).label('new_deal_amount'),
        ).first()
        amb_new_refer_user_count = int(amb_daily_refer_report.new_refer_count or 0)
        amb_new_deal_user_count = int(amb_daily_refer_report.new_deal_count or 0)
        amb_refer_deal_amount = quantize_amount(amb_daily_refer_report.new_deal_amount, 2,
                                                rounding=ROUND_HALF_EVEN)

        # 大使邀请总数据
        amb_total_refer_user_count = 0
        amb_total_deal_user_count = 0
        bus_statis_model = BusinessAmbassadorStatistics
        for chunk_amb_user_ids in batch_iter(ambassador_user_ids, 200):
            chunk_amb_statistics_summary = (
                bus_statis_model.query.filter(
                    bus_statis_model.user_id.in_(chunk_amb_user_ids),
                )
                .with_entities(
                    func.sum(bus_statis_model.effect_refer_count),
                    func.sum(bus_statis_model.effect_refer_trade_count),
                )
                .first()
            )
            amb_total_refer_user_count += int(chunk_amb_statistics_summary[0] or 0)
            amb_total_deal_user_count += int(chunk_amb_statistics_summary[1] or 0)

        # 3、大使国家分布 TOP N 的数据
        num = 5
        all_user_country_map, country_ambassador_str, country_new_ambassador_str = self.get_country_amb_str(
            ambassador_user_ids, ambassador_user_ids, new_amb_user_ids, num, total_ambassador_count)

        def get_prev(key_, default_val):
            if self.prev_cache_data:
                return self.prev_cache_data.get(key_, default_val)
            return default_val

        # 4. 交易量
        site_trade_usd = self.get_side_trade(self.report_start, self.report_end)

        template_var_dict = dict(
            # 大使数量-统计信息
            new_ambassador_count=new_ambassador_count,  # 新增大使数
            total_ambassador_count=total_ambassador_count,  # 累计大使数
            ambassador_user_ids=ambassador_user_ids,  # 用于计算新增大使数
            new_amb_inc_p=self.calc_incr_percent(new_ambassador_count, get_prev("new_ambassador_count", 0)),
            # 大使邀请-统计信息
            amb_new_refer_user_count=amb_new_refer_user_count,  # 大使邀请新增注册用户
            amb_new_deal_user_count=amb_new_deal_user_count,  # 大使邀请新增交易用户
            amb_refer_deal_amount=amb_refer_deal_amount,  # 大使邀请交易量
            amb_total_refer_user_count=amb_total_refer_user_count,  # 大使邀请总注册用户
            amb_total_deal_user_count=amb_total_deal_user_count,  # 大使邀请总交易用户
            new_ref_inc_p=self.calc_incr_percent(amb_new_refer_user_count, get_prev("amb_new_refer_user_count", 0)),
            new_deal_inc_p=self.calc_incr_percent(amb_new_deal_user_count, get_prev("amb_new_deal_user_count", 0)),
            total_ref_inc_p=self.calc_incr_percent(amb_total_refer_user_count,
                                                   get_prev("amb_total_refer_user_count", 0)),
            total_deal_inc_p=self.calc_incr_percent(amb_refer_deal_amount, get_prev("amb_refer_deal_amount", 0)),
            deal_amount_p=self.calc_incr_percent(amb_refer_deal_amount, get_prev("amb_refer_deal_amount", 0)),
            new_ref_p=self.calc_percent(amb_new_refer_user_count, new_user_count),
            new_deal_p=self.calc_percent(amb_new_deal_user_count, new_deal_user_count),
            total_ref_p=self.calc_percent(amb_total_refer_user_count, total_user_count),
            total_deal_p=self.calc_percent(amb_total_deal_user_count, total_deal_user_count),
            total_deal_amount_p=self.calc_percent(amb_refer_deal_amount, site_trade_usd),
            # 国家分布 TOP N 信息
            country_new_ambassador_str=country_new_ambassador_str or "-",  # 平台大使新增来源
            country_ambassador_str=country_ambassador_str or "-",  # 平台大使总数
        )
        cache_data_dict = dict(
            new_ambassador_count=new_ambassador_count,
            ambassador_user_ids=ambassador_user_ids,
            amb_new_refer_user_count=amb_new_refer_user_count,
            amb_new_deal_user_count=amb_new_deal_user_count,
            amb_total_refer_user_count=amb_total_refer_user_count,
            amb_total_deal_user_count=amb_total_deal_user_count,
            total_deal_user_count=total_deal_user_count,
            amb_refer_deal_amount=amb_refer_deal_amount,
        )
        return template_var_dict, cache_data_dict

    def format_content(self, var_dict: Dict, title: str, monthly: bool = False) -> str:
        end_date_str = (self.report_end - datetime.timedelta(days=1)).strftime('%Y/%m/%d')
        contents = [
            f"{title}：{self.report_start.strftime('%Y/%m/%d')} - {end_date_str}",
            f"",
            f"一、商务大使数目",
            f"1. 新增商务大使：{var_dict['new_ambassador_count']}（{var_dict['new_amb_inc_p']}）",
            f"2. 累计商务大使：{var_dict['total_ambassador_count']}",
            f"",
            f"二、商务大使邀请",
            f"1. 新增注册用户：{var_dict['amb_new_refer_user_count']}（{var_dict['new_ref_inc_p']}；"
            f"占比 {var_dict['new_ref_p']}）",
            f"2. 新增交易用户：{var_dict['amb_new_deal_user_count']}（{var_dict['new_deal_inc_p']}；"
            f"占比 {var_dict['new_deal_p']}）",
            f"",
            f"三、国家分布Top 5",
            f"1. 商务大使新增来源",
            f"  {var_dict['country_new_ambassador_str']}",
            f"2. 商务大使总数",
            f"  {var_dict['country_ambassador_str']}",
            f"四、交易量",
            f"1. 商务大使邀请用户交易量：{var_dict['amb_refer_deal_amount']} USD（环比 {var_dict['deal_amount_p']}；"
            f"占比 {var_dict['total_deal_amount_p']}）",
            f"",
            f"--------------------------------------------------------------------",
        ]
        return "\n".join(contents)


class BusinessAmbMonthlyReportHelper(BusinessAmbWeeklyReportHelper, MonthlyReportBase):
    """ 商务大使月报数据, 每月1号发送上月的周报到微信群 """
    cache = BusMonthlyAmbassadorReportCache
    name = "CoinEx商务大使月报"

    def __init__(self, report_date: datetime.date):
        MonthlyReportBase.__init__(self, report_date)


@scheduled(crontab(day_of_week=1, minute="10,20,30", hour="4"))
@lock_call()
def push_ambassador_weekly_report_schedule():
    # 大使周报推送
    today_ = today()
    for cls in [AmbassadorWeeklyReportHelper, BusinessAmbWeeklyReportHelper]:
        report_helper = cls(today_)
        update_cache = today_.weekday() == 0
        report_helper.send_report(update_cache=update_cache)


@scheduled(crontab(day_of_month=1, minute="12,22,32", hour="9,10"))
@lock_call()
def push_ambassador_monthly_report_schedule():
    # 大使月报推送
    today_ = today()

    # 保证在每月大使考核后 再推送
    report_date = last_month(today_.year, today_.month)
    last = AppraisalHistory.query.filter(
        AppraisalHistory.business_type == AppraisalHistory.BusinessType.AMBASSADOR,
        AppraisalHistory.report_date == report_date,
    ).first()
    if not last:
        return

    for cls in [AmbassadorMonthlyReportHelper, BusinessAmbMonthlyReportHelper]:
        report_helper = cls(today_)
        update_cache = today_.day == 1
        report_helper.send_report(update_cache=update_cache)


def update_monthly_referral_code_asset_detail(start_date, end_date):
    daily_code_details = (
        ReferralCodeAssetDetail.query.filter(
            ReferralCodeAssetDetail.date >= start_date,
            ReferralCodeAssetDetail.date < end_date,
        )
        .group_by(
            ReferralCodeAssetDetail.user_id,
            ReferralCodeAssetDetail.referral_id,
            ReferralCodeAssetDetail.type,
            ReferralCodeAssetDetail.asset,
        )
        .with_entities(
            ReferralCodeAssetDetail.user_id,
            ReferralCodeAssetDetail.referral_id,
            ReferralCodeAssetDetail.type,
            ReferralCodeAssetDetail.asset,
            func.sum(ReferralCodeAssetDetail.spot_amount).label("total_spot_amount"),
            func.sum(ReferralCodeAssetDetail.perpetual_amount).label("total_perpetual_amount"),
            func.sum(ReferralCodeAssetDetail.spot_fee_usd).label("total_spot_fee_usd"),
            func.sum(ReferralCodeAssetDetail.perpetual_fee_usd).label("total_perpetual_fee_usd"),
        )
        .all()
    )
    for row in daily_code_details:
        monthly_row = MonthlyReferralCodeAssetDetail.get_or_create(
            date=start_date,
            user_id=row.user_id,
            referral_id=row.referral_id,
            type=row.type.name,  # note: row.type is enum
            asset=row.asset,
        )
        monthly_row.spot_amount = row.total_spot_amount
        monthly_row.perpetual_amount = row.total_perpetual_amount
        monthly_row.spot_fee_usd = row.total_spot_fee_usd
        monthly_row.perpetual_fee_usd = row.total_perpetual_fee_usd
        db.session.add(monthly_row)
    db.session.commit()


@scheduled(crontab(minute=20, hour="5-6"))
@lock_call()
def update_monthly_referral_code_asset_detail_schedule():
    # 用户邀请码的返佣-月报  user_id和referral_id纬度的聚合
    # 要在更新ReferralCodeAssetDetail之后执行
    last = MonthlyReferralCodeAssetDetail.query.order_by(MonthlyReferralCodeAssetDetail.date.desc()).first()
    if last:
        start_month = next_month(last.date.year, last.date.month)
    else:
        first_ref_date = ReferralCodeAssetDetail.query.order_by(ReferralCodeAssetDetail.date.asc()).first().date
        start_month = datetime.date(first_ref_date.year, first_ref_date.month, 1)

    _today = today()
    cur_month = datetime.date(_today.year, _today.month, 1)
    if start_month > cur_month:
        # 重复更新当月的数据
        start_month = cur_month

    while start_month <= cur_month:
        end_month = next_month(start_month.year, start_month.month)
        update_monthly_referral_code_asset_detail(start_month, end_month)
        start_month = end_month


@scheduled(crontab(minute=25, hour='2-4'))
@lock_call()
def update_daily_business_report_schedule():
    today_ = datetime.datetime.utcnow().date()
    last_record = DailyBusinessAmbassadorReferralReport.query.order_by(
        DailyBusinessAmbassadorReferralReport.report_date.desc()
    ).first()
    if last_record:
        start_date = last_record.report_date + datetime.timedelta(days=1)
    else:
        start_date = datetime.date(2024, 3, 1)
    while start_date < today_:
        end_date = start_date + datetime.timedelta(days=1)
        update_daily_business_report(start_date, end_date)
        start_date += datetime.timedelta(days=1)


def get_share_data():
    """整理商务代理与商务大使关联数据"""
    share_data = defaultdict(set)
    for item in BusinessAmbassadorShareUser.query.filter(
            BusinessAmbassadorShareUser.status == BusinessAmbassadorShareUser.Status.VALID,
    ).with_entities(
        BusinessAmbassadorShareUser.bus_amb_id,
        BusinessAmbassadorShareUser.target_user_id,
    ).all():
        share_data[item.target_user_id].add(item.bus_amb_id)
    return share_data


def get_business_data(root_amb_child_ids_map: dict[int, set[int]]) -> dict[int, set[int]]:
    """整理商务与商务大使关联数据"""
    business_data = defaultdict(set)
    for item in BusinessAmbassador.query.filter(
            BusinessAmbassador.status == BusinessAmbassador.Status.VALID,
    ).with_entities(
        BusinessAmbassador.user_id,
        BusinessAmbassador.bus_user_id,
    ).all():
        if item.bus_user_id is not None:
            business_data[item.bus_user_id].add(item.user_id)
            if item.user_id in root_amb_child_ids_map:
                # 把root商务大使的全部下级大使也加到该商务名下
                business_data[item.bus_user_id].update(root_amb_child_ids_map[item.user_id])
    return business_data


def get_leader_and_pr_data(business_data):
    """整理商务组长与商务大使关联数据"""
    leader_data = defaultdict(set)
    pr_id_amb_ids_map = defaultdict(set)

    team_rows = BusinessTeam.query.filter(
        BusinessTeam.status == BusinessTeam.Status.VALID,
    ).with_entities(
        BusinessTeam.id,
        BusinessTeam.leader_id,
        BusinessTeam.pr_user_ids,
    ).all()
    team_leader_data = {
        item.id: item.leader_id
        for item in team_rows
    }
    team_id_pr_ids_map = {
        item.id: item.pr_user_ids
        for item in team_rows
    }

    for item in BusinessUser.query.filter(
            BusinessUser.status == BusinessUser.Status.VALID,
    ).with_entities(
        BusinessUser.user_id,
        BusinessUser.team_id,
    ).all():
        if item.team_id not in team_leader_data:
            continue
        if item.user_id not in business_data:
            continue
        leader_data[team_leader_data[item.team_id]].update(business_data[item.user_id])
        pr_ids = team_id_pr_ids_map.get(item.team_id, set())
        for pr_id in pr_ids:
            pr_id_amb_ids_map[pr_id].update(business_data[item.user_id])
    return leader_data, pr_id_amb_ids_map


def group_by_child_indirect_ref_amount(start_date, end_date) -> dict[int, dict]:
    """ 按下级大使汇总给上级大使的非直客返佣数 """
    rows = IndirectReferralAssetDetail.query.filter(
        IndirectReferralAssetDetail.date >= start_date,
        IndirectReferralAssetDetail.date < end_date,
        IndirectReferralAssetDetail.type == IndirectReferralAssetDetail.Type.TREE_AMB_TO_PARENT,
    ).group_by(
        IndirectReferralAssetDetail.referrer_id,
        IndirectReferralAssetDetail.target_user_id,
    ).with_entities(
        IndirectReferralAssetDetail.referrer_id,
        IndirectReferralAssetDetail.target_user_id,
        func.sum(IndirectReferralAssetDetail.spot_amount + IndirectReferralAssetDetail.perpetual_amount).label("total_amount"),
    ).all()
    child_res_map = defaultdict(dict)
    for r in rows:
        child_res_map[r.referrer_id][r.target_user_id] = r.total_amount
    return child_res_map


def update_daily_business_report(start_date, end_date):
    """更新商务大使与商务代理返佣日报"""
    if not check_data_ready(start_date):
        current_app.logger.warning("{} update_daily_business_and_share_report-UserTradeSummary 数据未就绪".format(start_date))
        return

    all_bus_amb_rows = BusinessAmbassador.query.with_entities(
        BusinessAmbassador.user_id,
        BusinessAmbassador.effected_at,
        BusinessAmbassador.type,
        BusinessAmbassador.status,
    ).all()
    bus_amb_rows = [i for i in all_bus_amb_rows if i.status == BusinessAmbassador.Status.VALID]
    bus_effected_map = {i.user_id: i.effected_at for i in bus_amb_rows}
    tree_amb_rows = TreeAmbassador.query.filter(
        TreeAmbassador.status == TreeAmbassador.Status.VALID,
    ).with_entities(
        TreeAmbassador.user_id,
        TreeAmbassador.root_id,
        TreeAmbassador.effected_at,
        TreeAmbassador.tree_height,
    ).all()
    root_amb_child_ids_map = defaultdict(set)
    tree_amb_ids = set()
    tree_amb_height_map = {}
    for _tree_amb in tree_amb_rows:
        root_amb_child_ids_map[_tree_amb.root_id].add(_tree_amb.user_id)
        bus_effected_map[_tree_amb.user_id] = _tree_amb.effected_at
        tree_amb_ids.add(_tree_amb.user_id)
        tree_amb_height_map[_tree_amb.user_id] = _tree_amb.tree_height
    for bus_amb in all_bus_amb_rows:
        if bus_amb.status == BusinessAmbassador.Status.VALID and bus_amb.type == BusinessAmbassador.Type.TREE_ROOT:
            tree_amb_height_map[bus_amb.user_id] = 0
    result = {user_id: {
        'refer_user_list': set(),
        'deposit_user_list': set(),
        'deal_user_list': set(),
        'spot_trade_usd': Decimal(),
        'spot_fee_usd': Decimal(),
        'perpetual_trade_usd': Decimal(),
        'perpetual_fee_usd': Decimal(),
        'refer_total_amount': Decimal(),
        'bro_spot_trade_usd': Decimal(),
        'bro_spot_fee_usd': Decimal(),
        'bro_perpetual_trade_usd': Decimal(),
        'bro_perpetual_fee_usd': Decimal(),
        'bro_refer_total_amount': Decimal(),
    } for user_id in bus_effected_map.keys()}
    
    # 商务大使被邀请人明细数据
    referree_result = defaultdict(lambda: defaultdict(lambda: {
        'spot_trade_usd': Decimal(),
        'spot_fee_usd': Decimal(),
        'perpetual_trade_usd': Decimal(),
        'perpetual_fee_usd': Decimal(),
        'refer_total_amount': Decimal(),
    }))
    referral_data = []
    for chunk_user_ids in batch_iter(bus_effected_map.keys(), 2000):
        referral_data.extend(ReferralHistory.query.filter(
            ReferralHistory.effected_at < end_date,
            ReferralHistory.status == ReferralHistory.Status.VALID,
            ReferralHistory.referrer_id.in_(chunk_user_ids),
        ).with_entities(
            ReferralHistory.referrer_id,
            ReferralHistory.referree_id,
            ReferralHistory.effected_at,
        ).all())
    referral_map = {}  # 邀请关系
    er_ee_ids_map = defaultdict(set)
    referrer_id_set = set()  # 邀请人
    referree_ids = []  # 被邀请人
    start_datetime = datetime.datetime(year=start_date.year, month=start_date.month, day=start_date.day, tzinfo=UTC)
    for item in referral_data:
        # 不判断生效时间，只要是有效的邀请关系就算
        # if item.effected_at < bus_effected_map[item.referrer_id]:
        #     continue
        referral_map[item.referree_id] = item.referrer_id
        er_ee_ids_map[item.referrer_id].add(item.referree_id)
        referrer_id_set.add(item.referrer_id)
        referree_ids.append(item.referree_id)
        if item.effected_at >= start_datetime:
            result[item.referrer_id]['refer_user_list'].add(item.referree_id)
    referrer_ids = list(referrer_id_set)  # 邀请人

    spot_data, perp_data = {}, {}
    deposit_user_data = []
    for chunk_user_ids in batch_iter(referree_ids, 2000):
        spot_data.update(get_period_spot_trade_mapping(start_date, end_date, user_ids=chunk_user_ids))
        perp_data.update(get_period_perp_trade_mapping(start_date, end_date, user_ids=chunk_user_ids))

        deposit_user_data.extend(Deposit.query.filter(
            Deposit.created_at >= start_date,
            Deposit.created_at < end_date,
            Deposit.type == Deposit.Type.ON_CHAIN,
            Deposit.user_id.in_(chunk_user_ids),
        ).with_entities(
            Deposit.user_id.distinct().label('user_id'),
        ).all())

    referral_asset_data = []
    referral_asset_detail_data = []
    for chunk_user_ids in batch_iter(referrer_ids, 2000):
        referral_asset_data.extend(ReferralAssetHistory.query.filter(
            ReferralAssetHistory.date == start_date,
            ReferralAssetHistory.user_id.in_(chunk_user_ids),
            ReferralAssetHistory.type == ReferralAssetHistory.Type.AMBASSADOR,
            ReferralAssetHistory.status == ReferralAssetHistory.Status.FINISHED,
        ).all())
        
        chunk_referral_asset_detail_data = ReferralAssetDetail.query.filter(
            ReferralAssetDetail.date == start_date,
            ReferralAssetDetail.user_id.in_(chunk_user_ids),
            ReferralAssetDetail.type == ReferralAssetHistory.Type.AMBASSADOR,
        ).all()
        referral_asset_detail_data.extend(chunk_referral_asset_detail_data)

    for user_id, amount_usd in spot_data.items():
        result_data = result[referral_map[user_id]]
        result_data['spot_trade_usd'] += amount_usd
        result_data['deal_user_list'].add(user_id)
        # 更新明细数据
        referree_result[referral_map[user_id]][user_id]['spot_trade_usd'] += amount_usd
    for user_id, amount_usd in perp_data.items():
        result_data = result[referral_map[user_id]]
        result_data['perpetual_trade_usd'] += amount_usd
        result_data['deal_user_list'].add(user_id)
        # 更新明细数据
        referree_result[referral_map[user_id]][user_id]['perpetual_trade_usd'] += amount_usd
    fee_rows_mapping = _get_spot_perp_fee_records(start_date, end_date)
    _update_fees(result, referrer_id_set, referree_ids, fee_rows_mapping)
    _update_referree_data(referree_result, referrer_ids, referree_ids, fee_rows_mapping)
    
    for item in referral_asset_data:
        result[item.user_id]['refer_total_amount'] = item.amount
    # 更新明细数据的返佣
    for item in referral_asset_detail_data:
        referree_result[item.user_id][item.referree_id]['refer_total_amount'] += (item.spot_amount + item.perpetual_amount)
    for item in deposit_user_data:
        result[referral_map[item.user_id]]['deposit_user_list'].add(item.user_id)

    # broker商务大使的数据
    bro_amb_ids = {i.user_id for i in bus_amb_rows if i.type == BusinessAmbassador.Type.BROKER and i.effected_at.date() <= start_date}
    ee_bro_trades_map = defaultdict(dict)
    bro_ee_trades_map = defaultdict(dict)
    for ch_bro_ids in batch_iter(bro_amb_ids, 500):
        ch_bro_data = DailyBrokerUserAssetReport.query.filter(
            DailyBrokerUserAssetReport.broker_user_id.in_(ch_bro_ids),
            DailyBrokerUserAssetReport.date == start_date,
        ).with_entities(
            DailyBrokerUserAssetReport.broker_user_id,
            DailyBrokerUserAssetReport.user_id,
            DailyBrokerUserAssetReport.spot_trade_usd,
            DailyBrokerUserAssetReport.perpetual_trade_usd,
            DailyBrokerUserAssetReport.spot_fee_usd,
            DailyBrokerUserAssetReport.perpetual_fee_usd,
        ).all()
        for _d in ch_bro_data:
            ee_bro_trades_map[_d.user_id][_d.broker_user_id] = [_d.spot_trade_usd, _d.perpetual_trade_usd,
                                                                _d.spot_fee_usd, _d.perpetual_fee_usd]
            bro_ee_trades_map[_d.broker_user_id][_d.user_id] = [_d.spot_trade_usd, _d.perpetual_trade_usd,
                                                                _d.spot_fee_usd, _d.perpetual_fee_usd]
    for bus_amb in bus_amb_rows:
        amb_id = bus_amb.user_id
        result_data = result[amb_id]
        bro_spot_trade_usd = bro_per_trade_usd = Decimal()
        bro_spot_fee_usd = bro_per_fee_usd = Decimal()
        # ee_ids = er_ee_ids_map[amb_id]  # _get_spot_perp_fee_records 查的返佣手续费，返佣已经处理ee bro的减半了，所以这里不再处理
        if amb_id in bro_amb_ids:
            for _ee_id, _spot_per in bro_ee_trades_map[amb_id].items():
                _er_id = referral_map.get(_ee_id, None)
                if _er_id in bus_effected_map:
                    bro_spot_trade_usd += quantize_amount((_spot_per[0] / 2), 8)
                    bro_per_trade_usd += quantize_amount((_spot_per[1] / 2), 8)
                    bro_spot_fee_usd += quantize_amount((_spot_per[2] / 2), 8)
                    bro_per_fee_usd += quantize_amount((_spot_per[3] / 2), 8)
                else:
                    # 没人邀请 或 邀请人不是商务大使，不需要减半
                    bro_spot_trade_usd += quantize_amount((_spot_per[0]), 8)
                    bro_per_trade_usd += quantize_amount((_spot_per[1]), 8)
                    bro_spot_fee_usd += quantize_amount((_spot_per[2]), 8)
                    bro_per_fee_usd += quantize_amount((_spot_per[3]), 8)
        result_data['bro_spot_trade_usd'] = bro_spot_trade_usd
        result_data['bro_spot_fee_usd'] = bro_spot_fee_usd
        result_data['bro_perpetual_trade_usd'] = bro_per_trade_usd
        result_data['bro_perpetual_fee_usd'] = bro_per_fee_usd
    for ch_bro_ids in batch_iter(bro_amb_ids, 1000):
        ch_bro_data = DailyBrokerAssetReport.query.filter(
            DailyBrokerAssetReport.user_id.in_(ch_bro_ids),
            DailyBrokerAssetReport.date == start_date,
        ).with_entities(
            DailyBrokerAssetReport.user_id,
            DailyBrokerAssetReport.amount,
        ).all()
        for bro_d in ch_bro_data:
            result_data = result[bro_d.user_id]
            result_data['bro_refer_total_amount'] = bro_d.amount

    # 整理商务代理与商务大使关联数据
    share_data = get_share_data()

    # 整理商务与商务大使关联数据
    business_data = get_business_data(root_amb_child_ids_map)

    # 整理商务组长、PR与商务大使关联数据
    leader_data, pr_id_amb_ids_map = get_leader_and_pr_data(business_data)

    child_indirect_ref_amount_map = group_by_child_indirect_ref_amount(start_date, end_date)
    indirect_amb_ids = set()
    for c_id, ref_v in child_indirect_ref_amount_map.items():
        indirect_amb_ids.add(c_id)
        indirect_amb_ids.update(ref_v.keys())
    _miss_tree_ids = indirect_amb_ids - set(tree_amb_height_map)
    if _miss_tree_ids:
        _miss_tree_rows = TreeAmbassador.query.filter(
            TreeAmbassador.user_id.in_(_miss_tree_ids)
        ).with_entities(
            TreeAmbassador.user_id,
            TreeAmbassador.tree_height,
        ).all()
        tree_amb_height_map.update(dict(_miss_tree_rows))

    share_referral_data = defaultdict(lambda: defaultdict(Decimal))
    for chunk_user_ids in batch_iter(share_data.keys(), 2000):
        for item in BusinessReferralAssetDetail.query.filter(
            BusinessReferralAssetDetail.date == start_date,
            BusinessReferralAssetDetail.bus_agent_id.in_(chunk_user_ids),
            BusinessReferralAssetDetail.type == BusinessReferralAssetDetail.Type.BUS_AGENT,
        ).all():
            share_referral_data[item.bus_agent_id][item.bus_amb_id] += (item.spot_amount + item.perpetual_amount)

    remove_0_result = {}
    for user_id, user_data in result.items():
        if len(user_data['refer_user_list']) == 0 \
                and len(user_data['deal_user_list']) == 0 \
                and len(user_data['deposit_user_list']) == 0 \
                and user_data['spot_trade_usd'] == 0 \
                and user_data['spot_fee_usd'] == 0 \
                and user_data['perpetual_trade_usd'] == 0 \
                and user_data['perpetual_fee_usd'] == 0 \
                and user_data['refer_total_amount'] == 0 \
                and user_data['bro_spot_trade_usd'] == 0 \
                and user_data['bro_spot_fee_usd'] == 0 \
                and user_data['bro_perpetual_trade_usd'] == 0 \
                and user_data['bro_perpetual_fee_usd'] == 0 \
                and user_data['bro_refer_total_amount'] == 0:
            continue
        remove_0_result[user_id] = user_data

    flush_count = 5000
    _row_count = 0
    amb_report_rows = DailyBusinessAmbassadorReferralReport.query.filter(
        DailyBusinessAmbassadorReferralReport.report_date == start_date,
    ).all()
    amb_report_row_map = {i.user_id: i for i in amb_report_rows}
    for user_id, user_data in remove_0_result.items():
        if user_id in amb_report_row_map:
            row = amb_report_row_map[user_id]
        else:
            row: DailyBusinessAmbassadorReferralReport = DailyBusinessAmbassadorReferralReport(
                report_date=start_date,
                user_id=user_id,
            )
        row.refer_count = len(user_data['refer_user_list'])
        row.deal_count = len(user_data['deal_user_list'])
        row.deposit_count = len(user_data['deposit_user_list'])
        row.refer_user_list = json.dumps(list(user_data['refer_user_list']))
        row.deal_user_list = json.dumps(list(user_data['deal_user_list']))
        row.deposit_user_list = json.dumps(list(user_data['deposit_user_list']))
        row.spot_trade_usd = user_data['spot_trade_usd']
        row.spot_fee_usd = user_data['spot_fee_usd']
        row.perpetual_trade_usd = user_data['perpetual_trade_usd']
        row.perpetual_fee_usd = user_data['perpetual_fee_usd']
        row.refer_total_amount = user_data['refer_total_amount']
        if user_id in bro_amb_ids:
            row.bro_spot_trade_usd = user_data['bro_spot_trade_usd']
            row.bro_spot_fee_usd = user_data['bro_spot_fee_usd']
            row.bro_perpetual_trade_usd = user_data['bro_perpetual_trade_usd']
            row.bro_perpetual_fee_usd = user_data['bro_perpetual_fee_usd']
            row.bro_refer_total_amount = user_data['bro_refer_total_amount']
        db.session.add(row)
        _row_count += 1
        if _row_count % flush_count == 0:
            db.session.flush()

    # 更新商务大使返佣明细日报表
    _row_count = 0
    # 为每个商务大使生成被邀请人的明细数据
    for user_id, user_data in referree_result.items():
        for referree_id, referree_data in user_data.items():
            
            detail_row = DailyBusinessAmbassadorDetailReferralReport.get_or_create(
                report_date=start_date,
                user_id=user_id,
                referree_id=referree_id,
            )
            detail_row.spot_trade_usd = referree_data['spot_trade_usd']
            detail_row.spot_fee_usd = referree_data['spot_fee_usd']
            detail_row.perpetual_trade_usd = referree_data['perpetual_trade_usd']
            detail_row.perpetual_fee_usd = referree_data['perpetual_fee_usd']
            detail_row.refer_total_amount = referree_data['refer_total_amount']
            
            db.session.add(detail_row)
            _row_count += 1
            if _row_count % flush_count == 0:
                db.session.flush()

    _row_count = 0
    tree_amb_report_rows = DailyTreeAmbassadorReferralReport.query.filter(
        DailyTreeAmbassadorReferralReport.report_date == start_date,
    ).all()
    tree_amb_report_row_map = {i.user_id: i for i in tree_amb_report_rows}
    for tree_amd_id in remove_0_result:  # 和DailyBusinessAmbassadorReferralReport一样，忽略空数据大使
        if tree_amd_id not in tree_amb_ids:
            continue
        if tree_amd_id in tree_amb_report_row_map:
            row = tree_amb_report_row_map[tree_amd_id]
        else:
            row: DailyTreeAmbassadorReferralReport = DailyTreeAmbassadorReferralReport(
                report_date=start_date,
                user_id=tree_amd_id,
            )
        parent_ref_amount_map = child_indirect_ref_amount_map.get(tree_amd_id, {})
        parent_refer_data = [
            {'user_id': k, 'amount': v, 'tree_height': tree_amb_height_map.get(k, -1)}
            for k, v in parent_ref_amount_map.items()
        ]
        row.tree_height = tree_amb_height_map.get(tree_amd_id, -1)
        row.parent_refer_data = parent_refer_data
        db.session.add(row)
        _row_count += 1
        if _row_count % flush_count == 0:
            db.session.flush()

    for target_user_id, bus_amb_ids in share_data.items():
        for bus_amb_id in bus_amb_ids:
            if bus_amb_id not in remove_0_result:
                continue
            row = DailyBusinessAgentReferralReport.get_or_create(
                report_date=start_date,
                bus_amb_id=bus_amb_id,
                target_user_id=target_user_id,
            )
            user_data = remove_0_result[bus_amb_id]
            row.refer_count = len(user_data['refer_user_list'])
            row.deal_count = len(user_data['deal_user_list'])
            row.deposit_count = len(user_data['deposit_user_list'])
            row.refer_user_list = json.dumps(list(user_data['refer_user_list']))
            row.deal_user_list = json.dumps(list(user_data['deal_user_list']))
            row.deposit_user_list = json.dumps(list(user_data['deposit_user_list']))
            row.spot_trade_usd = user_data['spot_trade_usd']
            row.spot_fee_usd = user_data['spot_fee_usd']
            row.perpetual_trade_usd = user_data['perpetual_trade_usd']
            row.perpetual_fee_usd = user_data['perpetual_fee_usd']
            row.refer_total_amount = share_referral_data[target_user_id][bus_amb_id]
            db.session.add(row)

    for business_user_id, bus_amb_ids in business_data.items():
        deposit_user_list, deal_user_list = set(), set()
        for bus_amb_id in bus_amb_ids:
            if bus_amb_id not in remove_0_result:
                continue
            user_data = remove_0_result[bus_amb_id]
            deposit_user_list.update(user_data['deposit_user_list'])
            deal_user_list.update(user_data['deal_user_list'])

        if len(deposit_user_list) == 0 and len(deal_user_list) == 0:
            continue

        row = DailyBusinessUserReferralReport.get_or_create(
            report_date=start_date,
            user_id=business_user_id,
        )
        row.deal_count = len(deal_user_list)
        row.deposit_count = len(deposit_user_list)
        row.deal_user_list = json.dumps(list(deal_user_list))
        row.deposit_user_list = json.dumps(list(deposit_user_list))
        db.session.add(row)

    for leader_user_id, bus_amb_ids in leader_data.items():
        deposit_user_list, deal_user_list = set(), set()
        for bus_amb_id in bus_amb_ids:
            if bus_amb_id not in remove_0_result:
                continue
            user_data = remove_0_result[bus_amb_id]
            deposit_user_list.update(user_data['deposit_user_list'])
            deal_user_list.update(user_data['deal_user_list'])

        if len(deposit_user_list) == 0 and len(deal_user_list) == 0:
            continue

        row = DailyBusinessTeamReferralReport.get_or_create(
            report_date=start_date,
            user_id=leader_user_id,
        )
        row.deal_count = len(deal_user_list)
        row.deposit_count = len(deposit_user_list)
        row.deal_user_list = json.dumps(list(deal_user_list))
        row.deposit_user_list = json.dumps(list(deposit_user_list))
        db.session.add(row)

    for pr_user_id, bus_amb_ids in pr_id_amb_ids_map.items():
        deposit_user_list, deal_user_list = set(), set()
        for bus_amb_id in bus_amb_ids:
            if bus_amb_id not in remove_0_result:
                continue
            user_data = remove_0_result[bus_amb_id]
            deposit_user_list.update(user_data['deposit_user_list'])
            deal_user_list.update(user_data['deal_user_list'])
        if len(deposit_user_list) == 0 and len(deal_user_list) == 0:
            continue
        row = DailyBusinessPrReferralReport.get_or_create(
            report_date=start_date,
            user_id=pr_user_id,
        )
        row.deal_count = len(deal_user_list)
        row.deposit_count = len(deposit_user_list)
        row.deal_user_list = json.dumps(list(deal_user_list))
        row.deposit_user_list = json.dumps(list(deposit_user_list))
        db.session.add(row)

    db.session.commit()


@scheduled(crontab(minute='31', hour='4-6'))
@lock_call()
def update_monthly_detail_business_report_schedule():
    cur_year_num = datetime.date.today().year
    cur_month_num = datetime.date.today().month
    cur_month = datetime.date(cur_year_num, cur_month_num, 1)
    start_month = get_monthly_report_date(
        MonthlyBusinessAmbassadorDetailReferralReport,
        DailyBusinessAmbassadorDetailReferralReport,
        include_curr_month=True,
    )
    if not start_month:
        return
    while start_month <= cur_month:
        end_month = next_month(start_month.year, start_month.month)
        update_monthly_detail_business_report(start_month, end_month)
        start_month = end_month
        

def update_monthly_detail_business_report(start_date, end_date):
    """更新商务大使返佣明细月报"""
    referree_result = defaultdict(lambda: defaultdict(lambda: {
        'spot_trade_usd': Decimal(),
        'spot_fee_usd': Decimal(),
        'perpetual_trade_usd': Decimal(),
        'perpetual_fee_usd': Decimal(),
        'refer_total_amount': Decimal(),
    }))
    for item in DailyBusinessAmbassadorDetailReferralReport.query.filter(
            DailyBusinessAmbassadorDetailReferralReport.report_date >= start_date,
            DailyBusinessAmbassadorDetailReferralReport.report_date < end_date,
    ).all():
        data = referree_result[item.user_id][item.referree_id]
        data['spot_trade_usd'] += item.spot_trade_usd
        data['spot_fee_usd'] += item.spot_fee_usd
        data['perpetual_trade_usd'] += item.perpetual_trade_usd
        data['perpetual_fee_usd'] += item.perpetual_fee_usd
        data['refer_total_amount'] += item.refer_total_amount
    for user_id, referree_data in referree_result.items():
        for referree_id, data in referree_data.items():
            row = MonthlyBusinessAmbassadorDetailReferralReport.get_or_create(
                report_date=start_date,
                user_id=user_id,
                referree_id=referree_id,
            )
            row.spot_trade_usd = data['spot_trade_usd']
            row.spot_fee_usd = data['spot_fee_usd']
            row.perpetual_trade_usd = data['perpetual_trade_usd']
            row.perpetual_fee_usd = data['perpetual_fee_usd']
            row.refer_total_amount = data['refer_total_amount']
            db.session.add(row)
    db.session.commit()
    

@scheduled(crontab(minute='25', hour='4-6'))
@lock_call()
def update_monthly_business_report_schedule():
    cur_year_num = datetime.date.today().year
    cur_month_num = datetime.date.today().month
    cur_month = datetime.date(cur_year_num, cur_month_num, 1)
    start_month = get_monthly_report_date(
        MonthlyBusinessAmbassadorReferralReport,
        DailyBusinessAmbassadorReferralReport,
        include_curr_month=True,
    )
    if not start_month:
        return
    while start_month <= cur_month:
        end_month = next_month(start_month.year, start_month.month)
        update_monthly_business_report(start_month, end_month)
        start_month = end_month


def update_monthly_business_report(start_date, end_date):
    """更新商务大使、商务代理、商务以及商务组长返佣月报"""
    report_map = defaultdict(lambda: {
        'refer_user_list': set(),
        'deposit_user_list': set(),
        'deal_user_list': set(),
        'spot_trade_usd': Decimal(),
        'spot_fee_usd': Decimal(),
        'perpetual_trade_usd': Decimal(),
        'perpetual_fee_usd': Decimal(),
        'refer_total_amount': Decimal(),
        'bro_spot_trade_usd': Decimal(),
        'bro_spot_fee_usd': Decimal(),
        'bro_perpetual_trade_usd': Decimal(),
        'bro_perpetual_fee_usd': Decimal(),
        'bro_refer_total_amount': Decimal(),
    })
    for item in DailyBusinessAmbassadorReferralReport.query.filter(
            DailyBusinessAmbassadorReferralReport.report_date >= start_date,
            DailyBusinessAmbassadorReferralReport.report_date < end_date,
    ).all():
        data = report_map[item.user_id]
        data['spot_trade_usd'] += item.spot_trade_usd
        data['spot_fee_usd'] += item.spot_fee_usd
        data['perpetual_trade_usd'] += item.perpetual_trade_usd
        data['perpetual_fee_usd'] += item.perpetual_fee_usd
        data['refer_total_amount'] += item.refer_total_amount
        data['bro_spot_trade_usd'] += item.bro_spot_trade_usd
        data['bro_spot_fee_usd'] += item.bro_spot_fee_usd
        data['bro_perpetual_trade_usd'] += item.bro_perpetual_trade_usd
        data['bro_perpetual_fee_usd'] += item.bro_perpetual_fee_usd
        data['bro_refer_total_amount'] += item.bro_refer_total_amount
        data['refer_user_list'].update(json.loads(item.refer_user_list))
        data['deposit_user_list'].update(json.loads(item.deposit_user_list))
        data['deal_user_list'].update(json.loads(item.deal_user_list))

    share_referral_data = defaultdict(lambda: defaultdict(Decimal))  # 商务代理返佣总计
    for item in DailyBusinessAgentReferralReport.query.filter(
            DailyBusinessAgentReferralReport.report_date >= start_date,
            DailyBusinessAgentReferralReport.report_date < end_date,
    ).all():
        share_referral_data[item.target_user_id][item.bus_amb_id] += item.refer_total_amount

    business_referral_data = defaultdict(Decimal)  # 商务返佣总计
    leader_referral_data = defaultdict(Decimal)    # 商务组长返佣总计
    pr_referral_data = defaultdict(Decimal)    # 商务PR返佣总计
    for item in BusinessUserReferralAssetHistory.query.filter(
        BusinessUserReferralAssetHistory.date >= start_date,
        BusinessUserReferralAssetHistory.date < end_date,
    ).all():
        if item.type in [
            BusinessUserReferralAssetHistory.Type.BUS_USER,
            BusinessUserReferralAssetHistory.Type.BROKER_TO_BUS_USER,
        ]:
            business_referral_data[item.user_id] += item.amount
        elif item.type in [
            BusinessUserReferralAssetHistory.Type.BUS_LEADER,
            BusinessUserReferralAssetHistory.Type.BROKER_TO_BUS_LEADER,
        ]:
            leader_referral_data[item.user_id] += item.amount
        elif item.type in [
            BusinessUserReferralAssetHistory.Type.BUS_PR,
            BusinessUserReferralAssetHistory.Type.BROKER_TO_BUS_PR,
        ]:
            pr_referral_data[item.user_id] += item.amount

    # 整理商务代理与商务大使关联数据
    share_data = get_share_data()

    # 整理商务与商务大使关联数据
    all_bus_amb_rows = BusinessAmbassador.query.with_entities(
        BusinessAmbassador.user_id,
        BusinessAmbassador.effected_at,
        BusinessAmbassador.type,
        BusinessAmbassador.status,
    ).all()
    tree_amb_rows = TreeAmbassador.query.filter(
        TreeAmbassador.status == TreeAmbassador.Status.VALID,
    ).with_entities(
        TreeAmbassador.user_id,
        TreeAmbassador.root_id,
        TreeAmbassador.effected_at,
        TreeAmbassador.tree_height,
    ).all()
    root_amb_child_ids_map = defaultdict(set)
    tree_amb_ids = set()
    new_child_amb_set = set()
    tree_amb_height_map = {}
    for _tree_amb in tree_amb_rows:
        root_amb_child_ids_map[_tree_amb.root_id].add(_tree_amb.user_id)
        tree_amb_ids.add(_tree_amb.user_id)
        tree_amb_height_map[_tree_amb.user_id] = _tree_amb.tree_height
        if start_date <= _tree_amb.effected_at.date() < end_date:
            new_child_amb_set.add(_tree_amb.user_id)
    for bus_amb in all_bus_amb_rows:
        if bus_amb.status == BusinessAmbassador.Status.VALID and bus_amb.type == BusinessAmbassador.Type.TREE_ROOT:
            tree_amb_height_map[bus_amb.user_id] = 0

    business_data = get_business_data(root_amb_child_ids_map)

    # 整理商务组长、PR与商务大使关联数据
    leader_data, pr_id_amb_ids_map = get_leader_and_pr_data(business_data)

    child_indirect_ref_amount_map = group_by_child_indirect_ref_amount(start_date, end_date)
    indirect_amb_ids = set()
    for c_id, ref_v in child_indirect_ref_amount_map.items():
        indirect_amb_ids.add(c_id)
        indirect_amb_ids.update(ref_v.keys())
    _miss_tree_ids = indirect_amb_ids - set(tree_amb_height_map)
    if _miss_tree_ids:
        _miss_tree_rows = TreeAmbassador.query.filter(
            TreeAmbassador.user_id.in_(_miss_tree_ids)
        ).with_entities(
            TreeAmbassador.user_id,
            TreeAmbassador.tree_height,
        ).all()
        tree_amb_height_map.update(dict(_miss_tree_rows))

    # 本月新增的商务大使
    new_business_set = {
        item.user_id
        for item in BusinessAmbassador.query.filter(
            BusinessAmbassador.effected_at >= start_date,
            BusinessAmbassador.effected_at < end_date,
            BusinessAmbassador.status == BusinessAmbassador.Status.VALID,
        ).with_entities(
            BusinessAmbassador.user_id,
        ).all()
    }

    flush_count = 5000
    _row_count = 0
    amb_report_rows = MonthlyBusinessAmbassadorReferralReport.query.filter(
        MonthlyBusinessAmbassadorReferralReport.report_date == start_date,
    ).all()
    amb_report_row_map = {i.user_id: i for i in amb_report_rows}
    for user_id, user_data in report_map.items():
        if user_id in amb_report_row_map:
            row = amb_report_row_map[user_id]
        else:
            row = MonthlyBusinessAmbassadorReferralReport(
                report_date=start_date,
                user_id=user_id,
            )
        row.refer_count = len(user_data['refer_user_list'])
        row.deal_count = len(user_data['deal_user_list'])
        row.deposit_count = len(user_data['deposit_user_list'])
        row.spot_trade_usd = user_data['spot_trade_usd']
        row.spot_fee_usd = user_data['spot_fee_usd']
        row.perpetual_trade_usd = user_data['perpetual_trade_usd']
        row.perpetual_fee_usd = user_data['perpetual_fee_usd']
        row.refer_total_amount = user_data['refer_total_amount']
        row.bro_spot_trade_usd = user_data['bro_spot_trade_usd']
        row.bro_spot_fee_usd = user_data['bro_spot_fee_usd']
        row.bro_perpetual_trade_usd = user_data['bro_perpetual_trade_usd']
        row.bro_perpetual_fee_usd = user_data['bro_perpetual_fee_usd']
        row.bro_refer_total_amount = user_data['bro_refer_total_amount']
        db.session.add(row)
        _row_count += 1
        if _row_count % flush_count == 0:
            db.session.flush()

    _row_count = 0
    tree_amb_report_rows = MonthlyTreeAmbassadorReferralReport.query.filter(
        MonthlyTreeAmbassadorReferralReport.report_date == start_date,
    ).all()
    tree_amb_report_row_map = {i.user_id: i for i in tree_amb_report_rows}
    for tree_amd_id in report_map:  # 和MonthlyBusinessAmbassadorReferralReport一样，忽略空数据大使
        if tree_amd_id not in tree_amb_ids:
            continue
        if tree_amd_id in tree_amb_report_row_map:
            row = tree_amb_report_row_map[tree_amd_id]
        else:
            row: MonthlyTreeAmbassadorReferralReport = MonthlyTreeAmbassadorReferralReport(
                report_date=start_date,
                user_id=tree_amd_id,
            )
        parent_ref_amount_map = child_indirect_ref_amount_map.get(tree_amd_id, {})
        parent_refer_data = [
            {'user_id': k, 'amount': v, 'tree_height': tree_amb_height_map.get(k, -1)}
            for k, v in parent_ref_amount_map.items()
        ]
        row.tree_height = tree_amb_height_map.get(tree_amd_id, -1)
        row.parent_refer_data = parent_refer_data
        db.session.add(row)
        _row_count += 1
        if _row_count % flush_count == 0:
            db.session.flush()

    for target_user_id, bus_amb_ids in share_data.items():
        for bus_amb_id in bus_amb_ids:
            if bus_amb_id not in report_map:
                continue
            user_data = report_map[bus_amb_id]
            row = MonthlyBusinessAgentReferralReport.get_or_create(
                report_date=start_date,
                bus_amb_id=bus_amb_id,
                target_user_id=target_user_id,
            )
            row.refer_count = len(user_data['refer_user_list'])
            row.deal_count = len(user_data['deal_user_list'])
            row.deposit_count = len(user_data['deposit_user_list'])
            row.spot_trade_usd = user_data['spot_trade_usd']
            row.spot_fee_usd = user_data['spot_fee_usd']
            row.perpetual_trade_usd = user_data['perpetual_trade_usd']
            row.perpetual_fee_usd = user_data['perpetual_fee_usd']
            row.refer_total_amount = share_referral_data[target_user_id][bus_amb_id]
            db.session.add(row)

    for business_user_id, bus_amb_ids in business_data.items():
        refer_user_list, deposit_user_list, deal_user_list = set(), set(), set()
        spot_trade_usd, spot_fee_usd = Decimal(), Decimal()
        perpetual_trade_usd, perpetual_fee_usd = Decimal(), Decimal()
        for bus_amb_id in bus_amb_ids:
            if bus_amb_id not in report_map:
                continue
            user_data = report_map[bus_amb_id]
            refer_user_list.update(user_data['refer_user_list'])
            deposit_user_list.update(user_data['deposit_user_list'])
            deal_user_list.update(user_data['deal_user_list'])
            spot_trade_usd += user_data['spot_trade_usd']
            spot_fee_usd += user_data['spot_fee_usd']
            perpetual_trade_usd += user_data['perpetual_trade_usd']
            perpetual_fee_usd += user_data['perpetual_fee_usd']
            spot_trade_usd += user_data['bro_spot_trade_usd']
            spot_fee_usd += user_data['bro_spot_fee_usd']
            perpetual_trade_usd += user_data['bro_perpetual_trade_usd']
            perpetual_fee_usd += user_data['bro_perpetual_fee_usd']

        refer_ambassador_count = len(bus_amb_ids.intersection(new_business_set))
        child_ambassador_count = len(bus_amb_ids.intersection(new_child_amb_set))
        if refer_ambassador_count == 0 and child_ambassador_count == 0 and len(refer_user_list) == 0 \
                and len(deposit_user_list) == 0 and len(deal_user_list) == 0 \
                and spot_trade_usd == 0 and spot_fee_usd == 0 \
                and perpetual_trade_usd == 0 and perpetual_fee_usd == 0:
            continue

        row = MonthlyBusinessUserReferralReport.get_or_create(
            report_date=start_date,
            user_id=business_user_id,
        )
        row.refer_ambassador_count = refer_ambassador_count
        row.child_ambassador_count = child_ambassador_count
        row.refer_count = len(refer_user_list)
        row.deal_count = len(deal_user_list)
        row.deposit_count = len(deposit_user_list)
        row.spot_trade_usd = spot_trade_usd
        row.spot_fee_usd = spot_fee_usd
        row.perpetual_trade_usd = perpetual_trade_usd
        row.perpetual_fee_usd = perpetual_fee_usd
        row.total_trade_usd = spot_trade_usd + perpetual_trade_usd
        row.total_fee_usd = spot_fee_usd + perpetual_fee_usd
        row.refer_total_amount = business_referral_data[business_user_id]
        db.session.add(row)

    for leader_user_id, bus_amb_ids in leader_data.items():
        refer_user_list, deposit_user_list, deal_user_list = set(), set(), set()
        spot_trade_usd, spot_fee_usd = Decimal(), Decimal()
        perpetual_trade_usd, perpetual_fee_usd = Decimal(), Decimal()
        for bus_amb_id in bus_amb_ids:
            if bus_amb_id not in report_map:
                continue
            user_data = report_map[bus_amb_id]
            refer_user_list.update(user_data['refer_user_list'])
            deposit_user_list.update(user_data['deposit_user_list'])
            deal_user_list.update(user_data['deal_user_list'])
            spot_trade_usd += user_data['spot_trade_usd']
            spot_fee_usd += user_data['spot_fee_usd']
            perpetual_trade_usd += user_data['perpetual_trade_usd']
            perpetual_fee_usd += user_data['perpetual_fee_usd']
            spot_trade_usd += user_data['bro_spot_trade_usd']
            spot_fee_usd += user_data['bro_spot_fee_usd']
            perpetual_trade_usd += user_data['bro_perpetual_trade_usd']
            perpetual_fee_usd += user_data['bro_perpetual_fee_usd']

        refer_ambassador_count = len(bus_amb_ids.intersection(new_business_set))
        child_ambassador_count = len(bus_amb_ids.intersection(new_child_amb_set))
        if refer_ambassador_count == 0 and child_ambassador_count == 0 and len(refer_user_list) == 0 \
                and len(deposit_user_list) == 0 and len(deal_user_list) == 0 \
                and spot_trade_usd == 0 and spot_fee_usd == 0 \
                and perpetual_trade_usd == 0 and perpetual_fee_usd == 0:
            continue

        row = MonthlyBusinessTeamReferralReport.get_or_create(
            report_date=start_date,
            user_id=leader_user_id,
        )
        row.refer_ambassador_count = refer_ambassador_count
        row.child_ambassador_count = child_ambassador_count
        row.refer_count = len(refer_user_list)
        row.deal_count = len(deal_user_list)
        row.deposit_count = len(deposit_user_list)
        row.spot_trade_usd = spot_trade_usd
        row.spot_fee_usd = spot_fee_usd
        row.perpetual_trade_usd = perpetual_trade_usd
        row.perpetual_fee_usd = perpetual_fee_usd
        row.total_trade_usd = spot_trade_usd + perpetual_trade_usd
        row.total_fee_usd = spot_fee_usd + perpetual_fee_usd
        row.refer_total_amount = leader_referral_data[leader_user_id]
        db.session.add(row)

    for pr_user_id, bus_amb_ids in pr_id_amb_ids_map.items():
        refer_user_list, deposit_user_list, deal_user_list = set(), set(), set()
        spot_trade_usd, spot_fee_usd = Decimal(), Decimal()
        perpetual_trade_usd, perpetual_fee_usd = Decimal(), Decimal()
        for bus_amb_id in bus_amb_ids:
            if bus_amb_id not in report_map:
                continue
            user_data = report_map[bus_amb_id]
            refer_user_list.update(user_data['refer_user_list'])
            deposit_user_list.update(user_data['deposit_user_list'])
            deal_user_list.update(user_data['deal_user_list'])
            spot_trade_usd += user_data['spot_trade_usd']
            spot_fee_usd += user_data['spot_fee_usd']
            perpetual_trade_usd += user_data['perpetual_trade_usd']
            perpetual_fee_usd += user_data['perpetual_fee_usd']
            spot_trade_usd += user_data['bro_spot_trade_usd']
            spot_fee_usd += user_data['bro_spot_fee_usd']
            perpetual_trade_usd += user_data['bro_perpetual_trade_usd']
            perpetual_fee_usd += user_data['bro_perpetual_fee_usd']

        refer_ambassador_count = len(bus_amb_ids.intersection(new_business_set))
        child_ambassador_count = len(bus_amb_ids.intersection(new_child_amb_set))
        if refer_ambassador_count == 0 and child_ambassador_count == 0 and len(refer_user_list) == 0 \
                and len(deposit_user_list) == 0 and len(deal_user_list) == 0 \
                and spot_trade_usd == 0 and spot_fee_usd == 0 \
                and perpetual_trade_usd == 0 and perpetual_fee_usd == 0:
            continue

        row = MonthlyBusinessPrReferralReport.get_or_create(
            report_date=start_date,
            user_id=pr_user_id,
        )
        row.refer_ambassador_count = refer_ambassador_count
        row.child_ambassador_count = child_ambassador_count
        row.refer_count = len(refer_user_list)
        row.deal_count = len(deal_user_list)
        row.deposit_count = len(deposit_user_list)
        row.spot_trade_usd = spot_trade_usd
        row.spot_fee_usd = spot_fee_usd
        row.perpetual_trade_usd = perpetual_trade_usd
        row.perpetual_fee_usd = perpetual_fee_usd
        row.total_trade_usd = spot_trade_usd + perpetual_trade_usd
        row.total_fee_usd = spot_fee_usd + perpetual_fee_usd
        row.refer_total_amount = pr_referral_data[pr_user_id]
        db.session.add(row)

    db.session.commit()


@scheduled(crontab(minute='35', hour='3-5'))
@lock_call()
def update_daily_business_ambassador_total_report_schedule():
    _today = datetime.datetime.now(datetime.UTC).date()
    last_record = DailyBusinessAmbassadorTotalReferralReport.query.order_by(
        DailyBusinessAmbassadorTotalReferralReport.report_date.desc()
    ).first()
    if last_record:
        start_date = last_record.report_date + datetime.timedelta(days=1)
    else:
        start_date = datetime.date(2024, 3, 1)
    while start_date < _today:
        end_date = start_date + datetime.timedelta(days=1)
        update_business_ambassador_total_report(start_date, end_date, ReportType.DAILY)
        start_date = end_date


@scheduled(crontab(minute='45', hour='3-5'))
@lock_call()
def update_weekly_business_ambassador_total_report_schedule():
    _today = datetime.datetime.now(datetime.UTC).date()
    last_record = WeeklyBusinessAmbassadorTotalReferralReport.query.order_by(
        WeeklyBusinessAmbassadorTotalReferralReport.report_date.desc()
    ).first()
    if last_record:
        start_date = last_record.report_date
    else:
        start_date = datetime.date(2025, 5, 4)
    while start_date <= _today:
        end_date = start_date + datetime.timedelta(days=7)
        update_business_ambassador_total_report(start_date, end_date, ReportType.WEEKLY)
        start_date = end_date


@scheduled(crontab(minute='55', hour='3-5'))
@lock_call()
def update_quarterly_business_ambassador_total_report_schedule():
    _today = datetime.datetime.now(datetime.UTC).date()
    last_record = QuarterlyBusinessAmbassadorTotalReferralReport.query.order_by(
        QuarterlyBusinessAmbassadorTotalReferralReport.report_date.desc()
    ).first()
    if last_record:
        start_date = last_record.report_date
    else:
        start_date = datetime.date(2025, 1, 1)
    while start_date <= _today:
        end_date = start_date + relativedelta(months=3)
        update_business_ambassador_total_report(start_date, end_date, ReportType.QUARTERLY)
        start_date = end_date


def get_team_business_dic(end_date):
    recs = BusinessUser.query.filter(
        BusinessUser.created_at < end_date,
        BusinessUser.status == BusinessUser.Status.VALID,
    ).with_entities(
        BusinessUser.user_id,
        BusinessUser.team_id,
    ).all()
    res = defaultdict(list)
    for rec in recs:
        res[rec.team_id].append(rec.user_id)
    return res


def update_business_ambassador_total_report(start_date, end_date, report_type):
    if not check_data_ready(start_date):
        return
    if not ReferralAssetHistory.query.filter(
        ReferralAssetHistory.date >= start_date,
        ReferralAssetHistory.date < end_date
    ).first():
        return

    if report_type == ReportType.DAILY:
        report_model = DailyBusinessAmbassadorTotalReferralReport
    elif report_type == ReportType.WEEKLY:
        report_model = WeeklyBusinessAmbassadorTotalReferralReport
    elif report_type == ReportType.MONTHLY:
        report_model = MonthlyBusinessAmbassadorTotalReferralReport
    elif report_type == ReportType.QUARTERLY:
        report_model = QuarterlyBusinessAmbassadorTotalReferralReport
    else:
        raise ValueError(f"Invalid report type: {report_type}")

    all_bus_user_ids = set()
    all_bus_amb_user_ids = set()
    all_new_bus_amb_user_ids = set()
    all_refer_user_ids = set()
    all_new_refer_user_ids = set()
    all_deal_user_ids = set()
    all_new_deal_user_ids = set()
    all_deal_total_usd = Decimal()
    all_deal_spot_usd = Decimal()
    all_deal_perp_usd = Decimal()
    all_fee_total_usd = Decimal()
    all_fee_spot_usd = Decimal()
    all_fee_perp_usd = Decimal()
    all_refer_amb_user_ids = set()
    all_refer_amb_amount = Decimal()

    start_datetime = date_to_datetime(start_date)
    team_business_dic = get_team_business_dic(end_date)
    if report_type == ReportType.DAILY:
        user_report_model = DailyUserReport
    elif report_type == ReportType.WEEKLY:
        user_report_model = WeeklyUserReport
    elif report_type == ReportType.MONTHLY:
        user_report_model = MonthlyUserReport
    elif report_type == ReportType.QUARTERLY:
        user_report_model = QuarterlyUserReport
    else:
        raise ValueError(f"Invalid report type: {report_type}")
    user_report = user_report_model.query.filter(
        user_report_model.report_date == start_date
    ).first()
    yesterday_ = yesterday()
    inc_deal_user_ids = get_period_increase_trade_users(start_date - datetime.timedelta(days=1), yesterday_)
    refer_bus_amount_dic = defaultdict(Decimal)  # 商务返佣金额
    for item in BusinessUserReferralAssetHistory.query.filter(
            BusinessUserReferralAssetHistory.date >= start_date,
            BusinessUserReferralAssetHistory.date < end_date,
            BusinessUserReferralAssetHistory.status == BusinessUserReferralAssetHistory.Status.FINISHED,
    ).with_entities(
        BusinessUserReferralAssetHistory.user_id,
        BusinessUserReferralAssetHistory.amount,
    ).all():
        refer_bus_amount_dic[item.user_id] += item.amount

    refer_agent_amount_dic = defaultdict(Decimal)  # 商务代理返佣金额
    for item in BusinessAgentReferralAssetHistory.query.filter(
            BusinessAgentReferralAssetHistory.date >= start_date,
            BusinessAgentReferralAssetHistory.date < end_date,
            BusinessAgentReferralAssetHistory.status == BusinessAgentReferralAssetHistory.Status.FINISHED,
    ).with_entities(
        BusinessAgentReferralAssetHistory.user_id,
        BusinessAgentReferralAssetHistory.amount,
    ).all():
        refer_agent_amount_dic[item.user_id] += item.amount
    refer_indir_amount_dic = defaultdict(Decimal)  # tree大使非直客返佣金额
    for item in IndirectReferralAssetHistory.query.filter(
        IndirectReferralAssetHistory.date >= start_date,
        IndirectReferralAssetHistory.date < end_date,
        IndirectReferralAssetHistory.status == IndirectReferralAssetHistory.Status.FINISHED,
    ).with_entities(
        IndirectReferralAssetHistory.user_id,
        IndirectReferralAssetHistory.amount,
    ).all():
        refer_indir_amount_dic[item.user_id] += item.amount

    fee_rows_mapping = _get_spot_perp_fee_records(start_date, end_date)

    ref_detail_rows: list[ReferralAssetDetail] = ReferralAssetDetail.query.filter(
        ReferralAssetDetail.date >= start_date,
        ReferralAssetDetail.date < end_date,
        ReferralAssetDetail.type == ReferralAssetHistory.Type.AMBASSADOR,
    ).with_entities(
        ReferralAssetDetail.user_id,
        ReferralAssetDetail.referree_id,
        ReferralAssetDetail.spot_amount,
        ReferralAssetDetail.perpetual_amount,
    ).all()

    today_ = today()
    end_date_prev_day = end_date - datetime.timedelta(days=1)
    if end_date_prev_day >= today_:
        end_date_prev_day = today_ - datetime.timedelta(days=1)
    main_user_balance_usd_map = get_main_user_balance_usd_map(int(date_to_datetime(end_date_prev_day).timestamp()))
    active_user_ids = get_report_active_user_ids_in_range(
        start_date=start_date,
        end_date=end_date_prev_day,
        realtime_start_date=end_date_prev_day,
        realtime_end_date=end_date_prev_day + datetime.timedelta(days=1),
    )

    for team_id, bus_user_ids in team_business_dic.items():
        bus_amb_user_ids = set()  # 商务大使列表
        team_bus_amb_user_ids = set()  # 所有商务大使列表(包括失效的)
        new_bus_amb_user_ids = set()  # 新增商务大使列表
        root_bus_amb_user_ids = set()  # root大使
        for item in BusinessAmbassador.query.filter(
                BusinessAmbassador.effected_at < end_date,
                BusinessAmbassador.bus_user_id.in_(bus_user_ids),
        ).with_entities(
            BusinessAmbassador.user_id,
            BusinessAmbassador.effected_at,
            BusinessAmbassador.status,
            BusinessAmbassador.type,
        ).all():
            team_bus_amb_user_ids.add(item.user_id)
            if item.type == BusinessAmbassador.Type.TREE_ROOT:
                root_bus_amb_user_ids.add(item.user_id)
            if item.status == BusinessAmbassador.Status.VALID:
                bus_amb_user_ids.add(item.user_id)
            if item.effected_at >= start_datetime:
                new_bus_amb_user_ids.add(item.user_id)
        tree_amb_rows = TreeAmbassador.query.filter(
            TreeAmbassador.effected_at < end_date,
            TreeAmbassador.root_id.in_(root_bus_amb_user_ids),
        ).with_entities(
            TreeAmbassador.user_id,
            TreeAmbassador.effected_at,
            TreeAmbassador.status,
        ).all()
        for item in tree_amb_rows:
            team_bus_amb_user_ids.add(item.user_id)
            if item.status == TreeAmbassador.Status.VALID:
                bus_amb_user_ids.add(item.user_id)
            if item.effected_at >= start_datetime:
                new_bus_amb_user_ids.add(item.user_id)

        all_bus_user_ids.update(bus_user_ids)
        all_bus_amb_user_ids.update(bus_amb_user_ids)
        all_new_bus_amb_user_ids.update(new_bus_amb_user_ids)

        referrer_user_ids = set()
        refer_user_ids = set()  # 大使邀请总用户数
        new_refer_user_ids = set()  # 大使邀请新增用户数
        for item in ReferralHistory.query.filter(
                ReferralHistory.effected_at < end_date,
                ReferralHistory.status == ReferralHistory.Status.VALID,
                ReferralHistory.referrer_id.in_(team_bus_amb_user_ids)
        ).with_entities(
            ReferralHistory.effected_at,
            ReferralHistory.referrer_id,
            ReferralHistory.referree_id,
        ):
            # 不判断生效时间，只要是有效的邀请关系就算
            # if item.effected_at < bus_effected_map[item.referrer_id]:
            #     continue
            referrer_user_ids.add(item.referrer_id)
            refer_user_ids.add(item.referree_id)
            if item.effected_at >= start_datetime:
                new_refer_user_ids.add(item.referree_id)

        all_refer_user_ids.update(refer_user_ids)
        all_new_refer_user_ids.update(new_refer_user_ids)

        spot_data, perp_data = {}, {}
        for chunk_user_ids in batch_iter(refer_user_ids, 2000):
            spot_data.update(get_period_spot_trade_mapping(start_date, end_date, user_ids=chunk_user_ids))
            perp_data.update(get_period_perp_trade_mapping(start_date, end_date, user_ids=chunk_user_ids))

        deal_user_ids = set(spot_data.keys()) | set(perp_data.keys())
        new_deal_user_ids = inc_deal_user_ids & deal_user_ids

        all_deal_user_ids.update(deal_user_ids)
        all_new_deal_user_ids.update(new_deal_user_ids)
        deal_total_usd = deal_spot_usd = deal_perp_usd = Decimal()
        for user_id, trade_amount in spot_data.items():
            deal_spot_usd += trade_amount
            deal_total_usd += trade_amount
        for user_id, trade_amount in perp_data.items():
            deal_perp_usd += trade_amount
            deal_total_usd += trade_amount

        all_deal_total_usd += deal_total_usd
        all_deal_spot_usd += deal_spot_usd
        all_deal_perp_usd += deal_perp_usd

        fee_total_usd = fee_spot_usd = fee_perp_usd = Decimal()
        for amb_user_id in referrer_user_ids:
            fee_rows = fee_rows_mapping.get(amb_user_id)
            if not fee_rows:
                continue
            for fee_row in fee_rows:
                if fee_row.referree_id not in refer_user_ids:
                    continue
                fee_spot_usd += fee_row.spot_fee_usd
                fee_perp_usd += fee_row.perpetual_fee_usd
                fee_total_usd += fee_row.spot_fee_usd
                fee_total_usd += fee_row.perpetual_fee_usd

        all_fee_total_usd += fee_total_usd
        all_fee_spot_usd += fee_spot_usd
        all_fee_perp_usd += fee_perp_usd

        refer_amb_user_ids = set()  # 收到返佣的大使
        refer_amb_amount = Decimal()  # 大使返佣金额
        for ref_detail in ref_detail_rows:
            if ref_detail.user_id in bus_amb_user_ids:
                refer_amb_user_ids.add(ref_detail.user_id)
            if ref_detail.user_id in bus_amb_user_ids or \
                    (ref_detail.user_id == ref_detail.referree_id and ref_detail.referree_id in refer_user_ids):
                refer_amb_amount += (ref_detail.spot_amount + ref_detail.perpetual_amount)

        all_refer_amb_user_ids.update(refer_amb_user_ids)
        all_refer_amb_amount += refer_amb_amount

        if not user_report or user_report.increase_user == 0:
            invitee_percent = Decimal()
        else:
            invitee_percent = quantize_amount(len(new_refer_user_ids) / user_report.increase_user, 4)
        if not user_report or user_report.active_trade_user == 0:
            trade_percent = Decimal()
        else:
            trade_percent = quantize_amount(len(deal_user_ids) / user_report.active_trade_user, 4)
        if not user_report or user_report.increase_trade_user == 0:
            new_trade_percent = Decimal()
        else:
            new_trade_percent = quantize_amount(len(new_deal_user_ids) / user_report.increase_trade_user, 4)

        refer_agent_user_ids = set()
        refer_agent_amount = Decimal()  # 非直客 算成 代理返佣
        for user_id, amount in chain(refer_agent_amount_dic.items(), refer_indir_amount_dic.items()):
            if user_id in team_bus_amb_user_ids:
                refer_agent_user_ids.add(user_id)
                refer_agent_amount += amount
        refer_bus_user_ids = set()
        refer_bus_amount = Decimal()
        for user_id, amount in refer_bus_amount_dic.items():
            if user_id in bus_user_ids:
                refer_bus_user_ids.add(user_id)
                refer_bus_amount += amount

        amb_ref_ee_balance_usd = sum([main_user_balance_usd_map[i] for i in refer_user_ids])

        record: report_model = report_model.get_or_create(report_date=start_date, team_id=team_id)
        record.bus_user_count = len(bus_user_ids)
        record.bus_amb_count = len(bus_amb_user_ids)
        record.new_bus_amb_count = len(new_bus_amb_user_ids)
        record.refer_count = len(refer_user_ids)
        record.new_refer_count = len(new_refer_user_ids)
        record.deal_count = len(deal_user_ids)
        # record.deal_user_list = json.dumps(list(deal_user_ids))
        record.new_deal_count = len(new_deal_user_ids)
        # record.new_deal_user_list = json.dumps(list(new_deal_user_ids))
        record.deal_usd = deal_total_usd
        record.deal_spot_usd = deal_spot_usd
        record.deal_perpetual_usd = deal_perp_usd
        record.fee_usd = fee_total_usd
        record.fee_spot_usd = fee_spot_usd
        record.fee_perpetual_usd = fee_perp_usd
        record.refer_amb_count = len(refer_amb_user_ids)
        # record.refer_amb_user_list = json.dumps(list(refer_amb_user_ids))
        record.refer_amb_amount = refer_amb_amount
        record.refer_agent_count = len(refer_agent_user_ids)
        # record.refer_agent_user_list = json.dumps(list(refer_agent_user_ids))
        record.refer_agent_amount = refer_agent_amount
        record.average_refer_rate = quantize_amount(
            (refer_amb_amount + refer_agent_amount) / fee_total_usd * 100, 2
        ) if fee_total_usd != 0 else 0
        record.invitee_percent = invitee_percent
        record.trade_percent = trade_percent
        record.new_trade_percent = new_trade_percent
        record.refer_user_balance_usd = quantize_amount(amb_ref_ee_balance_usd, 2)
        record.refer_active_user_count = len(active_user_ids & refer_user_ids)
        if report_type != ReportType.DAILY:
            record.refer_bus_count = len(refer_bus_user_ids)
            record.refer_bus_amount = refer_bus_amount
        db.session.add(record)

    if not user_report or user_report.increase_user == 0:
        all_invitee_percent = Decimal()
    else:
        all_invitee_percent = quantize_amount(len(all_new_refer_user_ids) / user_report.increase_user, 4)
    if not user_report or user_report.active_trade_user == 0:
        all_trade_percent = Decimal()
    else:
        all_trade_percent = quantize_amount(len(all_deal_user_ids) / user_report.active_trade_user, 4)
    if not user_report or user_report.increase_trade_user == 0:
        all_new_trade_percent = Decimal()
    else:
        all_new_trade_percent = quantize_amount(len(all_new_deal_user_ids) / user_report.increase_trade_user, 4)

    all_refer_agent_count = len(set(refer_agent_amount_dic.keys()) | set(refer_indir_amount_dic))
    all_refer_agent_amount = sum(refer_agent_amount_dic.values()) + sum(refer_indir_amount_dic.values())
    all_refer_bus_count = len(set(refer_bus_amount_dic.keys()))
    all_refer_bus_amount = sum(refer_bus_amount_dic.values()) or Decimal()
    all_record = report_model.get_or_create(report_date=start_date, team_id=0)
    all_record.bus_user_count = len(all_bus_user_ids)
    all_record.bus_amb_count = len(all_bus_amb_user_ids)
    all_record.new_bus_amb_count = len(all_new_bus_amb_user_ids)
    all_record.refer_count = len(all_refer_user_ids)
    all_record.new_refer_count = len(all_new_refer_user_ids)
    all_record.deal_count = len(all_deal_user_ids)
    all_record.new_deal_count = len(all_new_deal_user_ids)
    all_record.deal_usd = all_deal_total_usd
    all_record.deal_spot_usd = all_deal_spot_usd
    all_record.deal_perpetual_usd = all_deal_perp_usd
    all_record.fee_usd = all_fee_total_usd
    all_record.fee_spot_usd = all_fee_spot_usd
    all_record.fee_perpetual_usd = all_fee_perp_usd
    all_record.refer_amb_count = len(all_refer_amb_user_ids)
    # all_record.refer_amb_user_list = json.dumps(list(all_refer_amb_user_ids))
    all_record.refer_amb_amount = all_refer_amb_amount
    all_record.refer_agent_count = all_refer_agent_count
    # all_record.refer_agent_user_list = json.dumps(list(all_refer_agent_user_ids))
    all_record.refer_agent_amount = all_refer_agent_amount
    all_record.average_refer_rate = quantize_amount(
        (all_refer_amb_amount + all_refer_agent_amount) / all_fee_total_usd * 100, 2
    ) if all_fee_total_usd != 0 else 0
    all_record.invitee_percent = all_invitee_percent
    all_record.trade_percent = all_trade_percent
    all_record.new_trade_percent = all_new_trade_percent
    all_amb_ref_ee_balance_usd = sum([main_user_balance_usd_map[i] for i in all_refer_user_ids])
    all_record.refer_user_balance_usd = quantize_amount(all_amb_ref_ee_balance_usd, 2)
    all_record.refer_active_user_count = len(active_user_ids & all_refer_user_ids)
    if report_type != ReportType.DAILY:
        all_record.refer_bus_count = all_refer_bus_count
        all_record.refer_bus_amount = all_refer_bus_amount
    db.session.add(all_record)
    db.session.commit()


def _get_spot_perp_fee_records(start, end) -> dict:
    # 剔除AMM分红和手续费返现券
    # 此处需找对应邀请关系，剔除返佣对被邀请人返佣情况（A -> B, B -> B）
    model = ReferralAssetDetail
    rows = model.query.with_entities(
        model.user_id,
        model.referree_id,
        model.spot_fee_usd,
        model.perpetual_fee_usd,
    ).filter(
        model.date >= start,
        model.date < end,
        model.user_id != model.referree_id,
    ).all()
    ret = defaultdict(list)
    for row in rows:
        ret[row.user_id].append(row)
    return ret


def _update_fees(result, referrer_ids, referree_ids, fee_rows_mapping):
    for referrer_id in referrer_ids:
        fee_rows = fee_rows_mapping.get(referrer_id)
        if not fee_rows:
            continue
        for fee_row in fee_rows:
            if fee_row.referree_id not in referree_ids:
                continue
            result[referrer_id]['spot_fee_usd'] += fee_row.spot_fee_usd
            result[referrer_id]['perpetual_fee_usd'] += fee_row.perpetual_fee_usd


@scheduled(crontab(minute='50', hour='3-5'))
@lock_call()
def update_monthly_business_ambassador_total_report_schedule():
    _today = datetime.datetime.now(datetime.UTC).date()
    last_record = MonthlyBusinessAmbassadorTotalReferralReport.query.order_by(
        MonthlyBusinessAmbassadorTotalReferralReport.report_date.desc()).first()
    if last_record:
        start_month = last_record.report_date
    else:
        start_month = datetime.date(2025, 5, 1)
    while start_month <= _today:
        end_month = next_month(start_month.year, start_month.month)
        update_business_ambassador_total_report(start_month, end_month, ReportType.MONTHLY)
        start_month = end_month


def update_monthly_business_ambassador_total_report(start_date, end_date):
    daily_refer_data = DailyBusinessAmbassadorTotalReferralReport.query.filter(
        DailyBusinessAmbassadorTotalReferralReport.report_date >= start_date,
        DailyBusinessAmbassadorTotalReferralReport.report_date < end_date,
    ).order_by(
        DailyBusinessAmbassadorTotalReferralReport.report_date
    ).all()
    if len(daily_refer_data) == 0:
        return

    monthly_inc_deal_users = get_period_increase_trade_users(
        start_date - datetime.timedelta(days=1),
        end_date - datetime.timedelta(days=1),
    )

    refer_bus_user_ids = set()  # 收到返佣的商务
    refer_bus_amount = Decimal()  # 商务返佣金额
    for item in BusinessUserReferralAssetHistory.query.filter(
            BusinessUserReferralAssetHistory.date >= start_date,
            BusinessUserReferralAssetHistory.date < end_date,
            BusinessUserReferralAssetHistory.status == BusinessUserReferralAssetHistory.Status.FINISHED,
    ).with_entities(
        BusinessUserReferralAssetHistory.user_id,
        BusinessUserReferralAssetHistory.amount,
    ).all():
        refer_bus_user_ids.add(item.user_id)
        refer_bus_amount += item.amount

    bus_user_count = bus_amb_count = new_bus_amb_count = refer_count = new_refer_count = 0
    deal_user_ids = set()
    deal_usd = deal_spot_usd = deal_perpetual_usd = fee_usd = fee_spot_usd = fee_perpetual_usd = Decimal()
    refer_amb_user_ids = set()
    refer_agent_user_ids = set()
    refer_amb_amount = refer_agent_amount = Decimal()

    for item in daily_refer_data:
        bus_user_count = item.bus_user_count
        bus_amb_count = item.bus_amb_count
        new_bus_amb_count += item.new_bus_amb_count
        refer_count = item.refer_count
        new_refer_count += item.new_refer_count
        deal_user_ids.update(json.loads(item.deal_user_list))
        deal_usd += item.deal_usd
        deal_spot_usd += item.deal_spot_usd
        deal_perpetual_usd += item.deal_perpetual_usd
        fee_usd += item.fee_usd
        fee_spot_usd += item.fee_spot_usd
        fee_perpetual_usd += item.fee_perpetual_usd
        refer_amb_user_ids.update(json.loads(item.refer_amb_user_list))
        refer_amb_amount += item.refer_amb_amount
        refer_agent_user_ids.update(json.loads(item.refer_agent_user_list))
        refer_agent_amount += item.refer_agent_amount

    record = MonthlyBusinessAmbassadorTotalReferralReport.get_or_create(report_date=start_date)
    record.bus_user_count = bus_user_count
    record.bus_amb_count = bus_amb_count
    record.new_bus_amb_count = new_bus_amb_count
    record.refer_count = refer_count
    record.new_refer_count = new_refer_count
    record.deal_count = len(deal_user_ids)
    record.new_deal_count = len(monthly_inc_deal_users & set(deal_user_ids))
    record.deal_usd = deal_usd
    record.deal_spot_usd = deal_spot_usd
    record.deal_perpetual_usd = deal_perpetual_usd
    record.fee_usd = fee_usd
    record.fee_spot_usd = fee_spot_usd
    record.fee_perpetual_usd = fee_perpetual_usd
    record.refer_amb_count = len(refer_amb_user_ids)
    record.refer_amb_amount = refer_amb_amount
    record.refer_agent_count = len(refer_agent_user_ids)
    record.refer_agent_amount = refer_agent_amount
    record.average_refer_rate = quantize_amount(
        (refer_amb_amount + refer_agent_amount) / fee_usd * 100, 2
    ) if fee_usd != 0 else 0
    record.refer_bus_count = len(refer_bus_user_ids)
    record.refer_bus_amount = refer_bus_amount

    user_report = MonthlyUserReport.query.filter(
        MonthlyUserReport.report_date >= start_date,
        MonthlyUserReport.report_date < end_date
    ).first()
    if not user_report or user_report.increase_user == 0:
        record.invitee_percent = Decimal()
    else:
        record.invitee_percent = quantize_amount(record.new_refer_count / user_report.increase_user, 4)
    if not user_report or user_report.active_trade_user == 0:
        record.trade_percent = Decimal()
    else:
        record.trade_percent = quantize_amount(record.deal_count / user_report.active_trade_user, 4)
    if not user_report or user_report.increase_trade_user == 0:
        record.new_trade_percent = Decimal()
    else:
        record.new_trade_percent = quantize_amount(record.new_deal_count / user_report.increase_trade_user, 4)

    db.session.add(record)
    db.session.commit()


@scheduled(crontab(minute="10", hour="4-6"))
@lock_call()
def push_biz_ambassador_daily_report_schedule():
    # 给商务大使发邀请数据明细邮件
    today_ = today()
    report_date = today_ - datetime.timedelta(days=1)
    cache = BusinessAmbassadorDetailReportCache()
    last_date = cache.read()
    if last_date and report_date <= str_to_datetime(last_date).date():
        return
    push_biz_ambassador_daily_report(report_date)
    cache.set(report_date.strftime('%Y-%m-%d'))


def push_biz_ambassador_daily_report(report_date: datetime.date):

    def _build_referral_history() -> dict[int, list[int]]:
        _model = BusinessAmbassador
        _rows = _model.query.with_entities(
            _model.user_id
        ).filter(
            _model.status == _model.Status.VALID
        ).all()
        amb_user_ids = {_row.user_id for _row in _rows}
        _ret = defaultdict(list)
        for chunk_ids in batch_iter(amb_user_ids, 1000):
            referrals = ReferralHistory.query.with_entities(
                ReferralHistory.referrer_id,
                ReferralHistory.referree_id,
            ).filter(
                ReferralHistory.status == ReferralHistory.Status.VALID,
                ReferralHistory.referrer_id.in_(chunk_ids)
            ).all()
            for referral in referrals:
                _ret[referral.referrer_id].append(referral.referree_id)
        return _ret

    def _create_or_update_report() -> dict[int, list[dict]]:

        def __get_new_referree_releated_data() -> dict:
            combined_uids = new_referree_ids | need_query_first_data_uids
            if not combined_uids:
                return {}
            # sql:571s
            deposit_rows = Deposit.query.with_entities(
                func.min(Deposit.id).label('id'),
            ).filter(
                Deposit.user_id.in_(combined_uids),
                Deposit.status.in_([
                    Deposit.Status.FINISHED,
                    Deposit.Status.TO_HOT,
                ])
            ).group_by(
                Deposit.user_id
            ).all()
            deposit_ids = {x.id for x in deposit_rows}
            deposits = Deposit.query.with_entities(
                Deposit.user_id,
                Deposit.asset,
                Deposit.amount,
                Deposit.created_at,
            ).filter(
                Deposit.id.in_(deposit_ids),
            ).all()
            deposits = {deposit.user_id: deposit for deposit in deposits}
            trade_dates = get_first_trade_users(target_user_ids=combined_uids)
            _ret = defaultdict(dict)
            for referree_id in combined_uids:
                if dep := deposits.get(referree_id):
                    _ret[referree_id]['deposit_date'] = dep.created_at.date()
                    price = __get_asset_price(_date=dep.created_at.date(), _asset=dep.asset)
                    _ret[referree_id]['deposit_usd'] = dep.amount * price
                if trade_date := trade_dates.get(referree_id):
                    _ret[referree_id]['trade_date'] = trade_date
            return _ret

        def __get_period_trade_data() -> dict:
            _st = query_start + datetime.timedelta(days=1)
            _et = report_date + datetime.timedelta(days=1)
            spot_data = get_period_spot_trade_mapping(_st, _et, user_ids=referree_ids)
            perp_data = get_period_perp_trade_mapping(_st, _et, user_ids=referree_ids)
            r_model = ReferralAssetDetail
            referral_data = defaultdict(lambda: defaultdict(Decimal))
            for chunk_ids in batch_iter(referree_ids, 1000):
                referral_rows = r_model.query.with_entities(
                    r_model.referree_id,
                    r_model.date,
                    r_model.asset,
                    r_model.spot_amount,
                    r_model.perpetual_amount,
                    r_model.spot_fee_usd,
                    r_model.perpetual_fee_usd,
                ).filter(
                    r_model.date >= _st,
                    r_model.date < _et,
                    r_model.user_id != r_model.referree_id,
                    r_model.referree_id.in_(chunk_ids)
                ).all()
                for row in referral_rows:
                    referral_data[row.referree_id]['spot_fee'] += row.spot_fee_usd
                    referral_data[row.referree_id]['perp_fee'] += row.perpetual_fee_usd
                    price = __get_asset_price(row.date, row.asset)
                    referral_data[row.referree_id]['spot_usd'] += row.spot_amount * price
                    referral_data[row.referree_id]['perp_usd'] += row.perpetual_amount * price
            _ret = defaultdict(dict)
            for referree_id in referree_ids:
                if spot_deal_usd := spot_data.get(referree_id):
                    _ret[referree_id]['spot_deal_usd'] = spot_deal_usd
                if perp_deal_usd := perp_data.get(referree_id):
                    _ret[referree_id]['perp_deal_usd'] = perp_deal_usd
                _ret[referree_id]['spot_fee'] = referral_data[referree_id].get('spot_fee', 0)
                _ret[referree_id]['perp_fee'] = referral_data[referree_id].get('perp_fee', 0)
                _ret[referree_id]['spot_usd'] = referral_data[referree_id].get('spot_usd', 0)
                _ret[referree_id]['perp_usd'] = referral_data[referree_id].get('perp_usd', 0)
            return _ret

        def __get_asset_price(_date, _asset) -> Decimal | None:
            if _date not in date_prices:
                date_prices[_date] = AssetPrice.get_close_price_map(_date)
            return date_prices.get(_date, {}).get(_asset, Decimal())

        date_prices = {}
        exists = {}
        need_query_first_data_uids = set()
        for _row in model.query.all():  # 商务大使邀请人数不会很多
            _row: BusinessAmbassadorReferralDetailReport
            exists[_row.user_id] = _row
            if not _row.trade_date or not _row.deposit_date:
                need_query_first_data_uids.add(_row.user_id)
        referree_ids = {uid for values in referral_mapping.values() for uid in values}
        new_referree_ids = referree_ids - set(exists.keys())
        common_data = _get_common_data(_user_ids=referree_ids)
        new_referree_data = __get_new_referree_releated_data()
        period_trade_data = __get_period_trade_data()
        ret = defaultdict(list)
        new_objs = []
        for amb_user_id, local_referree_ids in referral_mapping.items():
            for referree_id in local_referree_ids:
                detail = exists.get(referree_id)
                if not detail:
                    detail = model(
                        report_date=report_date,
                        user_id=referree_id,
                        amb_user_id=amb_user_id,
                        trade_date=new_referree_data[referree_id].get('trade_date'),
                        deposit_date=new_referree_data[referree_id].get('deposit_date'),
                        deposit_usd=new_referree_data[referree_id].get('deposit_usd', 0),
                        spot_deal_usd=0,
                        spot_deal_fee=0,
                        spot_refer_usd=0,
                        perpetual_deal_usd=0,
                        perpetual_deal_fee=0,
                        perpetual_refer_usd=0,
                    )
                    new_objs.append(detail)
                detail.report_date = report_date
                detail.spot_deal_usd += period_trade_data[referree_id].get('spot_deal_usd', 0)
                detail.spot_deal_fee += period_trade_data[referree_id].get('spot_fee', 0)
                detail.spot_refer_usd += period_trade_data[referree_id].get('spot_usd', 0)
                detail.perpetual_deal_usd += period_trade_data[referree_id].get('perp_deal_usd', 0)
                detail.perpetual_deal_fee += period_trade_data[referree_id].get('perp_fee', 0)
                detail.perpetual_refer_usd += period_trade_data[referree_id].get('perp_usd', 0)
                if not detail.deposit_date:  # 生成关系时，可能没有首次数据，需要持续更新
                    detail.deposit_date = new_referree_data[referree_id].get('deposit_date')
                    detail.deposit_usd = new_referree_data[referree_id].get('deposit_usd', 0)
                if not detail.trade_date:  # 生成关系时，可能没有首次数据，需要持续更新
                    detail.trade_date = new_referree_data[referree_id].get('trade_date')

                deposit_date_str = None
                if detail.deposit_date:
                    deposit_date_str = detail.deposit_date.strftime('%Y-%m-%d')
                trade_date_str = None
                if detail.trade_date:
                    trade_date_str = detail.trade_date.strftime('%Y-%m-%d')
                ret[amb_user_id].append(dict(
                    email=common_data[referree_id]['email'],
                    account_name=common_data[referree_id]['account_name'],
                    created_at=datetime_to_str(common_data[referree_id]['created_at']),
                    deposit_date=deposit_date_str,
                    trade_date=trade_date_str,
                    deposit_usd=amount_to_str(detail.deposit_usd, 2),
                    spot_deal_usd=amount_to_str(detail.spot_deal_usd, 2),
                    spot_deal_fee=amount_to_str(detail.spot_deal_fee, 2),
                    spot_refer_usd=amount_to_str(detail.spot_refer_usd, 2),
                    perpetual_deal_usd=amount_to_str(detail.perpetual_deal_usd, 2),
                    perpetual_deal_fee=amount_to_str(detail.perpetual_deal_fee, 2),
                    perpetual_refer_usd=amount_to_str(detail.perpetual_refer_usd, 2),
                ))
        for chunk_objs in batch_iter(new_objs, 5000):
            db.session.bulk_save_objects(chunk_objs)
            db.session.commit()
        db.session.commit()
        return ret

    def _send_report(data: dict[int, list[dict]] | None = None):

        def __get_sending_data():
            _ret = defaultdict(list)
            _rows = model.query.all()
            user_ids = {x.user_id for x in _rows}
            common_data = _get_common_data(_user_ids=user_ids)
            for _row in _rows:  # 商务大使邀请人数不会很多
                _row: BusinessAmbassadorReferralDetailReport
                referree_id = _row.user_id
                deposit_date_str = None
                if _row.deposit_date:
                    deposit_date_str = _row.deposit_date.strftime('%Y-%m-%d')
                trade_date_str = None
                if _row.trade_date:
                    trade_date_str = _row.trade_date.strftime('%Y-%m-%d')
                _ret[_row.amb_user_id].append(dict(
                    email=common_data[referree_id]['email'],
                    account_name=common_data[referree_id]['account_name'],
                    created_at=datetime_to_str(common_data[referree_id]['created_at']),
                    deposit_date=deposit_date_str,
                    trade_date=trade_date_str,
                    deposit_usd=amount_to_str(_row.deposit_usd, 2),
                    spot_deal_usd=amount_to_str(_row.spot_deal_usd, 2),
                    spot_deal_fee=amount_to_str(_row.spot_deal_fee, 2),
                    spot_refer_usd=amount_to_str(_row.spot_refer_usd, 2),
                    perpetual_deal_usd=amount_to_str(_row.perpetual_deal_usd, 2),
                    perpetual_deal_fee=amount_to_str(_row.perpetual_deal_fee, 2),
                    perpetual_refer_usd=amount_to_str(_row.perpetual_refer_usd, 2),
                ))
            return _ret

        def __get_export_url(_records):
            wb = Workbook()
            del wb['Sheet']
            header_data = (
                ('email', _('用户邮箱')),
                ('account_name', _('账户名')),
                ('created_at', _('注册时间')),
                ('deposit_date', _('首次充值时间')),
                ('trade_date', _('首次交易时间')),
                ('deposit_usd', _('首次充值金额')),
                ('spot_deal_usd', _('币币交易金额')),
                ('perpetual_deal_usd', _('合约交易金额')),
                ('spot_deal_fee', _('币币手续费')),
                ('perpetual_deal_fee', _('合约手续费')),
                ('spot_refer_usd', _('币币返佣')),
                ('perpetual_refer_usd', _('合约返佣')),
            )
            with force_locale(lang):
                headers = [_(item[1]) for item in header_data]
                ws = wb.create_sheet('detail')
                ws.append(headers)
            fields = [item[0] for item in header_data]
            for _record in _records:
                ws.append([_record[k] for k in fields])
            stream = io.BytesIO()
            wb.save(stream)
            stream.seek(0)
            file_key = AWSBucketTmp.new_file_key(suffix='xlsx')
            if not AWSBucketTmp.put_file(file_key, stream):
                return ''
            return AWSBucketTmp.get_file_url(file_key, ttl=60 * 60 * 24 * 7)

        if data is None:
            data = __get_sending_data()
        for amb_user_id, referree_datas in data.items():
            lang = UserPreferences(amb_user_id).language.value
            with force_locale(lang):
                title = render_template_string(_("【CoinEx】{{ report_date }} 商务大使邀请数据"), report_date=report_date)
                export_url = __get_export_url(_records=referree_datas)
                email = User.query.get(amb_user_id).email
                send_business_ambassador_referral_detail_email(email, title, lang, export_url)

    def _get_common_data(_user_ids: list | set) -> dict:
        _ret = {}
        for chunk_ids in batch_iter(_user_ids, 5000):
            user_rows = User.query.with_entities(
                User.id,
                User.created_at,
                User.email,
            ).filter(
                User.id.in_(chunk_ids)
            ).all()
            extra_rows = UserExtra.query.with_entities(
                UserExtra.user_id,
                UserExtra.account_name,
            ).filter(
                UserExtra.user_id.in_(chunk_ids)
            ).all()
            acc_name_map = dict(extra_rows)
            for _user_r in user_rows:
                _acc_name = acc_name_map.get(_user_r.id) or UserExtra.default_account_name(_user_r.id)
                _ret[_user_r.id] = {
                    'email': hide_email(_user_r.email),
                    'created_at': _user_r.created_at,
                    'account_name': f"@{_acc_name}",
                }
        return _ret

    if not check_data_ready(report_date):
        current_app.logger.warning(f"{report_date} dump_history 数据还未同步完成")
        return
    model = BusinessAmbassadorReferralDetailReport
    max_report_date = model.query.with_entities(
        func.max(model.report_date).label('report_date')
    ).filter().scalar() or None
    referral_mapping = _build_referral_history()
    if max_report_date and max_report_date == report_date:
        _send_report()
    else:
        if max_report_date:
            delta_days = (report_date - max_report_date).days
            query_start = report_date - datetime.timedelta(days=delta_days)
        else:
            query_start = UserTradeSummary.query.with_entities(
                func.min(UserTradeSummary.report_date).label('report_date')
            ).filter().scalar()
            query_start -= datetime.timedelta(days=1)
        sending_data = _create_or_update_report()
        _send_report(sending_data)


@scheduled(crontab(minute="10", hour="6"))
@lock_call()
def send_daily_ambassador_report_schedule():
    header_data = (
        ("report_date", "Date"),
        ("email", "Ambassadors Email"),
        ("bus_user_name", "BD'email"),
        ("refer_count", "New registration from the ambassador"),
        ("deposit_count", "Deposit user from the ambassador"),
        ("deal_count", "Trading user from the ambassador"),
        ("spot_trade_usd", "Volume of Spot from referral"),
        ("perpetual_trade_usd", "Volume of futures from referral"),
        ("spot_fee_usd", "handling fee of Spot"),
        ("perpetual_fee_usd", "handling fee of futures"),
        ("refer_total_amount", "BD's commission"),
    )

    today_ = today()
    month_start = today_ - datetime.timedelta(days=today_.day - 1)
    biz_query = BusinessAmbassador.query.with_entities(
        BusinessAmbassador.user_id
    ).filter(
        BusinessAmbassador.status == BusinessAmbassador.Status.VALID
    ).with_entities(
        BusinessAmbassador.user_id,
        BusinessAmbassador.bus_user_id,
    ).all()
    biz_user_mapper = defaultdict(list)
    for biz_row in biz_query:
        biz_user_mapper[biz_row.bus_user_id].append(biz_row.user_id)
    
    report_query = DailyBusinessAmbassadorReferralReport.query.filter(
        DailyBusinessAmbassadorReferralReport.report_date >= month_start,
        DailyBusinessAmbassadorReferralReport.report_date < today_,
    ).all()
    referree_user_set = {i.user_id for i in report_query}
    user_report_mapper = defaultdict(list)
    for report_row in report_query:
        user_report_mapper[report_row.user_id].append(report_row)

    user_email_mapper = {
        u.id: u.email for u in User.query.filter(
            User.id.in_(set(biz_user_mapper.keys()) | referree_user_set)
        ).with_entities(
            User.id,
            User.email
        )
    }
    
    for biz_user_id, referree_ids in biz_user_mapper.items():
        biz_user_email = user_email_mapper[biz_user_id]
        biz_user_data = []
        for referree_id in referree_ids:
            user_data = user_report_mapper.get(referree_id, [])
            if not user_data:
                continue
            biz_user_data.append(user_data)
        biz_user_data = [j for i in biz_user_data for j in i if j]
        if not biz_user_data:
            continue
        biz_user_items = sorted(biz_user_data, key=lambda x: x.report_date, reverse=True)
        data = []
        for item in biz_user_items:  # type: DailyAmbassadorReferralDetailReport
            data.append(dict(
                report_date=item.report_date,
                email=user_email_mapper.get(item.user_id, ""),
                bus_user_name=biz_user_email,
                refer_count=item.refer_count,
                deposit_count=item.deposit_count,
                deal_count=item.deal_count,
                spot_trade_usd=item.spot_trade_usd,
                perpetual_trade_usd=item.perpetual_trade_usd,
                spot_fee_usd=item.spot_fee_usd,
                perpetual_fee_usd=item.perpetual_fee_usd,
                refer_total_amount=item.refer_total_amount,
            ))

        fields = [r[0] for r in header_data]
        headers = [r[1] for r in header_data]
        streams = ExcelExporter(
            data_list=data, fields=fields, headers=headers
        ).export_streams()
        file_url = upload_file(streams, 'xlsx', ttl=60 * 60 * 24)
        lang = UserPreferences(biz_user_id).language.value
        with force_locale(lang):
            title = render_template_string(_("【CoinEx】{{ report_date }} 商务大使数据"), report_date=today_)
            EmailSender.send_from_template(
                'notice',
                'business_ambassador_report',
                dict(
                    name=biz_user_email,
                    file_url=file_url,
                ),
                biz_user_email,
                lang,
                subject=title
            )

