# -*- coding: utf-8 -*-

import datetime
import json
from collections import defaultdict
from decimal import Decimal
from typing import Named<PERSON><PERSON><PERSON>

from celery.schedules import crontab
from flask import current_app
from pyroaring import BitMap
from sqlalchemy import func, and_, or_

from app.business.market_maker import MarketMakerHelper
from app.business.referral import ReferralRepository
from app.business.summary import get_period_trade_amount_mapping, get_period_spot_trade_mapping, \
    get_period_perp_trade_mapping, get_period_trade_fee_mapping, get_period_increase_trade_users, check_data_ready
from app.common import CeleryQueues
from app.business import lock_call
from app.models import DailyReferReport, MonthlyReferReport, db, \
    MonthlyUserReport, ReferralHistory, ReferralAssetHistory, \
    MonthlyAmbassadorReferralReport, AmbassadorAgent, Ambassador, AssetPrice, \
    DailyAmbassadorReferralReport, AmbassadorAgentHistory, \
    DailyReferTypeReport, MonthlyReferTypeReport, UserBusinessRecord, LiquiditySlice, User, DailyUserReport
from app.models.referral import ReferralAssetDetail, BusinessAmbassador, TreeAmbassador, IndirectReferralAssetHistory
from app.business.statistic import get_main_user_balance_usd_map
from app.schedules.reports.utils import get_monthly_report_date, check_report_date_exists, get_report_active_user_ids_in_range
from app.utils import quantize_amount, scheduled, batch_iter, route_module_to_celery_queue
from app.utils.date_ import date_to_datetime, next_month, now, today

route_module_to_celery_queue(__name__, CeleryQueues.REPORT)


def get_market_maker_ids():
    market_makers = MarketMakerHelper.list_all_maker_ids(include_sub_account=False)
    market_makers = set(market_makers)
    return market_makers


def update_monthly_refer_report(start_month, end_month, force_update=False):
    if not check_data_ready(start_month, with_fee=False):
        current_app.logger.warning("{} update_monthly_refer_report-UserTradeSummary 数据未就绪".format(start_month))
        return

    query = DailyReferReport.query.filter(
        DailyReferReport.report_date >= start_month,
        DailyReferReport.report_date < end_month
    ).order_by(DailyReferReport.report_date)

    monthly_invitee_trade_usd = \
    monthly_invitee_all_fee = \
    monthly_refer_amount = \
    monthly_normal_refer_usd = \
    monthly_broker_amount = \
    monthly_ambassador_amount = \
    monthly_ambassador_agent_amount = \
    monthly_all_trade_fee_usd = \
    monthly_refer_usd = Decimal()
    referrer_count = 0
    total_invitee = 0

    for item in query:
        monthly_invitee_trade_usd += item.invitee_trade_usd
        monthly_invitee_all_fee += item.invitee_all_fee
        monthly_refer_amount += item.refer_amount
        monthly_normal_refer_usd += item.normal_refer_usd
        monthly_broker_amount += item.broker_amount
        monthly_ambassador_amount += item.ambassador_amount
        monthly_ambassador_agent_amount += item.ambassador_agent_amount
        monthly_refer_usd += item.refer_usd
        monthly_all_trade_fee_usd += item.all_trade_fee_usd
        referrer_count += item.referrer_count
        total_invitee = item.total_invitee

    start_exp_dt = start_month - datetime.timedelta(days=365)
    end_exp_dt = end_month - datetime.timedelta(days=365)
    referee_user_ids = {
        v.referree_id for v in
        ReferralHistory.query.filter(
            or_(
                and_(
                    # 当月末邀请关系有效的所有用户
                    ReferralHistory.created_at < date_to_datetime(end_month),
                    ReferralHistory.status == ReferralHistory.Status.VALID,
                ),
                and_(
                    # 当月失效的用户，普通邀请默认1年过期
                    ReferralHistory.created_at >= start_exp_dt,
                    ReferralHistory.created_at < end_exp_dt,
                    ReferralHistory.status == ReferralHistory.Status.EXPIRED,
                )
            )
        ).with_entities(ReferralHistory.referree_id).all()
    }

    trade_summary = get_period_trade_amount_mapping(start_month, end_month)
    refer_trade_users = set(trade_summary.keys()) & referee_user_ids
    invitee_count = len(refer_trade_users)
    monthly_inc_trade_users = get_period_increase_trade_users(
        start_month - datetime.timedelta(days=1),
        end_month - datetime.timedelta(days=1),
    )
    new_invitee_trade_users = monthly_inc_trade_users & refer_trade_users
    new_invitee_count = len(new_invitee_trade_users)

    inviter_count = ReferralAssetHistory.query.filter(
            ReferralAssetHistory.date >= start_month,
            ReferralAssetHistory.date < end_month
        ).with_entities(
            func.count(ReferralAssetHistory.user_id.distinct())
        ).scalar() or 0

    record = MonthlyReferReport.get_or_create(report_date=start_month)
    if not record.refer_usd or force_update:
        record.invitee_count = invitee_count
        record.new_invitee_count = new_invitee_count
        record.invitee_trade_usd = monthly_invitee_trade_usd
        record.invitee_all_fee = monthly_invitee_all_fee
        record.inviter_count = inviter_count
        record.referrer_count = referrer_count
        record.refer_amount = monthly_refer_amount
        record.normal_refer_usd = monthly_normal_refer_usd
        record.broker_amount = monthly_broker_amount
        record.refer_usd = monthly_refer_usd
        record.ambassador_amount = monthly_ambassador_amount
        record.ambassador_agent_amount = monthly_ambassador_agent_amount
        record.all_trade_fee_usd = monthly_all_trade_fee_usd
        record.total_invitee = total_invitee

        user_report = MonthlyUserReport.query.filter(
            MonthlyUserReport.report_date >= start_month,
            MonthlyUserReport.report_date < end_month
        ).first()
        if not user_report or user_report.active_trade_user == 0:
            record.invitee_trade_percent = Decimal()
        else:
            record.invitee_trade_percent = quantize_amount(record.invitee_count / user_report.active_trade_user, 4)
        if not user_report or user_report.increase_user == 0:
            record.invitee_percent = Decimal()
        else:
            record.invitee_percent = quantize_amount(record.referrer_count / user_report.increase_user, 4)
        if not user_report or user_report.increase_trade_user == 0:
            record.new_trade_percent = Decimal()
        else:
            record.new_trade_percent = quantize_amount(record.new_invitee_count / user_report.increase_trade_user, 4)

        amm_liq_slice_rows = LiquiditySlice.query.filter(
            LiquiditySlice.date >= start_month,
            LiquiditySlice.date < end_month,
        ).with_entities(
            LiquiditySlice.fee_usd,
        ).all()
        amm_refund_fee_usd = sum([i.fee_usd for i in amm_liq_slice_rows])
        total_fee_usd = max(record.all_trade_fee_usd - amm_refund_fee_usd, 0)
        total_refer_rate = quantize_amount(record.refer_usd / total_fee_usd, 4) if total_fee_usd else Decimal()

        record.refer_rate = quantize_amount(
            record.refer_usd / record.invitee_all_fee, 4) \
            if record.invitee_all_fee > Decimal() else Decimal()
        record.total_refer_rate = total_refer_rate
        db.session.add(record)

    db.session.commit()


def update_monthly_ambassador_referral_report(start_month, end_month, force_update=False):
    if not check_data_ready(start_month):
        current_app.logger.warning(
            "{} update_monthly_ambassador_referral_report-UserTradeSummary 数据未就绪".format(start_month)
        )
        return

    # 依赖MonthlyUserReport, MonthlyReferTypeReport, UserTradeFeeSummary
    # 另依赖 UserBusinessRecord
    user_report: MonthlyUserReport = MonthlyUserReport.query.filter(
        MonthlyUserReport.report_date == start_month).first()

    refer_type_report = DailyReferTypeReport.query.filter(
        DailyReferTypeReport.report_date >= start_month,
        DailyReferTypeReport.report_date < end_month,
        DailyReferTypeReport.type == DailyReferTypeReport.Type.REFERRAL
    ).with_entities(
        func.sum(DailyReferTypeReport.fee_usd).label('fee_usd')).first()

    if not user_report or not refer_type_report:
        return

    # 周期内大使邀请新增的交易用户/周期内全站新增的交易用户
    def get_history_trade_users(business, date_):
        if date_ >= now().date():
            row = UserBusinessRecord.query.with_entities(
                UserBusinessRecord.history_user_bit_map
            ).filter(
                UserBusinessRecord.business == business,
            ).order_by(UserBusinessRecord.report_at.desc()).first()
        else:
            row = UserBusinessRecord.query.with_entities(
                UserBusinessRecord.history_user_bit_map
            ).filter(
                UserBusinessRecord.business == business,
                UserBusinessRecord.report_at == date_
            ).first()
        return set(BitMap.deserialize(row.history_user_bit_map)) if row else None

    end_month_yes_day = end_month - datetime.timedelta(days=1)
    spot_trade_0 = get_history_trade_users(
        UserBusinessRecord.Business.SPOT_TRADE,
        end_month_yes_day)
    spot_trade_1 = get_history_trade_users(
        UserBusinessRecord.Business.SPOT_TRADE,
        start_month - datetime.timedelta(days=1)
    )
    perp_trade_0 = get_history_trade_users(
        UserBusinessRecord.Business.PERPETUAL_TRADE,
        end_month_yes_day)
    perp_trade_1 = get_history_trade_users(
        UserBusinessRecord.Business.PERPETUAL_TRADE,
        start_month - datetime.timedelta(days=1)
    )
    if spot_trade_0 is None or spot_trade_1 is None:
        return
    if perp_trade_0 is None or perp_trade_1 is None:
        return

    daily_records = DailyAmbassadorReferralReport.query.filter(
        DailyAmbassadorReferralReport.report_date >= start_month,
        DailyAmbassadorReferralReport.report_date < end_month
    ).order_by(DailyAmbassadorReferralReport.report_date.asc()).all()

    # 金额相关的直接叠加
    referree_deal_amount = \
        referree_fee_amount = \
        ambassador_referral_amount = \
        ambassador_agent_referral_amount = Decimal()
    refer_user_balance_usd = Decimal()
    for record in daily_records:
        referree_deal_amount += record.refer_deal_amount
        referree_fee_amount += record.refer_fee_amount
        ambassador_referral_amount += \
            record.ambassador_referral_amount
        ambassador_agent_referral_amount += \
            record.ambassador_agent_referral_amount
        refer_user_balance_usd = record.refer_user_balance_usd  # 取当月最后一天数据

    ambassador_agent_count = AmbassadorAgent.query.filter(
        AmbassadorAgent.status == AmbassadorAgent.Status.VALID
    ).with_entities(func.count()).scalar() or 0
    ambassador_count = DailyAmbassadorReferralReport.query.filter(
        DailyAmbassadorReferralReport.report_date <= end_month
    ).order_by(
        DailyAmbassadorReferralReport.report_date.desc()
    ).first().ambassador_count
    agent_rows = AmbassadorAgent.query.with_entities(
        AmbassadorAgent.user_id
    ).filter(
        AmbassadorAgent.status == AmbassadorAgent.Status.VALID,
        AmbassadorAgent.created_at < end_month,
    ).all()
    agent_user_ids = {row.user_id for row in agent_rows}

    new_ambassador_agent_count = AmbassadorAgent.query.filter(
        AmbassadorAgent.created_at >= start_month,
        AmbassadorAgent.created_at < end_month,
        AmbassadorAgent.status == AmbassadorAgent.Status.VALID
    ).with_entities(func.count()).scalar() or 0
    new_ambassador_count = Ambassador.query.filter(
        Ambassador.effected_at >= start_month,
        Ambassador.effected_at < end_month,
        Ambassador.status == Ambassador.Status.VALID
    ).with_entities(func.count()).scalar() or 0

    # 大使
    ambassador_ids = Ambassador.query.filter(
        Ambassador.status == Ambassador.Status.VALID
    ).with_entities(
        Ambassador.user_id
    ).all()
    ambassador_ids = [item.user_id for item in ambassador_ids]
    only_agent_count = len(agent_user_ids - set(ambassador_ids))

    active_user_ids = get_report_active_user_ids_in_range(
        start_date=start_month,
        end_date=end_month_yes_day,
        realtime_start_date=end_month_yes_day,
        realtime_end_date=end_month,
    )

    referrees = []
    history_referees = []
    history_referrees_1 = []
    amb_ref_ee_ids = set()
    market_makers = get_market_maker_ids()

    for chunk_amb_ids in batch_iter(ambassador_ids, 1000):
        chunk_referrees = ReferralHistory.query.filter(
            ReferralHistory.created_at >= start_month,
            ReferralHistory.created_at < end_month,
            ReferralHistory.referrer_id.in_(chunk_amb_ids),
            ReferralHistory.referree_id.notin_(market_makers),
            ReferralHistory.status == ReferralHistory.Status.VALID
        ).with_entities(ReferralHistory.referree_id.distinct().label('referree_id')).all()
        referrees.extend(chunk_referrees)

        chunk_history_referees = ReferralHistory.query.filter(
            ReferralHistory.created_at < end_month,
            ReferralHistory.referrer_id.in_(chunk_amb_ids),
            ReferralHistory.referree_id.notin_(market_makers),
            ReferralHistory.status == ReferralHistory.Status.VALID
        ).with_entities(ReferralHistory.referree_id.distinct().label('referree_id')).all()
        history_referees.extend(chunk_history_referees)

        chunk_history_referrees_1 = ReferralHistory.query.filter(
            ReferralHistory.created_at < start_month,
            ReferralHistory.referrer_id.in_(chunk_amb_ids),
            ReferralHistory.referree_id.notin_(market_makers),
            ReferralHistory.status == ReferralHistory.Status.VALID
        ).with_entities(ReferralHistory.referree_id.distinct().label('referree_id')).all()
        history_referrees_1.extend(chunk_history_referrees_1)

        _all_ref_ee_rows = ReferralHistory.query.filter(
            ReferralHistory.created_at < end_month,
            ReferralHistory.referrer_id.in_(chunk_amb_ids),
            ReferralHistory.referree_id.notin_(market_makers),
        ).with_entities(
            ReferralHistory.referree_id,
        ).all()
        amb_ref_ee_ids.update({i.referree_id for i in _all_ref_ee_rows})

    trade_users_0 = (spot_trade_0 | perp_trade_0) - market_makers
    trade_users_1 = (spot_trade_1 | perp_trade_1) - market_makers
    new_trade_user_site_count = len(trade_users_0 - trade_users_1)

    referree_ids = [r.referree_id for r in referrees]
    history_referee_ids = [r.referree_id for r in history_referees]
    referree_ids_1 = [r.referree_id for r in history_referrees_1]
    new_trade_user_count = len(
        (trade_users_0 & set(referree_ids))
        -
        (trade_users_1 & set(referree_ids_1))
    )
    deal_count = 0
    for chunk_ee_ids in batch_iter(history_referee_ids, 1000):
        trade_mapping = get_period_trade_amount_mapping(start_month, end_month, chunk_ee_ids)
        deal_count += len(trade_mapping.keys())

    # 收到返佣大使人数
    referral_counts = ReferralAssetHistory.query.filter(
        ReferralAssetHistory.date >= start_month,
        ReferralAssetHistory.date < end_month,
        ReferralAssetHistory.amount > Decimal(),
        ReferralAssetHistory.type.in_([ReferralAssetHistory.Type.AMBASSADOR, ReferralAssetHistory.Type.AMBASSADOR_AGENT]),
    ).group_by(
        ReferralAssetHistory.user_id,
        ReferralAssetHistory.type,
    ).with_entities(
        ReferralAssetHistory.user_id,
        ReferralAssetHistory.type,
    ).all()
    ambassador_refer_count = ambassador_agent_refer_count = 0
    for r in referral_counts:
        if r.type == ReferralAssetHistory.Type.AMBASSADOR:
            if r.user_id in ambassador_ids:
                ambassador_refer_count += 1
        elif r.type == ReferralAssetHistory.Type.AMBASSADOR_AGENT:
            if r.user_id in agent_user_ids:
                ambassador_agent_refer_count += 1

    monthly_record: MonthlyAmbassadorReferralReport = MonthlyAmbassadorReferralReport.get_or_create(
        report_date=start_month)

    fees = get_period_trade_fee_mapping(start_month, end_month)
    site_total_fee = sum(fees.values())

    increase_user = User.query.filter(
        User.created_at >= start_month,
        User.created_at < end_month,
        User.user_type == User.UserType.NORMAL,
    ).count()
    if increase_user == 0:
        new_user_percent = 0
    else:
        new_user_percent = quantize_amount(
            len(referree_ids) / increase_user, 4)
    if (denominator := site_total_fee - refer_type_report.fee_usd - ambassador_referral_amount - ambassador_agent_referral_amount) == 0:
        fee_percent = 0
    else:
        fee_percent = quantize_amount(
            (referree_fee_amount - ambassador_referral_amount - ambassador_agent_referral_amount) / denominator, 4)

    if user_report.active_trade_user == 0:
        trade_percent = Decimal()
    else:
        trade_percent = quantize_amount(deal_count / user_report.active_trade_user, 4)

    if not monthly_record.refer_fee_amount or force_update:
        monthly_record.ambassador_agent_count = ambassador_agent_count
        monthly_record.ambassador_count = ambassador_count
        monthly_record.new_ambassador_agent_count = new_ambassador_agent_count
        monthly_record.new_ambassador_count = new_ambassador_count
        monthly_record.refer_count = len(referree_ids),
        monthly_record.refer_deal_count = deal_count
        monthly_record.refer_deal_amount = referree_deal_amount
        monthly_record.refer_fee_amount = referree_fee_amount
        monthly_record.refer_ambassador_count = ambassador_refer_count
        monthly_record.ambassador_referral_amount = ambassador_referral_amount
        monthly_record.refer_ambassador_agent_count = ambassador_agent_refer_count
        monthly_record.ambassador_agent_referral_amount = ambassador_agent_referral_amount
        monthly_record.ambassador_referral_rate = quantize_amount(
            monthly_record.ambassador_referral_amount / monthly_record.refer_fee_amount, 4) \
            if monthly_record.refer_fee_amount > Decimal() else Decimal()
        monthly_record.new_user_percent = new_user_percent
        monthly_record.fee_percent = fee_percent
        monthly_record.only_agent_count = only_agent_count
        monthly_record.ambassador_refer_user_count = len(amb_ref_ee_ids)
        monthly_record.new_trade_user_count = new_trade_user_count
        monthly_record.new_trade_user_site_count = new_trade_user_site_count
        monthly_record.trade_percent = trade_percent
        monthly_record.refer_user_balance_usd = refer_user_balance_usd
        monthly_record.refer_active_user_count = len(active_user_ids & amb_ref_ee_ids)
        db.session.add(monthly_record)
    db.session.commit()


class NormalReferralInfo(NamedTuple):
    all_referree_ids: set  # 包括失效用户
    total_referree_ids: set  # 不包括失效用户
    referrer_count: int
    new_referrer_count: int
    invitee_count: int


def update_daily_refer_type_report(start_date, end_date):
    # 检查快照是否已经生成
    if not check_data_ready(start_date):
        return

    if not ReferralAssetHistory.query.filter(
            ReferralAssetHistory.date >= start_date,
            ReferralAssetHistory.date < end_date
    ).first():
        return
    if not UserBusinessRecord.query.filter(
        UserBusinessRecord.report_at == start_date,
    ).with_entities(UserBusinessRecord.id).first():
        return
    # 不考虑做市商
    market_makers = get_market_maker_ids()

    ambassador_ids = [i.user_id for i in Ambassador.query.filter(
        Ambassador.created_at < end_date,
        Ambassador.status == Ambassador.Status.VALID
    ).with_entities(Ambassador.user_id)]

    business_ambassador_ids = [i.user_id for i in BusinessAmbassador.query.filter(
        BusinessAmbassador.created_at < end_date,
        BusinessAmbassador.status == BusinessAmbassador.Status.VALID
    ).with_entities(BusinessAmbassador.user_id)]
    ambassador_ids.extend(business_ambassador_ids)
    tree_amb_ids = [i.user_id for i in TreeAmbassador.query.filter(
        TreeAmbassador.created_at < end_date,
        TreeAmbassador.status == TreeAmbassador.Status.VALID,
    ).with_entities(TreeAmbassador.user_id).all()]
    ambassador_ids.extend(tree_amb_ids)

    agent_ambassador_ids = [i.ambassador_id for i in AmbassadorAgentHistory.query.filter(
        AmbassadorAgentHistory.created_at < end_date,
        AmbassadorAgentHistory.status == AmbassadorAgentHistory.Status.VALID
    ).with_entities(AmbassadorAgentHistory.ambassador_id)]

    agent_ambassador_ids = set(ambassador_ids) & set(agent_ambassador_ids)
    normal_referral_info = get_normal_ref_info(start_date, end_date, ambassador_ids, market_makers)

    ambassador_referree_ids = {v.referree_id for v in
                    ReferralHistory.query.filter(
                        ReferralHistory.created_at < end_date,
                        ReferralHistory.status == ReferralHistory.Status.VALID,
                        ReferralHistory.referrer_id.in_(ambassador_ids)
                    ).with_entities(ReferralHistory.referree_id) if v.referree_id not in market_makers}

    agent_referree_ids = {v.referree_id for v in
                    ReferralHistory.query.filter(
                        ReferralHistory.created_at < end_date,
                        ReferralHistory.status == ReferralHistory.Status.VALID,
                        ReferralHistory.referrer_id.in_(agent_ambassador_ids)
                    ).with_entities(ReferralHistory.referree_id) if v.referree_id not in market_makers}

    price_map = AssetPrice.get_close_price_map(start_date)

    def add_refer_data(user_ids, refer_type):
        spot_data, perp_data = {}, {}
        for _ids in batch_iter(user_ids, 1000):
            spot_data.update(get_period_spot_trade_mapping(start_date, end_date, _ids))
            perp_data.update(get_period_perp_trade_mapping(start_date, end_date, _ids))
        fee_total_usd = 0
        for _ids in batch_iter(user_ids, 1000):
            # 不能直接汇总手续费，邀请码如果设置分成比例，用户A和B都会写一条记录(A->A, A->B)
            fee = ReferralAssetDetail.query.filter(
                ReferralAssetDetail.referree_id.in_(_ids),
                ReferralAssetDetail.date == start_date,
                ReferralAssetDetail.user_id != ReferralAssetDetail.referree_id,
            ).with_entities(
                func.sum(ReferralAssetDetail.spot_fee_usd + ReferralAssetDetail.perpetual_fee_usd)
            ).scalar() or 0
            fee_total_usd += fee

        refer_data = ReferralAssetHistory.query.filter(
            ReferralAssetHistory.date == start_date,
            ReferralAssetHistory.type == refer_type
        ).all()
        asset = 'CET' if refer_type == 'REFERRAL' else 'USDT'
        trade_user_ids = set(spot_data.keys()) | set(perp_data.keys())
        trade_total_usd = trade_spot_usd = trade_perpetual_usd = Decimal()
        for user_id, trade_amount in spot_data.items():
            trade_spot_usd += trade_amount
            trade_total_usd += trade_amount
        for user_id, trade_amount in perp_data.items():
            trade_perpetual_usd += trade_amount
            trade_total_usd += trade_amount
        refer_user_ids = {i.user_id for i in refer_data}
        refer_total_amount = sum([i.amount for i in refer_data])
        if refer_type == DailyReferTypeReport.Type.AMBASSADOR.name:
            # 大使非直客返佣
            indir_ref_rows = IndirectReferralAssetHistory.query.filter(
                IndirectReferralAssetHistory.date == start_date,
                IndirectReferralAssetHistory.type == IndirectReferralAssetHistory.Type.TREE_AMB_TO_PARENT,
            ).with_entities(
                IndirectReferralAssetHistory.user_id,
                IndirectReferralAssetHistory.amount,
            ).all()
            refer_user_ids |= {i.user_id for i in indir_ref_rows}
            refer_total_amount += sum([i.amount for i in indir_ref_rows])
        asset_price = price_map[asset]
        refer_total_usd = refer_total_amount * asset_price
        refer_rate = refer_total_usd/fee_total_usd if fee_total_usd else 0

        _new_trade_users = daily_inc_trade_users & trade_user_ids

        user_report = DailyUserReport.query.filter(
            DailyUserReport.report_date == start_date
        ).first()
        if not user_report or user_report.active_trade_user == 0:
            trade_percent = Decimal()
        else:
            trade_percent = quantize_amount(len(trade_user_ids) / user_report.active_trade_user, 4)
        if not user_report or user_report.increase_trade_user == 0:
            new_trade_percent = Decimal()
        else:
            new_trade_percent = quantize_amount(len(_new_trade_users) / user_report.increase_trade_user, 4)

        if refer_type == DailyReferTypeReport.Type.AMBASSADOR.name:
            type_user_count = len(ambassador_ids)
            refer_user_ids = refer_user_ids & set(ambassador_ids)  # 大使返佣报表: 只统计周期内有收到返佣的大使，不统计收到返佣的被邀请人
        elif refer_type == DailyReferTypeReport.Type.AMBASSADOR_AGENT.name:
            type_user_count = len(agent_ambassador_ids)
        else:
            type_user_count = 0

        record: DailyReferTypeReport = DailyReferTypeReport.get_or_create(report_date=start_date, type=refer_type)
        record.asset = asset
        record.type_user_count = type_user_count
        record.trade_count = len(trade_user_ids)
        record.trade_user_list = json.dumps(list(trade_user_ids))
        record.new_trade_count = len(_new_trade_users)
        record.trade_usd = trade_total_usd
        record.trade_spot_usd = trade_spot_usd
        record.trade_perpetual_usd = trade_perpetual_usd
        record.fee_usd = fee_total_usd
        record.refer_count = len(refer_user_ids)
        record.refer_user_list = json.dumps(list(refer_user_ids))
        record.refer_amount = refer_total_amount
        record.refer_usd = refer_total_usd
        record.refer_rate = refer_rate
        record.trade_percent = trade_percent
        record.new_trade_percent = new_trade_percent
        return record

    def update_invitee_percent(record: DailyReferTypeReport):
        user_report = DailyUserReport.query.filter(
            DailyUserReport.report_date == start_date
        ).first()
        if not user_report or user_report.increase_user == 0:
            record.invitee_percent = Decimal()
        else:
            record.invitee_percent = quantize_amount(record.invitee_count / user_report.increase_user, 4)

    def add_normal_data(normal_data: NormalReferralInfo):
        referee_ids = normal_data.total_referree_ids
        record = add_refer_data(referee_ids, DailyReferTypeReport.Type.REFERRAL.name)
        record.referrer_count = normal_data.referrer_count
        record.new_referrer_count = normal_data.new_referrer_count
        record.invitee_count = normal_data.invitee_count
        record.total_invitee = len(normal_data.all_referree_ids)

        update_invitee_percent(record)

        db.session.add(record)

    def add_ambassador_or_agent_data(user_ids, amb_user_ids, refer_type):
        record = add_refer_data(user_ids, refer_type)
        record.invitee_count = get_invitee_count(start_date, end_date, amb_user_ids, market_makers)
        invitee_ee_ids = get_invitee_ee_ids(end_date, amb_user_ids, market_makers)
        record.total_invitee = len(invitee_ee_ids)
        record.refer_active_user_count = len(invitee_ee_ids & active_user_ids)
        if refer_type == DailyReferTypeReport.Type.AMBASSADOR.name:
            main_user_balance_usd_map = get_main_user_balance_usd_map(int(date_to_datetime(start_date).timestamp()))
            record.refer_user_balance_usd = quantize_amount(sum([main_user_balance_usd_map[i] for i in invitee_ee_ids]), 2)

        update_invitee_percent(record)

        db.session.add(record)

    daily_inc_trade_users = get_period_increase_trade_users(start_date - datetime.timedelta(days=1), start_date)
    active_user_ids = get_report_active_user_ids_in_range(
        start_date=start_date,
        end_date=start_date,
        realtime_end_date=start_date + datetime.timedelta(days=1),
    )
    add_ambassador_or_agent_data(ambassador_referree_ids, ambassador_ids, DailyReferTypeReport.Type.AMBASSADOR.name)
    add_ambassador_or_agent_data(agent_referree_ids, agent_ambassador_ids, DailyReferTypeReport.Type.AMBASSADOR_AGENT.name)
    add_normal_data(normal_referral_info)
    db.session.commit()


def get_normal_ref_info(start_date, end_date, ambassador_ids, market_makers) -> NormalReferralInfo:
    start = date_to_datetime(start_date)
    records = ReferralHistory.query.filter(
        ReferralHistory.created_at < end_date,
        ReferralHistory.referrer_id.notin_(ambassador_ids),
    ).with_entities(
        ReferralHistory.created_at,
        ReferralHistory.referrer_id,
        ReferralHistory.referree_id,
        ReferralHistory.status,
    )
    all_referree_ids, total_referree_ids, referree_ids, referer_ids = set(), set(), set(), set()
    for record in records:
        referee_id = record.referree_id
        referrer_id = record.referrer_id
        if referee_id in market_makers:
            continue
        all_referree_ids.add(referee_id)
        if record.status != ReferralHistory.Status.VALID:
            continue
        total_referree_ids.add(referee_id)
        if record.created_at >= start:
            referree_ids.add(referee_id)
            referer_ids.add(referrer_id)
    new_referrer_count = get_new_referrer_count(referer_ids, start_date)
    ret = NormalReferralInfo(
        all_referree_ids=all_referree_ids,
        total_referree_ids=total_referree_ids,
        referrer_count=len(referer_ids),
        new_referrer_count=new_referrer_count,
        invitee_count=len(referree_ids),
    )
    return ret


def get_invitee_count(start_date, end_date, ambassador_ids, market_makers):
    referrer_count = ReferralHistory.query.filter(
        ReferralHistory.created_at < end_date,
        ReferralHistory.created_at >= start_date,
        ReferralHistory.referrer_id.in_(ambassador_ids),
        ReferralHistory.referree_id.notin_(market_makers),
        ReferralHistory.status == ReferralHistory.Status.VALID,
    ).with_entities(
        func.count(ReferralHistory.referree_id.distinct().label('referree_id'))
    ).scalar() or 0
    return referrer_count


def get_invitee_ee_ids(end_date, ambassador_ids, market_makers) -> set[int]:
    rows = ReferralHistory.query.filter(
        ReferralHistory.created_at < end_date,
        ReferralHistory.referrer_id.in_(ambassador_ids),
        ReferralHistory.referree_id.notin_(market_makers),
        ReferralHistory.status == ReferralHistory.Status.VALID,
    ).with_entities(
        ReferralHistory.referree_id,
    ).all()
    return {i.referree_id for i in rows}


def get_new_referrer_count(user_ids, start_date):
    records = ReferralHistory.query.filter(
        ReferralHistory.created_at < start_date,
        ReferralHistory.referrer_id.in_(user_ids)
    ).with_entities(
        ReferralHistory.referrer_id
    ).all()
    return len(user_ids - {i[0] for i in records})


def update_monthly_refer_type_report(start_date, end_date):
    daily_refer_data = DailyReferTypeReport.query.filter(
        DailyReferTypeReport.report_date >= start_date,
        DailyReferTypeReport.report_date < end_date,
    ).order_by(DailyReferTypeReport.report_date.asc()).all()
    end_date_yes_day = end_date - datetime.timedelta(days=1)
    monthly_inc_trade_users = get_period_increase_trade_users(
        start_date - datetime.timedelta(days=1),
        end_date_yes_day,
    )
    refer_type_map = defaultdict(lambda: {
        'asset': str(),
        'trade_user_list': set(),
        'refer_user_list': set(),
        'trade_usd': Decimal(),
        'trade_spot_usd': Decimal(),
        'trade_perpetual_usd': Decimal(),
        'fee_usd': Decimal(),
        'refer_amount': Decimal(),
        'refer_usd': Decimal(),
        "new_referrer_count": 0,
        "invitee_count": 0,
        "total_invitee": 0,
        "refer_user_balance_usd": Decimal(),
    })
    for item in daily_refer_data:
        type_ = item.type.name
        refer_type_map[type_]['asset'] = item.asset
        refer_type_map[type_]['trade_usd'] += item.trade_usd
        refer_type_map[type_]['trade_spot_usd'] += item.trade_spot_usd
        refer_type_map[type_]['trade_perpetual_usd'] += item.trade_perpetual_usd
        refer_type_map[type_]['fee_usd'] += item.fee_usd
        refer_type_map[type_]['refer_amount'] += item.refer_amount
        refer_type_map[type_]['refer_usd'] += item.refer_usd
        refer_type_map[type_]['trade_user_list'].update(json.loads(item.trade_user_list))
        refer_type_map[type_]['refer_user_list'].update(json.loads(item.refer_user_list))
        refer_type_map[type_]['new_referrer_count'] += item.new_referrer_count
        refer_type_map[type_]['invitee_count'] += item.invitee_count
        refer_type_map[type_]['total_invitee'] = item.total_invitee
        refer_type_map[type_]['type_user_count'] = item.type_user_count
        refer_type_map[type_]['refer_user_balance_usd'] = item.refer_user_balance_usd

    normal_referrers = ReferralRepository.get_period_referrers(
        date_to_datetime(start_date), date_to_datetime(end_date),
        ReferralHistory.ReferralType.NORMAL, ReferralHistory.Status.VALID)

    today_ = today()
    active_rt_start_date = end_date_yes_day
    if active_rt_start_date >= today_:
        active_rt_start_date = today_ - datetime.timedelta(days=1)
    active_user_ids = get_report_active_user_ids_in_range(
        start_date=start_date,
        end_date=end_date_yes_day,
        realtime_start_date=active_rt_start_date,
        realtime_end_date=active_rt_start_date + datetime.timedelta(days=1),
    )

    market_makers = get_market_maker_ids()
    ambassador_ids = [i.user_id for i in Ambassador.query.filter(
        Ambassador.created_at < end_date,
        Ambassador.status == Ambassador.Status.VALID
    ).with_entities(Ambassador.user_id)]
    business_ambassador_ids = [i.user_id for i in BusinessAmbassador.query.filter(
        BusinessAmbassador.created_at < end_date,
        BusinessAmbassador.status == BusinessAmbassador.Status.VALID
    ).with_entities(BusinessAmbassador.user_id)]
    ambassador_ids.extend(business_ambassador_ids)
    tree_amb_ids = [i.user_id for i in TreeAmbassador.query.filter(
        TreeAmbassador.created_at < end_date,
        TreeAmbassador.status == TreeAmbassador.Status.VALID,
    ).with_entities(TreeAmbassador.user_id).all()]
    ambassador_ids.extend(tree_amb_ids)
    invitee_ee_ids = get_invitee_ee_ids(end_date, ambassador_ids, market_makers)
    refer_active_user_count = len(invitee_ee_ids & active_user_ids)

    for type_, data_ in refer_type_map.items():
        row: MonthlyReferTypeReport = MonthlyReferTypeReport.get_or_create(
            report_date=start_date,
            type=type_)
        if type_ == MonthlyReferTypeReport.Type.REFERRAL.name:
            row.referrer_count = len(normal_referrers)
        row.new_referrer_count = data_['new_referrer_count']
        row.invitee_count = data_['invitee_count']

        row.trade_count = len(data_['trade_user_list'])
        _new_trade_users = monthly_inc_trade_users & set(data_['trade_user_list'])
        row.new_trade_count = len(_new_trade_users)
        row.refer_count = len(data_['refer_user_list'])
        row.asset = data_['asset']
        row.type_user_count = data_['type_user_count']
        row.trade_usd = data_['trade_usd']
        row.trade_spot_usd = data_['trade_spot_usd']
        row.trade_perpetual_usd = data_['trade_perpetual_usd']
        row.fee_usd = data_['fee_usd']
        row.refer_amount = data_['refer_amount']
        row.refer_usd = data_['refer_usd']
        row.refer_rate = data_['refer_usd'] / data_['fee_usd'] if data_['fee_usd'] else 0
        row.total_invitee = data_['total_invitee']
        row.refer_user_balance_usd = data_['refer_user_balance_usd']
        row.refer_active_user_count = refer_active_user_count if type_ == MonthlyReferTypeReport.Type.AMBASSADOR.name else 0

        user_report = MonthlyUserReport.query.filter(
            MonthlyUserReport.report_date >= start_date,
            MonthlyUserReport.report_date < end_date
        ).first()
        if not user_report or user_report.increase_user == 0:
            row.invitee_percent = Decimal()
        else:
            row.invitee_percent = quantize_amount(row.invitee_count / user_report.increase_user, 4)
        if not user_report or user_report.active_trade_user == 0:
            row.trade_percent = Decimal()
        else:
            row.trade_percent = quantize_amount(row.trade_count / user_report.active_trade_user, 4)
        if not user_report or user_report.increase_trade_user == 0:
            row.new_trade_percent = Decimal()
        else:
            row.new_trade_percent = quantize_amount(row.new_trade_count / user_report.increase_trade_user, 4)

        db.session.add(row)
    db.session.commit()


@scheduled(crontab(minute="20", hour='4-6', day_of_month=1))
@lock_call()
def update_monthly_refer_report_schedule():
    cur_year_num = datetime.date.today().year
    cur_month_num = datetime.date.today().month
    cur_month = datetime.date(cur_year_num, cur_month_num, 1)

    start_month = get_monthly_report_date(MonthlyReferReport, DailyReferReport)

    if not start_month:
        return
    yesterday = cur_month - datetime.timedelta(days=1)
    if not check_report_date_exists(DailyReferReport, yesterday):
        return
    while start_month < cur_month:
        end_month = next_month(start_month.year, start_month.month)
        update_monthly_refer_report(start_month, end_month)
        start_month = end_month


@scheduled(crontab(minute="20", hour='5-7'))
@lock_call()
def update_monthly_ambassador_referral_report_schedule():
    cur_year_num = datetime.date.today().year
    cur_month_num = datetime.date.today().month
    cur_month = datetime.date(cur_year_num, cur_month_num, 1)

    last_record = MonthlyAmbassadorReferralReport.query.order_by(
        MonthlyAmbassadorReferralReport.report_date.desc()
    ).first()
    if last_record:
        start_month = datetime.date(last_record.report_date.year,
                                    last_record.report_date.month, 1)
    else:
        start_month = datetime.date(2020, 1, 1)

    if not start_month:
        return
    while start_month <= cur_month:
        end_month = next_month(start_month.year, start_month.month)
        update_monthly_ambassador_referral_report(start_month, end_month, force_update=True)
        start_month = end_month


@scheduled(crontab(minute="10", hour='2-4'))
@lock_call()
def update_daily_refer_type_report_schedule():
    today = datetime.datetime.utcnow().date()
    last_record = DailyReferTypeReport.query.order_by(
        DailyReferTypeReport.report_date.desc()
    ).first()
    if last_record:
        start_date = last_record.report_date + datetime.timedelta(days=1)
    else:
        start_date = datetime.date(2019, 6, 1)
    while start_date < today:
        end_date = start_date + datetime.timedelta(days=1)
        update_daily_refer_type_report(start_date, end_date)
        start_date += datetime.timedelta(days=1)


@scheduled(crontab(minute="20", hour='4-6', day_of_month=1))
@lock_call()
def update_monthly_refer_type_report_schedule():
    cur_year_num = datetime.date.today().year
    cur_month_num = datetime.date.today().month
    cur_month = datetime.date(cur_year_num, cur_month_num, 1)
    start_month = get_monthly_report_date(MonthlyReferTypeReport, DailyReferTypeReport)
    if not start_month:
        return
    yesterday = cur_month - datetime.timedelta(days=1)
    if not check_report_date_exists(DailyReferTypeReport, yesterday):
        return
    while start_month < cur_month:
        end_month = next_month(start_month.year, start_month.month)
        update_monthly_refer_type_report(start_month, end_month)
        start_month = end_month
