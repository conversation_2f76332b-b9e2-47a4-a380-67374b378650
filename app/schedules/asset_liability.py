#!/usr/bin/env python3
import json
import time
from collections import defaultdict
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict

from celery.schedules import crontab

from flask import current_app
from sqlalchemy import func, DECIMAL

from app.assets import list_all_assets
from app.business import (
    SPOT_ACCOUNT_ID, PerpetualLogDB, PriceManager,
    TradeLogDB, WalletClient, lock_call, send_alert_notice,
)
from app.business.market_maker import MarketMakerHelper
from app.caches.prices import InvisibleAssetsCache
from app.caches.admin import RealTimeAssetAdequacyCache
from app.models.spot import SystemAssetLiability, WalletAssetRewardHistory
from app.models.pledge import PledgePosition
from app.models.payment import PaymentAssetHedgingAmount
from app.models import PreTradingUserAsset
from app.common import CeleryQueues, PREVENT_RISK_CONTROL_REASONS
from app.common.onchain import Chain
from app.common.onchain import CHAIN_MONEY_MAPPING
from app.config import config
from app.models import (
    CreditBalance, Deposit,
    AssetInvestmentConfig, MarginInsurance, MarginLoanOrder,
    RedPacketHistory, Withdrawal, db, AssetPrice,
)
from app.models.staking import StakingAccount
from app.models.wallet import DepositAudit
from app.utils import (
    current_timestamp, datetime_to_time,
    route_module_to_celery_queue, scheduled,
    timestamp_to_datetime, batch_iter, celery_task, today_timestamp_utc,
)
from app.utils.date_ import convert_datetime, today
from app.utils.parser import JsonEncoder
from app.utils import spawn_greenlet


route_module_to_celery_queue(__name__, CeleryQueues.MARKET_MAKER)

interval = 1800


def get_margin_loans(end_time: datetime) -> Dict[str, Decimal]:
    margin_unflats = MarginLoanOrder.query.filter(
        MarginLoanOrder.status.in_(
            [MarginLoanOrder.StatusType.PASS,
             MarginLoanOrder.StatusType.ARREARS,
             MarginLoanOrder.StatusType.BURST]
        ),
        MarginLoanOrder.created_at < end_time
    ).with_entities(
        MarginLoanOrder.asset,
        func.sum(MarginLoanOrder.unflat_amount + MarginLoanOrder.interest_amount).label('total_unflat_amount')
    ).group_by(
        MarginLoanOrder.asset
    ).all()
    margin_unflat_dict = defaultdict(Decimal)
    for _v in margin_unflats:
        margin_unflat_dict[_v.asset] += _v.total_unflat_amount

    return margin_unflat_dict


def get_credit_loans(end_time: datetime) -> Dict[str, Decimal]:
    credit_unflats = CreditBalance.query.filter(
        CreditBalance.created_at < end_time
    ).with_entities(
        CreditBalance.asset,
        func.sum(CreditBalance.unflat_amount + CreditBalance.interest_amount).label('total_unflat_amount')
    ).group_by(
        CreditBalance.asset
    ).all()
    credit_unflat_dict = defaultdict(Decimal)
    for _v in credit_unflats:
        credit_unflat_dict[_v.asset] += _v.total_unflat_amount

    return credit_unflat_dict


def get_pre_asset_issues(end_time: datetime) -> Dict[str, Decimal]:
    rows = PreTradingUserAsset.query.filter(PreTradingUserAsset.issue_amount > 0,
                                            PreTradingUserAsset.created_at < end_time) \
        .group_by(PreTradingUserAsset.asset).with_entities(
            PreTradingUserAsset.asset,
            func.sum(PreTradingUserAsset.issue_amount)
    ).all()
    rs = defaultdict(Decimal)
    rs.update({k:v for k,v in rows})
    return rs


def get_pledge_loans(end_time):
    pledge_unflats = PledgePosition.query.filter(
        PledgePosition.status.in_(PledgePosition.ACTIVE_STATUSES),
        PledgePosition.created_at < end_time,
    ).with_entities(
        PledgePosition.loan_asset,
        func.sum(PledgePosition.debt_amount + PledgePosition.interest_amount).label('total_unflat_amount')
    ).group_by(
        PledgePosition.loan_asset,
    ).all()
    pledge_unflat_dict = defaultdict(Decimal)
    for _v in pledge_unflats:
        pledge_unflat_dict[_v.loan_asset] += _v.total_unflat_amount
    return pledge_unflat_dict


def get_abnormal_deposits(end_time: datetime) -> Dict[str, Decimal]:
    client = WalletClient()
    r = client.get_abnormal_deposit_balance(datetime_to_time(end_time))
    return defaultdict(Decimal, r)


def get_processing_withdrawals(end_time: datetime) -> tuple[dict, dict]:
    asset_processing_withdrawals= Withdrawal.query.filter(
        Withdrawal.status.in_([
            Withdrawal.Status.AUDIT_REQUIRED,
            Withdrawal.Status.AUDITED,
            Withdrawal.Status.FAILED,
            Withdrawal.Status.PROCESSING
        ]),
        Withdrawal.created_at < end_time
    ).with_entities(
        Withdrawal.asset,
        Withdrawal.type,
        func.sum(Withdrawal.amount).label('total_amount')
    ).group_by(
        Withdrawal.asset, Withdrawal.type
    ).all()
    asset_processing_withdrawal_dict = defaultdict(Decimal)
    asset_processing_transfer_dict = defaultdict(Decimal)
    for _r in asset_processing_withdrawals:
        if _r.type == Withdrawal.Type.ON_CHAIN:
            asset_processing_withdrawal_dict[_r.asset] = _r.total_amount or Decimal()
        else:
            asset_processing_transfer_dict[_r.asset] = _r.total_amount or Decimal()

    return asset_processing_withdrawal_dict, asset_processing_transfer_dict


def get_processing_deposits(end_time: datetime) -> Dict[str, Decimal]:
    asset_processing_deposits = Deposit.query.filter(
        Deposit.status.in_([Deposit.Status.PROCESSING, Deposit.Status.TOO_SMALL]),
        Deposit.created_at < end_time,
    ).with_entities(
        Deposit.asset,
        func.sum(Deposit.amount).label('total_amount')
    ).group_by(
        Deposit.asset
    )
    asset_processing_deposit_dict = defaultdict(Decimal)
    for _r in asset_processing_deposits:
        asset_processing_deposit_dict[_r.asset] = _r.total_amount or Decimal()

    return asset_processing_deposit_dict


def get_ungrabbed_red_packets(end_time: datetime) -> Dict[str, Decimal]:
    red_packet_records = RedPacketHistory.query.filter(
        RedPacketHistory.grab_at.is_(None),
        RedPacketHistory.status != RedPacketHistory.Status.EXPIRED,
        RedPacketHistory.created_at < end_time
    ).group_by(
        RedPacketHistory.asset
    ).with_entities(
        RedPacketHistory.asset,
        func.sum(RedPacketHistory.amount).label('total_amount')
    ).all()
    asset_ungrabbed_red_packet_dict = defaultdict(Decimal)
    for _r in red_packet_records:
        asset_ungrabbed_red_packet_dict[_r.asset] = _r.total_amount or Decimal()

    return asset_ungrabbed_red_packet_dict


def get_margin_insurance(end_time: datetime) -> Dict[str, Decimal]:
    margin_insurance_records = MarginInsurance.query.filter(
        MarginInsurance.created_at < end_time
    ).group_by(
        MarginInsurance.asset
    ).with_entities(
        MarginInsurance.asset,
        MarginInsurance.amount.label('amount'),
        MarginInsurance.real_amount.label('real_amount'),
    ).all()
    margin_insurance_dict = defaultdict(Decimal)
    for _r in margin_insurance_records:
        margin_insurance_dict[_r.asset] = _r.real_amount or Decimal()

    return margin_insurance_dict


def get_payment_asset_hedging_amounts(end_time: datetime) -> dict[str, Decimal]:
    """ 获取 币种当前垫付数目（要买回的数目） """
    rows = PaymentAssetHedgingAmount.query.filter(
        PaymentAssetHedgingAmount.created_at < end_time,
    ).with_entities(
        PaymentAssetHedgingAmount.asset,
        PaymentAssetHedgingAmount.amount,
    ).all()
    return dict(rows)


def wait_server_balance_snapshot(end_time: datetime) -> bool:
    ts = datetime_to_time(end_time)
    for _ in range(20):
        t1 = TradeLogDB.get_slice_history_timestamp(ts, interval=interval)
        t2 = PerpetualLogDB.get_slice_history_timestamp(ts, interval=interval)
        if t1 and t2:
            return True
        time.sleep(30)
    return False


def get_spot_balances(end_time: datetime) -> Dict[str, Dict[int, Decimal]]:
    slice_balance_table = TradeLogDB.slice_balance_table(datetime_to_time(end_time), interval=interval)
    user_balances = slice_balance_table.select(
        'asset', 'account',
        'SUM(`balance`) `balance`',
        group_by='asset, account'
    )
    asset_balance_dict = defaultdict(lambda: defaultdict(Decimal))
    for _d in user_balances:
        asset_balance_dict[_d[0]][_d[1]] = Decimal(_d[2])
    return asset_balance_dict


def get_perpetual_balances(end_time: datetime) -> Dict[str, Decimal]:
    perpetual_slice_balance_table = PerpetualLogDB.slice_balance_table(datetime_to_time(end_time), interval=interval)
    user_perpetual_balances = dict(perpetual_slice_balance_table.select(
        'asset',
        'SUM(`balance`) `balance`',
        group_by='asset'
    ))
    return defaultdict(Decimal, user_perpetual_balances)


def get_perpetual_insurance(end_time: datetime) -> Dict[str, Decimal]:
    rows = PerpetualLogDB.get_insurance_balance(datetime_to_time(end_time), interval=interval)
    return defaultdict(Decimal, rows)


def get_wallet_balances() -> Dict[str, Dict[str, Decimal]]:
    balances = WalletClient().get_account_balance()
    wallet_balance_dict = defaultdict(lambda : defaultdict(Decimal))
    for item in balances:
        asset = item['asset']
        for account, balance in item['balances'].items():
            wallet_balance_dict[asset][account] += Decimal(balance['total'])

    return wallet_balance_dict


def get_onchain_balances() -> dict[str, Decimal]:
    sol_usdt_address = CHAIN_MONEY_MAPPING[Chain.SOL]['USDT']['contract']
    erc20_usdt_address = CHAIN_MONEY_MAPPING[Chain.ERC20]['USDT']['contract']
    bsc_usdt_address = CHAIN_MONEY_MAPPING[Chain.BSC]['USDT']['contract']

    key_map = {
        'SOL': 'SOL',
        'ERC20': 'ETH',
        'BSC': 'BNB',
        f'SOL{sol_usdt_address}': 'USDT',
        f'ERC20{erc20_usdt_address}': 'USDT',
        f'BSC{bsc_usdt_address}': 'USDT',
    }

    data = defaultdict(Decimal)
    for item in WalletClient().get_onchain_asset_statistics():
        chain, identity = item['chain'], item['identity']
        key = f'{chain}{identity}'
        if key not in key_map:
            continue
        data[key_map[key]] += Decimal(item['amount'])
    return data


def get_staking_summary() -> Dict[str, Decimal]:
    wallet_assets = WalletClient().get_staking_assets()
    result = defaultdict(Decimal)
    for item in wallet_assets:
        asset = item['asset']
        chain = item['chain']
        try:
            staking_summary = WalletClient().get_staking_summary(asset, chain)
            result[(asset, 'reward')] = staking_summary['pending_withdrawal_reward']
            result[(asset, 'amount')] = staking_summary['staking_amount']
        except Exception:
            current_app.logger.error(f"asset liability get staking summary failed: {asset}")
            last_record = SystemAssetLiability.query.filter(
                SystemAssetLiability.asset == asset).order_by(SystemAssetLiability.id.desc()).first()
            if not last_record:
                raise
            result[(asset, 'reward')] = last_record.staking_pending_reward
            result[(asset, 'amount')] = last_record.staking_amount
    return result


def get_kyt_frozen() -> Dict[str, Decimal]:
    model = DepositAudit
    rows = model.query.with_entities(
        model.deposit_id
    ).filter(
        model.status.in_([
            model.Status.INFO_AUDIT_REQUIRED,
            model.Status.INFO_REQUIRED,
            model.Status.EXTRA_INFO_REQUIRED,
            model.Status.FREEZING,
            model.Status.AUDIT_REQUIRED,
        ]),
        model.type.in_(model.KYT_TYPES),
    ).all()
    ret = defaultdict(Decimal)
    deposit_ids = {row.deposit_id for row in rows}
    for chunk_ids in batch_iter(deposit_ids, 1000):
        rows = Deposit.query.with_entities(
            Deposit.asset,
            Deposit.amount,
        ).filter(
            Deposit.id.in_(chunk_ids)
        ).all()
        for row in rows:
            ret[row.asset] += row.amount
    return ret


def sum_latest_system_asset_liability():
    # 需要保证币种执行
    _record = SystemAssetLiability.query.order_by(SystemAssetLiability.id.desc()).first()
    _current_hour = convert_datetime(_record.created_at, "hour")
    result = {}
    assets = list_all_assets()
    records = SystemAssetLiability.query.order_by(SystemAssetLiability.id.desc()).limit(
        len(assets) * 2).all()

    ignore_asset = set()  # 一个asset中的所有chain都为DELISTING状态时才跳过检查
    for item in WalletClient().get_assets():
        ignore = True
        for chain in item['chains']:
            if not PREVENT_RISK_CONTROL_REASONS & set(chain['deposit_suspension_reasons']):
                ignore = False
                break
        if ignore:
            ignore_asset.add(item['code'])

    for row in records:
        if row.asset in ignore_asset:
            continue
        _hour = convert_datetime(row.created_at, "hour")
        if _hour == _current_hour and row.asset not in result:
            result[row.asset] = row
    q = AssetPrice.query.filter(
        AssetPrice.date == _current_hour
    ).with_entities(AssetPrice.asset, AssetPrice.price).all()
    prices = PriceManager.assets_to_usd()
    for v in q:
        prices[v.asset] = v.price
    save_asset = "ALL"
    record = SystemAssetLiability(asset=save_asset)
    fields = [v.name for v in SystemAssetLiability.__table__.columns
              if isinstance(v.type, DECIMAL)]
    for field in fields:
        value = sum([prices.get(_asset, Decimal()) * getattr(_row, field)
                     for _asset, _row in result.items()])

        setattr(record, field, value)
    db.session_add_and_commit(record)


def get_visible_assets():
    assets = list_all_assets()
    invisible_assets = InvisibleAssetsCache().read()
    return [x for x in assets if x not in invisible_assets]


@celery_task
@lock_call(with_args=True)
def update_single_asset_liability(asset: str):
    old_record = SystemAssetLiability.query.filter(SystemAssetLiability.asset == asset).order_by(SystemAssetLiability.id.desc()).first()

    balances = WalletClient().get_account_balance(asset=old_record.asset)
    wallet_balance_dict = defaultdict(Decimal)
    for item in balances:
        for account, balance in item['balances'].items():
            wallet_balance_dict[account] += Decimal(balance['total'])
    onchain_balance = get_onchain_balances().get(asset, Decimal())

    _now = current_timestamp(to_int=True)
    end_time = timestamp_to_datetime(_now - (_now % interval))
    record = SystemAssetLiability()
    record.asset = asset
    record.updated_at = end_time

    fields = [v.name for v in SystemAssetLiability.__table__.columns
              if isinstance(v.type, DECIMAL)]
    for field in fields:
        setattr(record, field, getattr(old_record, field))
    record.extra_data = SystemAssetLiability.build_extra_data_dict(old_record.extra_data)

    payment_hedging_amount = Decimal(record.extra_data.get('payment_hedging_amount', Decimal())) if record.extra_data else Decimal()

    # only update wallet balances
    record.deposit_wallet = wallet_balance_dict['DEPOSIT']
    record.hot_wallet = wallet_balance_dict['HOT']
    record.cold_wallet = wallet_balance_dict['COLD']
    record.onchain_wallet = onchain_balance

    record.sys_total = \
        record.cold_wallet + record.hot_wallet + record.deposit_wallet + record.onchain_wallet + \
        record.margin_unflat + record.credit_unflat + record.pledge_unflat + record.pre_asset_issue + \
        record.staking_pending_reward + record.staking_amount
    record.sys_debt = \
        record.spot + record.margin + record.investment + record.pledge + \
        record.perpetual + record.perpetual_insurance + \
        record.margin_insurance + record.except_deposit + \
        record.processing_deposit + record.processing_withdrawal + \
        record.processing_local_transfer + record.ungrabbed_red_packet - payment_hedging_amount

    record.sys_income = record.sys_total - record.sys_debt
    db.session.add(record)
    db.session.commit()


@scheduled(crontab(minute="0,30", hour='*/1'))
def update_system_asset_liability():
    from app.business.pledge.helper import is_pledge_account
    from app.schedules.system_notice import asset_liability_monitor_task

    _now = current_timestamp(to_int=True)
    end_time = timestamp_to_datetime(_now - (_now % interval))
    assets = get_visible_assets()
    # 这个最慢，异步执行
    task = spawn_greenlet(get_processing_withdrawals, end_time)

    wallet_balance_dict = get_wallet_balances()
    onchain_balance_dict = get_onchain_balances()

    margin_unflat_dict = get_margin_loans(end_time)
    credit_unflat_dict = get_credit_loans(end_time)
    pledge_unflat_dict = get_pledge_loans(end_time)
    pre_assset_issue_dict = get_pre_asset_issues(end_time)
    asset_except_deposit_dict = get_abnormal_deposits(end_time)
    asset_processing_deposit_dict = get_processing_deposits(end_time)

    asset_ungrabbed_red_packet_dict = get_ungrabbed_red_packets(end_time)
    margin_insurance_dict = get_margin_insurance(end_time)
    payment_hedging_amount_dict = get_payment_asset_hedging_amounts(end_time)

    staking_pending_reward_dict = get_staking_summary()
    kyt_frozen_dict = get_kyt_frozen()

    asset_processing_withdrawal_dict, asset_processing_local_transfer_dict = task.get()

    if not wait_server_balance_snapshot(end_time):
        current_app.logger.error('update_system_asset_liability failed: no balance snpashot')
        return

    spot_balance_dict = get_spot_balances(end_time)
    perpetual_balance_dict = get_perpetual_balances(end_time)
    perpetual_insurance_dict = get_perpetual_insurance(end_time)

    def update_one(asset):
        record = SystemAssetLiability()
        record.asset = asset
        record.updated_at = end_time

        record.deposit_wallet = wallet_balance_dict[asset]['DEPOSIT']
        record.hot_wallet = wallet_balance_dict[asset]['HOT']
        record.cold_wallet = wallet_balance_dict[asset]['COLD']
        record.onchain_wallet = onchain_balance_dict.get(asset, Decimal())

        record.margin_unflat = margin_unflat_dict[asset]
        record.credit_unflat = credit_unflat_dict[asset]
        record.pledge_unflat = pledge_unflat_dict[asset]
        record.pre_asset_issue = pre_assset_issue_dict[asset]

        record.except_deposit = asset_except_deposit_dict[asset]
        record.processing_deposit = asset_processing_deposit_dict[asset]
        record.processing_withdrawal = asset_processing_withdrawal_dict[asset]
        record.processing_local_transfer = asset_processing_local_transfer_dict[asset]
        record.ungrabbed_red_packet = asset_ungrabbed_red_packet_dict[asset]
        record.kyt_frozen = kyt_frozen_dict[asset]

        record.spot = spot_balance_dict[asset][SPOT_ACCOUNT_ID]
        record.investment = spot_balance_dict[asset][AssetInvestmentConfig.ACCOUNT_ID] \
            + spot_balance_dict[asset][StakingAccount.ACCOUNT_ID]
        record.margin = sum([value
            for account, value in spot_balance_dict[asset].items()
            if SPOT_ACCOUNT_ID < account < AssetInvestmentConfig.ACCOUNT_ID
        ])
        record.pledge = sum([
            value for account, value in spot_balance_dict[asset].items()
            if is_pledge_account(int(account))
        ])
        record.perpetual = perpetual_balance_dict[asset]

        record.margin_insurance = margin_insurance_dict[asset]
        record.perpetual_insurance = perpetual_insurance_dict[asset]
        record.staking_pending_reward = staking_pending_reward_dict.get((asset, 'reward'), 0)
        record.staking_amount = staking_pending_reward_dict.get((asset, 'amount'), 0)

        payment_hedging_amount = payment_hedging_amount_dict.get(asset, Decimal())
        extra_data = {
            'payment_hedging_amount': payment_hedging_amount,
        }
        record.extra_data = extra_data

        record.sys_total = \
            record.cold_wallet + record.hot_wallet + record.deposit_wallet + record.onchain_wallet + \
            record.margin_unflat + record.credit_unflat + record.pledge_unflat + record.pre_asset_issue + \
            record.staking_pending_reward + record.staking_amount
        record.sys_debt = \
            record.spot + record.margin + record.investment + record.pledge + \
            record.perpetual + record.perpetual_insurance + \
            record.margin_insurance + record.except_deposit + \
            record.processing_deposit + record.processing_withdrawal + \
            record.processing_local_transfer + record.ungrabbed_red_packet - payment_hedging_amount

        record.sys_income = record.sys_total - record.sys_debt

        return record

    for asset in assets:
        db.session.add(update_one(asset))
    db.session.commit()
    sum_latest_system_asset_liability()

    asset_liability_monitor_task.delay()


@scheduled(crontab(hour='0', minute='30'))
@lock_call()
def asset_wallet_reward_schedule():
    wallet_client = WalletClient()
    today_ts = today_timestamp_utc()
    start_ts = today_ts - 86400
    _report_date = today() - timedelta(days=1)
    data = wallet_client.get_wallet_assets_rewards(start_ts=start_ts, end_ts=today_ts)
    records = []
    prices = PriceManager.assets_to_usd()
    may_error_usd = Decimal('100000')
    may_error_data = []

    def check_may_error_data(_asset: str, _amount: Decimal) -> bool:
        price = prices.get(_asset, Decimal('0'))
        if price * _amount > may_error_usd or _amount < Decimal():
            may_error_data.append(
                dict(asset=_asset, amount=_amount, usd=price * _amount, start_ts=start_ts, end_ts=today_ts)
            )
            return True
        return False

    for k, v in data.items():
        _amount = Decimal(v)
        if _amount == Decimal():
            continue
        if check_may_error_data(k, _amount):
            continue
        records.append(
            WalletAssetRewardHistory(
                report_date=_report_date,
                asset=k,
                amount=_amount,
            )
        )
    records.append(WalletAssetRewardHistory.gen_finish_record(_report_date))
    db.session.add_all(records)
    db.session.commit()
    if may_error_data:
        send_alert_notice(
            f"部分利息币数据可能有问题，具体数据如下: {may_error_data}",
            config["ADMIN_CONTACTS"]["web_notice"],
        )


@scheduled(crontab(minute=10, hour='*/1'))
def update_asset_adequacy():
    """资金充足率统计"""
    wallet_balance_dict = get_wallet_balances()
    onchain_balance_dict = get_onchain_balances()
    staking_balance_dict = get_staking_summary()
    inner_maker_ids = MarketMakerHelper.list_inner_maker_ids()
    external_balance_dict = get_asset_balance_total(inner_maker_ids)
    _now = current_timestamp(to_int=True)
    end_time = timestamp_to_datetime(_now - (_now % 3600))
    margin_unflat_dict = get_margin_loans_exclude(inner_maker_ids, end_time)
    credit_unflat_dict = get_credit_loans_exclude(inner_maker_ids, end_time)
    pledge_unflat_dict = get_pledge_loans_exclude(inner_maker_ids, end_time)
    visible_assets = get_visible_assets()
    ret = []
    prices = PriceManager.assets_to_usd()
    for asset in visible_assets:
        ret.append({
            'asset': asset,
            'hot_wallet': wallet_balance_dict[asset]['HOT'],
            'cold_wallet': wallet_balance_dict[asset]['COLD'],
            'deposit_wallet': wallet_balance_dict[asset]['DEPOSIT'],
            'onchain_wallet': onchain_balance_dict.get(asset, Decimal()),
            'staking': staking_balance_dict[(asset, 'reward')] + staking_balance_dict[(asset ,'amount')],
            'margin_unflat': margin_unflat_dict[asset],
            'credit_unflat': credit_unflat_dict[asset],
            'pledge_unflat': pledge_unflat_dict[asset],
            'balance': external_balance_dict[asset],
            'balance_usd': external_balance_dict[asset] * prices.get(asset, 0),
        })
    cache = RealTimeAssetAdequacyCache()
    cache.hmset({
        'timestamp': _now,
        'data': json.dumps(ret, cls=JsonEncoder)
    })


def get_asset_balance_total(inner_maker_ids):
    """外部用户资产总额"""
    slice_time = TradeLogDB.get_slice_history_timestamp()
    perpetual_slice_time = PerpetualLogDB.get_slice_history_timestamp()
    # 总资产
    asset_balance = defaultdict(Decimal)
    for item in TradeLogDB.get_user_balances(slice_time):
        user_id, asset, account, balance = item
        if user_id in inner_maker_ids:
            continue
        asset_balance[asset] += balance
    for item in PerpetualLogDB.get_user_balances(perpetual_slice_time):
        user_id, asset, balance = item
        if user_id in inner_maker_ids:
            continue
        asset_balance[asset] += balance
    return asset_balance


def get_margin_loans_exclude(inner_maker_ids, end_time: datetime) -> Dict[str, Decimal]:
    model = MarginLoanOrder
    margin_unflats = model.query.filter(
        model.status.in_(
            [model.StatusType.PASS,
             model.StatusType.ARREARS,
             model.StatusType.BURST]
        ),
        model.created_at < end_time
    ).with_entities(
        model.user_id,
        model.asset,
        model.unflat_amount,
        model.interest_amount
    ).all()
    margin_unflat_dict = defaultdict(Decimal)
    for _v in margin_unflats:
        if _v.user_id in inner_maker_ids:
            continue
        margin_unflat_dict[_v.asset] += _v.unflat_amount + _v.interest_amount

    return margin_unflat_dict


def get_credit_loans_exclude(inner_maker_ids, end_time: datetime) -> Dict[str, Decimal]:
    model = CreditBalance
    credit_unflats = model.query.filter(
        model.created_at < end_time
    ).with_entities(
        model.user_id,
        model.asset,
        model.unflat_amount,
        model.interest_amount,
    ).all()
    credit_unflat_dict = defaultdict(Decimal)
    for _v in credit_unflats:
        if _v.user_id in inner_maker_ids:
            continue
        credit_unflat_dict[_v.asset] += _v.unflat_amount + _v.interest_amount

    return credit_unflat_dict


def get_pledge_loans_exclude(inner_maker_ids, end_time):
    model = PledgePosition
    pledge_unflats = model.query.filter(
        model.status.in_(model.ACTIVE_STATUSES),
        model.created_at < end_time,
    ).with_entities(
        model.user_id,
        model.loan_asset,
        model.debt_amount,
        model.interest_amount,
    ).all()
    pledge_unflat_dict = defaultdict(Decimal)
    for _v in pledge_unflats:
        if _v.user_id in inner_maker_ids:
            continue
        pledge_unflat_dict[_v.loan_asset] += _v.debt_amount + _v.interest_amount
    return pledge_unflat_dict
