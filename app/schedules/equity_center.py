# -*- coding: utf-8 -*-
from datetime import timedelta

from celery.schedules import crontab
from flask import current_app

from app import config
from app.business import lock_call
from app.business.alert import send_alert_notice
from app.business.equity_center.airdrop import send_user_airdrop_equity_task
from app.business.equity_center.cashback import (
    CashbackEquityHelper, CashbackTradeFeeCollector, CashbackSettleHelper, retry_cashback_equity_settle_transfer,
)
from app.business.equity_center.inv_increase import (
    IncreaseEquityHelper,
    IncreaseEquitySchedule,
)
from app.business.equity_center.send import create_user_equity_by_send_apply
from app.business.equity_center.helper import check_sys_balance_enough, EquitySettings, EquityCenterService, \
    EquityDailyConsumptionService
from app.business.equity_center.notice import clean_up_equity_notice_send_cache_task, send_equity_expiring_notice_task
from app.common import CeleryQueues
from app.models.equity_center import db, EquityType, EquitySendApply
from app.utils import (
    scheduled,
    route_module_to_celery_queue,
    now, amount_to_str,
)
from app.utils.date_ import convert_datetime, today

route_module_to_celery_queue(__name__, CeleryQueues.REWARD_CENTER)


@scheduled(crontab(hour="*/4", minute='25'))
@lock_call()
def cashback_manual_collect_trade_fee_schedule():
    """ 返现权益：手动补偿收集交易手续费 """
    now_ = now()
    end_time = now_ - timedelta(minutes=10)
    start_time = end_time - timedelta(days=1)
    c = CashbackTradeFeeCollector(start_time, end_time)
    c.new_equity_user_collect()


@scheduled(crontab(hour="0,23", minute='4'))
@lock_call()
def cashback_equity_update_biz_tag_schedule():
    """ 返现权益：更新标签 """
    CashbackEquityHelper.update_biz_tag()


@scheduled(crontab(minute="*/5"))
@lock_call()
def cashback_equity_expire_schedule():
    """ 返现权益：过期 """
    CashbackEquityHelper.update_equities_to_finished_status()


@scheduled(crontab(hour="*/1", minute='1'))
@lock_call()
def cashback_equity_settle_schedule():
    """ 返现权益：结算 """
    # 严格按交易顺序来返现，返现前补偿新权益用户上个小时的缺失手续费
    now_ = now()
    end_time = convert_datetime(now_ - timedelta(minutes=1), 'hour')
    start_time = end_time - timedelta(hours=1)
    c = CashbackTradeFeeCollector(start_time, end_time)
    try:
        c.new_equity_user_collect()
    except Exception as _e:
        db.session.rollback()
        current_app.logger.exception(f"new_equity_user_collect error: {_e!r}")

    helper = CashbackSettleHelper()
    helper.generate_settle_history()
    if EquitySettings.all_currency_transfer_enabled:
        helper.execute_cashback()


@scheduled(crontab(hour="*/1", minute='41'))
@lock_call()
def retry_cashback_equity_settle_transfer_schedule():
    """ 返现权益的划转重试 """
    if EquitySettings.all_currency_transfer_enabled:
        retry_cashback_equity_settle_transfer()


@scheduled(crontab(hour="4", minute='11'))
@lock_call()
def cashback_equity_update_settle_his_to_expired_schedule():
    """ 返现权益：已过期的结算记录更新（结算中->已过期） """
    CashbackSettleHelper.update_settle_his_to_expired()


@scheduled(crontab(hour="4", minute='21'))
@lock_call()
def cashback_equity_update_settle_fees_to_invalid_schedule():
    """ 返现权益：无效手续费记录更新 """
    CashbackSettleHelper.update_settle_fees_to_invalid()


@scheduled(crontab(hour="*/1", minute='11'))
@lock_call()
def airdrop_equity_send_schedule():
    """ 空投权益：发放 """
    send_user_airdrop_equity_task()


@scheduled(crontab(hour="*/1", minute='5'))
@lock_call()
def send_equity_notice_schedule():
    send_equity_expiring_notice_task.delay()


@scheduled(crontab(hour="4", minute='45'))
@lock_call()
def clean_up_equity_notice_send_cache_schedule():
    clean_up_equity_notice_send_cache_task.delay()


@scheduled(crontab(hour="*/1", minute='9'))
@lock_call()
def notice_equity_sys_balance_not_enough():
    asset_warning_configs = EquitySettings.get_balance_warning_configs()
    not_enough_assets = check_sys_balance_enough(asset_warning_configs)
    if not_enough_assets:
        content = "【奖励中心】账户余额不足，请尽快补充！\n"
        slack_users = EquitySettings.slack_warning_users
        for asset in not_enough_assets:
            content += (f"{asset}: 当前余额为 {amount_to_str(not_enough_assets[asset]['available'], 8)}  "
                        f"预警额度为 {amount_to_str(not_enough_assets[asset]['warning_amount'], 8)}\n")
        send_alert_notice(
            content,
            config["ADMIN_CONTACTS"]["equity_notice"],
            at=",".join(slack_users) if slack_users else None,
        )


@scheduled(crontab(minute="*/10"))
@lock_call()
def equity_send_apply_execute_send_schedule():
    """权益发放申请：执行发放"""
    send_pending_statuses = [
        EquitySendApply.Status.PASSED,
        EquitySendApply.Status.SENDING,
    ]
    apply_rows: list[EquitySendApply] = EquitySendApply.query.filter(
        EquitySendApply.status.in_(send_pending_statuses),
        EquitySendApply.send_at <= now(),
    ).with_entities(
        EquitySendApply.id,
        EquitySendApply.equity_type,
    ).order_by(
        EquitySendApply.id.asc(),
    ).all()
    has_airdrop = False
    for apply in apply_rows:
        try:
            create_user_equity_by_send_apply(apply.id)
            if apply.equity_type == EquityType.AIRDROP:
                has_airdrop = True
        except Exception as _e:
            db.session.rollback()
            current_app.logger.exception(f"create_user_equity_by_send_apply {apply.id} error: {_e!r}")

    if has_airdrop:
        EquityCenterService.async_send_airdrop_equity()
        
        
@scheduled(crontab(hour="*/1", minute='5'))
@lock_call()
def invest_increase_equity_expire_schedule():
    """理财加息权益：过期"""
    IncreaseEquityHelper.update_equities_to_finished_status()


@scheduled(crontab(minute="5,10,15", hour="*/1"))
@lock_call()
def invest_increase_equity_hourly_schedule():
    """理财小时任务调度"""
    IncreaseEquitySchedule().hour_interest_schedule()


@scheduled(crontab(minute="10,20", hour="0-3"))
@lock_call()
def invest_increase_equity_day_schedule():
    """理财日任务调度"""
    IncreaseEquitySchedule().day_interest_schedule()


@scheduled(crontab(minute="20,40", hour="0-3"))
@lock_call()
def invest_increase_equity_day_payout_schedule():
    """理财日利息发放调度"""
    IncreaseEquitySchedule().day_payout_schedule()


@scheduled(crontab(hour="1", minute='0'))
@lock_call()
def equity_daily_consumption_statistics_schedule():
    """发奖账户每日消耗统计"""
    today_ = today()
    last_date = EquityDailyConsumptionService.query_last_consumption_date()
    if not last_date:
        last_date = today_ - timedelta(days=1)

    while last_date < today_:
        try:
            EquityDailyConsumptionService.calculate_daily_consumption(last_date)
            last_date += timedelta(days=1)
        except Exception as e:
            db.session.rollback()
            current_app.logger.exception(f"equity_daily_consumption_statistics error: {e!r}")
