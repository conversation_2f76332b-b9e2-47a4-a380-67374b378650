# -*- coding: utf-8 -*-

import datetime
from datetime import timedelta
import io
import json
from collections import defaultdict
from decimal import Decimal
from typing import Dict

from celery.schedules import crontab
from dateutil.relativedelta import relativedelta
from sqlalchemy import func
from openpyxl import Workbook

from app import config
from app.business.market_maker import MarketMakerHelper
from app.business import (CeleryQueues, route_module_to_celery_queue, lock_call)
from app.business.alert import send_slack_message
from app.business.email import send_appraisal_email, send_ambassador_level_change_pre_notify_email
from app.business.summary import get_period_first_trade_user_set, get_period_trade_amount_mapping, get_trade_users, \
    get_period_spot_trade_mapping, get_period_perp_trade_mapping, \
    get_period_exchange_trade_mapping
from app.business.user_status import AmbassadorChangeType
from app.business.referral import ReferralBusiness, AmbassadorBusiness, AmbassadorAgentBusiness, ReferralRepository, TreeAmbHelper
from app.business.ambassador import Amb<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, AmbassadorPackageHelper
from app.business.bus_referral import BusRela<PERSON><PERSON>ser<PERSON><PERSON>ier, BusinessAmbassador<PERSON>ep<PERSON><PERSON>el<PERSON>, BusAmbAppraisalHelper
from app.business.statistic import get_main_user_balance_usd_map
from app.caches import AmbassadorRankCache
from app.models import (Ambassador, AppraisalHistory, Deposit,
                        MonthlyAmbassadorReport, ReferralAssetHistory, MonthlyAmbassadorAgentReport,
                        ReferralHistory, User, db,
                        AmbassadorAgent, AmbassadorAgentHistory,
                        TreeAmbassador, IndirectReferralAssetHistory, TreeAmbassadorStatistics,
                        AmbassadorStatistics, MonthlyAmbassadorAgentAssetReport,
                        DailyAmbassadorAgentAssetReport, UserStatusChangeHistory, AmbassadorApplication, SignOffUser,
                        ReferralAssetSummary, BusinessReferralAssetDetail,
                        BusinessAmbassadorStatistics, BusinessTeamStatistics,
                        MonthlyBusinessAmbassadorReferralReport,
                        BusinessUserStatistics, BusinessUserReferralAssetHistory, ReferralAssetDetail)
from app.models.broker import DailyBrokerUserAssetReport
from app.models.operation import Activity
from app.models.referral import AmbassadorPackageBatch, UserAmbassadorPackage, BusinessAmbassador
from app.utils import (amount_to_str, batch_iter, last_month, quantize_amount,
                       scheduled, upload_file, celery_task)
from app.utils.date_ import next_month, this_month, today, date_to_datetime, now

route_module_to_celery_queue(__name__, CeleryQueues.REPORT)


def _get_month_deals(report_date, user_ids):
    end_date = next_month(report_date.year, report_date.month)
    ret = get_period_trade_amount_mapping(report_date, end_date, user_ids)
    return set(ret.keys()), quantize_amount(sum(ret.values()), 2)


def update_monthly_ambassador_report(start_date, end_date, is_current_month):
    ambassadors = Ambassador.query.filter(Ambassador.status == Ambassador.Status.VALID).all()
    ambassador_dict = {i.user_id: i for i in ambassadors}
    ref_rows = ReferralHistory.query.filter(
        ReferralHistory.status == ReferralHistory.Status.VALID,
        ReferralHistory.effected_at < end_date,
    ).with_entities(ReferralHistory.referrer_id, ReferralHistory.referree_id, ReferralHistory.effected_at).all()
    market_makers = MarketMakerHelper.list_all_maker_ids(include_sub_account=False)
    market_makers = set(market_makers)

    delta_refs_dict = defaultdict(list)  # 当月新增的推荐人数
    refs = defaultdict(list)
    for user_id, ref_user_id, effected_at in ref_rows:
        if user_id in ambassador_dict:
            # 大使邀请不用判断大使/邀请关系生效时间
            # if effected_at >= ambassador_info.effected_at:
            refs[user_id].append(ref_user_id)
            if effected_at.date() >= start_date:
                delta_refs_dict[user_id].append(ref_user_id)

    all_ref_user_ids = set()
    for user_id, ref_user_ids in refs.items():
        all_ref_user_ids.update(set(ref_user_ids))
    all_ref_user_ids -= market_makers
    new_referral_trade_user_set = get_period_first_trade_user_set(start_date, end_date, all_ref_user_ids)
    delta_users = set()
    for user_id, ref_user_ids in refs.items():
        delta_users.update(set(ref_user_ids))
    delta_users -= market_makers
    range_mapping = get_period_trade_amount_mapping(start_date, end_date, delta_users)

    for ambassador in ambassadors:
        ref_user_ids = refs.get(ambassador.user_id)
        if not ref_user_ids:
            continue
        ref_amount = ReferralAssetHistory.query.filter(
            ReferralAssetHistory.date >= start_date,
            ReferralAssetHistory.date < end_date,
            ReferralAssetHistory.user_id == ambassador.user_id,
            ReferralAssetHistory.type == ReferralAssetHistory.Type.AMBASSADOR
        ).with_entities(func.sum(ReferralAssetHistory.amount)).scalar() or 0
        # 做市商交易量不算在考核内
        whitelist = [x for x in ref_user_ids if x not in market_makers]
        if not whitelist:
            deal_user_count, deal_amount = 0, Decimal()
        else:
            deal_user_ids, deal_amount = _get_month_deals(start_date, whitelist)
            deal_user_count = len(deal_user_ids)

        deposit_count = 0
        for chunk_ref_user_ids in batch_iter(ref_user_ids, 1000):
            chunk_deposit_count = Deposit.query.filter(
                Deposit.created_at >= start_date,
                Deposit.created_at < end_date,
                Deposit.user_id.in_(chunk_ref_user_ids)
            ).with_entities(func.count(Deposit.user_id.distinct())).scalar() or 0
            deposit_count += chunk_deposit_count

        delta_referral_trade_users = {ref_user for ref_user in whitelist if ref_user in new_referral_trade_user_set}
        delta_ref_users = delta_refs_dict[ambassador.user_id]
        delta_referral_amount = 0
        for user_id in delta_ref_users:
            delta_referral_amount += range_mapping.get(user_id, 0)
        report = MonthlyAmbassadorReport.get_or_create(report_date=start_date, user_id=ambassador.user_id)
        report.referral_count = len(ref_user_ids)
        report.delta_referral_count = len(delta_ref_users)
        report.delta_referral_amount = delta_referral_amount
        report.delta_referral_trade_count = len(delta_referral_trade_users)
        report.asset = ReferralBusiness.GIFT_ASSETS[ReferralAssetHistory.Type.AMBASSADOR]
        report.referral_amount = ref_amount
        report.deal_amount = deal_amount
        report.deal_user_count = deal_user_count
        report.deposit_rate = quantize_amount(deposit_count / len(ref_user_ids), 4)
        report.trade_rate = quantize_amount(deal_user_count / len(ref_user_ids), 4)
        if not report.level or is_current_month:
            report.level = ambassador.level
        db.session.add(report)
    db.session.commit()


@scheduled(crontab(minute=15, hour='2-4'))
@lock_call()
def update_monthly_ambassador_report_schedule():
    """大使月报"""
    _today = today()
    last = MonthlyAmbassadorReport.query.order_by(
        MonthlyAmbassadorReport.report_date.desc()
    ).first()
    if last:
        start = datetime.date(last.report_date.year, last.report_date.month, 1)
    else:
        start = datetime.date(_today.year, _today.month, 1)
    current = datetime.date(_today.year, _today.month, 1)
    while start <= current:
        end = next_month(start.year, start.month)
        update_monthly_ambassador_report(start, end, start == current)
        start = end


@scheduled(crontab(minute=25, hour='5', day_of_month='24'))
@lock_call()
def monthly_ambassador_appraisal_pre_notify_schedule():
    """大使月度考核晋降级预提醒触达(实际考核发生在下月 1日)"""

    def get_pre_data():
        ambassadors = Ambassador.query.filter(
            Ambassador.status == Ambassador.Status.VALID,
            Ambassador.type != Ambassador.Type.BUSINESS,
        ).all()
        reports = MonthlyAmbassadorReport.query.filter(
            MonthlyAmbassadorReport.report_date == report_date
        ).all()  # 月表每日累计更新
        deal_amount_dict = {}
        new_referral_trade_user_count_dict = {}
        for report in reports:
            deal_amount_dict.update({report.user_id: report.deal_amount})
            new_referral_trade_user_count_dict.update({report.user_id: report.delta_referral_trade_count})

        pre_down_appr_data, pre_up_appr_data = [], []
        for ambassador in ambassadors:
            deal_amount = deal_amount_dict.get(ambassador.user_id, 0)
            new_user_count = new_referral_trade_user_count_dict.get(ambassador.user_id, 0)
            cur_level = ambassador.level
            cur_appr_amount = ReferralBusiness.AMBASSADOR_LEVEL_DEAL_AMOUNT_MAP[cur_level]
            cur_appr_count = ReferralBusiness.AMBASSADOR_LEVEL_NEW_TRADE_COUNT_MAP[cur_level]
            if deal_amount < cur_appr_amount or new_user_count < cur_appr_count:
                if cur_level is Ambassador.Level.SILVER:
                    if deal_amount < cur_appr_amount and new_user_count < cur_appr_count:
                        pass
                    else:
                        continue
                pre_down_appr_data.append({
                    'user_id': ambassador.user_id,
                    'level': cur_level,
                    'cur_amount': amount_to_str(deal_amount, 2),
                    'cur_count': new_user_count,
                    'cur_delta_amount': amount_to_str(cur_appr_amount - deal_amount, 2),
                    'cur_delta_count': cur_appr_count - new_user_count,
                })
            if deal_amount > cur_appr_amount:
                if cur_level is Ambassador.Level.SILVER:
                    levels = [Ambassador.Level.DIAMOND, Ambassador.Level.GOLD]
                elif cur_level is Ambassador.Level.GOLD:
                    levels = [Ambassador.Level.DIAMOND]
                else:
                    levels = []
                for appr_level in levels:
                    appr_amount = ReferralBusiness.AMBASSADOR_LEVEL_DEAL_AMOUNT_MAP[appr_level]
                    if deal_amount >= appr_amount * Decimal('0.8'):
                        pre_up_appr_data.append({
                            'user_id': ambassador.user_id,
                            'level': cur_level,
                            'next_level': appr_level,
                            'cur_amount': amount_to_str(deal_amount, 2),
                            'cur_count': new_user_count,
                        })
                        break
        return pre_down_appr_data, pre_up_appr_data

    def get_user_data():
        user_ids = list(set(down_user_ids + up_user_ids))
        if not user_ids:
            return {}
        rows = User.query.with_entities(
            User.id,
            User.email
        ).filter(User.id.in_(user_ids)).all()
        return dict(rows)

    def get_tg_data():
        user_ids = list(set(down_user_ids + up_user_ids))
        if not user_ids:
            return {}
        model = AmbassadorApplication
        rows = model.query.with_entities(
            model.user_id,
            model.telegram,
        ).filter(
            model.user_id.in_(user_ids)
        ).all()
        return dict(rows)

    def get_ambassador_history_levels():
        if not up_user_ids:
            return {}
        mapping = {}
        model = UserStatusChangeHistory
        rows = model.query.with_entities(
            model.detail,
            model.user_id
        ).filter(
            model.type == model.Type.AMBASSADOR.name,
            model.user_id.in_(up_user_ids)
        ).all()
        levels = {e for e in Ambassador.Level}
        for row in rows:
            _levels = mapping.get(row.user_id, set())
            left = levels - _levels
            if not left:
                continue
            details = json.loads(row.detail)
            if new_level := details.get("new_level"):
                mapping.setdefault(row.user_id, set()).add(Ambassador.Level[new_level])
        return mapping

    def update_pre_data():
        for item in down_data:
            email = user_emails.get(item['user_id'], '-')
            tg_num = tg_data.get(item['user_id'], '-')
            item.update(email=email)
            item.update(tg_num=tg_num)

        for item in up_data:
            email = user_emails.get(item['user_id'], '-')
            tg_num = tg_data.get(item['user_id'], '-')
            history_lvs = ambassador_history_lvs.get(item['user_id'], set())
            get_lv = '是' if item['next_level'] in history_lvs else '否'
            item.update(email=email)
            item.update(tg_num=tg_num)
            item.update(get_lv=get_lv)

    def send_to_email():
        for item in down_data:
            send_ambassador_level_change_pre_notify_email.delay(
                user_id=item['user_id'],
                level=item['level'].name,
                cur_amount=item['cur_amount'],
                cur_delta_amount=item['cur_delta_amount'],
                cur_count=item['cur_count'],
                cur_delta_count=item['cur_delta_count'],
            )

    def send_to_slack():

        def _upload_file(headers, data):
            wb = Workbook()
            header_list = [x[1] for x in headers]
            field_list = [x[0] for x in headers]
            ws = wb.create_sheet()
            ws.append(header_list)
            for item in data:
                ws.append([item[field] for field in field_list])
            stream = io.BytesIO()
            wb.save(stream)
            stream.seek(0)
            return upload_file(stream, 'xlsx', ttl=60 * 60 * 24 * 7)

        hook_url = config['ADMIN_CONTACTS'].get('ambassador_pre_notice')
        if not hook_url:
            return
        if down_data:
            down_headers = [
                ('user_id', 'id'),
                ('email', '邮箱'),
                ('level', '当前等级'),
                ('cur_amount', '本月邀请用户累计交易量（USD）'),
                ('cur_delta_amount', '交易量距离保级差额（USD）'),
                ('cur_count', '本月邀请新交易用户数'),
                ('cur_delta_count', '交易用户数距离保级差额'),
                ('tg_num', '电报号'),
            ]
            data = []
            for item in down_data:
                _copy_item = item.copy()
                _copy_item['level'] = _copy_item['level'].value
                data.append(_copy_item)

            down_url = _upload_file(headers=down_headers, data=data)
            send_slack_message.delay(
                f'预降级大使的情况，推送表格链接：{down_url}',
                hook_url
            )

        if up_data:
            up_headers = [
                ('user_id', 'id'),
                ('email', '邮箱'),
                ('level', '当前等级'),
                ('next_level', '预晋级等级'),
                ('cur_amount', '本月邀请用户累计交易量（USD）'),
                ('cur_count', '本月邀请新交易用户数'),
                ('get_lv', '是否达到过预晋级等级'),
                ('tg_num', '电报号'),
            ]
            data = []
            for item in up_data:
                _copy_item = item.copy()
                _copy_item['level'] = _copy_item['level'].value
                _copy_item['next_level'] = _copy_item['next_level'].value
                data.append(_copy_item)
            up_url = _upload_file(headers=up_headers, data=data)
            send_slack_message.delay(
                f'预晋级大使的情况，推送表格链接：{up_url}',
                hook_url
            )

    _now = now()
    _today = today()
    report_date = _today.replace(day=1)  # 取当月 1日进行数据预测
    down_data, up_data = get_pre_data()
    down_user_ids = [d['user_id'] for d in down_data]
    up_user_ids = [d['user_id'] for d in up_data]

    user_emails = get_user_data()
    tg_data = get_tg_data()
    ambassador_history_lvs = get_ambassador_history_levels()
    update_pre_data()

    send_to_email()
    send_to_slack()


@scheduled(crontab(minute=25, hour='4-6', day_of_month='1-3'))
@lock_call()
def update_monthly_ambassador_appraisal_schedule():
    """大使月度考核"""
    _now = now()
    _today = today()
    report_date = last_month(_today.year, _today.month)
    last = AppraisalHistory.query.filter(
        AppraisalHistory.business_type == AppraisalHistory.BusinessType.AMBASSADOR,
        AppraisalHistory.report_date == report_date
    ).first()
    if last:
        return

    ambassadors = Ambassador.query.filter(
        Ambassador.status == Ambassador.Status.VALID,
        Ambassador.type != Ambassador.Type.BUSINESS,
    ).all()
    ambassador_old_level_map = {i.user_id: i.level for i in ambassadors}
    ambassador_change_type_map: Dict[Ambassador, AmbassadorChangeType] = {}

    reports = MonthlyAmbassadorReport.query.filter(
        MonthlyAmbassadorReport.report_date == report_date
    ).all()
    deal_amount_dict = {}
    deal_user_count_dict = {}
    new_referral_trade_user_count_dict = {}
    for report in reports:
        deal_amount_dict.update({report.user_id: report.deal_amount})
        deal_user_count_dict.update({report.user_id: report.deal_user_count})
        new_referral_trade_user_count_dict.update({report.user_id: report.delta_referral_trade_count})

    appr_rows = []
    invalid_ambassadors = []
    for ambassador in ambassadors:
        # 按 <被邀请人上个月累计交易量> 考核
        deal_amount = deal_amount_dict.get(ambassador.user_id, 0)
        trade_user_count = new_referral_trade_user_count_dict.get(ambassador.user_id, 0)
        appraisal_level, ok = ReferralBusiness.get_ambassador_appraisal_level(deal_amount, trade_user_count)
        deal_user_count = deal_user_count_dict.get(ambassador.user_id, 0)
        old_level = ambassador.level
        ambassador.appraisal_level = appraisal_level
        # 计算新等级
        new_level = ambassador.level

        if not ok and not (ambassador.expired_time and ambassador.expired_time > date_to_datetime(_today)):
            result = AppraisalHistory.ResultStatus.NOT_LEAST_LEVEL
        else:
            cmp = ReferralBusiness.compare_ambassador_level(new_level.value, old_level.value)
            if cmp < 0:
                result = AppraisalHistory.ResultStatus.LEVEL_DOWN
                ambassador_change_type_map[ambassador] = AmbassadorChangeType.LEVEL_CHANGE
            elif cmp > 0:
                result = AppraisalHistory.ResultStatus.LEVEL_UP
                ambassador_change_type_map[ambassador] = AmbassadorChangeType.LEVEL_CHANGE
            else:
                result = AppraisalHistory.ResultStatus.NOT_CHANGE

        real_result = AppraisalHistory.ResultStatus.NOT_LEAST_LEVEL if not ok else result
        # 满足月中成为大使，使考核满足三个月的场景
        max_buffer_month = 4
        month_count = _get_buffer_protection_month(ambassador.user_id,
                                                   report_date - relativedelta(months=max_buffer_month-1),
                                                   report_date - relativedelta(months=1),
                                                   AppraisalHistory.BusinessType.AMBASSADOR)
        if result == AppraisalHistory.ResultStatus.NOT_LEAST_LEVEL:
            month_count += 1
        else:
            month_count = 0
        if month_count >= max_buffer_month:
            invalid_ambassadors.append(ambassador)
            ambassador_change_type_map[ambassador] = AmbassadorChangeType.REMOVE
        row = AppraisalHistory(
            user_id=ambassador.user_id,
            business_type=AppraisalHistory.BusinessType.AMBASSADOR,
            report_date=report_date,
            result_status=result,
            result=json.dumps(dict(
                old_level=Ambassador.LEVEL_NAMES[old_level],
                new_level=Ambassador.LEVEL_NAMES[new_level],
                month_count=month_count,
                deal_amount=amount_to_str(deal_amount),
                deal_user_count=deal_user_count,
                real_result=real_result.name,
            ))
        )
        appr_rows.append(row)

    for rows in batch_iter(appr_rows, 1000):
        db.session.bulk_save_objects(rows)
    db.session.commit()
    # 失效大使身份
    for invalid_ambassador in invalid_ambassadors:
        AmbassadorBusiness.make_ambassador_invalid(invalid_ambassador, add_log=False)

    # 大使变更记录（考核逻辑只有2种）
    for amb, change_type in ambassador_change_type_map.items():
        # 等级变动记录new_level、old_level ， 删除大使记录old_level
        ReferralBusiness.add_ambassador_change_history(
            user_id=amb.user_id,
            change_type=change_type,
            new_level=amb.level,
            old_level=ambassador_old_level_map.get(amb.user_id),
            new_type=amb.type,
            old_type=amb.type,  # 考核不会改变类型
            expired_time=amb.expired_time,
            create_time=_now,
            commit=False,
        )
    db.session.commit()

    # bulk_save_objects donot return id.
    rows = AppraisalHistory.query.filter(
        AppraisalHistory.report_date == report_date,
        AppraisalHistory.business_type == AppraisalHistory.BusinessType.AMBASSADOR
    ).with_entities(
        AppraisalHistory.user_id, AppraisalHistory.id
    ).all()
    for user_id, record_id in rows:
        send_appraisal_email.delay(user_id, record_id)


@scheduled(crontab(minute='30', hour='4'))
@lock_call()
def bus_amb_daily_appraisal_schedule():
    """ 商务大使每天预淘汰任务 """
    BusAmbAppraisalHelper.execute()


def _get_buffer_protection_month(user_id, from_report_date, end_report_date, appraisal_type):
        # after insert record.
        q = AppraisalHistory.query.filter(
            AppraisalHistory.user_id == user_id,
            AppraisalHistory.business_type == appraisal_type,
            AppraisalHistory.report_date >= from_report_date,
            AppraisalHistory.report_date <= end_report_date,
        ).with_entities(AppraisalHistory.result_status).order_by(
            AppraisalHistory.report_date.asc()
        )
        result = []
        for r in q:
            if r.result_status == AppraisalHistory.ResultStatus.NOT_LEAST_LEVEL:
                result.append(True)
            else:
                result.append(False)
        count = 0
        for e in result:
            if e is False:
                count = 0
            else:
                count += 1
        return count


@scheduled(crontab(minute=35, hour=1))
@lock_call()
def update_ambassador_rank_schedule():
    """大使邀请排行"""
    refs = ReferralHistory.query.group_by(
        ReferralHistory.referrer_id
    ).with_entities(
        ReferralHistory.referrer_id,
        func.count('*')
    ).all()
    ambassadors = Ambassador.query.filter(
        Ambassador.status == Ambassador.Status.VALID
    ).with_entities(Ambassador.user_id).all()
    exclude_amb_user_ids = [656367]  # 某些大使不想展示在`大使推广排行榜页面`
    ambassadors = {x for x, in ambassadors if x not in exclude_amb_user_ids}

    result = []
    rank = 1
    refs.sort(key=lambda x: x[1], reverse=True)
    for user_id, count in refs:
        if user_id in ambassadors:
            result.append(dict(
                user_id=user_id,
                referral_count=count,
                rank=rank
            ))
            rank += 1
            if rank == 6:
                break
    AmbassadorRankCache().set(json.dumps(result))


@scheduled(crontab(minute="15", hour="0-2"))
@lock_call()
def update_ambassador_statistics_info():
    """ 更新大使纬度的统计信息 """
    from app.schedules.reports.utils import get_report_active_user_ids_in_range

    today_ = today()
    last_update = AmbassadorStatistics.query.with_entities(func.max(AmbassadorStatistics.last_update_time)).first()
    if last_update and last_update[0] and last_update[0].date() >= today_:
        return

    ambassador_list = Ambassador.query.filter(Ambassador.status == Ambassador.Status.VALID).all()
    ambassador_user_ids = [i.user_id for i in ambassador_list]
    ambassador_user_ids.sort()

    off_model = SignOffUser
    chunk_size = 100
    total_refer_user_ids_dict = defaultdict(set)  # 大使总邀请人数
    valid_refer_user_id_dict = defaultdict(set)  # 大使当前有效邀请人数
    history_valid_refer_deal_user_id_dict = defaultdict(set)  # 大使历史有效邀请交易人数
    for chunk_amb_user_ids in batch_iter(ambassador_user_ids, chunk_size):
        ref_his_list = ReferralHistory.query.filter(ReferralHistory.referrer_id.in_(chunk_amb_user_ids)).all()
        rows = off_model.query.filter(
            off_model.user_id.in_([i.referree_id for i in ref_his_list])
        ).with_entities(off_model.user_id)
        off_user_ids = {i.user_id for i in rows}
        for row in ref_his_list:
            referree_id = row.referree_id
            # 产品要求去除已经注销的用户
            if referree_id in off_user_ids:
                continue
            amb_user_id = row.referrer_id
            if row.referral_type and row.referral_type is ReferralHistory.ReferralType.AMBASSADOR:
                total_refer_user_ids_dict[amb_user_id].add(referree_id)
                # 大使邀请只判断状态, 不用判断大使/邀请关系生效时间
                if row.status == ReferralHistory.Status.VALID:
                    valid_refer_user_id_dict[amb_user_id].add(referree_id)

        referral_model = ReferralAssetSummary
        referral_rows = referral_model.query.with_entities(
            referral_model.referree_id,
            referral_model.user_id
        ).filter(
            referral_model.user_id.in_(chunk_amb_user_ids),
            referral_model.type == ReferralAssetHistory.Type.AMBASSADOR,
        ).all()
        for row in referral_rows:
            if row.referree_id in off_user_ids:
                continue
            history_valid_refer_deal_user_id_dict[row.user_id].add(row.referree_id)
            total_refer_user_ids_dict[row.user_id].add(row.referree_id)
    # 查询是否交易过
    chunk_size = 1000
    all_referree_ids = [i for referrees in valid_refer_user_id_dict.values() for i in referrees]
    deal_referree_id_set = set()
    for chunk_refferee_ids in batch_iter(all_referree_ids, chunk_size):
        trade_users = get_trade_users(chunk_refferee_ids)
        deal_referree_id_set.update(trade_users)

    main_user_balance_usd_map = get_main_user_balance_usd_map(int(date_to_datetime(today_).timestamp()))
    active_user_ids = get_report_active_user_ids_in_range(
        start_date=today_ - timedelta(days=1),
        end_date=today_,
        realtime_end_date=today_,
    )

    new_statistics_list = []
    amb_statistics_map = {i.user_id: i for i in AmbassadorStatistics.query.all()}
    for amb_use_id in ambassador_user_ids:
        if not (statistics_info := amb_statistics_map.get(amb_use_id)):
            statistics_info = AmbassadorStatistics(
                user_id=amb_use_id,
                refer_count=0,
                refer_deal_count=0,
                cur_valid_refer_count=0,
                last_update_time=today_,
            )
            new_statistics_list.append(statistics_info)

        total_ref_ee_ids = total_refer_user_ids_dict[amb_use_id]
        statistics_info.refer_count = len(total_ref_ee_ids)
        valid_refer_user_ids = valid_refer_user_id_dict[amb_use_id]
        statistics_info.cur_valid_refer_count = len(valid_refer_user_ids)
        valid_deal_user_ids = [i for i in valid_refer_user_ids if i in deal_referree_id_set]
        statistics_info.refer_deal_count = len(valid_deal_user_ids)
        statistics_info.refer_total_deal_count = len(history_valid_refer_deal_user_id_dict[amb_use_id])
        statistics_info.refer_user_balance_usd = quantize_amount(sum([main_user_balance_usd_map[i] for i in total_ref_ee_ids]), 2)
        statistics_info.cur_refer_user_balance_usd = quantize_amount(sum([main_user_balance_usd_map[i] for i in valid_refer_user_ids]), 2)
        statistics_info.refer_active_user_count = len(active_user_ids & total_ref_ee_ids)
        statistics_info.cur_refer_active_user_count = len(active_user_ids & valid_refer_user_ids)
        statistics_info.last_update_time = today_

    #
    if new_statistics_list:
        db.session.add_all(new_statistics_list)
    db.session.commit()


@scheduled(crontab(minute="23", hour="0-2"))
@lock_call()
def update_tree_ambassador_statistics_info(force_update: bool = False):
    """ 更新Tree大使的统计信息 """
    from app.schedules.reports.utils import get_report_active_user_ids_in_range

    today_ = today()
    if not force_update:
        last_update = TreeAmbassadorStatistics.query.with_entities(func.max(TreeAmbassadorStatistics.last_update_time)).first()
        if last_update and last_update[0] and last_update[0].date() >= today_:
            return

    tree_amb_rows = TreeAmbassador.query.filter(
        TreeAmbassador.status == TreeAmbassador.Status.VALID,
    ).with_entities(
        TreeAmbassador.user_id,
        TreeAmbassador.effected_at,
    ).all()
    tree_amb_map = {i.user_id: i for i in tree_amb_rows}
    tree_amb_ids = set(tree_amb_map)

    off_model = SignOffUser
    chunk_size = 2000
    eff_refer_user_id_dict = defaultdict(set)  # 大使当前有效邀请人数
    ee_er_id_map = {}
    for chunk_amb_user_ids in batch_iter(tree_amb_ids, chunk_size):
        ref_his_list = ReferralHistory.query.filter(
            ReferralHistory.referrer_id.in_(chunk_amb_user_ids),
        ).with_entities(
            ReferralHistory.referree_id,
            ReferralHistory.referrer_id,
            ReferralHistory.status,
        ).all()
        rows = off_model.query.filter(
            off_model.user_id.in_([i.referree_id for i in ref_his_list])
        ).with_entities(off_model.user_id)
        off_user_ids = {i.user_id for i in rows}
        for row in ref_his_list:
            referree_id = row.referree_id
            if referree_id in off_user_ids:  # 产品要求去除已经注销的用户
                continue
            amb_user_id = row.referrer_id
            row: ReferralHistory
            if row.status == ReferralHistory.Status.VALID:
                eff_refer_user_id_dict[amb_user_id].add(referree_id)
                ee_er_id_map[referree_id] = amb_user_id

    # 查询是否交易过
    chunk_size = 1000
    all_referree_ids = {i for ee in eff_refer_user_id_dict.values() for i in ee}
    deal_referree_id_set = set()
    for chunk_refferee_ids in batch_iter(all_referree_ids, chunk_size):
        trade_users = get_trade_users(chunk_refferee_ids)
        deal_referree_id_set.update(trade_users)
    market_makers = MarketMakerHelper.list_all_maker_ids(include_sub_account=False)
    market_makers = set(market_makers)
    trade_ids = set()
    for v in eff_refer_user_id_dict.values():
        trade_ids.update(v)

    trade_query_ee_ids = [x for x in trade_ids if x not in market_makers]
    start_date = min(i.effected_at.date() for i in tree_amb_rows)
    spot_trade_map = get_period_spot_trade_mapping(start_date, today_, trade_query_ee_ids)
    perp_trade_map = get_period_perp_trade_mapping(start_date, today_, trade_query_ee_ids)
    exchange_trade_map = get_period_exchange_trade_mapping(start_date, today_, trade_query_ee_ids)

    main_user_balance_usd_map = get_main_user_balance_usd_map(int(date_to_datetime(today_).timestamp()))
    active_user_ids = get_report_active_user_ids_in_range(
        start_date=today_ - timedelta(days=1),
        end_date=today_,
        realtime_end_date=today_,
    )

    usdt = "USDT"
    cur_month = today_.replace(day=1)

    new_statistics_list = []
    amb_statistics_map = {i.user_id: i for i in TreeAmbassadorStatistics.query.all()}
    _row_count = 0
    for amb_use_id, amb in tree_amb_map.items():
        if not (st_row := amb_statistics_map.get(amb_use_id)):
            st_row = TreeAmbassadorStatistics(
                user_id=amb_use_id,
            )
            new_statistics_list.append(st_row)

        effect_ref_user_ids = eff_refer_user_id_dict[amb_use_id]
        valid_deal_user_ids = [i for i in effect_ref_user_ids if i in deal_referree_id_set]
        st_row.effect_refer_count = len(effect_ref_user_ids)
        st_row.effect_refer_trade_count = len(valid_deal_user_ids)
        st_row.effect_refer_user_balance_usd = quantize_amount(sum([main_user_balance_usd_map[i] for i in effect_ref_user_ids]), 2)
        st_row.effect_refer_active_user_count = len(active_user_ids & effect_ref_user_ids)

        # 返佣数
        model = ReferralAssetHistory
        ref_amount_rows = model.query.filter(
            model.user_id == amb_use_id,
            model.asset == usdt,
        ).with_entities(
            model.date,
            model.amount,
        ).all()
        total_refer_amount = month_refer_amount = 0
        for r in ref_amount_rows:
            total_refer_amount += r.amount
            if today_ > r.date >= cur_month:
                month_refer_amount += r.amount

        # 减去给自己的返佣
        detail_model = ReferralAssetDetail
        self_amount_rows = detail_model.query.filter(
            detail_model.user_id == amb_use_id,
            detail_model.referree_id == amb_use_id,
            detail_model.asset == usdt,
        ).with_entities(
            detail_model.date,
            detail_model.spot_amount,
            detail_model.perpetual_amount,
        ).all()
        total_self_amount = month_self_amount = 0
        for r in self_amount_rows:
            total_self_amount += (r.spot_amount + r.perpetual_amount)
            if today_ > r.date >= cur_month:
                month_self_amount += (r.spot_amount + r.perpetual_amount)
        st_row.total_refer_amount = total_refer_amount - total_self_amount
        st_row.month_refer_amount = month_refer_amount - month_self_amount

        # 非直客返佣数
        ind_ref_model = IndirectReferralAssetHistory
        ind_ref_amount_rows = ind_ref_model.query.filter(
            ind_ref_model.user_id == amb_use_id,
            ind_ref_model.asset == usdt,
        ).with_entities(
            ind_ref_model.date,
            ind_ref_model.amount,
        ).all()
        total_ind_refer_amount = month_ind_refer_amount = 0
        for r in ind_ref_amount_rows:
            total_ind_refer_amount += r.amount
            if today_ > r.date >= cur_month:
                month_ind_refer_amount += r.amount
        st_row.total_indirect_refer_amount = total_ind_refer_amount
        st_row.month_indirect_refer_amount = month_ind_refer_amount

        ee_ids = eff_refer_user_id_dict[amb_use_id]
        spot_usd, perp_usd = Decimal(), Decimal()
        for ee in ee_ids:
            _e_spot_usd = spot_trade_map.get(ee, Decimal()) + exchange_trade_map.get(ee, Decimal())
            _e_per_usd = perp_trade_map.get(ee, Decimal())
            spot_usd += _e_spot_usd
            perp_usd += _e_per_usd

        st_row.effect_refer_spot_amount = quantize_amount(spot_usd, 2)
        st_row.effect_refer_perpetual_amount = quantize_amount(perp_usd, 2)
        st_row.effect_refer_trade_amount = st_row.effect_refer_spot_amount + st_row.effect_refer_perpetual_amount
        st_row.last_update_time = today_

        _row_count += 1
        if _row_count % 5000 == 0:
            db.session.flush()

    if new_statistics_list:
        db.session.add_all(new_statistics_list)
    db.session.commit()


@scheduled(crontab(minute="19", hour="3-4"))
@lock_call()
def update_bus_ambassador_statistics_info(force_update: bool = False):
    """ 更新商务大使纬度的统计信息 """
    from app.schedules.reports.utils import get_report_active_user_ids_in_range

    today_ = today()
    st_model = BusinessAmbassadorStatistics
    if not force_update:
        last_update = st_model.query.with_entities(func.max(st_model.last_update_time)).first()
        if last_update and last_update[0] and last_update[0].date() >= today_:
            return

    bus_amb_list = BusRelationUserQuerier.get_all_valid_bus_ambassadors()
    bus_amb_dict = {i.user_id: i for i in bus_amb_list}
    bus_amb_user_ids = list(bus_amb_dict)
    bus_amb_user_ids.sort()

    nor_amb_rows = Ambassador.query.with_entities(Ambassador.user_id, Ambassador.created_at).all()
    nor_amb_map = {i.user_id: i for i in nor_amb_rows}
    bus_earliest_eff_map = {}  # 商务大使最早生效时间
    for amb_id in bus_amb_user_ids:
        _eff_times = [bus_amb_dict[amb_id].effected_at]
        if amb_id in nor_amb_map:
            _nor_amb = nor_amb_map[amb_id]
            _eff_times.append(_nor_amb.created_at)  # 普通大使可能重复生效，取created_at
        bus_earliest_eff_map[amb_id] = min(_eff_times)

    off_model = SignOffUser
    chunk_size = 100
    eff_refer_user_id_dict = defaultdict(set)  # 大使当前有效邀请人数
    new_refer_user_id_dict = defaultdict(set)  # 本次成为商务大使后的邀请人数
    amb_refer_user_id_dict = defaultdict(set)  # 之前做大使和本次做商务大使期间的邀请人数
    ee_er_id_map = {}
    for chunk_amb_user_ids in batch_iter(bus_amb_user_ids, chunk_size):
        ref_his_list = ReferralHistory.query.filter(ReferralHistory.referrer_id.in_(chunk_amb_user_ids)).all()
        rows = off_model.query.filter(
            off_model.user_id.in_([i.referree_id for i in ref_his_list])
        ).with_entities(off_model.user_id)
        off_user_ids = {i.user_id for i in rows}
        for row in ref_his_list:
            referree_id = row.referree_id
            if referree_id in off_user_ids:  # 产品要求去除已经注销的用户
                continue
            amb_user_id = row.referrer_id
            row: ReferralHistory
            # 只要valid则为有效邀请，不判断生效时间
            if row.status == ReferralHistory.Status.VALID:
                eff_refer_user_id_dict[amb_user_id].add(referree_id)
                ee_er_id_map[referree_id] = amb_user_id
                amb_info = bus_amb_dict[amb_user_id]
                if row.effected_at >= amb_info.effected_at:
                    new_refer_user_id_dict[amb_user_id].add(referree_id)
                if row.effected_at >= bus_earliest_eff_map[amb_user_id]:
                    amb_refer_user_id_dict[amb_user_id].add(referree_id)

    # 查询是否交易过
    chunk_size = 1000
    all_referree_ids = {i for ee in eff_refer_user_id_dict.values() for i in ee}
    deal_referree_id_set = set()
    for chunk_refferee_ids in batch_iter(all_referree_ids, chunk_size):
        trade_users = get_trade_users(chunk_refferee_ids)
        deal_referree_id_set.update(trade_users)
    market_makers = MarketMakerHelper.list_all_maker_ids(include_sub_account=False)
    market_makers = set(market_makers)
    trade_ids = set()
    for v in eff_refer_user_id_dict.values():
        trade_ids.update(v)
    whitelist = [x for x in trade_ids if x not in market_makers]
    start_date = min(i.effected_at.date() for i in bus_amb_list)
    spot_trade_map = get_period_spot_trade_mapping(start_date, today_, whitelist)
    perp_trade_map = get_period_perp_trade_mapping(start_date, today_, whitelist)
    exchange_trade_map = get_period_exchange_trade_mapping(start_date, today_, whitelist)

    this_month = today_.replace(day=1)
    if this_month == start_date:
        this_month_spot_trade_map = spot_trade_map
        this_month_perp_trade_map = perp_trade_map
        this_month_exchange_trade_map = exchange_trade_map
    else:
        new_trade_ids = set()
        for v in new_refer_user_id_dict.values():
            new_trade_ids.update(v)
        new_ref_trade_user_ids = [x for x in new_trade_ids if x not in market_makers]
        this_month_spot_trade_map = get_period_spot_trade_mapping(this_month, today_, new_ref_trade_user_ids)
        this_month_perp_trade_map = get_period_perp_trade_mapping(this_month, today_, new_ref_trade_user_ids)
        this_month_exchange_trade_map = get_period_exchange_trade_mapping(this_month, today_, new_ref_trade_user_ids)

    bro_amb_ids = [i.user_id for i in bus_amb_list if i.is_broker_amb]
    ee_bro_trades_map = defaultdict(lambda: defaultdict(dict))
    bro_ee_trades_map = defaultdict(lambda: defaultdict(dict))
    for ch_bro_ids in batch_iter(bro_amb_ids, 500):
        ch_bro_data = DailyBrokerUserAssetReport.query.filter(
            DailyBrokerUserAssetReport.broker_user_id.in_(ch_bro_ids),
            DailyBrokerUserAssetReport.date >= start_date,
        ).group_by(
            DailyBrokerUserAssetReport.broker_user_id,
            DailyBrokerUserAssetReport.user_id,
            DailyBrokerUserAssetReport.date,
        ).with_entities(
            DailyBrokerUserAssetReport.broker_user_id,
            DailyBrokerUserAssetReport.user_id,
            DailyBrokerUserAssetReport.date,
            func.sum(DailyBrokerUserAssetReport.spot_trade_usd).label("spot_trade_usd"),
            func.sum(DailyBrokerUserAssetReport.perpetual_trade_usd).label("perpetual_trade_usd"),
        ).all()
        for _d in ch_bro_data:
            ee_bro_trades_map[_d.user_id][_d.date][_d.broker_user_id] = [_d.spot_trade_usd, _d.perpetual_trade_usd]
            bro_ee_trades_map[_d.broker_user_id][_d.date][_d.user_id] = [_d.spot_trade_usd, _d.perpetual_trade_usd]

    main_user_balance_usd_map = get_main_user_balance_usd_map(int(date_to_datetime(today_).timestamp()))
    active_user_ids = get_report_active_user_ids_in_range(
        start_date=today_ - timedelta(days=1),
        end_date=today_,
        realtime_end_date=today_,
    )

    new_statistics_list = []
    amb_statistics_map = {i.user_id: i for i in st_model.query.all()}
    for amb_use_id, amb in bus_amb_dict.items():
        if not (st_row := amb_statistics_map.get(amb_use_id)):
            st_row = st_model(
                user_id=amb_use_id,
                effect_refer_count=0,
                effect_refer_trade_count=0,
                effect_refer_trade_amount=0,
                last_update_time=today_,
            )
            new_statistics_list.append(st_row)

        effect_ref_user_ids = eff_refer_user_id_dict[amb_use_id]
        st_row.effect_refer_count = len(effect_ref_user_ids)
        valid_deal_user_ids = [i for i in effect_ref_user_ids if i in deal_referree_id_set]
        st_row.effect_refer_trade_count = len(valid_deal_user_ids)
        new_ref_user_ids = new_refer_user_id_dict[amb_use_id]
        st_row.new_refer_count = len(new_ref_user_ids)
        new_deal_user_ids = [i for i in new_ref_user_ids if i in deal_referree_id_set]
        st_row.new_refer_trade_count = len(new_deal_user_ids)
        amb_ref_user_ids = amb_refer_user_id_dict[amb_use_id]
        st_row.amb_refer_count = len(amb_ref_user_ids)
        amb_deal_user_ids = [i for i in amb_ref_user_ids if i in deal_referree_id_set]
        st_row.amb_refer_trade_count = len(amb_deal_user_ids)
        st_row.amb_refer_user_balance_usd = quantize_amount(sum([main_user_balance_usd_map[i] for i in amb_ref_user_ids]), 2)
        st_row.new_refer_user_balance_usd = quantize_amount(sum([main_user_balance_usd_map[i] for i in new_ref_user_ids]), 2)
        st_row.amb_refer_active_user_count = len(active_user_ids & amb_ref_user_ids)
        st_row.new_refer_active_user_count = len(active_user_ids & new_ref_user_ids)

        # 返佣数
        eff_date = amb.effected_at.date()
        model = ReferralAssetHistory
        ref_amount_rows = model.query.filter(
            model.user_id == amb_use_id,
            model.asset == "USDT",
        ).with_entities(
            model.date,
            model.amount,
        ).all()
        total_refer_amount = month_refer_amount = 0
        new_total_refer_amount = new_month_refer_amount = 0
        for r in ref_amount_rows:
            total_refer_amount += r.amount
            if today_ > r.date >= this_month:
                month_refer_amount += r.amount
            if r.date >= eff_date:
                new_total_refer_amount += r.amount
                if today_ > r.date >= this_month:
                    new_month_refer_amount += r.amount

        detail_model = ReferralAssetDetail
        self_amount_query = detail_model.query.filter(
            detail_model.user_id == amb_use_id,
            detail_model.referree_id == amb_use_id,
            detail_model.asset == "USDT",
        )
        # 减去给自己的返佣
        self_amount_rows = self_amount_query.with_entities(
            detail_model.date,
            detail_model.spot_amount,
            detail_model.perpetual_amount,
        ).all()
        total_self_amount = new_total_self_amount = 0
        month_self_amount = new_month_self_amount = 0
        for r in self_amount_rows:
            _s_amount_ = (r.spot_amount + r.perpetual_amount)
            total_self_amount += _s_amount_
            if today_ > r.date >= this_month:
                month_self_amount += _s_amount_
            if r.date >= eff_date:
                new_total_self_amount += _s_amount_
                if today_ > r.date >= this_month:
                    new_month_self_amount += _s_amount_
        st_row.total_refer_amount = total_refer_amount - total_self_amount
        st_row.new_total_refer_amount = new_total_refer_amount - new_total_self_amount
        st_row.month_refer_amount = month_refer_amount - month_self_amount
        st_row.new_month_refer_amount = new_month_refer_amount - new_month_self_amount

        # 非直客返佣数
        ind_ref_model = IndirectReferralAssetHistory
        ind_ref_amount_rows = ind_ref_model.query.filter(
            ind_ref_model.user_id == amb_use_id,
            ind_ref_model.asset == "USDT",
        ).with_entities(
            ind_ref_model.date,
            ind_ref_model.amount,
        ).all()
        total_ind_refer_amount = month_ind_refer_amount = new_total_ind_refer_amount = new_month_ind_refer_amount = 0
        for r in ind_ref_amount_rows:
            total_ind_refer_amount += r.amount
            if today_ > r.date >= this_month:
                month_ind_refer_amount += r.amount
            if r.date >= eff_date:
                new_total_ind_refer_amount += r.amount
                if today_ > r.date >= this_month:
                    new_month_ind_refer_amount += r.amount
        st_row.total_indirect_refer_amount = total_ind_refer_amount
        st_row.month_indirect_refer_amount = month_ind_refer_amount
        st_row.new_total_indirect_refer_amount = new_total_ind_refer_amount
        st_row.new_month_indirect_refer_amount = new_month_ind_refer_amount

        ee_ids = eff_refer_user_id_dict[amb_use_id]
        new_ee_ids = new_refer_user_id_dict[amb_use_id]
        spot_usd, perp_usd = Decimal(), Decimal()
        new_spot_usd, new_perp_usd = Decimal(), Decimal()
        bro_eff_ref_spot_amount = bro_eff_ref_per_amount = Decimal()
        for ee in ee_ids:
            # A是普通商务大使，C是Broker大使，A邀请B注册，B使用C的BrokerID产生的交易
            # 则A的上级商务（组长）这一层的交易额需要减半
            _e_spot_usd = spot_trade_map.get(ee, Decimal()) + exchange_trade_map.get(ee, Decimal())
            _e_per_usd = perp_trade_map.get(ee, Decimal())
            spot_usd += _e_spot_usd
            perp_usd += _e_per_usd
            if ee in new_ee_ids:
                new_spot_usd += _e_spot_usd
                new_perp_usd += _e_per_usd
            for _date, _bro_d in ee_bro_trades_map[ee].items():
                if _date < amb.effected_at.date():
                    continue
                for _bro_id, _spot_per in _bro_d.items():
                    bro_eff_ref_spot_amount -= quantize_amount((_spot_per[0] / 2), 8)
                    bro_eff_ref_per_amount -= quantize_amount((_spot_per[1] / 2), 8)
        if amb.is_broker_amb:
            # 则C的上级商务（组长）这一层的交易额需要增半
            for _date, _ee_d in bro_ee_trades_map[amb.user_id].items():
                if _date < amb.effected_at.date():
                    continue
                for _ee_id, _spot_per in _ee_d.items():
                    _er_id = ee_er_id_map.get(_ee_id, None)
                    if _er_id in bus_amb_dict:
                        bro_eff_ref_spot_amount += quantize_amount((_spot_per[0] / 2), 8)
                        bro_eff_ref_per_amount += quantize_amount((_spot_per[1] / 2), 8)
                    else:
                        # 没人邀请 或 邀请人不是商务大使，不需要减半
                        bro_eff_ref_spot_amount += _spot_per[0]
                        bro_eff_ref_per_amount += _spot_per[1]
            st_row.bro_effect_refer_spot_amount = bro_eff_ref_spot_amount
            st_row.bro_effect_refer_perpetual_amount = bro_eff_ref_per_amount
            st_row.bro_effect_refer_trade_amount = st_row.bro_effect_refer_spot_amount + st_row.bro_effect_refer_perpetual_amount
        st_row.effect_refer_spot_amount = quantize_amount(spot_usd, 2)
        st_row.effect_refer_perpetual_amount = quantize_amount(perp_usd, 2)
        st_row.effect_refer_trade_amount = st_row.effect_refer_spot_amount + st_row.effect_refer_perpetual_amount
        st_row.new_refer_spot_amount = quantize_amount(new_spot_usd, 2)
        st_row.new_refer_perpetual_amount = quantize_amount(new_perp_usd, 2)
        st_row.new_refer_trade_amount = st_row.new_refer_spot_amount + st_row.new_refer_perpetual_amount
        this_month_trade_amount = 0
        for ee in new_ee_ids:
            this_month_trade_amount += this_month_spot_trade_map.get(ee, Decimal()) + this_month_exchange_trade_map.get(ee, Decimal())
            this_month_trade_amount += this_month_perp_trade_map.get(ee, Decimal())
        st_row.new_refer_month_trade_amount = this_month_trade_amount
        st_row.last_update_time = today_

    if new_statistics_list:
        db.session.add_all(new_statistics_list)
    db.session.commit()


@scheduled(crontab(minute="24", hour="3-4"))
@lock_call()
def update_bus_user_statistics_info(force_update: bool = False):
    """ 更新商务用户纬度的统计信息 """
    today_ = today()
    st_model = BusinessUserStatistics
    if not force_update:
        last_update = st_model.query.with_entities(func.max(st_model.last_update_time)).first()
        if last_update and last_update[0] and last_update[0].date() >= today_:
            return

    bus_users = BusRelationUserQuerier.get_all_valid_bus_users()
    bus_user_dict = {i.user_id: i for i in bus_users}
    bus_user_ids = list(bus_user_dict)

    bus_user_ambs_dict = defaultdict(list)
    root_amb_bus_user_dict = {}
    delete_bus_user_ambs_dict = defaultdict(list)
    bus_amb_list = BusRelationUserQuerier.get_all_bus_ambassadors()
    for amb in bus_amb_list:
        if not amb.bus_user_id:
            continue
        if amb.status == BusinessAmbassador.Status.VALID:
            bus_user_ambs_dict[amb.bus_user_id].append(amb)
            if amb.is_tree_root_amb:
                root_amb_bus_user_dict[amb.user_id] = amb.bus_user_id
        elif amb.status == BusinessAmbassador.Status.DELETED:
            delete_bus_user_ambs_dict[amb.bus_user_id].append(amb)

    bus_user_tree_ambs_dict = defaultdict(list)
    tree_amb_rows = TreeAmbHelper.get_all_valid_ambassadors()
    for amb in tree_amb_rows:
        bus_u_id = root_amb_bus_user_dict.get(amb.root_id)
        if bus_u_id:
            bus_user_tree_ambs_dict[bus_u_id].append(amb)

    ref_model = BusinessUserReferralAssetHistory
    ref_detail_model = BusinessReferralAssetDetail

    chunk_size = 1000
    cur_month_ = today_.replace(day=1)
    bus_user_total_ref_map = {}
    bus_user_pending_ref_map = {}
    for chunk_u_ids in batch_iter(bus_user_ids, chunk_size):
        total_ref_rows = ref_model.query.filter(
            ref_model.user_id.in_(chunk_u_ids),
            ref_model.type.in_([ref_model.Type.BUS_USER, ref_model.Type.BROKER_TO_BUS_USER]),
        ).group_by(
            ref_model.user_id,
        ).with_entities(
            ref_model.user_id,
            func.sum(ref_model.amount).label("amount"),
        ).all()
        for r in total_ref_rows:
            bus_user_total_ref_map[r.user_id] = r.amount
        pending_ref_rows = ref_detail_model.query.filter(
            ref_detail_model.bus_user_id.in_(chunk_u_ids),
            ref_detail_model.date >= cur_month_,  # 本月
        ).group_by(
            ref_detail_model.bus_user_id,
        ).with_entities(
            ref_detail_model.bus_user_id,
            func.sum(ref_detail_model.spot_amount * (1 - ref_detail_model.leader_rate)).label("spot_amount"),
            func.sum(ref_detail_model.perpetual_amount * (1 - ref_detail_model.leader_rate)).label("perpetual_amount"),
        ).all()
        for r in pending_ref_rows:
            bus_user_pending_ref_map[r.bus_user_id] = r.spot_amount + r.perpetual_amount

    all_amb_ids = {i.user_id for i in bus_amb_list} | {i.user_id for i in tree_amb_rows}
    cur_month = today_.replace(day=1)
    pre_month = last_month(cur_month.year, cur_month.month)
    model = MonthlyBusinessAmbassadorReferralReport
    amb_month_ref_report_rows = model.query.filter(
        model.user_id.in_(all_amb_ids),
        model.report_date.in_([cur_month, pre_month]),
    ).with_entities(
        model.user_id,
        model.report_date,
        model.spot_trade_usd,
        model.perpetual_trade_usd,
        model.bro_spot_trade_usd,
        model.bro_perpetual_trade_usd,
    ).all()
    cur_month_trade_map = defaultdict(Decimal)
    last_month_trade_map = defaultdict(Decimal)
    for row in amb_month_ref_report_rows:
        deal_amount = row.spot_trade_usd + row.perpetual_trade_usd + row.bro_spot_trade_usd + row.bro_perpetual_trade_usd
        if row.report_date == cur_month:
            cur_month_trade_map[row.user_id] += deal_amount
        elif row.report_date == pre_month:
            last_month_trade_map[row.user_id] += deal_amount

    amb_statistics_map = {i.user_id: i for i in BusinessAmbassadorStatistics.query.all()}
    tree_amb_statistics_map = {i.user_id: i for i in TreeAmbassadorStatistics.query.all()}

    new_statistics_list = []
    statistics_map = {i.user_id: i for i in st_model.query.all()}
    for bus_user_id, bus_user in bus_user_dict.items():
        ambs = bus_user_ambs_dict.get(bus_user_id, [])
        delete_ambs = delete_bus_user_ambs_dict.get(bus_user_id, [])
        amb_sts = [amb_statistics_map[i.user_id] for i in ambs if amb_statistics_map.get(i.user_id)]
        tree_ambs = bus_user_tree_ambs_dict.get(bus_user_id, [])
        tree_amb_sts = [tree_amb_statistics_map[i.user_id] for i in tree_ambs if tree_amb_statistics_map.get(i.user_id)]
        refer_count = refer_trade_count = 0
        amb_effect_trade_amount = tree_amb_effect_trade_amount = 0
        refer_user_balance_usd = 0
        refer_active_user_count = 0
        for amb_st in amb_sts:
            refer_count += amb_st.effect_refer_count
            refer_trade_count += amb_st.effect_refer_trade_count
            amb_effect_trade_amount += (amb_st.effect_refer_trade_amount + amb_st.bro_effect_refer_trade_amount)
            refer_user_balance_usd += amb_st.amb_refer_user_balance_usd
            refer_active_user_count += amb_st.amb_refer_active_user_count
        for tree_st in tree_amb_sts:
            refer_count += tree_st.effect_refer_count
            refer_trade_count += tree_st.effect_refer_trade_count
            tree_amb_effect_trade_amount += tree_st.effect_refer_trade_amount
            refer_user_balance_usd += tree_st.effect_refer_user_balance_usd
            refer_active_user_count += tree_st.effect_refer_active_user_count
        if not (st_row := statistics_map.get(bus_user_id)):
            st_row = st_model(
                user_id=bus_user_id,
                refer_bus_amb_count=len(ambs),
                refer_bus_amb_delete_count=len(delete_ambs),
                refer_count=refer_count,
                refer_trade_count=refer_trade_count,
                last_update_time=today_,
            )
            new_statistics_list.append(st_row)
        total_refer_amount = bus_user_total_ref_map.get(bus_user_id, 0)
        pending_refer_amount = bus_user_pending_ref_map.get(bus_user_id, 0)
        st_row.total_refer_amount = total_refer_amount
        st_row.pending_refer_amount = quantize_amount(pending_refer_amount, 8)
        st_row.refer_bus_amb_count = len(ambs)
        st_row.refer_bus_amb_delete_count = len(delete_ambs)
        st_row.refer_tree_amb_count = len(tree_ambs)
        st_row.refer_count = refer_count
        st_row.refer_trade_count = refer_trade_count
        st_row.refer_user_balance_usd = quantize_amount(refer_user_balance_usd, 2)
        st_row.refer_active_user_count = refer_active_user_count

        biz_amb_ids = {i.user_id for i in ambs} | {i.user_id for i in tree_ambs}
        amb_month_trade_amount = sum([cur_month_trade_map[i] for i in biz_amb_ids])
        amb_last_month_trade_amount = sum([last_month_trade_map[i] for i in biz_amb_ids])
        st_row.effect_trade_amount = amb_effect_trade_amount + tree_amb_effect_trade_amount
        st_row.month_effect_trade_amount = amb_month_trade_amount
        st_row.last_month_effect_trade_amount = amb_last_month_trade_amount

        st_row.last_update_time = today_

    if new_statistics_list:
        db.session.add_all(new_statistics_list)
    db.session.commit()


@scheduled(crontab(minute="28", hour="3-4"))
@lock_call()
def update_bus_team_statistics_info(force_update: bool = False):
    """ 更新商务团队纬度的统计信息 """
    today_ = today()
    st_model = BusinessTeamStatistics
    if not force_update:
        last_update = st_model.query.with_entities(func.max(st_model.last_update_time)).first()
        if last_update and last_update[0] and last_update[0].date() >= today_:
            return

    bus_teams = BusRelationUserQuerier.get_all_valid_bus_teams()
    team_ids = [i.id for i in bus_teams]

    ref_model = BusinessUserReferralAssetHistory
    ref_detail_model = BusinessReferralAssetDetail

    cur_month_ = today_.replace(day=1)
    team_total_ref_map = {}
    team_pending_ref_map = {}
    for chunk_t_ids in batch_iter(team_ids, 100):
        total_ref_rows = ref_model.query.filter(
            ref_model.team_id.in_(chunk_t_ids)
        ).group_by(
            ref_model.team_id,
        ).with_entities(
            ref_model.team_id,
            func.sum(ref_model.amount).label("amount"),
        ).all()
        for r in total_ref_rows:
            team_total_ref_map[r.team_id] = r.amount
        pending_ref_rows = ref_detail_model.query.filter(
            ref_detail_model.team_id.in_(chunk_t_ids),
            ref_detail_model.date >= cur_month_,  # 本月
        ).group_by(
            ref_detail_model.team_id,
        ).with_entities(
            ref_detail_model.team_id,
            func.sum(ref_detail_model.spot_amount).label("spot_amount"),
            func.sum(ref_detail_model.perpetual_amount).label("perpetual_amount"),
        ).all()
        for r in pending_ref_rows:
            team_pending_ref_map[r.team_id] = r.spot_amount + r.perpetual_amount

    new_statistics_list = []
    statistics_map = {i.team_id: i for i in st_model.query.all()}
    for team_id in team_ids:
        if not (st_row := statistics_map.get(team_id)):
            st_row = st_model(
                team_id=team_id,
                total_refer_amount=0,
                pending_refer_amount=0,
                last_update_time=today_,
            )
            new_statistics_list.append(st_row)
        st_row.total_refer_amount = team_total_ref_map.get(team_id, 0)
        st_row.pending_refer_amount = team_pending_ref_map.get(team_id, 0)
        st_row.last_update_time = today_

    if new_statistics_list:
        db.session.add_all(new_statistics_list)
    db.session.commit()


@scheduled(crontab(minute="40", hour='0-3'))
@lock_call()
def bus_ambassador_repay_schedule():
    """ 欠款的商务大使还币（每日返佣之后） """
    BusinessAmbassadorRepayHelper.repay()


def update_monthly_ambassador_agent_report(start_date, end_date):
    start_date_time = date_to_datetime(start_date)
    agent_rows = AmbassadorAgent.query.filter(
        AmbassadorAgent.status == AmbassadorAgent.Status.VALID,
        AmbassadorAgent.created_at < end_date,
    ).all()

    # 代理邀请的大使
    agent_user_ids = [i.user_id for i in agent_rows]
    amb_agent_history_rows = AmbassadorAgentHistory.query.filter(
        AmbassadorAgentHistory.user_id.in_(agent_user_ids),
        AmbassadorAgentHistory.status == AmbassadorAgentHistory.Status.VALID,
        AmbassadorAgentHistory.created_at < end_date,
    ).all()
    agent_id_amb_ids_dict = defaultdict(list)
    delta_agent_id_amb_ids_dict = defaultdict(list)
    for amb_agent_h in amb_agent_history_rows:
        agent_id_amb_ids_dict[amb_agent_h.user_id].append(amb_agent_h.ambassador_id)
        if amb_agent_h.effected_at >= start_date_time:
            delta_agent_id_amb_ids_dict[amb_agent_h.user_id].append(amb_agent_h.ambassador_id)

    # 大使邀请的用户
    ambassador_rows = Ambassador.query.filter(
        Ambassador.status == Ambassador.Status.VALID,
    ).all()
    ambassador_dict = {i.user_id: i for i in ambassador_rows}
    amb_refer_history_rows = (
        ReferralHistory.query.filter(
            ReferralHistory.status == ReferralHistory.Status.VALID,
            ReferralHistory.effected_at < end_date,
        )
        .with_entities(ReferralHistory.referrer_id, ReferralHistory.referree_id, ReferralHistory.effected_at)
        .all()
    )

    market_makers = MarketMakerHelper.list_all_maker_ids(include_sub_account=False)
    market_makers = set(market_makers)

    refs = defaultdict(list)
    delta_refs = defaultdict(list)
    for user_id, ref_user_id, effected_at in amb_refer_history_rows:
        if user_id in ambassador_dict:
            # if effected_at >= ambassador_info.effected_at:
            # 大使有效邀请不需要判断生效时间
            refs[user_id].append(ref_user_id)
            if effected_at >= start_date_time:
                delta_refs[user_id].append(ref_user_id)

    agent: AmbassadorAgent
    for agent in agent_rows:
        # 累计的相关数据
        agent_ambassadors = [
            ambassador_dict[amb_uid] for amb_uid in agent_id_amb_ids_dict[agent.user_id] if amb_uid in ambassador_dict
        ]
        referral_deal_ambassador_count = 0  # 有交易用户的大使数
        total_ref_user_ids = set()
        total_deal_user_ids = set()
        total_deal_amount = Decimal()
        for ambassador in agent_ambassadors:
            ref_user_ids = refs.get(ambassador.user_id)
            if not ref_user_ids:
                continue
            # 做市商交易量不算在考核内
            whitelist = [x for x in ref_user_ids if x not in market_makers]
            if not whitelist:
                deal_user_ids, deal_amount = [], Decimal()
            else:
                deal_user_ids, deal_amount = _get_month_deals(start_date, whitelist)

            total_ref_user_ids.update(whitelist)
            total_deal_user_ids.update(deal_user_ids)
            total_deal_amount += deal_amount
            if deal_user_ids:
                referral_deal_ambassador_count += 1

        # 当月新增的相关数据
        delta_agent_ambassadors = [
            ambassador_dict[amb_uid] for amb_uid in delta_agent_id_amb_ids_dict[agent.user_id] if amb_uid in ambassador_dict
        ]  # 代理当月新增邀请的大使
        delta_referral_deal_ambassador_count = 0  # 代理当月新增邀请的有交易用户的大使数
        delta_ref_user_ids = set()
        delta_deal_user_ids = set()
        delta_deal_amount = Decimal()
        for ambassador in delta_agent_ambassadors:
            amb_delta_ref_user_ids = delta_refs.get(ambassador.user_id)
            if not amb_delta_ref_user_ids:
                continue
            # 做市商交易量不算在考核内
            whitelist = [x for x in amb_delta_ref_user_ids if x not in market_makers]
            if not whitelist:
                amb_delta_deal_user_ids, amb_delta_deal_amount = [], Decimal()
            else:
                amb_delta_deal_user_ids, amb_delta_deal_amount = _get_month_deals(start_date, whitelist)

            delta_ref_user_ids.update(whitelist)
            delta_deal_user_ids.update(amb_delta_deal_user_ids)
            delta_deal_amount += amb_delta_deal_amount
            if amb_delta_deal_user_ids:
                delta_referral_deal_ambassador_count += 1

        ref_amount = (
            ReferralAssetHistory.query.filter(
                ReferralAssetHistory.date >= start_date,
                ReferralAssetHistory.date < end_date,
                ReferralAssetHistory.user_id == agent.user_id,
                ReferralAssetHistory.type == ReferralAssetHistory.Type.AMBASSADOR_AGENT,
            )
            .with_entities(func.sum(ReferralAssetHistory.amount))
            .scalar()
            or 0
        )

        report = MonthlyAmbassadorAgentReport.get_or_create(report_date=start_date, user_id=agent.user_id)
        report.referral_ambassador_count = len(agent_ambassadors)
        report.delta_referral_ambassador_count = len(delta_agent_ambassadors)
        report.referral_deal_ambassador_count = referral_deal_ambassador_count
        report.delta_referral_deal_ambassador_count = delta_referral_deal_ambassador_count
        report.referral_user_count = len(total_ref_user_ids)
        report.delta_referral_user_count = len(delta_ref_user_ids)
        report.deal_user_count = len(total_deal_user_ids)
        report.delta_deal_user_count = len(delta_deal_user_ids)
        report.deal_amount = total_deal_amount
        report.delta_deal_amount = delta_deal_amount
        report.referral_amount = ref_amount
        report.asset = ReferralBusiness.GIFT_ASSETS[ReferralAssetHistory.Type.AMBASSADOR_AGENT]
        db.session.add(report)
    db.session.commit()


@scheduled(crontab(minute=20, hour="2-3"))
@lock_call()
def update_monthly_ambassador_agent_report_schedule():
    """ 大使代理-邀请月报 """
    _today = today()
    last = MonthlyAmbassadorAgentReport.query.order_by(MonthlyAmbassadorAgentReport.report_date.desc()).first()
    if last:
        start_month = next_month(last.report_date.year, last.report_date.month)
    else:
        first_ref_date = ReferralAssetHistory.query.order_by(ReferralAssetHistory.date.asc()).first().date
        start_month = datetime.date(first_ref_date.year, first_ref_date.month, 1)

    cur_month = datetime.date(_today.year, _today.month, 1)
    if start_month > cur_month:
        # 重复更新当月的数据
        start_month = cur_month

    while start_month <= cur_month:
        end_month = next_month(start_month.year, start_month.month)
        update_monthly_ambassador_agent_report(start_month, end_month)
        start_month = end_month


@scheduled(crontab(minute=25, hour='5-7', day_of_month='1-3'))
@lock_call()
def update_monthly_ambassador_agent_appraisal_schedule():
    """大使代理月度考核"""

    def has_appraisal():
        model = AppraisalHistory
        last = model.query.filter(
            model.business_type == model.BusinessType.AMBASSADOR_AGENT,
            model.report_date == report_date
        ).first()
        if last:
            return True
        return False

    def get_agents_to_appraisal():
        model = AmbassadorAgent
        objs = model.query.filter(
            model.status == model.Status.VALID,
            model.is_appraisal.is_(True),
        ).all()
        return objs

    def get_relations_mapping():
        model = AmbassadorAgentHistory
        objs = model.query.with_entities(
            model.user_id,
            func.count(model.ambassador_id).label('ambassador_count')
        ).filter(
            model.status == model.Status.VALID,
        ).group_by(model.user_id).all()
        mapping = {obj.user_id: obj.ambassador_count for obj in objs}
        return mapping

    def get_result_after_appraisal():
        to_appraisal_agents = get_agents_to_appraisal()
        relations_mapping = get_relations_mapping()

        appraisal_objs = []
        invalid_agents = []
        max_buffer_month = 3
        for agent in to_appraisal_agents:
            month_count = _get_buffer_protection_month(
                agent.user_id,
                report_date - relativedelta(months=max_buffer_month - 1),
                report_date - relativedelta(months=1),
                AppraisalHistory.BusinessType.AMBASSADOR_AGENT
            )
            ambassador_count = relations_mapping.get(agent.user_id, 0)
            if ambassador_count >= 2:
                result_status = AppraisalHistory.ResultStatus.NOT_CHANGE
                month_count = 0
            else:
                result_status = AppraisalHistory.ResultStatus.NOT_LEAST_LEVEL
                month_count += 1

            old_status = new_status = agent.status
            if month_count >= max_buffer_month:
                invalid_agents.append(agent)
                new_status = AmbassadorAgent.Status.DELETED

            row = AppraisalHistory(
                user_id=agent.user_id,
                business_type=AppraisalHistory.BusinessType.AMBASSADOR_AGENT,
                report_date=report_date,
                result_status=result_status,
                result=json.dumps(
                    dict(
                        old_status=old_status.name,
                        new_status=new_status.name,
                        month_count=month_count,
                        ambassador_count=ambassador_count
                    )
                )
            )
            appraisal_objs.append(row)
        return appraisal_objs, invalid_agents

    _now = now()
    _today = today()
    report_date = last_month(_today.year, _today.month)

    if has_appraisal():
        return

    appraisal_objs, invalid_agents = get_result_after_appraisal()
    for rows in batch_iter(appraisal_objs, 1000):
        db.session.bulk_save_objects(rows)
    db.session.commit()

    for invalid_agent in invalid_agents:
        AmbassadorAgentBusiness.make_ambassador_agent_invalid(invalid_agent)

    # bulk_save_objects donot return id.
    rows = AppraisalHistory.query.filter(
        AppraisalHistory.report_date == report_date,
        AppraisalHistory.business_type == AppraisalHistory.BusinessType.AMBASSADOR_AGENT
    ).with_entities(
        AppraisalHistory.user_id, AppraisalHistory.id
    ).all()
    for user_id, record_id in rows:
        send_appraisal_email.delay(user_id, record_id)


@scheduled(crontab(minute=20, hour="1-3"))
@lock_call()
def update_monthly_ambassador_agent_asset_schedule():
    """更新大使维度-代理返佣汇总月表"""
    _today = today()
    model = MonthlyAmbassadorAgentAssetReport
    last = model.query.order_by(model.report_date.desc()).first()
    if last:
        start_month = next_month(last.report_date.year, last.report_date.month)
    else:
        daily_model = DailyAmbassadorAgentAssetReport
        row = daily_model.query.order_by(daily_model.report_date.asc()).first()
        if not row:
            first_date = _today
        else:
            first_date = row.report_date
        start_month = datetime.date(first_date.year, first_date.month, 1)

    cur_month = datetime.date(_today.year, _today.month, 1)
    if start_month > cur_month:
        # 重复更新当月的数据
        start_month = cur_month

    while start_month <= cur_month:
        end_month = next_month(start_month.year, start_month.month)
        update_monthly_ambassador_agent_asset(start_month, end_month)
        start_month = end_month


def update_monthly_ambassador_agent_asset(start_month, end_month):

    def summary_daily_reports():
        model = DailyAmbassadorAgentAssetReport
        rows = model.query.filter(
            model.report_date >= start_month,
            model.report_date < end_month
        ).all()

        mapping = defaultdict(lambda: {'fee_amount': 0, 'referral_amount': 0, 'deal_user_ids': set()})
        for row in rows:
            deal_user_ids = set(row.get_deal_user_ids())
            mapping[(row.user_id, row.agent_id)]['fee_amount'] += row.fee_amount
            mapping[(row.user_id, row.agent_id)]['referral_amount'] += row.referral_amount
            mapping[(row.user_id, row.agent_id)]['deal_user_ids'] |= deal_user_ids
        return mapping

    def get_monthly_reports():
        model = MonthlyAmbassadorAgentAssetReport
        rows = model.query.filter(
            model.report_date == start_month
        ).all()
        return {(row.user_id, row.agent_id): row for row in rows}

    monthly_mapping = get_monthly_reports()
    summary_daily_mapping = summary_daily_reports()
    to_creates = set(summary_daily_mapping.keys()) - set(monthly_mapping.keys())
    to_updates = set(summary_daily_mapping.keys()) & set(monthly_mapping.keys())
    pending_objs = []
    for user_id, agent_id in to_creates:
        values = summary_daily_mapping[(user_id, agent_id)]
        obj = MonthlyAmbassadorAgentAssetReport(
            user_id=user_id,
            agent_id=agent_id,
            report_date=start_month,
            fee_amount=values['fee_amount'],
            referral_amount=values['referral_amount'],
            deal_user_count=len(values['deal_user_ids'])
        )
        pending_objs.append(obj)

    for user_id, agent_id in to_updates:
        values = summary_daily_mapping[(user_id, agent_id)]
        obj = monthly_mapping[(user_id, agent_id)]
        obj.fee_amount = values['fee_amount']
        obj.referral_amount = values['referral_amount']
        obj.deal_user_count = len(values['deal_user_ids'])

    for chunk_pending_objs in batch_iter(pending_objs, 1000):
        db.session.bulk_save_objects(chunk_pending_objs)
    db.session.commit()


@scheduled(crontab(hour="*/1", minute='10'))
@lock_call()
def update_amb_apl_last_invalid_reason_schedule():
    """ 更新大使申请的上次失效原因 """
    apl_rows = AmbassadorApplication.query.filter(
        AmbassadorApplication.type == AmbassadorApplication.Type.AMBASSADOR,
        AmbassadorApplication.status == AmbassadorApplication.Status.CREATED,
        AmbassadorApplication.last_invalid_reason.is_(None),
    ).all()
    if not apl_rows:
        return

    apl_amb_ids = {i.user_id for i in apl_rows}
    amb_rows = Ambassador.query.filter(
        Ambassador.user_id.in_(apl_amb_ids),
    ).with_entities(
        Ambassador.user_id,
        Ambassador.effected_at,
    ).all()
    amb_map = {i.user_id: i for i in amb_rows}
    amb_remove_his_map = AmbassadorBusiness.batch_query_last_amb_remove_his(list(amb_map))
    for apl_row in apl_rows:
        amb = amb_map.get(apl_row.user_id)
        if not amb:
            reason = AmbassadorApplication.LastInvalidReason.NOT_AMB
        else:
            remove_his = amb_remove_his_map.get(apl_row.user_id)
            if remove_his and remove_his.created_at > amb.effected_at and remove_his.admin_user_id:
                reason = AmbassadorApplication.LastInvalidReason.MANUAL
            else:
                reason = AmbassadorApplication.LastInvalidReason.SYSTEM
        apl_row.last_invalid_reason = reason
        db.session.add(apl_row)
        db.session.commit()


@scheduled(crontab(hour='10', minute='10'), queue=CeleryQueues.DAILY)
@lock_call(ttl=3600 * 12)
def update_amb_dashboard_schedule():
    """ 大使面板 """
    yes_day = today() - datetime.timedelta(days=1)
    last_report_dt = AmbDashboardHelper.get_last_report_date()
    start_dt = last_report_dt + datetime.timedelta(days=1)
    while start_dt <= yes_day:
        helper = AmbDashboardHelper(start_dt)
        if not helper.run():
            break
        start_dt += datetime.timedelta(days=1)


@scheduled(crontab(hour='*/1', minute='1'))
@lock_call()
def check_and_release_packages_schedule():
    """ 检查并释放激励包 """
    AmbassadorPackageHelper.check_and_release_packages()


@scheduled(crontab(hour='0,1', minute='20,32,43'))
@lock_call()
def update_package_stats_schedule():
    """ 更新激励包统计数据"""
    AmbassadorPackageHelper.update_package_stats()


@scheduled(crontab(minute='55', hour='7'))
@lock_call()
def update_batch_stats_schedule():
    """ 更新批次统计数据 """
    AmbassadorPackageHelper.update_batch_stats()


@scheduled(crontab(hour='*/1', minute='3'))
@lock_call()
def check_package_expiration_schedule():
    """ 检查激励包过期 """
    AmbassadorPackageHelper.check_package_expiration()


@scheduled(crontab(minute='45', hour='5-9', day_of_month='1-3'))
@lock_call()
def monthly_package_settlement_schedule():
    """ 月度激励包结算 """
    if not AmbassadorPackageHelper.has_normal_appraisal():
        return
    if not AmbassadorPackageHelper.finish_package_stats():
        return
    packages = UserAmbassadorPackage.query.filter(
        UserAmbassadorPackage.status == UserAmbassadorPackage.Status.ACTIVE,
        UserAmbassadorPackage.current_package_release_at == this_month(),
    ).all()
    if not packages:
        return
    user_ids = {i.user_id for i in packages}
    normal_ambs, business_ambs = ReferralRepository.get_ambassador_ids(user_ids)

    def meet_package_requirement(p, b):
        require_amount = b.refer_trade_amount
        require_user_count = b.refer_trade_users
        current_amount = p.current_refer_amount
        current_user_count = p.current_refer_users
        return current_amount >= require_amount and current_user_count >= require_user_count
    
    batch_ids = {i.batch_id for i in packages}
    batches = AmbassadorPackageBatch.query.filter(
        AmbassadorPackageBatch.id.in_(batch_ids),
    ).all()
    batch_map = {i.id: i for i in batches}
    activity_id = Activity.get_or_create_ambassador_package_activity_id()

    for package in packages:
        batch = batch_map[package.batch_id]
        if package.user_id not in normal_ambs and package.user_id not in business_ambs:
            handle_user_not_ambassador.delay(package.id)
        elif not meet_package_requirement(package, batch):
            handle_user_not_meet_package_requirement.delay(package.id)
        else:
            handle_user_meet_package_requirement.delay(package.id, activity_id)


@celery_task
@lock_call(with_args=True)
def handle_user_not_ambassador(package_id: int):
    package = UserAmbassadorPackage.query.get(package_id)
    if not package:
        raise ValueError(f'package not found: {package_id}')
    batch = AmbassadorPackageBatch.query.get(package.batch_id)
    if not batch:
        raise ValueError(f'batch not found: {package.batch_id}')
    AmbassadorPackageHelper.handle_user_not_ambassador(package, batch)


@celery_task
@lock_call(with_args=True)
def handle_user_not_meet_package_requirement(package_id: int):
    package = UserAmbassadorPackage.query.get(package_id)
    if not package:
        raise ValueError(f'package not found: {package_id}')
    batch = AmbassadorPackageBatch.query.get(package.batch_id)
    if not batch:
        raise ValueError(f'batch not found: {package.batch_id}')
    AmbassadorPackageHelper.handle_user_not_meet_package_requirement(package, batch)


@celery_task
@lock_call(with_args=True)
def handle_user_meet_package_requirement(package_id: int, activity_id: int):
    package = UserAmbassadorPackage.query.get(package_id)
    if not package:
        raise ValueError(f'package not found: {package_id}')
    batch = AmbassadorPackageBatch.query.get(package.batch_id)
    if not batch:
        raise ValueError(f'batch not found: {package.batch_id}')
    AmbassadorPackageHelper.handle_user_meet_package_requirement(package, batch, activity_id)


@celery_task
@lock_call(with_args=True)
def update_business_ambassadors_last_team_id(user_id: int):
    # 修改商务关系
    last_team_id = BusRelationUserQuerier.get_last_team_id(user_id)
    bus_amb: BusinessAmbassador = BusinessAmbassador.query.filter(
        BusinessAmbassador.user_id == user_id,
    ).first()
    if bus_amb:
        bus_amb.last_team_id = last_team_id
        db.session.commit()


@celery_task
@lock_call(with_args=True)
def update_business_user_ambassadors_last_team_id(bus_user_id: int):
    # 商务更换团队
    u_ids = {v.user_id for v in BusinessAmbassador.query.filter(
            BusinessAmbassador.bus_user_id == bus_user_id,
            BusinessAmbassador.status == BusinessAmbassador.Status.VALID
        ).with_entities(BusinessAmbassador.user_id).all()
    }
    for uid in u_ids:
        update_business_ambassadors_last_team_id.delay(uid)