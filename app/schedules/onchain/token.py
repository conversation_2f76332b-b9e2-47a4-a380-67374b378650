from decimal import Decimal

from collections import defaultdict

from celery.schedules import crontab

from flask import current_app

from app.config import config

from app.common import CeleryQueues
from app.common.onchain import Chain, CHAIN_EXCHANGER_ID_MAPPING

from app.business import send_alert_notice
from app.business.lock import lock_call
from app.business.onchain.base import schedule_enable
from app.business.onchain.base import OnchainSettings
from app.business.onchain.base import Onchain<PERSON>ddressHelper
from app.business.onchain.client import OnchainSwapClient
from app.business.onchain.client import CoingeckoHelper
from app.business.onchain.client import DataInterfaceHelper
from app.business.onchain.token import batch_get_token
from app.business.onchain.token import batch_get_token_info
from app.business.onchain.token import batch_update_token
from app.business.onchain.token import batch_get_or_update_token
from app.business.onchain.token import batch_update_token_info
from app.business.onchain.token import batch_update_token_quote
from app.business.onchain.token import all_token_by_chain

from app.models.onchain import OnchainStopOrder
from app.models.onchain import OnchainTokenBlocklist

from app.caches.onchain import OnchainHotTokenCache
from app.caches.onchain import OnchainTokenQuoteCache
from app.caches.onchain import OnchainCoingeckoTokenIDSOLCache
from app.caches.onchain import OnchainCoingeckoTokenIDETHCache
from app.caches.onchain import OnchainCoingeckoTokenIDBSCCache
from app.caches.onchain import OnchainCoingeckoLowValueTokenIDCache
from app.caches.onchain import OnchainUpdateTokenInfoCache
from app.caches.onchain import OnchainUpdateTokenBaseInfoCache
from app.caches.onchain import OnchainUpdateTokenQuoteCache
from app.caches.onchain import OnchainTokenUpdatedAtInfoCache
from app.caches.onchain import OnchainTokenUpdatedAtQuoteCache
from app.caches.onchain import OnchainTokenUpdatedAtBaseInfoCache
from app.caches.onchain import OnchainWalletSupportSwapTokenAddressSOLCache
from app.caches.onchain import OnchainWalletSupportSwapTokenAddressETHCache
from app.caches.onchain import OnchainWalletSupportSwapTokenAddressBSCCache
from app.caches.onchain import OnchainTokenVisitAtCache

from app.utils import scheduled
from app.utils import route_module_to_celery_queue
from app.utils.onchain import decimal_mul

route_module_to_celery_queue(__name__, CeleryQueues.ONCHAIN)


@scheduled(crontab(minute='*/5'))
@lock_call()
def update_coingecko_token_id_schedule():
    """更新Coingecko的Token ID缓存"""
    if not schedule_enable():
        current_app.logger.info('update_coingecko_token_id_schedule not enable')
        return

    token_id_map = CoingeckoHelper().get_token_ids()
    OnchainCoingeckoTokenIDSOLCache().update(token_id_map[Chain.SOL])
    OnchainCoingeckoTokenIDETHCache().update(token_id_map[Chain.ERC20])
    OnchainCoingeckoTokenIDBSCCache().update(token_id_map[Chain.BSC])


@scheduled(crontab(minute='*/5'))
@lock_call()
def update_wallet_support_swap_token_address_schedule():
    """更新Wallet支持Swap的Token地址缓存"""
    wallet_chain_type = {
        'SOL': Chain.SOL,
        'ETH': Chain.ERC20,
        'BNB': Chain.BSC,
    }

    support_token_addresses = defaultdict(set)
    for item in OnchainSwapClient().coin_all():
        swap_token_id: str = item['id']
        if '.' not in swap_token_id:
            # 原生代币排除
            continue
        swap_split = swap_token_id.split('.', maxsplit=1)
        swap_chain = swap_split[0]
        contract = swap_split[1]
        if swap_chain not in wallet_chain_type:
            # 非支持的公链排除
            continue
        chain = wallet_chain_type[swap_chain]
        if not (set(CHAIN_EXCHANGER_ID_MAPPING[chain]) & set(item['exchanger_ids'])):
            # exchanger_ids中没有必须的选项排除
            continue
        support_token_addresses[chain].add(OnchainAddressHelper(chain).normalise_address(contract))

    OnchainWalletSupportSwapTokenAddressSOLCache().update(support_token_addresses[Chain.SOL])
    OnchainWalletSupportSwapTokenAddressETHCache().update(support_token_addresses[Chain.ERC20])
    OnchainWalletSupportSwapTokenAddressBSCCache().update(support_token_addresses[Chain.BSC])


def update_hot_token_by_chain(helper: DataInterfaceHelper, blocklist: set[str]) -> list[int]:
    hot_token_count = config['ONCHAIN_CONFIGS']['hot_token_count']

    support_token_addresses = None
    if helper.chain == Chain.SOL:
        # 因为SOL接入的是DEX, 所以这里还是需要保留对钱包支持Swap白名单的过滤机制
        support_token_addresses = OnchainWalletSupportSwapTokenAddressSOLCache().all()

    sort_all_token_addresses = helper.sort_all_token_address(blocklist)
    if support_token_addresses:
        sort_all_token_addresses = [item for item in sort_all_token_addresses if item in support_token_addresses]
    sort_all_token_addresses = sort_all_token_addresses[: int(hot_token_count * 1.25)]

    batch_get_or_update_token(helper, sort_all_token_addresses)  # 检查未创建的Token初始化一下

    token_ids = []
    for token in all_token_by_chain(helper.chain):
        if token.contract in blocklist:
            continue
        if support_token_addresses and token.contract not in support_token_addresses:
            continue
        token_ids.append(token.id)
    token_info_map = batch_get_token_info(token_ids)
    token_quote_map = OnchainTokenQuoteCache().get_many(token_ids)

    hot_key = OnchainSettings.HotLimitKey
    hot_amount_24h_limit = OnchainSettings.get_hot_limit_value(helper.chain, hot_key.AMOUNT_24H)
    hot_fdv_limit = OnchainSettings.get_hot_limit_value(helper.chain, hot_key.FDV)
    hot_top_pool_liquidity_limit = OnchainSettings.get_hot_limit_value(helper.chain, hot_key.TOP_POOL_LIQUIDITY)
    hot_hold_address_count_limit = OnchainSettings.get_hot_limit_value(helper.chain, hot_key.HOLD_ADDRESS_COUNT)
    hot_top10_percentage_limit = OnchainSettings.get_hot_limit_value(helper.chain, hot_key.TOP10_PERCENTAGE)
    token_id_amount_24_list = []
    for token_id in token_ids:
        if token_id not in token_info_map or token_id not in token_quote_map:
            continue
        token_info = token_info_map[token_id]
        token_quote = token_quote_map[token_id]

        if not token_info.risk.get('is_not_honeypot', True):
            # 禁止蜜罐Token上榜
            continue

        if any([
            not token_quote['amount_24h'],
            not token_quote['price'],
            not token_info.top_pool,
            not token_info.total_supply,
            not token_info.top_pool_liquidity,
            not token_info.circulating_supply,
            not token_info.holders_count,
            not token_info.top10_percentage
        ]):
            continue
        fdv = decimal_mul(token_info.total_supply, token_quote['price'])
        if hot_amount_24h_limit and token_quote['amount_24h'] < hot_amount_24h_limit:
            continue
        if hot_fdv_limit and fdv < hot_fdv_limit:
            continue
        if hot_top_pool_liquidity_limit and Decimal(token_info.top_pool_liquidity) < hot_top_pool_liquidity_limit:
            continue
        if hot_hold_address_count_limit and token_info.holders_count < hot_hold_address_count_limit:
            continue
        if hot_top10_percentage_limit and token_info.top10_percentage > hot_top10_percentage_limit:
            continue
        token_id_amount_24_list.append((token_id, token_quote['amount_24h']))

    token_id_amount_24_list.sort(key=lambda x: x[1], reverse=True)

    current_app.logger.info(f'update {helper.chain.name} hot token success, number of calls: {helper.number_of_calls}')

    return [token_id for token_id, _ in token_id_amount_24_list[: hot_token_count]]


@scheduled(crontab(minute='*/10'))
@lock_call()
def update_hot_token_schedule():
    """每10分钟更新一次热门Token列表"""
    if not schedule_enable():
        current_app.logger.info('update_hot_token_schedule not enable')
        return

    blocklist = defaultdict(set)
    for item in OnchainTokenBlocklist.query.filter(
        OnchainTokenBlocklist.block_type.in_(OnchainTokenBlocklist.BlockType.can_not_hot_type())
    ).all():
        blocklist[item.chain].add(item.contract)

    hot_token_ids = []
    sol_hot_token_ids = update_hot_token_by_chain(CoingeckoHelper(Chain.SOL), blocklist[Chain.SOL])
    hot_token_ids.extend(sol_hot_token_ids)
    eth_hot_token_ids = update_hot_token_by_chain(CoingeckoHelper(Chain.ERC20), blocklist[Chain.ERC20])
    hot_token_ids.extend(eth_hot_token_ids)
    bsc_hot_token_ids = update_hot_token_by_chain(CoingeckoHelper(Chain.BSC), blocklist[Chain.BSC])
    hot_token_ids.extend(bsc_hot_token_ids)

    if len(hot_token_ids) > 0:
        OnchainHotTokenCache().set(hot_token_ids)
        OnchainUpdateTokenBaseInfoCache().batch_add(hot_token_ids)

    current_app.logger.info(f'update hot token success, sol: {len(sol_hot_token_ids)}, eth: {len(eth_hot_token_ids)}, '
                            f'bsc: {len(bsc_hot_token_ids)}, total: {len(hot_token_ids)}')


def _add_hot_and_stop_order_token_ids(token_ids: set[int]) -> set[int]:
    token_ids.update(set(OnchainHotTokenCache().all()))
    token_ids.update({
        stop_order.token_id for stop_order in OnchainStopOrder.query.filter(
            OnchainStopOrder.status == OnchainStopOrder.Status.CREATED,
        ).all()
    })
    return token_ids


def batch_update_token_price(helper: DataInterfaceHelper, token_ids: list[int]):
    """批量更新Token的Price数据"""
    if not token_ids:
        return
    address_to_token_id_map = {
        token.contract: token_id for token_id, token in batch_get_token(token_ids).items()
    }
    if not address_to_token_id_map:
        return

    data = {}
    for quote_data in helper.get_token_quote_list_super(address_to_token_id_map.keys()):
        if quote_data.contract not in address_to_token_id_map:
            continue
        data[address_to_token_id_map[quote_data.contract]] = quote_data

    if len(data) > 0:
        OnchainTokenQuoteCache().save_many(data)

    current_app.logger.info(f'update {helper.chain.name} price success, number of calls: {helper.number_of_calls}')


@scheduled(30)
@lock_call()
def update_token_price_schedule():
    """30s更新一次过去24H请求过的Token价格数据, 这里使用Coingecko的simple_price接口, 每次可以请求500个Token的最新价格
    如果更换的第三方数据没有单次请求Token特别大的价格接口的话, 应禁用该定时任务, 并将下面update_token_quote_schedule更新频率提高到1min"""
    if not schedule_enable():
        current_app.logger.info('update_token_price_schedule not enable')
        return

    token_ids = _add_hot_and_stop_order_token_ids(OnchainTokenVisitAtCache().all())

    chain_token_id_map = defaultdict(list)
    for token_id, token in batch_get_token(token_ids).items():
        chain_token_id_map[token.chain].append(token_id)

    batch_update_token_price(CoingeckoHelper(Chain.SOL), chain_token_id_map[Chain.SOL])
    batch_update_token_price(CoingeckoHelper(Chain.ERC20), chain_token_id_map[Chain.ERC20])
    batch_update_token_price(CoingeckoHelper(Chain.BSC), chain_token_id_map[Chain.BSC])

    current_app.logger.info(
        f'update token price success, sol: {len(chain_token_id_map[Chain.SOL])}, '
        f'eth: {len(chain_token_id_map[Chain.ERC20])}, '
        f'bsc: {len(chain_token_id_map[Chain.BSC])}, '
        f'total: {len(token_ids)}'
    )


@scheduled(crontab(minute='*/10'))
@lock_call()
def update_token_quote_schedule():
    """10分钟更新一次有请求Token的Quote信息(默认包括热门Token&存在计划单的Token)"""
    if not schedule_enable():
        current_app.logger.info('update_token_quote_schedule not enable')
        return

    token_ids = _add_hot_and_stop_order_token_ids(OnchainUpdateTokenQuoteCache().all())

    chain_token_id_map = defaultdict(list)
    for token_id, token in batch_get_token(token_ids).items():
        chain_token_id_map[token.chain].append(token_id)

    batch_update_token_quote(CoingeckoHelper(Chain.SOL), chain_token_id_map[Chain.SOL])
    batch_update_token_quote(CoingeckoHelper(Chain.ERC20), chain_token_id_map[Chain.ERC20])
    batch_update_token_quote(CoingeckoHelper(Chain.BSC), chain_token_id_map[Chain.BSC])

    OnchainUpdateTokenQuoteCache().clear(token_ids)
    OnchainTokenUpdatedAtQuoteCache().update_many(list(token_ids))

    current_app.logger.info(
        f'update token quote success, sol: {len(chain_token_id_map[Chain.SOL])}, '
        f'eth: {len(chain_token_id_map[Chain.ERC20])}, '
        f'bsc: {len(chain_token_id_map[Chain.BSC])}, '
        f'total: {len(token_ids)}'
    )


@scheduled(crontab(minute='*/1'))
@lock_call()
def update_token_base_info_schedule():
    """更新有请求Token的基础信息"""
    if not schedule_enable():
        current_app.logger.info('update_token_base_info_schedule not enable')
        return

    limit = 180  # 每次每条链最多更新的Token数量

    token_ids = OnchainTokenUpdatedAtBaseInfoCache().get_need_update_token_ids(
        list(OnchainUpdateTokenBaseInfoCache().all())
    )
    if len(token_ids) == 0:
        return

    chain_token_address_map = defaultdict(list)
    update_token_ids = set()
    for token_id, token in batch_get_token(token_ids).items():
        if len(chain_token_address_map[token.chain]) >= limit:
            continue
        chain_token_address_map[token.chain].append(token.contract)
        update_token_ids.add(token_id)

    batch_update_token(CoingeckoHelper(Chain.SOL), chain_token_address_map[Chain.SOL])
    batch_update_token(CoingeckoHelper(Chain.ERC20), chain_token_address_map[Chain.ERC20])
    batch_update_token(CoingeckoHelper(Chain.BSC), chain_token_address_map[Chain.BSC])

    OnchainUpdateTokenBaseInfoCache().clear(update_token_ids)
    OnchainTokenUpdatedAtBaseInfoCache().update_many(list(update_token_ids))

    current_app.logger.info(
        f'update token base success, sol: {len(chain_token_address_map[Chain.SOL])}, '
        f'eth: {len(chain_token_address_map[Chain.ERC20])}, '
        f'bsc: {len(chain_token_address_map[Chain.BSC])}, '
        f'total: {sum([len(item) for item in chain_token_address_map.values()])}'
    )


@scheduled(crontab(minute='*/1'))
@lock_call()
def update_token_info_schedule():
    """更新有请求Token的Info信息"""
    if not schedule_enable():
        current_app.logger.info('update_token_info_schedule not enable')
        return

    limit = 30  # 每次每条链最多更新的Token数量

    token_ids = OnchainTokenUpdatedAtInfoCache().get_need_update_token_ids(list(OnchainUpdateTokenInfoCache().all()))
    if len(token_ids) == 0:
        return

    chain_token_id_map = defaultdict(list)
    update_token_ids = set()
    for token_id, token in batch_get_token(token_ids).items():
        if len(chain_token_id_map[token.chain]) >= limit:
            continue
        chain_token_id_map[token.chain].append(token_id)
        update_token_ids.add(token_id)

    batch_update_token_info(CoingeckoHelper(Chain.SOL), chain_token_id_map[Chain.SOL])
    batch_update_token_info(CoingeckoHelper(Chain.ERC20), chain_token_id_map[Chain.ERC20])
    batch_update_token_info(CoingeckoHelper(Chain.BSC), chain_token_id_map[Chain.BSC])

    OnchainUpdateTokenInfoCache().clear(update_token_ids)
    OnchainTokenUpdatedAtInfoCache().update_many(list(update_token_ids))

    current_app.logger.info(
        f'update token info success, sol: {len(chain_token_id_map[Chain.SOL])}, '
        f'eth: {len(chain_token_id_map[Chain.ERC20])}, '
        f'bsc: {len(chain_token_id_map[Chain.BSC])}, '
        f'total: {len(token_ids)}'
    )


@scheduled(crontab(hour='0', minute='10'))
def count_all_support_swap_token_schedule():
    """每天执行统计热榜更新时查询Token的数量"""
    chain_count_map = {}
    low_value_token_ids = set(OnchainCoingeckoLowValueTokenIDCache().all())
    for chain in Chain:
        cache = {
            Chain.SOL: OnchainCoingeckoTokenIDSOLCache,
            Chain.ERC20: OnchainCoingeckoTokenIDETHCache,
            Chain.BSC: OnchainCoingeckoTokenIDBSCCache,
        }[chain]()
        count = 0
        for address, token_id in cache.all().items():
            if token_id not in low_value_token_ids:
                count += 1
        chain_count_map[chain] = count
    message = '\n'.join(f'{chain.name}: {count}' for chain, count in chain_count_map.items())
    send_alert_notice(
        f'*【热榜更新时查询Token的数量统计】*\n{message}',
        config['ADMIN_CONTACTS']['onchain_notice'],
    )
