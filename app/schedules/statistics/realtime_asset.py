#!/usr/bin/env python3
import json
from collections import defaultdict
from datetime import timedelta
from functools import cached_property
from itertools import chain
from typing import Dict, Any, List, Tuple, Optional
from decimal import Decimal

from celery.schedules import crontab
from sqlalchemy import and_, func

from app.business.statistic import get_user_account_balances
from app.business.user import UserRepository
from app.caches.statistics import PledgeStatisticsCache
from app.config import config
from app.assets import list_all_assets, try_get_asset_config
from app.business.market_maker import MarketMakerHelper
from app.caches.spot import SpotMarketDealCache
from app.common import PerpetualMarketType
from app.business import PriceManager, filter_active_users, lock_call
from app.business.external_dbs import TradeLogDB, PerpetualLogDB, ExchangeLogDB
from app.business.margin.helper import MarginAssetStatistic
from app.business.utils import yield_query_records_by_time_range
from app.caches import HashCache
from app.caches import PerpetualMarketCache
from app.caches.admin import RealTimeAssetStatisticCache, \
    RealtimeDepositWithdrawStatisticCache, AssetSummaryStatisticCache, \
    ActiveUserStatisticCache, MarketRealDealSummaryCache, MarketRealDealSpotAmountCache, \
    MarketRealDealContractAmountCache, SpotStageDepthCache, PerpetualHoursRealLeverageCache, \
    PerpetualStageDepthCache, MARKET_DATA_UPDATE_TIME, MarketRealDealSpotAmountHistoryCache, \
    BinancePerpetualStageDepthCache, OKXPerpetualStageDepthCache, HuobiPerpetualStageDepthCache, \
    BybitPerpetualStageDepthCache, KucoinPerpetualStageDepthCache, GatePerpetualStageDepthCache, \
    BitgetPerpetualStageDepthCache, PerpetualHoursTradeCache
from app.caches.margin import MarginAccountIdCache
from app.common import CeleryQueues, PrecisionEnum, AccountBalanceType
from app.models import Deposit, Withdrawal, AssetInvestmentConfig, UserTradeSummary, AssetTagRelation, AssetTag
from app.models.daily import DailyMarginAssetReport, DailyPledgeSiteReport
from app.models.margin import MarginLoanOrder
from app.models.spot import LoanStatistic
from app.models.user import User
from app.models.pledge import MIN_PLEDGE_ACCOUNT_ID, MAX_PLEDGE_ACCOUNT_ID, PledgePosition
from app.models import db
from app.utils import route_module_to_celery_queue, scheduled, amount_to_str, \
    current_timestamp, timestamp_to_datetime, datetime_to_time, today, now, group_by, quantize_amount, safe_div
from app.utils.date_ import yesterday
from app.utils.parser import JsonEncoder


route_module_to_celery_queue(__name__, CeleryQueues.STATISTIC)


@scheduled(crontab(minute="0", hour='*/1'))
@lock_call()
def update_real_time_asset_report_schedule():
    asset_user_balance, account_asset_user_balance = get_user_account_balances()
    asset_balance, all_user_set_map = defaultdict(Decimal), defaultdict(set)
    for asset, user_balances in asset_user_balance.items():
        asset_balance[asset] = sum(user_balances.values())
        all_user_set_map['ALL'] |= set(user_balances.keys())
        all_user_set_map[asset] |= set(user_balances.keys())
    account_asset_balance, account_all_user_set_map = defaultdict(lambda: defaultdict(Decimal)), defaultdict(lambda: defaultdict(set))
    for acc, asset_user_balances in account_asset_user_balance.items():
        for asset, user_balances in asset_user_balances.items():
            account_asset_balance[acc][asset] = sum(user_balances.values())
            account_all_user_set_map[acc]['ALL'] |= set(user_balances.keys())
            account_all_user_set_map[acc][asset] |= set(user_balances.keys())

    special_user_ids = set(_get_special_user_ids())
    all_maker_ids = set(MarketMakerHelper.list_all_maker_ids())
    outer_maker_ids = set(MarketMakerHelper.list_outer_maker_ids())

    internal_user_ids = UserRepository.get_internal_users()
    not_normal_user_ids = set(outer_maker_ids) | internal_user_ids

    min_amount_map = _get_threshold_map()
    asset_prices = PriceManager.assets_to_usd()

    all_tags = AssetTag.query.filter(
        AssetTag.status != AssetTag.StatusType.INVALID
    ).all()
    tag_ids = {v.id for v in all_tags}
    tag_query = AssetTagRelation.query.filter(
        AssetTagRelation.tag_id.in_(tag_ids),
        AssetTagRelation.status == AssetTagRelation.StatusType.PASSED,
    ).all()
    tag_assets_map = defaultdict(set)
    for v in tag_query:
        tag_assets_map[v.tag_id].add(v.asset)

    cache_cls = RealTimeAssetStatisticCache

    res_data_all = _build_real_time_asset_data(
        all_maker_ids, all_user_set_map['ALL'], asset_balance,
        asset_user_balance, min_amount_map, special_user_ids,
        not_normal_user_ids, outer_maker_ids, asset_prices,
    )
    timestamp = current_timestamp(to_int=True)
    res_data_all['timestamp'] = timestamp
    cache_cls(account_type="ALL").hmset(res_data_all)
    for tag_id, assets in tag_assets_map.items():
        all_user_set = set()
        for asset in assets:
            all_user_set |= all_user_set_map[asset]
        res_data_all = _build_real_time_asset_data(
            all_maker_ids, all_user_set, asset_balance,
            asset_user_balance, min_amount_map, special_user_ids,
            not_normal_user_ids, outer_maker_ids,
            asset_prices, assets
        )
        timestamp = current_timestamp(to_int=True)
        res_data_all['timestamp'] = timestamp
        cache_cls(account_type="ALL", tag_id=tag_id).hmset(res_data_all)

    for acc_name in [i.name for i in AccountBalanceType]:
        _acc_all_user_set = account_all_user_set_map[acc_name]['ALL']
        _acc_asset_balance = account_asset_balance[acc_name]
        _acc_asset_user_balance = account_asset_user_balance[acc_name]
        res_data_acc = _build_real_time_asset_data(
            all_maker_ids, _acc_all_user_set, _acc_asset_balance,
            _acc_asset_user_balance, min_amount_map, special_user_ids,
            not_normal_user_ids, outer_maker_ids, asset_prices,
        )
        res_data_acc['timestamp'] = timestamp
        cache_cls(account_type=acc_name).hmset(res_data_acc)

        for tag_id, assets in tag_assets_map.items():
            _acc_all_user_set = set()
            for asset in assets:
                _acc_all_user_set |= account_all_user_set_map[acc_name][asset]
            _acc_asset_balance = account_asset_balance[acc_name]
            _acc_asset_user_balance = account_asset_user_balance[acc_name]
            res_data_acc = _build_real_time_asset_data(
                all_maker_ids, _acc_all_user_set, _acc_asset_balance,
                _acc_asset_user_balance, min_amount_map, special_user_ids,
                not_normal_user_ids, outer_maker_ids,
                asset_prices, assets
            )
            res_data_acc['timestamp'] = timestamp
            cache_cls(account_type=acc_name, tag_id=tag_id).hmset(res_data_acc)


def _get_threshold_map():
    min_amount_map = defaultdict(Decimal)
    assets = list_all_assets()
    for asset in assets:
        if (config := try_get_asset_config(asset)) is None:
            min_amount_map[asset] = Decimal()
        else:
            min_amount_map[asset] = config.asset_user_min_amount or Decimal()

    return min_amount_map


def _get_special_user_ids() -> List[int]:
    user_ids = User.query.filter(User.location_code == 'CHN').with_entities(User.id).all()
    return [i.id for i in user_ids]


def _build_real_time_asset_data(
        all_maker_ids: set[int], all_user_set: set[int],
        asset_balance: dict[str, Decimal],
        asset_user_balance: dict[str, dict[int, Decimal]],
        min_amount_map: dict[str, Decimal],
        special_user_ids: set[int],
        not_normal_user_ids: set[int],
        outer_maker_ids: set[int],
        asset_prices: dict[str, Decimal],
        include_assets: set[str] = None,
) -> dict:
    all_maker_valid_user_count = 0
    all_special_valid_user_count = 0
    # 统计资产大于币种阈值的用户数
    valid_user_ids_map = defaultdict(set)
    all_valid_user_set = set()
    record = []
    for asset, user_balance in asset_user_balance.items():
        if include_assets and asset not in include_assets:
            continue
        price = asset_prices.get(asset, Decimal())
        threshold = min_amount_map[asset]
        valid_user_ids_map[asset] = {user_id for user_id, amount in user_balance.items() if amount >= threshold}
        all_valid_user_set.update(valid_user_ids_map[asset])

        maker_user_balance_map = {k: v for k, v in user_balance.items() if k in all_maker_ids and v >= threshold}
        maker_user_balance = sum(maker_user_balance_map.values())
        maker_user_balance_usd = maker_user_balance * price
        all_maker_valid_user_count += len(maker_user_balance_map)

        special_user_balance_map = {k: v for k, v in user_balance.items() if k in special_user_ids and v >= threshold}
        special_user_balance = sum(special_user_balance_map.values())
        special_user_balance_usd = special_user_balance * price
        all_special_valid_user_count += len(special_user_balance_map)

        normal_user_balance_map = {k: v for k, v in user_balance.items() if k not in not_normal_user_ids}
        normal_user_balance = sum(normal_user_balance_map.values())
        normal_user_balance_usd = normal_user_balance * price

        outer_maker_user_balance_map = {k: v for k, v in user_balance.items() if k in outer_maker_ids and v >= threshold}
        outer_maker_user_balance = sum(outer_maker_user_balance_map.values())
        outer_maker_user_balance_usd = outer_maker_user_balance * price

        record.append(
            {
                'user_count': len(user_balance),
                'valid_user_count': len(valid_user_ids_map[asset]),
                'balance': asset_balance[asset],
                'balance_usd': asset_balance[asset] * price,
                'special_valid_user_count': len(special_user_balance_map),
                'special_user_balance': special_user_balance,
                'special_user_balance_usd': special_user_balance_usd,
                'maker_valid_user_count': len(maker_user_balance_map),
                'maker_user_balance': maker_user_balance,
                'maker_user_balance_usd': maker_user_balance_usd,
                'normal_user_balance': normal_user_balance,
                'normal_user_balance_usd': normal_user_balance_usd,
                'outer_maker_user_balance': outer_maker_user_balance,
                'outer_maker_user_balance_usd': outer_maker_user_balance_usd,
                'asset': asset,
             }
        )
    # 饼图
    record.sort(key=lambda x: x['balance_usd'], reverse=True)
    all_balance_usd = sum([i['balance_usd'] for i in record])
    all_special_balance_usd = sum([i['special_user_balance_usd'] for i in record])
    all_normal_user_balance_usd = sum([i['normal_user_balance_usd'] for i in record])
    all_outer_maker_user_balance_usd = sum([i['outer_maker_user_balance_usd'] for i in record])
    pie_limit = 20
    pie_chart_data = [[i['asset'], amount_to_str(i['balance_usd'], PrecisionEnum.CASH_PLACES)] for i in
                      record[:pie_limit]]
    pie_chart_data.append(
        ['other', amount_to_str(sum([i['balance_usd'] for i in record[pie_limit:]]), PrecisionEnum.CASH_PLACES)])
    for item in record:
        item['balance_proportion'] = amount_to_str(item['balance_usd'] / all_balance_usd \
                                                   if all_balance_usd else 0, PrecisionEnum.COIN_PLACES)
        item['balance'] = amount_to_str(item['balance'], PrecisionEnum.COIN_PLACES)
        item['special_user_balance'] = amount_to_str(item['special_user_balance'], PrecisionEnum.COIN_PLACES)
        item['balance_usd'] = amount_to_str(item['balance_usd'], PrecisionEnum.CASH_PLACES)
        item['special_user_balance_usd'] = amount_to_str(item['special_user_balance_usd'], PrecisionEnum.CASH_PLACES)
        item['maker_user_balance'] = amount_to_str(item['maker_user_balance'], PrecisionEnum.COIN_PLACES)
        item['maker_user_balance_usd'] = amount_to_str(item['maker_user_balance_usd'], PrecisionEnum.CASH_PLACES)
        item['normal_user_balance'] = amount_to_str(item['normal_user_balance'], PrecisionEnum.COIN_PLACES)
        item['normal_user_balance_usd'] = amount_to_str(item['normal_user_balance_usd'], PrecisionEnum.CASH_PLACES)
        item['outer_maker_user_balance'] = amount_to_str(item['outer_maker_user_balance'], PrecisionEnum.COIN_PLACES)
        item['outer_maker_user_balance_usd'] = amount_to_str(item['outer_maker_user_balance_usd'], PrecisionEnum.CASH_PLACES)

    res_data = {
        'all_balance_usd': amount_to_str(all_balance_usd, PrecisionEnum.CASH_PLACES),
        'all_special_balance_usd': amount_to_str(all_special_balance_usd, PrecisionEnum.CASH_PLACES),
        'all_user_count': len(all_user_set),
        'all_valid_user_count': len(all_valid_user_set),
        'all_special_valid_user_count': all_special_valid_user_count,
        'all_maker_valid_user_count': all_maker_valid_user_count,
        'all_normal_user_balance_usd': amount_to_str(all_normal_user_balance_usd, PrecisionEnum.CASH_PLACES),
        'all_outer_maker_user_balance_usd': amount_to_str(all_outer_maker_user_balance_usd, PrecisionEnum.CASH_PLACES),
        'pie_chart_data': json.dumps(pie_chart_data),
        'data': json.dumps(record),
    }
    return res_data


def reload_deposit_withdraw_statistic_cache():
    now = current_timestamp()
    start_time_map = {
        '15m': timestamp_to_datetime(now - 15 * 60),
        '30m': timestamp_to_datetime(now - 30 * 60),
        '1h': timestamp_to_datetime(now - 1 * 3600),
        '6h': timestamp_to_datetime(now - 6 * 3600),
        '1d': timestamp_to_datetime(now - 1 * 86400),
        '3d': timestamp_to_datetime(now - 3 * 86400),
        '7d': timestamp_to_datetime(now - 7 * 86400),
        '30d': timestamp_to_datetime(now - 30 * 86400),
    }
    start_time = start_time_map['30d']

    data = defaultdict(lambda: defaultdict(lambda: {
        'withdraw_user_set': set(),
        'withdraw_count': int(),
        'withdraw_amount': Decimal(),
        'deposit_user_set': set(),
        'deposit_count': int(),
        'deposit_amount': Decimal(),
    }))

    for item in yield_query_records_by_time_range(
            Withdrawal, None, None,
            [Withdrawal.updated_at, Withdrawal.user_id, Withdrawal.amount, Withdrawal.asset],
            and_(
                Withdrawal.type == Withdrawal.Type.ON_CHAIN,
                Withdrawal.status.in_([
                    Withdrawal.Status.PROCESSING,
                    Withdrawal.Status.CONFIRMING,
                    Withdrawal.Status.FINISHED
                ]),
                Withdrawal.updated_at >= start_time
            ),
    ):
        for time_type, type_start_time in start_time_map.items():
            if item.updated_at >= type_start_time:
                coin_info = data[time_type][item.asset]
                coin_info['withdraw_user_set'].add(item.user_id)
                coin_info['withdraw_count'] += 1
                coin_info['withdraw_amount'] += item.amount

    for item in yield_query_records_by_time_range(
            Deposit, start_time, None,
            [Deposit.created_at, Deposit.user_id, Deposit.amount, Deposit.asset],
            and_(
                Deposit.type == Deposit.Type.ON_CHAIN,
                Deposit.status.in_([
                    Deposit.Status.CONFIRMING,
                    Deposit.Status.FINISHED,
                    Deposit.Status.TO_HOT
                ])
            ),
    ):
        for time_type, type_start_time in start_time_map.items():
            if item.created_at >= type_start_time:
                coin_info = data[time_type][item.asset]
                coin_info['deposit_user_set'].add(item.user_id)
                coin_info['deposit_count'] += 1
                coin_info['deposit_amount'] += item.amount

    pool_user_id = config['CLIENT_CONFIGS']['viabtc_pool']['user_id']
    for item in yield_query_records_by_time_range(
            Deposit, start_time, None,
            [Deposit.created_at, Deposit.user_id, Deposit.amount, Deposit.asset],
            and_(
                Deposit.type == Deposit.Type.LOCAL,
                Deposit.sender_user_id == pool_user_id,
                Deposit.status.in_(
                    [
                        Deposit.Status.CONFIRMING,
                        Deposit.Status.FINISHED,
                        Deposit.Status.TO_HOT,
                    ]
                ),
            ),
    ):
        for time_type, type_start_time in start_time_map.items():
            if item.created_at >= type_start_time:
                coin_info = data[time_type][item.asset]
                coin_info['deposit_user_set'].add(item.user_id)
                coin_info['deposit_count'] += 1
                coin_info['deposit_amount'] += item.amount

    result = defaultdict(list)
    for asset in list_all_assets():
        asset_price = PriceManager.asset_to_usd(asset)
        for time_type in start_time_map.keys():
            asset_data = data[time_type].get(asset)
            if not asset_data:
                continue

            result[time_type].append({
                'asset': asset,
                'withdraw_user_count': len(asset_data.get('withdraw_user_set', set())),
                'withdraw_count': asset_data.get('withdraw_count', 0),
                'withdraw_amount': amount_to_str(asset_data.get('withdraw_amount', Decimal('0')), 8),
                'withdraw_usd': amount_to_str(asset_data.get('withdraw_amount', Decimal('0')) * asset_price, 2),
                'deposit_user_count': len(asset_data.get('deposit_user_set', set())),
                'deposit_count': asset_data.get('deposit_count', 0),
                'deposit_amount': amount_to_str(asset_data.get('deposit_amount', Decimal('0')), 8),
                'deposit_usd': amount_to_str(asset_data.get('deposit_amount', Decimal('0')) * asset_price, 2),
            })

    for time_type in DEPOSIT_WITHDRAW_STATISTIC_TIME_TYPES:
        ret = {
            'timestamp': current_timestamp(),
            'data': result[time_type]
        }
        RealtimeDepositWithdrawStatisticCache(time_type).set(json.dumps(ret))


DEPOSIT_WITHDRAW_STATISTIC_TIME_TYPES = ['15m', '30m', '1h', '6h', '1d', '3d', '7d', '30d']


@scheduled(crontab(minute='*/15'))
@lock_call()
def update_market_real_deal_schedule():
    reload_deposit_withdraw_statistic_cache()


@scheduled(crontab(minute=0, hour='*/1'))
def update_real_time_margin_asset_report_schedule():
    MarginAssetStatistic.save_real_time_total_balance()


@scheduled(crontab(minute=5, hour='*/1'))
def update_asset_summary_report_schedule():
    """资产统计
    {
        spot: {
            total: '0'
            available: '0',
            available_count: 1,
            frozen,
            lock,
            ...
        },
        investment: ...,
        margin: ...,
        perpetual: ...,
        all: ...
        timestamp: 1599728400
    }
    """

    def _multi_dict(kvs):
        result = {}
        for k, v in kvs:
            if (lis := result.get(k)) is None:
                lis = result[k] = []
            lis.append(v)
        return result

    def _to_result(balances, counts, spot_or_perpetual) -> Dict[str, Dict[str, Any]]:
        result = defaultdict(lambda: dict(total=Decimal(), asset='',
                                          available=Decimal(), frozen=Decimal(), lock=Decimal(),
                                          available_count=0, frozen_count=0,
                                          lock_count=0))
        counts = {(x,y): z for x,y,z in counts}
        for asset, t, balance in balances:
            result[asset]['total'] += balance
            result[asset]['asset'] = asset
            field = ''
            if spot_or_perpetual == 'spot':
                if t == 1:
                    field = 'available'
                elif t == 2:
                    field = 'frozen'
                elif t == 3:
                    field = 'lock'
            elif spot_or_perpetual == 'perpetual':
                if t == 1 or t == 2:
                    field = 'available'
                elif t == 3 or t == 4:
                    field = 'frozen'
            if field != '':
                result[asset][field] += balance
                result[asset][f'{field}_count'] = max(result[asset][f'{field}_count'], counts.get((asset, t), 0))
        return result

    result = {}
    # 现货、理财、杠杆、合约资产
    slice_time = TradeLogDB.get_slice_history_timestamp()
    spot_table = TradeLogDB.slice_balance_table(slice_time)
    margin_accounts = MarginAccountIdCache.list_online_markets().keys()
    for key, where in [('spot', 'account=0'),
                       ('investment', f'account={AssetInvestmentConfig.ACCOUNT_ID}'),
                       ('margin', 'account in ({})'.format(','.join(str(x) for x in margin_accounts))),
                       ('pledge', f"account >= {MIN_PLEDGE_ACCOUNT_ID} and account <= {MAX_PLEDGE_ACCOUNT_ID}"),
                      ]:
        balances = spot_table.select("asset", "t", "sum(balance)", where=where, group_by="asset, t")
        counts = spot_table.select("asset", "t", "count(distinct user_id)", where=where, group_by="asset, t")
        result[key] = _to_result(balances, counts, 'spot')

    perpetual_table = PerpetualLogDB.slice_balance_table()
    balances = PerpetualLogDB.get_balances(
        "asset", "type", "sum(balance)",
        group_by="asset, type",
    )

    counts = PerpetualLogDB.get_balances(
        "asset", "type", "count(distinct user_id)",
        group_by="asset, type"
    )

    result['perpetual'] = _to_result(balances, counts, 'perpetual')
    # 所有账户汇总资产
    all_data = {}
    m = _multi_dict(chain.from_iterable((x.items() for x in result.values())))
    for asset, datas in m.items():
        info = {'asset': asset}
        available = frozen = lock = 0
        for data in datas:
            available += data.get('available', 0)
            frozen += data.get('frozen', 0)
            lock += data.get('lock', 0)
        info['available'] = available
        info['frozen'] = frozen
        info['lock'] = lock
        info['total'] = available + frozen + lock
        all_data[asset] = info

    available_users = _multi_dict(spot_table.select("distinct asset,user_id", where="t=1"))
    frozen_users = _multi_dict(spot_table.select("distinct asset,user_id", where="t=2"))
    lock_users = _multi_dict(spot_table.select("distinct asset,user_id", where="t=3"))
    perpetual_available_users = _multi_dict(perpetual_table.select("distinct asset,user_id", where="type=1 or type=2"))
    perpetual_frozen_users = _multi_dict(perpetual_table.select("distinct asset,user_id", where="type=3 or type=4"))
    for asset, data in all_data.items():
        data['available_count'] = len(set(available_users.get(asset, [])) | set(perpetual_available_users.get(asset, [])))
        data['frozen_count'] = len(set(frozen_users.get(asset, [])) | set(perpetual_frozen_users.get(asset, [])))
        data['lock_count'] = len(lock_users.get(asset, []))

    result['all'] = all_data
    #  format decimal to str
    for d in result.values():
        for data in d.values():
            for k,v in data.items():
                if isinstance(v, Decimal):
                    data[k] = amount_to_str(v, PrecisionEnum.COIN_PLACES.value)

    result = {k: json.dumps(v) for k,v in result.items()}
    result['timestamp'] = slice_time

    AssetSummaryStatisticCache().hmset(result)


@scheduled(crontab(minute=0, hour=1))
def update_active_user_statistic_schedule():
    """
    最近30天活跃用户统计
    """
    days_list = ['1', '1-3', '3-7', '7-15', '15']

    def _get_result_map(count_map):
        result_map = defaultdict(int)
        for count in count_map.values():
            if count <= 1:
                key = days_list[0]
            elif count <= 3:
                key = days_list[1]
            elif count <= 7:
                key = days_list[2]
            elif count <= 15:
                key = days_list[3]
            else:
                key = days_list[4]
            result_map[key] += 1
        return result_map

    end = today()
    start = end - timedelta(days=30)
    # 活跃用户数
    curr = start
    active_count_map, active_map = defaultdict(int), defaultdict(int)
    while curr < end:
        active_user_ids = filter_active_users(curr, curr)
        for id_ in active_user_ids:
            active_count_map[id_] += 1
        curr += timedelta(days=1)
    total_active_user_count = len(active_count_map)
    active_map = _get_result_map(active_count_map)

    # 交易/现货/合约 用户数
    trade_count_map, spot_count_map, perpetual_count_map = \
        defaultdict(int), defaultdict(int), defaultdict(int)
    trade_summary_query = UserTradeSummary.query.filter(
        UserTradeSummary.report_date >= start,
        UserTradeSummary.report_date < end
    ).with_entities(
        UserTradeSummary.report_date,
        UserTradeSummary.system,
        UserTradeSummary.user_id
    )
    seen_trade_user_map = defaultdict(set)
    for summary in trade_summary_query.all():
        if summary.user_id not in seen_trade_user_map[summary.report_date]:
            seen_trade_user_map[summary.user_id].add(summary.user_id)
            trade_count_map[summary.user_id] += 1
        if summary.system == UserTradeSummary.System.SPOT:
            spot_count_map[summary.user_id] += 1
        else:
            perpetual_count_map[summary.user_id] += 1

    # 交易用户
    trade_map = _get_result_map(trade_count_map)
    # 现货
    spot_map = _get_result_map(spot_count_map)
    # 合约
    perpetual_map = _get_result_map(perpetual_count_map)
    fields = ('active_days', 'active_users', 'active_user_percentage',
              'trade_users', 'trade_user_percentage', 'spot_users',
              'spot_user_percentage', 'perpetual_users', 'perpetual_user_percentage')
    result_list = []
    total_trade_user_count = len(trade_count_map)
    total_spot_user_count = len(spot_count_map)
    total_perpetual_user_count = len(perpetual_count_map)
    for day in days_list:
        result = [day,
                  active_map[day],
                  active_map[day] / total_active_user_count
                    if total_active_user_count > 0 else 0,
                  trade_map[day],
                  trade_map[day] / total_trade_user_count
                    if total_trade_user_count > 0 else 0,
                  spot_map[day],
                  spot_map[day] / total_spot_user_count
                    if total_spot_user_count > 0 else 0,
                  perpetual_map[day],
                  perpetual_map[day] / total_perpetual_user_count
                    if total_perpetual_user_count > 0 else 0,
                  ]
        result_list.append({k: v for k, v in zip(fields, result)})

    cache = ActiveUserStatisticCache()
    cache.hmset({
        'timestamp': datetime_to_time(end),
        'data': json.dumps(result_list)
    })


@scheduled(crontab(minute='*/30'))
@lock_call()
def update_real_time_deal_statistic_schedule():
    """实时成交"""
    MarketRealDealSpotAmountCache().reload()
    MarketRealDealSummaryCache().reload()


@scheduled(crontab(minute='*/30'))
@lock_call()
def update_contract_real_deal_schedule():
    MarketRealDealContractAmountCache().reload()



@scheduled(crontab(minute='*/10'))
@lock_call()
def update_market_deal_history_schedule():
    # 历史实时成交
    ts = current_timestamp(to_int=True)
    hour_ts = ts - ts % MARKET_DATA_UPDATE_TIME
    MarketRealDealSpotAmountHistoryCache(hour_ts).reload()
    SpotMarketDealCache.reload()


@scheduled(crontab(minute=10, hour='*/1'))
def update_perpetual_real_leverage_schedule():
    """合约实时交易数据"""
    n = now()
    PerpetualHoursRealLeverageCache(n.date()).reload(n.hour)
    PerpetualHoursTradeCache(n.date()).reload(n.hour)


class RealTimeDepthCalculator(object):
    stage_list = [Decimal("0.002"), Decimal("0.005"), Decimal("0.01"), Decimal("0.02"), Decimal("0.05")]
    real_dept_cache: HashCache

    def save_to_cache(self, data: Dict):
        self.real_dept_cache.hmset({
            "update_time": current_timestamp(),
            "data": json.dumps(data, cls=JsonEncoder)
        })

    def get_depth_usd(self, price: Decimal, amount: Decimal, _data: Dict, _extra: Dict = None):
        raise NotImplementedError

    @classmethod
    def get_snapshot_ts(cls) -> Tuple[int, int]:
        current_ts = current_timestamp(to_int=True)
        today_ts = current_ts - current_ts % 86400
        # 五分钟前, 五分钟一个备份数据
        snapshot_ts = current_ts - current_ts % 300 - 300
        return today_ts, snapshot_ts

    def get_market_stage_data(self, _market_depth_list: List):
        stage_depth_dict = dict()
        # 升序排列
        _market_depth_list = sorted(_market_depth_list, key=lambda x: Decimal(x["depth"]))
        for _stage in self.stage_list:
            # json dumps无法处理 key是Decimal类型的情况
            _stage_key = str(_stage)
            for index, _depth_data in enumerate(_market_depth_list):
                _data = json.loads(_depth_data["data"])
                extra = {
                    'contract_size': _data.get('contract_size', 1),
                    'contract_mult': _data.get('contract_mult', 1),
                }
                if not _data or "last" not in _data:
                    continue
                last_price = Decimal(_data["last"])
                _depth = Decimal(_depth_data["depth"])
                # (max, min)
                price_range = (last_price * (Decimal('1') + _stage),
                               last_price * (Decimal('1') - _stage))
                # 按照深度格式化(max, min)
                fix_price_range = (price_range[0] - price_range[0] % _depth + _depth,
                                   price_range[1] - price_range[1] % _depth)
                # 从大到小排序
                _depths = sorted(_data["asks"] + _data["bids"], reverse=True, key=lambda item: Decimal(item[0]))
                # (max, min)
                if len(_depths) == 0:
                    break
                _depth_range = (Decimal(_depths[0][0]), Decimal(_depths[-1][0]))

                def check_range_cover(_range1: Tuple[Decimal, Decimal],
                                      _range2: Tuple[Decimal, Decimal]) -> bool:
                    return _range1[0] >= _range2[0] and _range1[1] <= _range2[1]

                def check_price_in_range(_price: Decimal,
                                         _range: Tuple[Decimal, Decimal]) -> bool:
                    return _range[0] >= _price >= _range[1]

                _take_range = None
                if check_range_cover(_depth_range, fix_price_range):
                    _take_range = fix_price_range
                if _take_range is not None:
                    stage_depth_dict[_stage_key] = sum([
                        self.get_depth_usd(Decimal(v[0]), Decimal(v[1]), _depth_data, extra)
                        for v in
                        list(filter(lambda x: check_price_in_range(Decimal(x[0]), _take_range), _depths))])
                    stage_depth_dict[_stage_key] = quantize_amount(stage_depth_dict[_stage_key],
                                                                   PrecisionEnum.CASH_PLACES)
                    break

                def get_same_interval(_range1: Tuple[Decimal, Decimal],
                                      _range2: Tuple[Decimal, Decimal]
                                      ) -> Optional[Tuple[Decimal, Decimal]]:

                    min_max = max(_range1[1], _range2[1])
                    max_min = min(_range1[0], _range2[0])
                    return (max_min, min_max) if max_min >= min_max else None

                if index == len(_market_depth_list) - 1:
                    take_range = get_same_interval(fix_price_range, _depth_range) or fix_price_range
                    stage_depth_dict[_stage_key] = sum([
                        self.get_depth_usd(Decimal(v[0]), Decimal(v[1]), _depth_data, extra)
                        for v in
                        list(filter(lambda x: check_price_in_range(Decimal(x[0]), take_range), _depths))])
                    stage_depth_dict[_stage_key] = quantize_amount(stage_depth_dict[_stage_key],
                                                                   PrecisionEnum.CASH_PLACES)
                    break

        return stage_depth_dict

    def get_depth_snapshot_data(self, table_ts: int, snapshot_ts: int):
        raise NotImplementedError

    def get_stage_depth_data(self):
        table_ts, snapshot_ts = self.get_snapshot_ts()
        group_items = self.get_depth_snapshot_data(table_ts, snapshot_ts)
        result = {}
        for market, _depth_data in group_items.items():
            market_data = self.get_market_stage_data(_depth_data)
            if market_data:
                result[market] = market_data
        return result

    def get_bid_ask_delta_amount(self, _market_depth_list: List) -> Decimal:
        if not _market_depth_list:
            return Decimal()
        _market_depth_list = sorted(_market_depth_list, key=lambda x: Decimal(x["depth"]))
        data = _market_depth_list[0]

        _data = json.loads(data["data"])
        asks, bids = _data["asks"], _data["bids"]
        if not asks or not bids:
            return Decimal()
        
        # 卖一价, 买一价
        ask_1, bid_1 = Decimal(asks[0][0]), Decimal(bids[0][0])
        # （卖一价-买一价）/[(卖一价+买一价)/2]
        return (ask_1 - bid_1) / ((ask_1 + bid_1) / 2)

class SpotRealTimeDepthCalculator(RealTimeDepthCalculator):
    real_dept_cache = SpotStageDepthCache()

    def get_depth_usd(self, price: Decimal, amount: Decimal, _data: Dict, _extra: Dict = None):
        return price * amount * _data["quote_asset_rate"]

    def get_depth_snapshot_data(self, table_ts: int, snapshot_ts: int):
        table = ExchangeLogDB.spot_depth_snapshot_table(table_ts)
        fields = ("depth", "market", "base_asset_rate", "quote_asset_rate",
                  "data", "snapshot_ts")
        results = table.select(*fields, where=f"`snapshot_ts` = {snapshot_ts}")
        items = [dict(zip(fields, i)) for i in results]
        group_items = group_by(lambda x: x["market"], items)
        result = dict()
        for market, _data in group_items.items():
            result[market] = _data
        return result


class PerpetualRealTimeDepthCalculator(RealTimeDepthCalculator):
    real_dept_cache = PerpetualStageDepthCache()

    def get_depth_usd(self, price: Decimal, amount: Decimal, _data: Dict, _extra: Dict = None):
        market = _data["market"]
        if market in self.all_reverse_markets:
            return amount
        return price * amount * Decimal(_data["money_rate"])

    @cached_property
    def all_reverse_markets(self):
        markets = [market for market, v in PerpetualMarketCache().read_aside().items()
                   if v["type"] == PerpetualMarketType.INVERSE]
        return markets

    def get_depth_snapshot_data(self, table_ts: int, snapshot_ts: int):
        table = ExchangeLogDB.perpetual_depth_snapshot_table(table_ts)
        fields = ("depth", "market", "money_rate", "stock_rate",
                  "data", "snapshot_ts")
        results = table.select(*fields, where=f"`snapshot_ts` = {snapshot_ts}")
        items = [dict(zip(fields, i)) for i in results]
        group_items = group_by(lambda x: x["market"], items)
        result = dict()
        for market, _data in group_items.items():
            result[market] = _data
        return result


class ThirdPerpetualRealTimeDepthCalculator(RealTimeDepthCalculator):

    def get_depth_usd(self, price: Decimal, amount: Decimal, _data: Dict, _extra: Dict = None):
        market = _data["market"]
        if market in self.all_reverse_markets:
            return amount
        return price * amount * Decimal(_data["money_rate"])

    @cached_property
    def all_reverse_markets(self):
        markets = [market for market, v in PerpetualMarketCache().read_aside().items()
                   if v["type"] == PerpetualMarketType.INVERSE]
        return markets

    def get_depth_snapshot_data(self, table_ts: int, snapshot_ts: int):
        table = self.get_table(table_ts)
        fields = ("depth", "market", "money_rate", "stock_rate",
                  "data", "snapshot_ts")
        results = table.select(*fields, where=f"`snapshot_ts` = {snapshot_ts}")
        items = [dict(zip(fields, i)) for i in results]
        group_items = group_by(lambda x: x["market"], items)
        result = dict()
        for market, _data in group_items.items():
            result[market] = _data
        return result

    def get_table(self, table_ts):
        raise NotImplementedError


class BinancePerpetualRealTimeDepthCalculator(ThirdPerpetualRealTimeDepthCalculator):
    real_dept_cache = BinancePerpetualStageDepthCache()
    tag = 'Binance'

    def get_depth_usd(self, price: Decimal, amount: Decimal, _data: Dict, _extra: Dict = None):
        contract_size = 1
        if _extra:
            contract_size = Decimal(_extra['contract_size'])
        market = _data["market"]
        if market in self.all_reverse_markets:
            return amount * contract_size
        return price * amount * Decimal(_data["money_rate"])

    def get_table(self, table_ts):
        return ExchangeLogDB.third_exchange_perpetual_depth_snapshot_table(table_ts, self.tag)


class OKXPerpetualRealTimeDepthCalculator(ThirdPerpetualRealTimeDepthCalculator):
    real_dept_cache = OKXPerpetualStageDepthCache()
    tag = 'OKX'

    def get_depth_usd(self, price: Decimal, amount: Decimal, _data: Dict, _extra: Dict = None):
        contract_size = 1
        if _extra:
            contract_size = Decimal(_extra['contract_size'])
        market = _data["market"]
        if market in self.all_reverse_markets:
            return amount * contract_size
        return price * amount * Decimal(_data["money_rate"]) * contract_size  # contract_mult 是误导

    def get_table(self, table_ts):
        return ExchangeLogDB.third_exchange_perpetual_depth_snapshot_table(table_ts, self.tag)


class HuobiPerpetualRealTimeDepthCalculator(ThirdPerpetualRealTimeDepthCalculator):
    real_dept_cache = HuobiPerpetualStageDepthCache()
    tag = 'Huobi'

    def get_depth_usd(self, price: Decimal, amount: Decimal, _data: Dict, _extra: Dict = None):
        contract_size = contract_mult = 1
        if _extra:
            contract_size = contract_mult = Decimal(_extra['contract_size'])
        market = _data["market"]
        if market in self.all_reverse_markets:
            return amount * contract_size
        return price * amount * Decimal(_data["money_rate"]) * contract_mult

    def get_table(self, table_ts):
        return ExchangeLogDB.third_exchange_perpetual_depth_snapshot_table(table_ts, self.tag)


class BybitPerpetualRealTimeDepthCalculator(ThirdPerpetualRealTimeDepthCalculator):
    real_dept_cache = BybitPerpetualStageDepthCache()
    tag = 'Bybit'

    def get_table(self, table_ts):
        return ExchangeLogDB.third_exchange_perpetual_depth_snapshot_table(table_ts, self.tag)


class KucoinPerpetualRealTimeDepthCalculator(ThirdPerpetualRealTimeDepthCalculator):
    real_dept_cache = KucoinPerpetualStageDepthCache()
    tag = 'Kucoin'

    def get_depth_usd(self, price: Decimal, amount: Decimal, _data: Dict, _extra: Dict = None):
        contract_mult = 1
        if _extra:
            contract_mult = Decimal(_extra['contract_mult'])
        market = _data["market"]
        if market in self.all_reverse_markets:
            return amount
        return price * amount * Decimal(_data["money_rate"]) * contract_mult

    def get_table(self, table_ts):
        return ExchangeLogDB.third_exchange_perpetual_depth_snapshot_table(table_ts, self.tag)


class GatePerpetualRealTimeDepthCalculator(ThirdPerpetualRealTimeDepthCalculator):
    real_dept_cache = GatePerpetualStageDepthCache()
    tag = 'Gate'

    def get_depth_usd(self, price: Decimal, amount: Decimal, _data: Dict, _extra: Dict = None):
        contract_mult = 1
        if _extra:
            contract_mult = Decimal(_extra['contract_mult'])
        market = _data["market"]
        if market in self.all_reverse_markets:
            return amount
        return price * amount * Decimal(_data["money_rate"]) * contract_mult

    def get_table(self, table_ts):
        return ExchangeLogDB.third_exchange_perpetual_depth_snapshot_table(table_ts, self.tag)


class BitgetPerpetualRealTimeDepthCalculator(ThirdPerpetualRealTimeDepthCalculator):
    real_dept_cache = BitgetPerpetualStageDepthCache()
    tag = 'Bitget'

    def get_depth_usd(self, price: Decimal, amount: Decimal, _data: Dict, _extra: Dict = None):
        market = _data["market"]
        if market in self.all_reverse_markets:
            return price * amount
        return price * amount * Decimal(_data["money_rate"])

    def get_table(self, table_ts):
        return ExchangeLogDB.third_exchange_perpetual_depth_snapshot_table(table_ts, self.tag)


THIRD_CALCULATORS = {
    BinancePerpetualRealTimeDepthCalculator.tag: BinancePerpetualRealTimeDepthCalculator,
    OKXPerpetualRealTimeDepthCalculator.tag: OKXPerpetualRealTimeDepthCalculator,
    HuobiPerpetualRealTimeDepthCalculator.tag: HuobiPerpetualRealTimeDepthCalculator,
    BybitPerpetualRealTimeDepthCalculator.tag: BybitPerpetualRealTimeDepthCalculator,
    KucoinPerpetualRealTimeDepthCalculator.tag: KucoinPerpetualRealTimeDepthCalculator,
    GatePerpetualRealTimeDepthCalculator.tag: GatePerpetualRealTimeDepthCalculator,
    BitgetPerpetualRealTimeDepthCalculator.tag: BitgetPerpetualRealTimeDepthCalculator,
}


@scheduled(crontab(minute='52', hour='0-4'))
@lock_call()
def update_loan_statistic_schedule():
    
    def has_created():
        today_ = today()
        rec = LoanStatistic.query.filter(LoanStatistic.report_date == today_).first()
        return bool(rec)
    
    def data_ready():
        yesterday_ = yesterday()
        margin_rec = DailyMarginAssetReport.query.filter(DailyMarginAssetReport.report_date == yesterday_).first()
        pledge_rec = DailyPledgeSiteReport.query.filter(DailyPledgeSiteReport.report_date == yesterday_).first()
        return margin_rec and pledge_rec

    if has_created():
        return
    if not data_ready():
        return
    price_map = PriceManager.assets_to_usd()
    margin_users, margin_value_map, asset_hold_map, margin_interest_data = get_margin_data(price_map)
    # 杠杆的有效抵押市值=抵押市值
    pledge_users, borrowed_value_map, pledge_value_map, pledge_valid_value_map, pledge_interest_data = get_pledge_data(price_map)
    margin_assets = aggravate_margin_pledge_data(margin_users, margin_value_map, asset_hold_map,
                                 asset_hold_map, margin_interest_data, LoanStatistic.LoanType.MARGIN)
    pledge_assets = aggravate_margin_pledge_data(pledge_users, borrowed_value_map,
                                 pledge_value_map, pledge_valid_value_map, pledge_interest_data, LoanStatistic.LoanType.PLEDGE)
    all_assets = margin_assets | pledge_assets
    aggravate_all_data(all_assets, margin_users, margin_value_map, asset_hold_map, asset_hold_map, margin_interest_data,
                       pledge_users, borrowed_value_map, pledge_value_map, pledge_valid_value_map, pledge_interest_data)
    db.session.commit()
    

def get_margin_data(price_map):
    margin_users = defaultdict(set)
    margin_value_map = defaultdict(Decimal)
    assets = set()
    records = MarginLoanOrder.query.filter(
        MarginLoanOrder.status.in_([
            MarginLoanOrder.StatusType.PASS,
            MarginLoanOrder.StatusType.BURST,
            MarginLoanOrder.StatusType.ARREARS]
        )).all()
    
    for record in records:
        asset = record.asset
        user_id = record.user_id
        rate = price_map.get(asset, 0)
        margin_users[asset].add(user_id)
        margin_value_map[asset] += record.unflat_amount * rate
        assets.add(asset)
    asset_hold_map = get_asset_hold_map(price_map)
    margin_interest_data = get_margin_interest_data(assets)
    return margin_users, margin_value_map, asset_hold_map, margin_interest_data


def get_asset_hold_map(price_map: dict):
    res = defaultdict(Decimal)
    recs = TradeLogDB.get_balances_group_by_asset()
    for asset, balance in recs.items():
        rate = price_map.get(asset, 0)
        res[asset] += balance * rate
    return res


def get_margin_interest_data(assets: set[str]):
    end = today()
    last_7_days = end - timedelta(days=7)
    last_30_days = end - timedelta(days=30)
    last_90_days = end - timedelta(days=90)
    last_180_days = end - timedelta(days=180)
    last_365_days = end - timedelta(days=365)
    
    interest_7d = get_daily_margin_interest_data(last_7_days, end, assets)
    interest_30d = get_daily_margin_interest_data(last_30_days, end, assets)
    interest_90d = get_daily_margin_interest_data(last_90_days, end, assets)
    interest_180d = get_daily_margin_interest_data(last_180_days, end, assets)
    interest_365d = get_daily_margin_interest_data(last_365_days, end, assets)
    res = defaultdict(lambda: {
        'interest_7d_usd': Decimal(),
        'interest_30d_usd': Decimal(),
        'interest_90d_usd': Decimal(),
        'interest_180d_usd': Decimal(),
        'interest_365d_usd': Decimal(),
    })
    for asset, data in interest_7d.items():
        res[asset]['interest_7d_usd'] = data
    for asset, data in interest_30d.items():
        res[asset]['interest_30d_usd'] = data
    for asset, data in interest_90d.items():
        res[asset]['interest_90d_usd'] = data
    for asset, data in interest_180d.items():
        res[asset]['interest_180d_usd'] = data
    for asset, data in interest_365d.items():
        res[asset]['interest_365d_usd'] = data
    return res
    

def get_daily_margin_interest_data(start, end, assets: set[str]):
    recs = DailyMarginAssetReport.query.filter(
        DailyMarginAssetReport.report_date < end,
        DailyMarginAssetReport.report_date >= start,
        DailyMarginAssetReport.asset.in_(assets),
    ).with_entities(
        DailyMarginAssetReport.asset,
        func.sum(DailyMarginAssetReport.interest_usd).label("sum_interest"),
    ).group_by(DailyMarginAssetReport.asset).all()
    return {record.asset: record.sum_interest for record in recs}


def get_pledge_data(price_map):
    pledge_users, borrowed_value_map = get_pledge_user_map(price_map)
    usdt_rate = price_map.get('USDT', 0)
    pledge_value_map, pledge_valid_value_map = get_pledge_value_map(usdt_rate)
    assets = set(borrowed_value_map.keys())
    pledge_interest_data = get_pledge_interest_data(assets)
    return pledge_users, borrowed_value_map, pledge_value_map, pledge_valid_value_map, pledge_interest_data


def get_pledge_user_map(price_map):
    pledge_users = defaultdict(set)
    borrowed_value_map = defaultdict(Decimal)
    records = PledgePosition.query.filter(
        PledgePosition.status.in_(PledgePosition.ACTIVE_STATUSES),
    ).all()
    
    for record in records:
        asset = record.loan_asset
        user_id = record.user_id
        rate = price_map.get(asset, 0)
        pledge_users[asset].add(user_id)
        borrowed_value_map[asset] += record.debt_amount * rate
    return pledge_users, borrowed_value_map


def get_pledge_value_map(usdt_rate):
    from app.api.admin.pledge import LoanAssetsResource
    loan_assets=LoanAssetsResource.get_loan_assets()
    value_map = defaultdict(Decimal)
    valid_value_map = defaultdict(Decimal)
    for loan_asset in loan_assets:
        detail_cache = PledgeStatisticsCache(loan_asset)
        if data:= detail_cache.read():
            data = json.loads(data)
            detail = data['detail']
            for item in detail:
                value, valid_value = Decimal(item['value']), Decimal(item['valid_value'])    # 这两个值都是转换为usdt数量存储的
                value_map[loan_asset] += safe_div(value, usdt_rate)
                valid_value_map[loan_asset] += safe_div(valid_value, usdt_rate)
    return value_map, valid_value_map


def get_pledge_interest_data(assets: set[str]):
    end = today()
    last_7_days = end - timedelta(days=7)
    last_30_days = end - timedelta(days=30)
    last_90_days = end - timedelta(days=90)
    last_180_days = end - timedelta(days=180)
    last_365_days = end - timedelta(days=365)
    
    interest_7d = get_daily_pledge_interest_data(last_7_days, end, assets)
    interest_30d = get_daily_pledge_interest_data(last_30_days, end, assets)
    interest_90d = get_daily_pledge_interest_data(last_90_days, end, assets)
    interest_180d = get_daily_pledge_interest_data(last_180_days, end, assets)
    interest_365d = get_daily_pledge_interest_data(last_365_days, end, assets)
    res = defaultdict(lambda: {
        'interest_7d_usd': Decimal(),
        'interest_30d_usd': Decimal(),
        'interest_90d_usd': Decimal(),
        'interest_180d_usd': Decimal(),
        'interest_365d_usd': Decimal(),
    })
    for asset, data in interest_7d.items():
        res[asset]['interest_7d_usd'] = data
    for asset, data in interest_30d.items():
        res[asset]['interest_30d_usd'] = data
    for asset, data in interest_90d.items():
        res[asset]['interest_90d_usd'] = data
    for asset, data in interest_180d.items():
        res[asset]['interest_180d_usd'] = data
    for asset, data in interest_365d.items():
        res[asset]['interest_365d_usd'] = data
    return res

def get_daily_pledge_interest_data(start, end, assets: set[str]):
    recs = DailyPledgeSiteReport.query.filter(
        DailyPledgeSiteReport.report_date < end,
        DailyPledgeSiteReport.report_date >= start,
        DailyPledgeSiteReport.asset.in_(assets),
    ).with_entities(
        DailyPledgeSiteReport.asset,
        func.sum(DailyPledgeSiteReport.total_interest_usd).label("sum_interest"),
    ).group_by(DailyPledgeSiteReport.asset).all()
    return {record.asset: record.sum_interest for record in recs}
    

def aggravate_margin_pledge_data(borrow_users, borrowed_value_map, collateral_value_map,
                                 valid_collateral_value_map, interest_data, type_):
    all_users = set()
    all_borrowed_usd, all_collateral_usd, all_valid_collateral_usd = Decimal(), Decimal(), Decimal()
    all_interest = defaultdict(Decimal)
    assets = set(borrow_users.keys()) | set(borrowed_value_map.keys()) | set(
        collateral_value_map.keys()) | set(valid_collateral_value_map.keys()) | set(interest_data.keys())
    for asset in assets:
        users = borrow_users[asset]
        borrower_count = len(users)
        borrowed_usd = borrowed_value_map[asset]
        collateral_usd = collateral_value_map[asset]
        valid_collateral_usd = valid_collateral_value_map[asset]
        all_borrowed_usd += borrowed_usd
        all_collateral_usd += collateral_usd
        all_valid_collateral_usd += valid_collateral_usd
        all_users |= users
        
        interest_7d_usd = interest_data[asset]['interest_7d_usd']
        interest_30d_usd = interest_data[asset]['interest_30d_usd']
        interest_90d_usd = interest_data[asset]['interest_90d_usd']
        interest_180d_usd = interest_data[asset]['interest_180d_usd']
        interest_365d_usd = interest_data[asset]['interest_365d_usd']
        
        all_interest['interest_7d_usd'] += interest_7d_usd
        all_interest['interest_30d_usd'] += interest_30d_usd
        all_interest['interest_90d_usd'] += interest_90d_usd
        all_interest['interest_180d_usd'] += interest_180d_usd
        all_interest['interest_365d_usd'] += interest_365d_usd
        db.session.add(LoanStatistic(
            asset=asset,
            loan_type=type_,
            borrower_count=borrower_count,
            borrowed_usd=borrowed_usd,
            collateral_usd=collateral_usd,
            effective_collateral_usd=valid_collateral_usd,
            interest_7d_usd=interest_7d_usd,
            interest_30d_usd=interest_30d_usd,
            interest_90d_usd=interest_90d_usd,
            interest_180d_usd=interest_180d_usd,
            interest_365d_usd=interest_365d_usd,
            report_date=today(),
        ))
        
    db.session.add(LoanStatistic(
        asset='ALL',
        loan_type=type_,
        borrower_count=len(all_users),
        borrowed_usd=all_borrowed_usd,
        collateral_usd=all_collateral_usd,
        effective_collateral_usd=all_valid_collateral_usd,
        interest_7d_usd=all_interest['interest_7d_usd'],
        interest_30d_usd=all_interest['interest_30d_usd'],
        interest_90d_usd=all_interest['interest_90d_usd'],
        interest_180d_usd=all_interest['interest_180d_usd'],
        interest_365d_usd=all_interest['interest_365d_usd'],
        report_date=today(),
    ))
    return assets
        
        
def aggravate_all_data(all_assets, margin_users, margin_value_map, asset_hold_map, valid_asset_hold_map,margin_interest_data,
                       pledge_users, borrowed_value_map, pledge_value_map, pledge_valid_value_map, pledge_interest_data):
    all_users = set()
    all_borrowed_usd, all_collateral_usd, all_valid_collateral_usd = Decimal(), Decimal(), Decimal()
    all_interest = defaultdict(Decimal)
    for asset in all_assets:
        users = margin_users[asset] | pledge_users[asset]
        borrower_count = len(users)
        borrowed_usd = (margin_value_map[asset] + borrowed_value_map[asset])
        collateral_usd = (asset_hold_map[asset] + pledge_value_map[asset])
        valid_collateral_usd = pledge_valid_value_map[asset] + valid_asset_hold_map[asset]
        all_borrowed_usd += borrowed_usd
        all_collateral_usd += collateral_usd
        all_valid_collateral_usd += valid_collateral_usd
        all_users |= users
        interest_7d_usd = margin_interest_data[asset]['interest_7d_usd'] + pledge_interest_data[asset]['interest_7d_usd']
        interest_30d_usd = margin_interest_data[asset]['interest_30d_usd'] + pledge_interest_data[asset]['interest_30d_usd']
        interest_90d_usd = margin_interest_data[asset]['interest_90d_usd'] + pledge_interest_data[asset]['interest_90d_usd']
        interest_180d_usd = margin_interest_data[asset]['interest_180d_usd'] + pledge_interest_data[asset]['interest_180d_usd']
        interest_365d_usd = margin_interest_data[asset]['interest_365d_usd'] + pledge_interest_data[asset]['interest_365d_usd']
        all_interest['interest_7d_usd'] += interest_7d_usd
        all_interest['interest_30d_usd'] += interest_30d_usd
        all_interest['interest_90d_usd'] += interest_90d_usd
        all_interest['interest_180d_usd'] += interest_180d_usd
        all_interest['interest_365d_usd'] += interest_365d_usd
        db.session.add(LoanStatistic(
            asset=asset,
            loan_type=LoanStatistic.LoanType.ALL,
            borrower_count=borrower_count,
            borrowed_usd=borrowed_usd,
            collateral_usd=collateral_usd,
            effective_collateral_usd=valid_collateral_usd,
            interest_7d_usd=interest_7d_usd,
            interest_30d_usd=interest_30d_usd,
            interest_90d_usd=interest_90d_usd,
            interest_180d_usd=interest_180d_usd,
            interest_365d_usd=interest_365d_usd,
            report_date=today(),
        ))
    db.session.add(LoanStatistic(
        asset='ALL',
        loan_type=LoanStatistic.LoanType.ALL,
        borrower_count=len(all_users),
        borrowed_usd=all_borrowed_usd,
        collateral_usd=all_collateral_usd,
        effective_collateral_usd=all_valid_collateral_usd,
        interest_7d_usd=all_interest['interest_7d_usd'],
        interest_30d_usd=all_interest['interest_30d_usd'],
        interest_90d_usd=all_interest['interest_90d_usd'],
        interest_180d_usd=all_interest['interest_180d_usd'],
        interest_365d_usd=all_interest['interest_365d_usd'],
        report_date=today(),
    ))