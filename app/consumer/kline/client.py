import json
import time
from typing import Callable

from kafka import KafkaConsumer
from websockets.sync.client import connect

from flask import current_app

from app import config
from app.business import ServerClient, PerpetualServerClient
from app.utils import spawn_greenlet
from app.consumer.kline.booster import new_booster, SpotConfig, PerpetualConfig


class WebsocketClient:

    def __init__(self, url):
        self.conn = connect(url)

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()

    def subscribe(self, markets: list[str]):
        params = [(x, 1, '0', False) for x in markets]  # (market, limit, interval, diff)
        self.conn.send(json.dumps({"method": "depth.subscribe_multi", "params": params, "id": 1}))

    def recv(self):
        return json.loads(self.conn.recv())

    def iter_msg(self):
        ping_time = time.time()
        while True:
            msg = self.recv()
            if 'result' in msg: # subscribe和ping的响应
                if err := msg.get("error"):
                    raise RuntimeError(f"server response error: {err}")
                continue
            n = time.time()
            if n - ping_time > 60:
                self._ping()
                ping_time = n
            yield msg

    def _ping(self):
        self.conn.send(json.dumps({"method": "server.ping", "params": [], "id": 1}))

    def close(self):
        if self.conn:
            self.conn.close()
            self.conn = None


def run_kafka_consumer(topics: list, configs: dict, on_new_deal: Callable):
    while True:
        consumber = None
        try:
            consumber = KafkaConsumer(*topics, **configs)
            while True:
                records = consumber.poll()
                for _, msgs in records.items():
                    for msg in msgs:
                        deal = json.loads(msg.value)
                        on_new_deal(deal)
                time.sleep(1)
        except Exception as e:
            if consumber:
                consumber.close()
            current_app.logger.error("selfeal kafka consumer error: %s. retry after 60s...", e)
            time.sleep(60)


def run_websocket_client(url: str, conf: SpotConfig | PerpetualConfig, on_new_depth: Callable):
    while True:
        try:
            with WebsocketClient(url) as client:
                client.subscribe(conf.market_list())
                for msg in client.iter_msg():
                    if conf.market_changed:
                        conf.market_changed = False
                        current_app.logger.info("config updated, re-subscribe websocket")
                        client.subscribe(conf.market_list())
                    method = msg['method']
                    if method == 'depth.update':
                        depth = msg['params'][1]
                        market = msg['params'][2]
                        depth['market'] = market
                        on_new_depth(depth)
        except Exception as e:
            current_app.logger.error("selfeal websocket error: %s. retry after 60s...", e)
            time.sleep(60)


def run_spot_booster():
    conf = SpotConfig()
    conf.update()
    booster = new_booster(conf, ServerClient)
    spawn_greenlet(conf.listen_for_update)
    spawn_greenlet(run_kafka_consumer, ['deals'], dict(
        bootstrap_servers=config['KAFKA_SPOT_CONFIG']['KAFKA_SERVERS'],
        group_id='kline.booster'
    ), booster.on_new_deal)
    spawn_greenlet(run_websocket_client, config['CLIENT_CONFIGS']['SPOT_WEBSOCKET']['url'], conf, booster.on_new_depth)
    booster.run_forever()


def run_perpetual_booster():
    conf = PerpetualConfig()
    conf.update()
    booster = new_booster(conf, PerpetualServerClient)
    spawn_greenlet(conf.listen_for_update)
    spawn_greenlet(run_kafka_consumer, ['perpetual_sys_deals'], dict(
        bootstrap_servers=config['KAFKA_PERPETUAL_CONFIG']['KAFKA_SERVERS'],
        group_id='kline.booster'
    ), booster.on_new_deal)
    spawn_greenlet(run_websocket_client, config['CLIENT_CONFIGS']['PERPETUAL_WEBSOCKET']['url'], conf, booster.on_new_depth)
    booster.run_forever()
