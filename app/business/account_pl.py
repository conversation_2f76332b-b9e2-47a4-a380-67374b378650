# -*- coding: utf-8 -*-
"""
账户总盈亏
"""
import copy
import json
from collections import defaultdict
from datetime import datetime, timedelta, date
from decimal import Decimal
from typing import Dict, Iterable, Union, Optional, List, Any, TypeVar
from itertools import chain as chain_iter

from typing import Tuple

from app.models.pledge import MIN_PLEDGE_ACCOUNT_ID, MAX_PLEDGE_ACCOUNT_ID
from app.models.user import SubAccount
from flask import current_app
from sqlalchemy import func
from werkzeug.datastructures import MultiDict

from app.business import (
    SPOT_ACCOUNT_ID, ServerClient, PriceManager, PerpetualServerClient,
    MAX_ORDER_ACCOUNT_ID
)
from app.business.amm import get_user_amm_assets
from app.business.site import BusinessSettings
from app.business.utils import query_records_by_time_range
from app.caches import PerpetualMarketCache, UserPerpetualAssetsCache
from app.caches.amm import LongLiveLiquidityPoolAmountCache
from app.caches.prices import InvisibleAssetsCache
from app.caches.profit_loss import UserAccountDeal<PERSON>ache, User<PERSON><PERSON>untPLDataCache, BalanceHistorySyncCache, BalanceHistoryDailyIdCache
from app.caches.spot import MarketCache
from app.common import AccountBalanceType, PrecisionEnum
from app.common.constants import BalanceBusiness, PerpetualMarketType
from app.exceptions import InvalidArgument
from app.models import (
    MarginLoanOrder, AssetInvestmentConfig, LiquidityHistory,
    Market, AssetPrice, UserLiquidity, PreTradingUserAsset,
)
from app.models.exchange import AssetExchangeOrder
from app.models.staking import StakingAccount
from app.utils import (
    current_timestamp, timestamp_to_datetime, DefaultDictWithArg,
    quantize_amount,
)
from app.business.external_dbs import ExchangeLogDB as E_db, PerpetualHistoryDB, PerpetualSummaryDB, TradeHistoryDB
from app.utils.date_ import date_to_datetime, today


T = TypeVar('T', int, Decimal)

TOTAL_ACCOUNT = 'ALL'

MAX_SUPPORT_DAYS = 180

#####
# 以下定义了一些账户资产变更的业务类型集合，新增业务时，如影响盈亏分析，把对应类型更新到集合中
#####

# 充提业务
DEPOSIT_WITHDRAWAL_BUSINESS_TYPES = (BalanceBusiness.DEPOSIT, BalanceBusiness.WITHDRAWAL)

CANCELLATION_BUSINESS_TYPES = (
    BalanceBusiness.DEPOSIT_CANCELLATION,
    BalanceBusiness.WITHDRAWAL_CANCELLATION,
    BalanceBusiness.WITHDRAWAL_FEE_CANCELLATION,
    BalanceBusiness.RED_PACKET_REFUND
)

# 奖励业务：包含了各类活动奖励、返佣等
REWARD_BUSINESS_TYPES = (
    BalanceBusiness.GIFT,
    BalanceBusiness.GIFT_REVOKE,
    BalanceBusiness.COUPON,
    BalanceBusiness.COUPON_RECYCLE,
    BalanceBusiness.TRADE_GIFT_COUPON,
    BalanceBusiness.CASHBACK_FEE,
    BalanceBusiness.PERPETUAL_SUBSIDY_COUPON,
    BalanceBusiness.COPY_TRADING_EXPERIENCE_FEE,
    BalanceBusiness.COPY_TRADING_EXPERIENCE_FEE_RECYCLE,
    BalanceBusiness.REFERRAL,
    BalanceBusiness.MAKER_CASH_BACK,
    BalanceBusiness.BROKER_REFERRAL,
    BalanceBusiness.NORMAL_REFERRAL,
    BalanceBusiness.AMBASSADOR_REFERRAL,
    BalanceBusiness.IEO_ACTIVITY_LOTTERY,
    BalanceBusiness.DIBS_ACTIVITY_LOTTERY,
    BalanceBusiness.BUS_USER_REFER,
)

# 划转业务：账户间划转
TRANSFER_BUSINESS_TYPES = (
    BalanceBusiness.MARGIN_TRANSFER,
    BalanceBusiness.INVESTMENT_IN,
    BalanceBusiness.INVESTMENT_OUT,
    BalanceBusiness.INVESTMENT_TRANSFER,
    BalanceBusiness.PERPETUAL_TRANSFER_IN,
    BalanceBusiness.PERPETUAL_TRANSFER_OUT,
    BalanceBusiness.PERPETUAL_TRANSFER,
    BalanceBusiness.SPOT_GRID_TRANSFER,
    BalanceBusiness.ADD_LIQUIDITY,
    BalanceBusiness.REMOVE_LIQUIDITY,
    BalanceBusiness.DIBS_ACTIVITY_SUBSCRIBE,
    BalanceBusiness.DIBS_ACTIVITY_CANCEL,
    BalanceBusiness.PLEDGE_ASSET_LOCK,
    BalanceBusiness.PLEDGE_ASSET_RELEASE,
    BalanceBusiness.COPY_TRADING_TRANSFER,
    BalanceBusiness.STAKING_ADD,
    BalanceBusiness.STAKING_REMOVE,
)

# 转账业务：用户间的转账，此类业务引起总账户资产增减
EXTERNAL_TRANSFER_BUSINESS_TYPES = (
    BalanceBusiness.SYSTEM,
    BalanceBusiness.RED_PACKET,
    BalanceBusiness.RED_PACKET_GRABBING,
    BalanceBusiness.SIGNED_OFF_USER_TO_ADMIN_TRANSFER,
    BalanceBusiness.CLEANED_BALANCE_TO_ADMIN_TRANSFER,
    BalanceBusiness.EXCHANGE_ORDER_TRANSFER,
    BalanceBusiness.AUTO_INVEST_TRANSFER,
    BalanceBusiness.AMM_FEE_TRANSFER,
    BalanceBusiness.ABNORMAL_DEPOSIT_APPLICATION,
    BalanceBusiness.IEO_ACTIVITY_SUBSCRIBE,
    BalanceBusiness.IEO_ACTIVITY_CANCEL,
    BalanceBusiness.PRE_TRADING_ISSUE,
    BalanceBusiness.PRE_TRADING_REDEMPTION,
    BalanceBusiness.PRE_TRADING_POS_SETTLE,
    BalanceBusiness.PRE_TRADING_ISSUE_SETTLE,
    BalanceBusiness.COPY_PROFIT_SETTLEMENT,
    BalanceBusiness.BUS_AMB_LOAN,
    BalanceBusiness.BUS_AMB_LOAN_REPAY,
    BalanceBusiness.P2P_SUB,
    BalanceBusiness.P2P_ADD,
    BalanceBusiness.P2P_MARGIN_PAYMENT,
    BalanceBusiness.P2P_MARGIN_REFUND,
    BalanceBusiness.P2P_MARGIN_SYS_ADD,
    BalanceBusiness.P2P_MARGIN_SYS_SUB,
    BalanceBusiness.COMMENT_TIP_IN,
    BalanceBusiness.COMMENT_TIP_OUT,
    BalanceBusiness.SUB_ACCOUNT_TRANSFER # 基于用户维度，子账号划转应属于外部划转
)

# 理财收益
INVESTMENT_INCOME_BUSINESS_TYPES = (
    BalanceBusiness.INVESTMENT_INTEREST,
    BalanceBusiness.INVESTMENT_INC_INTEREST,
    BalanceBusiness.EQUITY_INVEST_INCREASE,
    BalanceBusiness.STAKING_INCOME
)

"""
借贷业务
此处没有包含杠杆借还币，因为统计资产时就排除了杠杆借币的部分，因此净划入划出也不统计借币；
而对于借贷业务及授信，借出的资产直接加到了现货账户上，计算现货账户盈亏时要计入净划入。
"""
LOAN_BUSINESS_TYPES = (
    BalanceBusiness.CREDIT,
    BalanceBusiness.CREDIT_REPAYMENT,
    BalanceBusiness.PLEDGE_LOAN_ASSET_ADD,
    BalanceBusiness.PLEDGE_REPAY,
    BalanceBusiness.PLEDGE_LIQ,
    # BalanceBusiness.MARGIN_LOAN,
    # BalanceBusiness.MARGIN_REPAYMENT
)

AMM_BUSINESS_TYPES = (
    BalanceBusiness.ADD_LIQUIDITY,
    BalanceBusiness.REMOVE_LIQUIDITY
)

# 需从盈亏分析中扣除的业务类型
PROFIT_LOSS_BUSINESS_TYPES = (
    *DEPOSIT_WITHDRAWAL_BUSINESS_TYPES,
    *CANCELLATION_BUSINESS_TYPES,
    *REWARD_BUSINESS_TYPES,
    *TRANSFER_BUSINESS_TYPES,
    *EXTERNAL_TRANSFER_BUSINESS_TYPES,
    *LOAN_BUSINESS_TYPES
)

"""
计算总账户盈亏时应排除的类型
TRANSFER_BUSINESS_TYPES 账户间划转
LOAN_BUSINESS_TYPES 借贷业务 需要排除是因为借币资产不会计入账户权益(实际资产)，因此也不计入净划入
"""
ALL_ACCOUNT_NEGLECT_BUSINESS_TYPES = (
    *TRANSFER_BUSINESS_TYPES,
    *LOAN_BUSINESS_TYPES
)


def check_today_data_ready():
    ts = current_timestamp(to_int=True)
    today_ts = ts - ts % 86400
    # if ts - today_ts < BusinessSettings.account_pl_snapshot_seconds:
    #     return False
    date_str = timestamp_to_datetime(today_ts).strftime('%Y-%m-%d')
    log_table = E_db.user_slice_balance_log_table()
    where = f"`report_date` = '{date_str}' and `table` = 'user_slice_profit_loss_snapshot' and status = 'finish'"
    if not log_table.select(where=where):
        return False
    return True


def _query_table_by_time_range(table, fields, where, start_ts, end_ts):
    flag = False
    last_id = 0
    all_records = []
    origin_where = where
    while not flag:
        if last_id:
            where_ = f'{origin_where} and id < {last_id}'
        else:
            where_ = origin_where
        records = table.select(
                    *fields,
                    where=where_,
                    limit=10000,
                    order_by='id desc'
                )
        if not records:
            break
        records = list(dict(zip(fields, record)) for record in records)
        min_ts = min(r['time'] for r in records)
        last_id = min(r['id'] for r in records)
        all_records.extend(records)
        if min_ts < start_ts:
            flag = True

    return [r for r in all_records if start_ts <= r['time'] <= end_ts]


def _query_balance_transfer_history(_db: TradeHistoryDB | PerpetualHistoryDB, start_ts, end_ts):
    balance_business_vals = {item.value for item in BalanceBusiness}
    result = []
    if _db is TradeHistoryDB:
        fields = ['id', 'user_id', 'time', 'account', 'business', 'asset', 'change', 'detail']
    else:
        fields = ['id', 'user_id', 'time', 'business', 'asset', 'change', 'detail']
    for i in range(_db.DB_COUNT):
        for j in range(_db.TABLE_COUNT):
            table_name = 'balance_history'
            db_, _table = _db.DBS[i], f'{table_name}_{j}'
            
            where = f'business not in ("trade", "trade_fee")'
            
            records = _query_table_by_time_range(db_.table(_table), fields, where, start_ts, end_ts)
            records = [r for r in records if r['business'] in balance_business_vals]
            if j % 20 == 0:
                current_app.logger.warning(f"Running balance history {i} {j}, records count {len(records)}")
            result.extend(records)
    return result


def group_amm_transfer_data(start: datetime, end: datetime):
    # 专门用于汇总AMM账户资产划转，其他账户的资产划转通过get_balance_transfer_data获取
    q = LiquidityHistory.query.filter(
        LiquidityHistory.created_at >= start,
        LiquidityHistory.created_at < end,
        LiquidityHistory.status == LiquidityHistory.Status.FINISHED
    ).all()
    result = defaultdict(lambda: defaultdict(Decimal))
    transfer_in_result = defaultdict(lambda: defaultdict(Decimal))
    transfer_out_result = defaultdict(lambda: defaultdict(Decimal))

    market_assets_mapping = {v.name: (v.base_asset, v.quote_asset) for v in Market.query.all()}
    for v in q:
        if v.market not in market_assets_mapping:
            continue
        base_asset, quote_asset = market_assets_mapping[v.market]
        if v.business == LiquidityHistory.Business.ADD:
            result[v.user_id][base_asset] += v.base_amount
            result[v.user_id][quote_asset] += v.quote_amount
            transfer_in_result[v.user_id][base_asset] += v.base_amount
            transfer_in_result[v.user_id][quote_asset] += v.quote_amount

        if v.business == LiquidityHistory.Business.REMOVE:
            result[v.user_id][base_asset] -= v.base_amount
            result[v.user_id][quote_asset] -= v.quote_amount
            transfer_out_result[v.user_id][base_asset] += v.base_amount
            transfer_out_result[v.user_id][quote_asset] += v.quote_amount
    return result, transfer_in_result, transfer_out_result

def _get_balance_map(start_ts: int, end_ts: int):
    pl_balance_types = {item.value for item in PROFIT_LOSS_BUSINESS_TYPES}
    spot_history = _query_balance_transfer_history(TradeHistoryDB, start_ts, end_ts)
    for item in spot_history:
        item['system'] = 'spot'
    perpetual_history = _query_balance_transfer_history(PerpetualHistoryDB, start_ts, end_ts)
    for item in perpetual_history:
        item['system'] = 'perpetual'
    history = spot_history + perpetual_history
    history = [item for item in history if item['business'] in pl_balance_types]
    total_transfer_in_map, net_transfer_in_map = defaultdict(lambda: defaultdict(Decimal)), defaultdict(lambda: defaultdict(Decimal))
    for item in history:
        amount = item['change']
        business = BalanceBusiness(item['business'])
        
        if item['system'] == 'spot':
            account_type = _get_account_type(item['account'], item['business'])
        else:
            account_type = AccountBalanceType.PERPETUAL.name
        if amount > 0:
            total_transfer_in_map[(item['user_id'], account_type)][item['asset']] += amount
            net_transfer_in_map[(item['user_id'], account_type)][item['asset']] += amount

            if business not in ALL_ACCOUNT_NEGLECT_BUSINESS_TYPES:
                net_transfer_in_map[(item['user_id'], TOTAL_ACCOUNT)][item['asset']] += amount
                total_transfer_in_map[(item['user_id'], TOTAL_ACCOUNT)][item['asset']] += amount
        else:
            net_transfer_in_map[(item['user_id'], account_type)][item['asset']] -= abs(amount)

            if business not in ALL_ACCOUNT_NEGLECT_BUSINESS_TYPES:
                net_transfer_in_map[(item['user_id'], TOTAL_ACCOUNT)][item['asset']] -= abs(amount)
    
    # amm_result 是净转入，amm_in_result是总转入
    amm_result, amm_in_result, _ = group_amm_transfer_data(timestamp_to_datetime(start_ts), timestamp_to_datetime(end_ts))
    for user_id, assets in amm_result.items():
        for asset, amount in assets.items():
            net_transfer_in_map[(user_id, AccountBalanceType.AMM.name)][asset] += amount
    
    for user_id, assets in amm_in_result.items():
        for asset, amount in assets.items():
            total_transfer_in_map[(user_id, AccountBalanceType.AMM.name)][asset] += amount

    return total_transfer_in_map, net_transfer_in_map


def _query_max_record_id_time(db_: TradeHistoryDB | PerpetualHistoryDB, user_id: int) -> Tuple[int, int]:
    _db, table_name = db_.user_to_db_and_table(user_id, 'balance_history')
    table = _db.table(table_name)
    record = table.select(
        'id', 'time',
        order_by='id desc',
        limit=1
    )
    if record:
        return record[0][0], int(record[0][1])
    return 0, 0


def _query_balance_history_by_time_range(db_: TradeHistoryDB | PerpetualHistoryDB,
                                         user_id, start_ts, end_ts, start_id=0):
    """
    查询单个用户时间范围内的资金流水
    合约流水强制使用idx_user_time索引查询, 现货无此索引, 使用primary索引
    """
    def _query_spot_balance_history_by_time_range(db_, user_id, start_ts, end_ts, start_id):
        fields = ['id', 'user_id', 'time', 'account', 'business', 'asset', 'change', 'detail']
        where = f'business not in ("trade", "trade_fee") and user_id = {user_id} and id > {start_id}'
        _db, table_name = db_.user_to_db_and_table(user_id, 'balance_history')
        table = _db.table(table_name)
        records = table.select(
            *fields,
            where=where,
            force_index='PRI'
        )
        if not records:
            return []
        records = list(dict(zip(fields, record)) for record in records)
        records.sort(key=lambda r: r['id'], reverse=True)
        return [r for r in records if start_ts <= r['time'] < end_ts]
    
    def _query_perpetual_balance_history_by_time_range(db_, user_id, start_ts, end_ts, start_id):
        fields = ['id', 'user_id', 'time', 'business', 'asset', 'change', 'detail']
        where = f'business not in ("trade", "trade_fee") and user_id = {user_id} and time >= {start_ts} and time < {end_ts}'
        _db, table_name = db_.user_to_db_and_table(user_id, 'balance_history')
        table = _db.table(table_name)
        records = table.select(
            *fields,
            where=where,
            force_index='idx_user_time'
        )
        if not records:
            return []
        records = list(dict(zip(fields, record)) for record in records)
        records.sort(key=lambda r: r['id'], reverse=True)
        return [r for r in records if r['id'] > start_id]

    if db_ is TradeHistoryDB:
        return _query_spot_balance_history_by_time_range(db_, user_id, start_ts, end_ts, start_id)
    else:
        return _query_perpetual_balance_history_by_time_range(db_, user_id, start_ts, end_ts, start_id)



def _get_account_type(account_id: int, business: str) -> str:
    if account_id == SPOT_ACCOUNT_ID:
        return AccountBalanceType.SPOT.name
    if SPOT_ACCOUNT_ID < account_id < MAX_ORDER_ACCOUNT_ID:
        return AccountBalanceType.MARGIN.name
    if account_id in (StakingAccount.ACCOUNT_ID, AssetInvestmentConfig.ACCOUNT_ID):
        return AccountBalanceType.INVESTMENT.name
    if business and business in AMM_BUSINESS_TYPES:
        return AccountBalanceType.AMM.name
    return AccountBalanceType.PERPETUAL.name

def sync_user_slice_profit_loss_snapshot(timestamp):
    if timestamp is None:
        timestamp = current_timestamp(to_int=True)
    timestamp = timestamp - timestamp % 86400
    date_str = timestamp_to_datetime(timestamp).strftime('%Y-%m-%d')
    log_table = E_db.user_slice_balance_log_table()
    # 依赖数据检查
    balance_where = (f"`report_date` = '{date_str}' and "
                     "`table` = 'user_slice_account_balance_sum' and `status` = 'finish'")
    if not log_table.select(where=balance_where):
        current_app.logger.warning(f"check {balance_where} error")
        return

    table_name = "user_slice_profit_loss_snapshot"
    if E_db.slice_table_synced(table_name, date_str):
        return

    last_ts = timestamp - 86400
    last_dt = timestamp_to_datetime(last_ts)
    last_date_str = timestamp_to_datetime(last_ts).strftime("%Y-%m-%d")
    E_db.clean_slice_table(table_name,
                           date_str,
                           last_date_str,
                           E_db.USER_SLICE_PROFIT_LOSS_SNAPSHOT_TABLE_COUNT)
    E_db.insert_log(table_name, date_str)

    columns = ('report_date', 'user_id', 'account_type', 'balance_usd')
    _last_date_key = "last_date"
    _current_date_key = "current_date"

    # (user_id, "last_date or current_date", account_type) balance_usd
    user_account_data = defaultdict(Decimal)

    for t in range(E_db.USER_SLICE_ACCOUNT_BALANCE_SUM_TABLE_COUNT):
        table = E_db.user_slice_account_balance_sum_table(t)
        _s = table.select(
            *columns,
            where=f'report_date in ("{last_date_str}", "{date_str}")'
        )
        for _v in _s:
            if _v[0] == last_dt.date():
                user_account_data[(_v[1], _last_date_key, _v[2])] = _v[3]
            else:
                user_account_data[(_v[1], _current_date_key, _v[2])] = _v[3]

    tables: Dict[int, E_db.Table] \
        = DefaultDictWithArg(E_db.user_slice_profit_loss_snapshot_table)
    user_id_to_idx = E_db.user_slice_profit_loss_snapshot_hash

    empty_data = {
        "balance_usd": Decimal(),
        "last_day_usd": Decimal(),
        "net_deposit_usd": Decimal(),
        "net_transfer_in_usd": Decimal(),
        "profit_usd": Decimal(),
        "profit_rate": Decimal(),
        "total_transfer_in_usd": Decimal(),
        "total_deposit_usd": Decimal()
    }
    # key: (user_id, account_type | TOTAL_ACCOUNT)
    final_result = defaultdict(lambda: copy.copy(empty_data))
    rates_data = AssetPrice.get_close_price_map(last_dt)

    def _sum_usd(asset_data: Dict[Any, Decimal]) -> T:
        return sum(
            [amount * rates_data.get(asset, Decimal()) for asset, amount in asset_data.items()])
    all_user_ids = sorted({_key[0] for _key in user_account_data.keys()})
    user_id_account_types_mapping = MultiDict(
        [
            (_key[0], _key[2])
            for _key in user_account_data.keys()
        ]
    )

    total_transfer_in_map, net_transfer_in_map = _get_balance_map(last_ts, timestamp)
    # 特殊子账号（跟单、策略交易）资产汇总到主账号的Total Account
    special_sub_accounts = SubAccount.query.filter(SubAccount.type.in_(
        (SubAccount.Type.COPY_TRADER, SubAccount.Type.COPY_FOLLOWER, SubAccount.Type.STRATEGY)
    )).with_entities(SubAccount.user_id, SubAccount.main_user_id).all()

    special_sub_map = dict(special_sub_accounts)
    for user_id in all_user_ids:
        _user_account_types = sorted(set(user_id_account_types_mapping.getlist(user_id)))
        for _str_v in _user_account_types:
            _v = AccountBalanceType[_str_v]
            k = final_result[(user_id, _v.name)]
            k["balance_usd"] = user_account_data[(user_id, _current_date_key, _v.name)]
            k["last_day_usd"] = user_account_data[(user_id, _last_date_key, _v.name)]
            final_result[(user_id, TOTAL_ACCOUNT)]["balance_usd"] += k["balance_usd"]
            final_result[(user_id, TOTAL_ACCOUNT)]["last_day_usd"] += k["last_day_usd"]

            if main_id:= special_sub_map.get(user_id):
                final_result[(main_id, TOTAL_ACCOUNT)]["balance_usd"] += k["balance_usd"]
                final_result[(main_id, TOTAL_ACCOUNT)]["last_day_usd"] += k["last_day_usd"]
            
            if _v == AccountBalanceType.PLEDGE:
                continue
            
            k["total_transfer_in_usd"] = _sum_usd(total_transfer_in_map[(user_id, _v.name)])
            k['net_transfer_in_usd'] = _sum_usd(net_transfer_in_map[(user_id, _v.name)])

            k["profit_usd"] = k["balance_usd"] - k["last_day_usd"] - k["net_transfer_in_usd"]
            principal_usd = k["last_day_usd"] + k["total_transfer_in_usd"]
            principal_usd = quantize_amount(principal_usd, PrecisionEnum.CASH_PLACES)
            k["profit_rate"] = k["profit_usd"] / principal_usd \
                if principal_usd != Decimal() else Decimal()
    for user_id in all_user_ids:
        k = final_result[(user_id, TOTAL_ACCOUNT)]
        k['net_transfer_in_usd'] = _sum_usd(net_transfer_in_map[(user_id, TOTAL_ACCOUNT)])
        k["profit_usd"] = k["balance_usd"] - k["last_day_usd"] - k["net_transfer_in_usd"]
        
        k["total_transfer_in_usd"] = _sum_usd(total_transfer_in_map[(user_id, TOTAL_ACCOUNT)])
        principal_usd = k["last_day_usd"] + k["total_transfer_in_usd"]
        principal_usd = quantize_amount(principal_usd, PrecisionEnum.CASH_PLACES)
        k["profit_rate"] = k["profit_usd"] / principal_usd \
            if principal_usd != Decimal() else Decimal()

    for _key, _user_data in final_result.items():
        user_id, account = _key
        if _user_data == empty_data:
            # empty data.
            continue
        if account == AccountBalanceType.PLEDGE.name:
            # 质押账户暂不增加独立的资产盈亏
            continue
        table = tables[user_id_to_idx(user_id)]
        table.insert(
            last_date_str, user_id, account,
            _user_data["balance_usd"], _user_data["last_day_usd"],
            _user_data["net_deposit_usd"], _user_data["net_transfer_in_usd"],
            _user_data["total_deposit_usd"], _user_data["total_transfer_in_usd"],
            _user_data["profit_usd"], _user_data["profit_rate"],
        )
    for table in tables.values():
        table.flush()

    E_db.update_log_finished(table_name, date_str)

def sync_user_slice_account_deal_value_sum(timestamp):

    table_name = "user_slice_account_deal_value_sum"

    timestamp -= timestamp % 86400

    # date_str是今天, last_date_str是报表日期
    date_str = timestamp_to_datetime(timestamp).strftime('%Y-%m-%d')
    last_ts = timestamp - 86400
    last_date_str = timestamp_to_datetime(last_ts).strftime('%Y-%m-%d')
    if E_db.slice_table_synced(table_name, date_str):
        return
    
    E_db.clean_slice_table(table_name,
                           date_str,
                           last_date_str,
                           E_db.USER_SLICE_ACCOUNT_DEAL_VALUE_SUM_TABLE_COUNT)
    E_db.insert_log(table_name, date_str)

    _sync_spot_user_slice_account_deal_value_sum(last_ts)
    _sync_perpetual_user_slice_account_deal_value_sum(last_ts)
    
    E_db.update_log_finished(table_name, date_str)


def _sync_spot_user_slice_account_deal_value_sum(timestamp):

    def _get_deal_records(table, start_ts, end_ts):
        limit = 10000
        last_id = None
        fields = ('id', 'user_id', 'time', 'account', 'market', 'deal')

        while True:
            if last_id is None:
                rows = table.select(*fields, limit=limit, order_by="id desc")
            else:
                rows = table.select(*fields, where=f'id < {last_id}',
                                    limit=limit, order_by="id desc")

            for row in rows:
                row = dict(zip(fields, row))
                if start_ts <= row["time"] <= end_ts:
                    yield row
            if len(rows) != limit:
                break
            last_record = dict(zip(fields, rows[-1]))
            last_id = last_record['id']
            if last_record['time'] < start_ts:
                break
    
    market_detail_map = MarketCache.online_markets_detail()
    start_ts = timestamp
    end_ts = timestamp + 86400
    price_map = AssetPrice.get_close_price_map(timestamp_to_datetime(start_ts))
    spot_deal_map, margin_deal_map = defaultdict(Decimal), defaultdict(Decimal)

    current_app.logger.warning(f"spot_user_slice_account_deal_value_sum begins {start_ts}")
    for _db, _table_name in TradeHistoryDB.iter_db_and_table('user_deal_history'):
        _table = _db.table(_table_name)
        records = _get_deal_records(_table, start_ts, end_ts)

        for record in records:
            quote_asset = market_detail_map.get(record['market'], {}).get('quote_asset')
            if not quote_asset:
                continue
            deal_value = record['deal'] * price_map.get(quote_asset, Decimal())
            deal_value = quantize_amount(deal_value, PrecisionEnum.CASH_PLACES)

            if record['account'] == 0:
                spot_deal_map[record['user_id']] += deal_value
            else:
                margin_deal_map[record['user_id']] += deal_value
    current_app.logger.warning(f"spot_user_slice_account_deal_value_sum ends {start_ts}")
    
    exchange_records = query_records_by_time_range(AssetExchangeOrder,
                                                   timestamp_to_datetime(start_ts), 
                                                   timestamp_to_datetime(end_ts),
                                                   filters={'status': AssetExchangeOrder.Status.FINISHED})
    for item in exchange_records:
        item: AssetExchangeOrder
        amount = item.target_asset_exchanged_amount
        spot_deal_map[item.user_id] += quantize_amount(amount * price_map.get(item.target_asset, 0), 
                                                       PrecisionEnum.CASH_PLACES)
    tables: Dict[int, E_db.Table] \
    = DefaultDictWithArg(E_db.user_slice_account_deal_value_sum_table)
    user_id_to_idx = E_db.user_slice_account_deal_value_sum_hash
    maps = {
        AccountBalanceType.SPOT: spot_deal_map,
        AccountBalanceType.MARGIN: margin_deal_map
    }
    date_str = timestamp_to_datetime(timestamp).strftime('%Y-%m-%d')
    for account, map_ in maps.items():
        for user_id, deal_value in map_.items():
            table = tables[user_id_to_idx(user_id)]
            table.insert(
                date_str, user_id, account.name, deal_value
            )
    for table in tables.values():
        table.flush()


def _sync_perpetual_user_slice_account_deal_value_sum(timestamp):

    start_ts = timestamp
    records = PerpetualSummaryDB.group_by_user_date_asset(timestamp_to_datetime(start_ts).date())
    deal_map = defaultdict(Decimal)
    for item in records:
        deal_map[item['user_id']] += item['deal_amount']
    
    tables: Dict[int, E_db.Table] \
    = DefaultDictWithArg(E_db.user_slice_account_deal_value_sum_table)
    user_id_to_idx = E_db.user_slice_account_deal_value_sum_hash
    date_str = timestamp_to_datetime(timestamp).strftime('%Y-%m-%d')
    for user_id, deal_value in deal_map.items():
        table = tables[user_id_to_idx(user_id)]
        table.insert(
            date_str, user_id, AccountBalanceType.PERPETUAL.name, deal_value
        )
    for table in tables.values():
        table.flush()


class RealtimeAccountProfitLossProcessor(object):

    @classmethod
    def check_transfer_data_ready(cls, user_id: int, account_type: Union[AccountBalanceType, str], ts: int) -> bool:
        if isinstance(account_type, AccountBalanceType):
            account_type = account_type.name
        if account_type == AccountBalanceType.PERPETUAL.name:
            return True
        return bool(cls._get_spot_balance_history_daily_id(user_id, ts))
    
    @classmethod
    def _get_spot_balance_history_daily_id(cls, user_id: int, ts: int) -> int:
        db_idx, table_idx = TradeHistoryDB.user_hash(user_id)
        data = BalanceHistoryDailyIdCache(db_index=db_idx, table_index=table_idx, ts=ts).read()
        if data:
            return int(data)
        return 0
    

    @classmethod
    def get_realtime_user_account_type_data(cls, user_id: int,
                                            account_type: Union[AccountBalanceType, str]):
        methods_mapping = {
            AccountBalanceType.AMM: cls.get_amm_data,
            AccountBalanceType.SPOT: cls.get_spot_data,
            AccountBalanceType.PERPETUAL: cls.get_perpetual_data,
            AccountBalanceType.INVESTMENT: cls.get_investment_data,
            AccountBalanceType.MARGIN: cls.get_margin_data,
            TOTAL_ACCOUNT: cls.get_all_data
        }
        end_ts = current_timestamp(to_int=True)
        start_ts = end_ts - end_ts % 86400
        if account_type not in methods_mapping:
            raise InvalidArgument
        

        # noinspection PyArgumentList
        return methods_mapping[account_type](user_id=user_id, start_ts=start_ts, end_ts=end_ts)

    @classmethod
    def get_user_asset_percent_data(cls, user_id: int, account_type: Union[AccountBalanceType, str]):
        methods_mapping = {
            AccountBalanceType.AMM: cls.get_amm_assets,
            AccountBalanceType.SPOT: cls.get_spot_assets,
            AccountBalanceType.PERPETUAL: cls.get_perpetual_assets,
            AccountBalanceType.INVESTMENT: cls.get_investment_assets,
            AccountBalanceType.MARGIN: cls.get_margin_assets,
            TOTAL_ACCOUNT: cls.get_all_assets
        }
        # noinspection PyArgumentList
        total_assets_data = methods_mapping[account_type](user_id=user_id)
        invisible_assets = InvisibleAssetsCache().read()
        result = {asset: v for asset, v in total_assets_data.items() if v > Decimal() and asset not in invisible_assets}
        assets_rate = PriceManager.assets_to_usd()
        assets_usd = [dict(asset=asset, usd=assets_rate.get(asset, Decimal()) * v)
                      for asset, v in result.items() if v > Decimal()]
        assets_usd.sort(key=lambda x: x["usd"], reverse=True)
        total_usd = sum([v["usd"] for v in assets_usd])
        if account_type == TOTAL_ACCOUNT:
            # 有借贷时，借币币种余额不能直接减去待还数（结果可能是负数），这里把总市值减去质押待还市值
            _pledge_unflat_data = cls.get_pledge_unflat_data(user_id)
            _pledge_unflat_usd = sum(
                [assets_rate.get(asset, Decimal()) * v for asset, v in _pledge_unflat_data.items()]
            )
            total_usd -= _pledge_unflat_usd
        result = [dict(asset=v["asset"],
                       total=v["usd"],
                       percent=quantize_amount(v["usd"]/total_usd, 4) if total_usd > 0 else Decimal())
                  for v in assets_usd]
        return result

    @classmethod
    def get_margin_unflat_data(cls, user_id: int, start: Optional[datetime],
                               end: Optional[datetime]):
        q = MarginLoanOrder.query.filter(
            MarginLoanOrder.user_id == user_id,
            MarginLoanOrder.status.notin_([
                MarginLoanOrder.StatusType.FINISH,
                MarginLoanOrder.StatusType.FAIL
            ])
        )
        if start:
            q = q.filter(MarginLoanOrder.created_at >= start)
        if end:
            q = q.filter(MarginLoanOrder.created_at < end)
        q = q.with_entities(
            MarginLoanOrder.asset,
            func.sum(MarginLoanOrder.unflat_amount + MarginLoanOrder.interest_amount).label("total_amount")
        ).group_by(MarginLoanOrder.asset).all()
        result = defaultdict(Decimal)
        for v in q:
            result[v.asset] += v.total_amount
        return result

    @classmethod
    def get_pledge_unflat_data(cls, user_id: int):
        from app.business.pledge.helper import get_user_pledge_unflat_amount_dict

        return get_user_pledge_unflat_amount_dict(user_id)


    @classmethod
    def get_balance_transfer_data(cls, user_id: int, account_type: AccountBalanceType | str, start: datetime, end: datetime):        
        
        def _get_new_result(old: Dict[str, Decimal], updated: Dict[str, Decimal]) -> Dict[str, Decimal]:
            all_keys = list(old) + list(updated)
            all_keys = set(all_keys)
            result = defaultdict(Decimal)
            for k in all_keys:
                result[k] = old.get(k, 0) + updated.get(k, 0)
            return result
        
        if isinstance(account_type, AccountBalanceType):
            account_type_str = account_type.name
        else:
            account_type_str = account_type
        
        # AMM账户使用专门的逻辑处理
        if account_type_str == AccountBalanceType.AMM.name:
            return cls.get_amm_transfer_data(user_id, start, end)
        
        daily_history_start_id = 0
        today_ts = int(date_to_datetime(today()).timestamp())
        if account_type_str == AccountBalanceType.PERPETUAL.name:
            type_, db_ = 'PERPETUAL', PerpetualHistoryDB
        else:
            type_, db_ = 'SPOT', TradeHistoryDB
        
        cache = BalanceHistorySyncCache(type_, user_id, account_type_str)
        sync_data = cache.hgetall()

        start_ts, end_ts = int(start.timestamp()), int(end.timestamp())
        last_net_result, last_transfer_in_result, last_transfer_out_result = {}, {}, {}

        if sync_data and int(sync_data['report_date']) >= start_ts:
            last_net_result = sync_data['net_transfer_result']

            if last_net_result:
                last_net_result = json.loads(last_net_result)
                last_net_result = {k: Decimal(v) for k, v in last_net_result.items()}
            last_transfer_in_result = sync_data['transfer_in_result']
            if last_transfer_in_result:
                last_transfer_in_result = json.loads(last_transfer_in_result)
                last_transfer_in_result = {k: Decimal(v) for k, v in last_transfer_in_result.items()}
            last_transfer_out_result = sync_data['transfer_out_result']
            if last_transfer_out_result:
                last_transfer_out_result = json.loads(last_transfer_out_result)
                last_transfer_out_result = {k: Decimal(v) for k, v in last_transfer_out_result.items()}
        
        start_id, max_record_ts = 0, 0
        if sync_data:
            start_id = int(sync_data['last_id'])
            max_record_ts = int(sync_data.get('start_timestamp', 0))
        
        if type_ == 'SPOT':
            last_report_at = int(sync_data.get('report_at', 0))
            if not last_report_at or last_report_at < today_ts:
                daily_history_start_id = cls._get_spot_balance_history_daily_id(user_id, today_ts)
                start_id = max(start_id, daily_history_start_id)
            if start_id == 0:
                return Decimal(), Decimal(), Decimal()
        # 查数据前先查出最大ID和时间, 供下次查询使用
        new_start_id, new_max_record_ts = _query_max_record_id_time(db_, user_id)
        records = _query_balance_history_by_time_range(db_, user_id, max(start_ts, max_record_ts), end_ts, start_id)

        pl_types = {item.value for item in PROFIT_LOSS_BUSINESS_TYPES}
        all_account_neglect_types = {item.value for item in ALL_ACCOUNT_NEGLECT_BUSINESS_TYPES}

        records = [r for r in records if r['business'] in pl_types]

        # 对于合约账户，通过perpetual_history_db查出数据，不需要根据account_type作筛选，取全部数据
        # 对于总账户，不包含账户间划转的业务
        if account_type_str == AccountBalanceType.PERPETUAL.name:
            records = records
        elif account_type_str == TOTAL_ACCOUNT:
            records = [r for r in records if r['business'] not in all_account_neglect_types]
        else:
            records = [r for r in records if _get_account_type(r['account'], r['business']) == account_type_str]

        result = defaultdict(Decimal)
        transfer_in_result = defaultdict(Decimal)
        transfer_out_result = defaultdict(Decimal)
        for item in records:
            amount = item['change']
            if amount > 0:
                transfer_in_result[item['asset']] += amount
            else:
                transfer_out_result[item['asset']] += abs(amount)
            result[item['asset']] += amount
        new_result = _get_new_result(result, last_net_result)
        new_transfer_in_result = _get_new_result(transfer_in_result, last_transfer_in_result)
        new_transfer_out_result = _get_new_result(transfer_out_result, last_transfer_out_result)
        net_transfer_usd = cls.get_assets_usd(new_result)
        transfer_in_usd = cls.get_assets_usd(new_transfer_in_result)
        transfer_out_usd = cls.get_assets_usd(new_transfer_out_result)

        cache.hmset(
            dict(
                report_date=today_ts, # 写入当日时间戳
                report_at=current_timestamp(to_int=True),
                start_timestamp=new_max_record_ts,
                last_id=new_start_id,
                net_transfer_usd=str(net_transfer_usd),
                transfer_in_usd=str(transfer_in_usd),
                transfer_out_usd=str(transfer_out_usd),
                net_transfer_result=json.dumps({k: str(v) for k, v in new_result.items()}),
                transfer_in_result=json.dumps({k: str(v) for k, v in new_transfer_in_result.items()}),
                transfer_out_result=json.dumps({k: str(v) for k, v in new_transfer_out_result.items()})
            )
        )
        return net_transfer_usd, transfer_in_usd, transfer_out_usd

    
    @classmethod
    def get_amm_transfer_data(cls, user_id: int, start: datetime, end: datetime):
        # 专门用于汇总AMM账户资产划转，其他账户的资产划转通过get_balance_transfer_data获取
        q = LiquidityHistory.query.filter(
            LiquidityHistory.user_id == user_id,
            LiquidityHistory.created_at >= start,
            LiquidityHistory.created_at < end,
            LiquidityHistory.status == LiquidityHistory.Status.FINISHED
        ).all()
        result = defaultdict(Decimal)
        transfer_in_result = defaultdict(Decimal)
        transfer_out_result = defaultdict(Decimal)

        market_assets_mapping = {v.name: (v.base_asset, v.quote_asset) for v in Market.query.all()}
        for v in q:
            if v.market not in market_assets_mapping:
                continue
            base_asset, quote_asset = market_assets_mapping[v.market]
            if v.business == LiquidityHistory.Business.ADD:
                result[base_asset] += v.base_amount
                result[quote_asset] += v.quote_amount
                transfer_in_result[base_asset] += v.base_amount
                transfer_in_result[quote_asset] += v.quote_amount

            if v.business == LiquidityHistory.Business.REMOVE:
                result[base_asset] -= v.base_amount
                result[quote_asset] -= v.quote_amount
                transfer_out_result[base_asset] += v.base_amount
                transfer_out_result[quote_asset] += v.quote_amount
        net_transfer_usd = cls.get_assets_usd(result)
        transfer_in_usd = cls.get_assets_usd(transfer_in_result)
        transfer_out_usd = cls.get_assets_usd(transfer_out_result)
        return net_transfer_usd, transfer_in_usd, transfer_out_usd
    
    @classmethod
    def get_snapshot_balance(cls, timestamp: int, user_id: int,
                             account_type: Union[AccountBalanceType, str]):
        date_str = timestamp_to_datetime(timestamp).strftime("%Y-%m-%d")
        snap_table = E_db.user_slice_account_balance_sum_table(
            E_db.user_slice_account_balance_sum_hash(user_id))
        if isinstance(account_type, AccountBalanceType):
            account_type_str = account_type.name
        else:
            account_type_str = account_type
        if account_type == TOTAL_ACCOUNT:
            result = Decimal()
            where = f"`report_date` = '{date_str}' and `user_id`= {user_id}"
            r = snap_table.select("sum(balance_usd)", where=where)
            result += Decimal(r[0][0]) if r[0][0] else Decimal()
            special_sub_ids = SubAccount.query.filter(SubAccount.main_user_id == user_id,
                                                      SubAccount.type.in_((SubAccount.Type.COPY_FOLLOWER, 
                                                                           SubAccount.Type.COPY_TRADER, 
                                                                           SubAccount.Type.STRATEGY))).with_entities(SubAccount.user_id).all()
            special_sub_ids = [v.user_id for v in special_sub_ids]
            for id_ in special_sub_ids:
                where = f"`report_date` = '{date_str}' and `user_id`= {id_}"
                snap_table = E_db.user_slice_account_balance_sum_table(E_db.user_slice_account_balance_sum_hash(id_))
                r = snap_table.select("sum(balance_usd)", where=where)
                result += Decimal(r[0][0]) if r[0][0] else Decimal()
            return result
        else:
            where = f"`report_date` = '{date_str}' and account_type = '{account_type_str}' " \
                    f"and `user_id`= {user_id}"
            r = snap_table.select("balance_usd",
                                  where=where, limit=1)
            return Decimal(r[0][0]) if r else Decimal()

    @classmethod
    def get_assets_usd(cls, _data, asset_rates: dict = None, allow_negative: bool = True) -> T:
        if not _data:
            return Decimal()
        if not asset_rates:
            asset_rates = PriceManager.assets_to_usd()
        usd = sum([amount * asset_rates.get(asset, Decimal()) for asset, amount in _data.items()])
        if not allow_negative:
            usd = max(usd, Decimal())
        return usd

    @classmethod
    def get_perpetual_assets_by_cache(cls, user_id: int) -> dict[str, Decimal]:
        # 先查
        cache = UserPerpetualAssetsCache(user_id)
        try:
            query_data = cls.get_perpetual_assets(user_id)
        except Exception:
            if cache_data := cache.get_data():
                return cache_data
            return {}
        cache.set_data(query_data)
        return query_data

    @classmethod
    def get_perpetual_assets(cls, user_id):
        assets_data = defaultdict(Decimal)
        perpetual_client = PerpetualServerClient()
        _r = perpetual_client.get_user_balances(user_id)
        for asset_, __values in _r.items():
            assets_data[asset_] += __values['available']
            assets_data[asset_] += __values['frozen']
            assets_data[asset_] += __values['margin']
        # 未实现盈亏
        positions = perpetual_client.position_pending(user_id)
        for position in positions:
            asset_ = PerpetualMarketCache().get_balance_asset(position['market'])
            assets_data[asset_] += Decimal(position['profit_unreal'])
        return assets_data

    @classmethod
    def get_margin_assets(cls, user_id):
        merge_assets = defaultdict(Decimal)
        result = ServerClient().get_user_accounts_balances(user_id)
        for account_id, assets in result.items():
            account_id = int(account_id)
            if SPOT_ACCOUNT_ID < account_id < MAX_ORDER_ACCOUNT_ID:
                for asset, values in assets.items():
                    merge_assets[asset] += Decimal(values['available'])
                    merge_assets[asset] += Decimal(values['frozen'])
        _unflat_data = cls.get_margin_unflat_data(user_id, None, None)
        for __asset, __amount in _unflat_data.items():
            merge_assets[__asset] -= __amount
        return merge_assets

    @classmethod
    def get_spot_assets(cls, user_id):
        merge_assets = defaultdict(Decimal)
        result = ServerClient().get_user_accounts_balances(user_id)
        for account_id, assets in result.items():
            account_id = int(account_id)
            if account_id == SPOT_ACCOUNT_ID:
                for asset, values in assets.items():
                    merge_assets[asset] += Decimal(values['available'])
                    merge_assets[asset] += Decimal(values['frozen'])
        return merge_assets

    @classmethod
    def get_investment_assets(cls, user_id):
        merge_assets = defaultdict(Decimal)
        result = ServerClient().get_user_accounts_balances(user_id)
        for account_id, assets in result.items():
            account_id = int(account_id)
            if account_id in (AssetInvestmentConfig.ACCOUNT_ID, StakingAccount.ACCOUNT_ID):
                for asset, values in assets.items():
                    merge_assets[asset] += Decimal(values['available'])
                    merge_assets[asset] += Decimal(values['frozen'])
        return merge_assets

    @classmethod
    def get_amm_assets(cls, user_id):
        merge_assets = defaultdict(Decimal)
        for asset, amount in get_user_amm_assets(user_id).items():
            merge_assets[asset] += amount
        return merge_assets

    @classmethod
    def get_amm_assets_by_cache(cls, user_id):
        rows = UserLiquidity.query.filter(
            UserLiquidity.user_id == user_id,
            UserLiquidity.liquidity > 0
        ).all()
        market_list = [v.market for v in rows]
        if not market_list:
            return {}
        cache_data = {
            market: json.loads(value) for (market, value) in
            LongLiveLiquidityPoolAmountCache().hmget_with_keys(market_list)
        }
        data = defaultdict(Decimal)
        for row in rows:
            if row.market not in cache_data:
                continue
            market_cache_data = cache_data[row.market]
            base_asset = market_cache_data['base_asset']
            quote_asset = market_cache_data['quote_asset']
            pool_liquidity = Decimal(market_cache_data['liquidity'])
            pool_base_amount = Decimal(market_cache_data[base_asset])
            pool_quote_amount = Decimal(market_cache_data[quote_asset])
            if pool_liquidity > 0:
                share = quantize_amount(row.liquidity / pool_liquidity, 4)
            else:
                share = Decimal()
            base_amount = quantize_amount(pool_base_amount * share, 8)
            quote_amount = quantize_amount(pool_quote_amount * share, 8)
            data[base_asset] += base_amount
            data[quote_asset] += quote_amount
        return data

    @classmethod
    def get_all_assets(cls, user_id):
        merge_assets = defaultdict(Decimal)
        pre_asset_data = cls.get_user_pre_asset_data(user_id)
        for asset, amount in pre_asset_data.items():
            merge_assets[asset] += amount

        result = ServerClient().get_user_accounts_balances(user_id)
        for account_id, assets in result.items():
            for asset, values in assets.items():
                merge_assets[asset] += Decimal(values['available'])
                merge_assets[asset] += Decimal(values['frozen'])
        for asset, amount in get_user_amm_assets(user_id).items():
            merge_assets[asset] += amount
        perpetual_assets = cls.get_perpetual_assets(user_id)
        for _asset, _amount in perpetual_assets.items():
            merge_assets[_asset] += _amount
        _unflat_data = cls.get_margin_unflat_data(user_id, None, None)
        for __asset, __amount in _unflat_data.items():
            merge_assets[__asset] -= __amount
        # 计算全账户币种数时 不能直接减去质押待还数，结果可能是负数
        return merge_assets

    @classmethod
    def _get_real_time_account_type_usd(
            cls,
            user_id: int,
            account_type: Union[AccountBalanceType, str],
            include_invisible_assets: bool = True,
            is_use_cache: bool = False
    ) -> Decimal:
        
        def _get_special_sub_ids():
            special_sub_accounts = SubAccount.query.filter(SubAccount.main_user_id == user_id,
                                    SubAccount.type.in_((SubAccount.Type.COPY_FOLLOWER, 
                                                         SubAccount.Type.COPY_TRADER, 
                                                         SubAccount.Type.STRATEGY))).with_entities(
                                                             SubAccount.user_id, SubAccount.type
                                                         ).all()
            strategy_sub_ids = [item.user_id for item in special_sub_accounts if item.type == SubAccount.Type.STRATEGY]
            copy_sub_ids = [item.user_id for item in special_sub_accounts if item.type in (SubAccount.Type.COPY_FOLLOWER,
                                                                                           SubAccount.Type.COPY_TRADER)]
            return strategy_sub_ids, copy_sub_ids


        merge_assets = defaultdict(Decimal)
        if not include_invisible_assets:
            exclude_assets = InvisibleAssetsCache().read()
        else:
            exclude_assets = set()

        if account_type == AccountBalanceType.SPOT:
            result = ServerClient().get_user_accounts_balances(user_id)
            for account_id, assets in result.items():
                account_id = int(account_id)
                if account_id == SPOT_ACCOUNT_ID:
                    for asset, values in assets.items():
                        if asset in exclude_assets:
                            continue
                        merge_assets[asset] += Decimal(values['available'])
                        merge_assets[asset] += Decimal(values['frozen'])

        elif account_type == AccountBalanceType.MARGIN:
            result = ServerClient().get_user_accounts_balances(user_id)
            for account_id, assets in result.items():
                account_id = int(account_id)
                if SPOT_ACCOUNT_ID < account_id < MAX_ORDER_ACCOUNT_ID:
                    for asset, values in assets.items():
                        if asset in exclude_assets:
                            continue
                        merge_assets[asset] += Decimal(values['available'])
                        merge_assets[asset] += Decimal(values['frozen'])
            _unflat_data = cls.get_margin_unflat_data(user_id, None, None)
            for __asset, __amount in _unflat_data.items():
                if __asset in exclude_assets:
                    continue
                merge_assets[__asset] -= __amount
        elif account_type == AccountBalanceType.PERPETUAL:
            merge_assets = cls.get_perpetual_assets_by_cache(user_id) if is_use_cache \
                else cls.get_perpetual_assets(user_id)
            for __exclude_asset in exclude_assets:
                merge_assets[__exclude_asset] = Decimal()
        elif account_type == AccountBalanceType.PLEDGE:
            result = ServerClient().get_user_accounts_balances(user_id)
            for account_id, assets in result.items():
                account_id = int(account_id)
                if MIN_PLEDGE_ACCOUNT_ID <= account_id <= MAX_PLEDGE_ACCOUNT_ID:
                    for asset, values in assets.items():
                        if asset in exclude_assets:
                            continue
                        merge_assets[asset] += Decimal(values['available'])
                        merge_assets[asset] += Decimal(values['frozen'])
            _pledge_unflat_data = cls.get_pledge_unflat_data(user_id)
            for __asset, __amount in _pledge_unflat_data.items():
                # 计算全账户市值时可以减去质押的未还usd
                if __asset in exclude_assets:
                    continue
                merge_assets[__asset] -= __amount
        elif account_type == AccountBalanceType.INVESTMENT:
            result = ServerClient().get_user_accounts_balances(user_id)
            for account_id, assets in result.items():
                account_id = int(account_id)
                if account_id == AssetInvestmentConfig.ACCOUNT_ID:
                    for asset, values in assets.items():
                        if asset in exclude_assets:
                            continue
                        merge_assets[asset] += Decimal(values['available'])
                        merge_assets[asset] += Decimal(values['frozen'])
        elif account_type == AccountBalanceType.STAKING:
            result = ServerClient().get_user_accounts_balances(user_id)
            for account_id, assets in result.items():
                account_id = int(account_id)
                if account_id == StakingAccount.ACCOUNT_ID:
                    for asset, values in assets.items():
                        if asset in exclude_assets:
                            continue
                        merge_assets[asset] += Decimal(values['available'])
                        merge_assets[asset] += Decimal(values['frozen'])
        elif account_type == AccountBalanceType.AMM:
            amm_assets_data = cls.get_amm_assets_by_cache(user_id) if is_use_cache else get_user_amm_assets(user_id)
            for asset, amount in amm_assets_data.items():
                if asset in exclude_assets:
                    continue
                merge_assets[asset] += amount

        elif account_type == TOTAL_ACCOUNT:
            strategy_sub_ids, copy_sub_ids = _get_special_sub_ids()

            pre_asset_data = cls.get_user_pre_asset_data(user_id)
            for asset, amount in pre_asset_data.items():
                merge_assets[asset] += amount

            result = ServerClient().get_user_accounts_balances(user_id)

            for account_id, assets in result.items():
                for asset, values in assets.items():
                    if asset in exclude_assets:
                        continue
                    merge_assets[asset] += Decimal(values['available'])
                    merge_assets[asset] += Decimal(values['frozen'])
            
            for id_ in strategy_sub_ids:
                result = ServerClient().get_user_accounts_balances(id_)
                for account_id, assets in result.items():
                    for asset, values in assets.items():
                        if asset in exclude_assets:
                            continue
                        merge_assets[asset] += Decimal(values['available'])
                        merge_assets[asset] += Decimal(values['frozen'])


            amm_assets_data = cls.get_amm_assets_by_cache(user_id) if is_use_cache else get_user_amm_assets(user_id)
            for asset, amount in amm_assets_data.items():
                if asset in exclude_assets:
                    continue
                merge_assets[asset] += amount

            perpetual_assets = cls.get_perpetual_assets_by_cache(user_id) if is_use_cache \
                else cls.get_perpetual_assets(user_id)
            for _asset, _amount in perpetual_assets.items():
                if _asset in exclude_assets:
                    continue
                merge_assets[_asset] += _amount
            
            for id_ in copy_sub_ids:
                perpetual_assets = cls.get_perpetual_assets_by_cache(id_) if is_use_cache \
                    else cls.get_perpetual_assets(id_)
                for _asset, _amount in perpetual_assets.items():
                    if _asset in exclude_assets:
                        continue
                    merge_assets[_asset] += _amount

            _unflat_data = cls.get_margin_unflat_data(user_id, None, None)
            for __asset, __amount in _unflat_data.items():
                if __asset in exclude_assets:
                    continue
                merge_assets[__asset] -= __amount
            _pledge_unflat_data = cls.get_pledge_unflat_data(user_id)
            for __asset, __amount in _pledge_unflat_data.items():
                # 计算全账户市值时可以减去质押的未还usd
                if __asset in exclude_assets:
                    continue
                merge_assets[__asset] -= __amount
        else:
            raise InvalidArgument
        return cls.get_assets_usd(merge_assets, allow_negative=False)

    @classmethod
    def get_profit_rate(cls, _profit_usd, _principal_usd):
        rate = _profit_usd / _principal_usd if _principal_usd != Decimal() else Decimal()
        return quantize_amount(rate, PrecisionEnum.RATE_PLACES)

    @classmethod
    def get_user_pre_asset_data(cls, user_id: int) -> dict[str, Decimal]:
        q = PreTradingUserAsset.query.filter(
            PreTradingUserAsset.user_id == user_id,
            PreTradingUserAsset.status == PreTradingUserAsset.Status.PLEDGE,
        ).with_entities(
            PreTradingUserAsset.pledge_asset,
            PreTradingUserAsset.pledge_amount,
            PreTradingUserAsset.asset,
            PreTradingUserAsset.issue_amount).all()
        result = defaultdict(Decimal)
        for v in q:
            result[v.pledge_asset] += v.pledge_amount
            result[v.asset] -= v.issue_amount
        return result

    @classmethod
    def get_spot_data(cls, user_id: int, start_ts: int, end_ts: int):
        snap_balance_usd = cls.get_snapshot_balance(start_ts,
                                                    user_id, AccountBalanceType.SPOT)
        c = ServerClient()
        merge_assets = defaultdict(Decimal)
        for asset, values in c.get_user_balances(user_id, account_id=SPOT_ACCOUNT_ID).items():
            merge_assets[asset] += values['available']
            merge_assets[asset] += values['frozen']
        real_balance_usd = cls.get_assets_usd(merge_assets, allow_negative=False)
        start_dt = timestamp_to_datetime(start_ts)
        end_dt = timestamp_to_datetime(end_ts)

        net_usd, transfer_in_usd, _ = \
            cls.get_balance_transfer_data(user_id, AccountBalanceType.SPOT, start_dt, end_dt)
        profit_usd = real_balance_usd - snap_balance_usd - net_usd
        principal_usd = snap_balance_usd + transfer_in_usd
        profit_rate = cls.get_profit_rate(profit_usd, principal_usd)
        return dict(
            profit_rate=profit_rate,
            profit_usd=profit_usd,
            snap_balance_usd=snap_balance_usd,
            balance_usd=real_balance_usd
        )

    @classmethod
    def get_real_time_account_type_usd(
            cls,
            user_id: int,
            account_type_str,
            include_invisible_assets: bool = True,
            is_user_cache: bool = False
    ):
        if account_type_str in AccountBalanceType.__members__.keys():
            return cls._get_real_time_account_type_usd(
                user_id,
                AccountBalanceType[account_type_str],
                include_invisible_assets,
                is_user_cache
            )
        if account_type_str == TOTAL_ACCOUNT:
            return cls._get_real_time_account_type_usd(
                user_id,
                TOTAL_ACCOUNT,
                include_invisible_assets,
                is_user_cache
            )
        raise InvalidArgument

    @classmethod
    def get_real_time_accounts_usd(cls, user_id: int, include_invisible_assets: bool = True) -> dict[str, Decimal]:
        """ 获取全部account对应的资产usd， """
        from app.business.pledge.helper import is_pledge_account

        if not include_invisible_assets:
            exclude_assets = InvisibleAssetsCache().read()
        else:
            exclude_assets = set()

        spot_merge_assets = defaultdict(Decimal)
        margin_merge_assets = defaultdict(Decimal)
        investment_merge_assets = defaultdict(Decimal)
        staking_merge_assets = defaultdict(Decimal)
        pledge_merge_assets = defaultdict(Decimal)
        amm_merge_assets = defaultdict(Decimal)
        perpetual_merge_assets = defaultdict(Decimal)

        pre_asset_data = cls.get_user_pre_asset_data(user_id)
        for asset, amount in pre_asset_data.items():
            spot_merge_assets[asset] += amount

        result = ServerClient().get_user_accounts_balances(user_id)
        for account_id, assets in result.items():
            account_id = int(account_id)
            if account_id == SPOT_ACCOUNT_ID:
                for asset, values in assets.items():
                    if asset in exclude_assets:
                        continue
                    spot_merge_assets[asset] += Decimal(values['available'])
                    spot_merge_assets[asset] += Decimal(values['frozen'])
            elif SPOT_ACCOUNT_ID < account_id < MAX_ORDER_ACCOUNT_ID:
                for asset, values in assets.items():
                    if asset in exclude_assets:
                        continue
                    margin_merge_assets[asset] += Decimal(values['available'])
                    margin_merge_assets[asset] += Decimal(values['frozen'])
            elif account_id == AssetInvestmentConfig.ACCOUNT_ID:
                for asset, values in assets.items():
                    if asset in exclude_assets:
                        continue
                    investment_merge_assets[asset] += Decimal(values['available'])
                    investment_merge_assets[asset] += Decimal(values['frozen'])
            elif account_id == StakingAccount.ACCOUNT_ID:
                for asset, values in assets.items():
                    if asset in exclude_assets:
                        continue
                    staking_merge_assets[asset] += Decimal(values['available'])
                    staking_merge_assets[asset] += Decimal(values['frozen'])
            elif is_pledge_account(account_id):
                for asset, values in assets.items():
                    if asset in exclude_assets:
                        continue
                    pledge_merge_assets[asset] += Decimal(values['available'])
                    pledge_merge_assets[asset] += Decimal(values['frozen'])

        _unflat_data = cls.get_margin_unflat_data(user_id, None, None)
        for __asset, __amount in _unflat_data.items():
            if __asset in exclude_assets:
                continue
            margin_merge_assets[__asset] -= __amount
        _pledge_unflat_data = cls.get_pledge_unflat_data(user_id)
        for __asset, __amount in _pledge_unflat_data.items():
            if __asset in exclude_assets:
                continue
            pledge_merge_assets[__asset] -= __amount

        for asset, amount in get_user_amm_assets(user_id).items():
            if asset in exclude_assets:
                continue
            amm_merge_assets[asset] += amount

        perpetual_assets = cls.get_perpetual_assets(user_id)
        for _asset, _amount in perpetual_assets.items():
            if _asset in exclude_assets:
                continue
            perpetual_merge_assets[_asset] += _amount

        asset_rates = PriceManager.assets_to_usd()
        result = {
            AccountBalanceType.SPOT.name: cls.get_assets_usd(spot_merge_assets, asset_rates=asset_rates, allow_negative=False),
            AccountBalanceType.MARGIN.name: cls.get_assets_usd(margin_merge_assets, asset_rates=asset_rates, allow_negative=False),
            AccountBalanceType.INVESTMENT.name: cls.get_assets_usd(investment_merge_assets, asset_rates=asset_rates, allow_negative=False),
            AccountBalanceType.STAKING.name: cls.get_assets_usd(staking_merge_assets, asset_rates=asset_rates, allow_negative=False),
            AccountBalanceType.PLEDGE.name: cls.get_assets_usd(pledge_merge_assets, asset_rates=asset_rates, allow_negative=False),
            AccountBalanceType.AMM.name: cls.get_assets_usd(amm_merge_assets, asset_rates=asset_rates, allow_negative=False),
            AccountBalanceType.PERPETUAL.name: cls.get_assets_usd(perpetual_merge_assets, asset_rates=asset_rates, allow_negative=False),
        }
        return result

    @classmethod
    def get_margin_data(cls, user_id: int, start_ts: int, end_ts: int):
        realtime_usd = cls._get_real_time_account_type_usd(user_id,
                                                           AccountBalanceType.MARGIN)
        snap_balance_usd = cls.get_snapshot_balance(current_timestamp(to_int=True),
                                                    user_id, AccountBalanceType.MARGIN)
        _end = timestamp_to_datetime(end_ts)
        _start = timestamp_to_datetime(start_ts)
        net_transfer_data, transfer_in_data, _ = cls.get_balance_transfer_data(user_id, AccountBalanceType.MARGIN, _start, _end)
        profit_usd = realtime_usd - snap_balance_usd - net_transfer_data
        principal_usd = snap_balance_usd + transfer_in_data
        profit_rate = cls.get_profit_rate(profit_usd, principal_usd)
        return dict(
            profit_rate=profit_rate,
            profit_usd=profit_usd,
            snap_balance_usd=snap_balance_usd,
            balance_usd=realtime_usd
        )

    @classmethod
    def get_perpetual_data(cls, user_id: int, start_ts: int, end_ts: int):
        realtime_usd = cls._get_real_time_account_type_usd(
            user_id,
            AccountBalanceType.PERPETUAL
        )
        snap_balance_usd = cls.get_snapshot_balance(current_timestamp(to_int=True),
                                                    user_id, AccountBalanceType.PERPETUAL)
        _end = timestamp_to_datetime(end_ts)
        _start = timestamp_to_datetime(start_ts)
        net_transfer_usd, transfer_in_usd, _ = cls.get_balance_transfer_data(user_id, AccountBalanceType.PERPETUAL, _start, _end)
        profit_usd = realtime_usd - snap_balance_usd - net_transfer_usd
        principal_usd = snap_balance_usd + transfer_in_usd
        profit_rate = cls.get_profit_rate(profit_usd, principal_usd)
        return dict(
            profit_rate=profit_rate,
            profit_usd=profit_usd,
            snap_balance_usd=snap_balance_usd,
            balance_usd=realtime_usd
        )

    @classmethod
    def get_amm_data(cls, user_id: int, start_ts: int, end_ts: int):
        realtime_usd = cls._get_real_time_account_type_usd(
            user_id,
            AccountBalanceType.AMM
        )
        snap_balance_usd = cls.get_snapshot_balance(current_timestamp(to_int=True),
                                                    user_id, AccountBalanceType.AMM)
        _end = timestamp_to_datetime(end_ts)
        _start = timestamp_to_datetime(start_ts)
        transfer_usd, transfer_in_usd, _ = cls.get_balance_transfer_data(user_id, AccountBalanceType.AMM, _start, _end)
        profit_usd = realtime_usd - snap_balance_usd - transfer_usd
        principal_usd = snap_balance_usd + transfer_in_usd
        profit_rate = cls.get_profit_rate(profit_usd, principal_usd)
        return dict(
            profit_rate=profit_rate,
            profit_usd=profit_usd,
            snap_balance_usd=snap_balance_usd,
            balance_usd=realtime_usd
        )

    @classmethod
    def get_investment_data(cls, user_id: int, start_ts: int, end_ts: int):
        realtime_usd = cls._get_real_time_account_type_usd(user_id,
                                                           AccountBalanceType.INVESTMENT)
        realtime_usd += cls._get_real_time_account_type_usd(user_id,
                                                            AccountBalanceType.STAKING)
        snap_balance_usd = cls.get_snapshot_balance(current_timestamp(to_int=True),
                                                    user_id, AccountBalanceType.INVESTMENT)
        snap_balance_usd += cls.get_snapshot_balance(current_timestamp(to_int=True),
                                                     user_id, AccountBalanceType.STAKING)
        _end = timestamp_to_datetime(end_ts)
        _start = timestamp_to_datetime(start_ts)
        net_transfer_usd, transfer_in_usd, _ = \
            cls.get_balance_transfer_data(user_id, AccountBalanceType.INVESTMENT, _start, _end)
        transfer_usd = net_transfer_usd
        profit_usd = realtime_usd - snap_balance_usd - transfer_usd
        principal_usd = snap_balance_usd + transfer_in_usd
        profit_rate = cls.get_profit_rate(profit_usd, principal_usd)
        return dict(
            profit_rate=profit_rate,
            profit_usd=profit_usd,
            snap_balance_usd=snap_balance_usd,
            balance_usd=realtime_usd
        )
    

    @classmethod
    def get_all_data(cls, user_id: int, start_ts: int, end_ts: int):
        """
        全部账户资产：包含了系统子账号（策略、跟单）的资产
        """
        realtime_usd = cls._get_real_time_account_type_usd(
            user_id,
            TOTAL_ACCOUNT
        )
        snap_balance_usd = cls.get_snapshot_balance(current_timestamp(to_int=True),
                                                    user_id, TOTAL_ACCOUNT)
        start_dt = timestamp_to_datetime(start_ts)
        end_dt = timestamp_to_datetime(end_ts)
        net_transfer_usd, transfer_in_usd, _ = cls.get_balance_transfer_data(user_id, TOTAL_ACCOUNT, start_dt, end_dt)
        profit_usd = realtime_usd - snap_balance_usd - net_transfer_usd
        principal_usd = snap_balance_usd + transfer_in_usd
        profit_rate = cls.get_profit_rate(profit_usd, principal_usd)
        return dict(
            profit_rate=profit_rate,
            profit_usd=profit_usd,
            snap_balance_usd=snap_balance_usd,
            balance_usd=realtime_usd
        )


def get_snapshot_data_new(user_id: int, start_date: date, end_date: date,
                      account_type: Union[AccountBalanceType, str]):
    if isinstance(account_type, AccountBalanceType):
        account_type_str = account_type.name
    else:
        account_type_str = account_type
    if account_type_str != AccountBalanceType.INVESTMENT.name:
        return get_snapshot_data(user_id, start_date, end_date, account_type)
    
    fields = ("report_date", "balance_usd", "profit_usd", "profit_rate",
              "last_day_usd", "total_transfer_in_usd", "total_deposit_usd")
    investment_records = get_snapshot_data(user_id, start_date, end_date, 
                                           AccountBalanceType.INVESTMENT, fields=fields)
    staking_records = get_snapshot_data(user_id, start_date, end_date, 
                                         AccountBalanceType.STAKING, fields=fields)
    record_map = defaultdict(lambda: defaultdict(Decimal))
    for record in chain_iter(investment_records, staking_records):
        date_ = record['report_date']
        for k, v in record.items():
            if k == 'report_date':
                continue
            record_map[date_][k] += v
    result = []
    # 重新计算收益率
    for date_, record in record_map.items():
        principal_usd = record['last_day_usd'] + record['total_transfer_in_usd'] + record['total_deposit_usd']
        profit_rate = (record['profit_usd'] / principal_usd) if principal_usd else Decimal()
        profit_rate = quantize_amount(profit_rate, PrecisionEnum.RATE_PLACES)
        result.append(dict(
            report_date=date_,
            balance_usd=record['balance_usd'],
            profit_usd=record['profit_usd'],
            profit_rate=profit_rate,
        ))
    result.sort(key=lambda x: x['report_date'])
    return result


def get_snapshot_data(user_id: int, start_date: date, end_date: date,
                      account_type: Union[AccountBalanceType, str], fields: Iterable[str] = None):
    tables: Dict[int, E_db.Table] \
        = DefaultDictWithArg(E_db.user_slice_profit_loss_snapshot_table)
    user_id_to_idx = E_db.user_slice_profit_loss_snapshot_hash
    table = tables[user_id_to_idx(user_id)]
    start_date_str = start_date.strftime("%Y-%m-%d")
    end_date_str = end_date.strftime("%Y-%m-%d")
    fields = fields or ("report_date", "balance_usd", "profit_usd", "profit_rate")
    if isinstance(account_type, AccountBalanceType):
        account_type_str = account_type.name
    else:
        account_type_str = account_type
    records = table.select(
        *fields,
        where=f"`user_id`={user_id} and report_date >= '{start_date_str}'"
              f" and report_date <= '{end_date_str}' and account_type='{account_type_str}'"
    )
    fields_records = [dict(zip(fields, v)) for v in records]
    fields_records.sort(key=lambda x: x['report_date'])
    return fields_records


def get_snapshot_sum_profit_data(user_id: int, start_date: date, end_date: date):
    tables: Dict[int, E_db.Table] \
        = DefaultDictWithArg(E_db.user_slice_profit_loss_snapshot_table)
    user_id_to_idx = E_db.user_slice_profit_loss_snapshot_hash
    table = tables[user_id_to_idx(user_id)]
    start_date_str = start_date.strftime("%Y-%m-%d")
    end_date_str = end_date.strftime("%Y-%m-%d")
    fields = ("account_type", "total")
    records = table.select(
        'account_type',
        'SUM(`profit_usd`) `total`',
        where=f"`user_id`={user_id} and report_date >= '{start_date_str}'"
              f" and report_date <= '{end_date_str}' and account_type!='{TOTAL_ACCOUNT}'",
        group_by="account_type"
    )
    fields_records = [dict(zip(fields, v)) for v in records]
    result = {
        v["account_type"]: v["total"]
        for v in fields_records
    }
    return result


def get_deal_snapshot_data(user_id: int, start_date: date, end_date: date, account_type: AccountBalanceType | None):

    tables: Dict[int, E_db.Table] \
        = DefaultDictWithArg(E_db.user_slice_account_deal_value_sum_table)
    user_id_to_idx = E_db.user_slice_account_deal_value_sum_hash
    table = tables[user_id_to_idx(user_id)]
    start_date_str = start_date.strftime("%Y-%m-%d")
    end_date_str = end_date.strftime("%Y-%m-%d")
    fields = ("report_date", "deal_usd")

    if account_type:
        account_type_str = account_type.name
        records = table.select(
            *fields,
            where=f"`user_id`={user_id} and report_date >= '{start_date_str}'"
                f" and report_date <= '{end_date_str}' and account_type='{account_type_str}'"
        )
    else:
        # all
        fields = ("report_date", "deal_usd")
        records = table.select(
            *("report_date", "sum(deal_usd) as deal_usd"),
            where=f"`user_id`={user_id} and report_date >= '{start_date_str}'"
                f" and report_date <= '{end_date_str}' group by report_date"
        )
    fields_records = [dict(zip(fields, v)) for v in records]
    fields_records.sort(key=lambda x: x['report_date'])
    return fields_records

def get_deal_realtime_data(user_id: int, account_type: Union[AccountBalanceType | str], ts: int = None):
    
    def _get_spot_deal_usd(ts):
        market_detail_map = MarketCache.online_markets_detail()
        today_ = today()
        today_ts = int(date_to_datetime(today_).timestamp())
        if not ts:
            ts = today_ts
        records = TradeHistoryDB.get_users_history([user_id], 
                                                ['user_id', 'time', 'account', 'market', 'deal'], 
                                                f'time >= {ts}', 'user_deal_history')
        deal_usd = 0
        price_map = PriceManager.assets_to_usd()
        if account_type_str == AccountBalanceType.SPOT.name:
            records = [v for v in records if v['account'] == SPOT_ACCOUNT_ID]
        elif account_type_str == AccountBalanceType.MARGIN.name:
            records = [v for v in records if v['account'] != SPOT_ACCOUNT_ID]

        for record in records:
            quote_asset = market_detail_map.get(record['market'], {}).get('quote_asset')
            if not quote_asset:
                continue
            deal_usd += record['deal'] * price_map.get(quote_asset, 0)
        
        if account_type_str in (AccountBalanceType.SPOT.name, TOTAL_ACCOUNT):
            datetime_ = timestamp_to_datetime(ts)
            exchange_orders = AssetExchangeOrder.query.filter(
                AssetExchangeOrder.user_id == user_id,
                AssetExchangeOrder.status == AssetExchangeOrder.Status.FINISHED,
                AssetExchangeOrder.created_at >= datetime_,
            ).with_entities(
                AssetExchangeOrder.source_asset,
                AssetExchangeOrder.source_asset_exchanged_amount
            )
            for asset, amount in exchange_orders:
                deal_usd += amount * price_map.get(asset, 0)
        return quantize_amount(deal_usd, PrecisionEnum.CASH_PLACES)

    def _get_perpetual_deal_usd(ts):
        today_ = today()
        today_ts = int(date_to_datetime(today_).timestamp())
        if not ts:
            ts = today_ts
        market_info_map = PerpetualMarketCache().read_aside()
        price_map = PriceManager.assets_to_usd()
        price_map['USD'] = Decimal(1)                         
        records = PerpetualHistoryDB.get_users_deals(
                    [user_id], 
                    ['time', 'market', 'price', 'amount'],  
                    f'time >= {ts}'
                )
        deal_usd = 0
        for t in records:
            if t['market'] not in market_info_map:
                continue
            info = market_info_map[t['market']]
            if info['type'] == PerpetualMarketType.DIRECT:
                deal = t['price'] * t['amount']
            else:
                deal = t['amount']
            deal_usd += deal * price_map.get(info['money'], 0)
        return quantize_amount(deal_usd, PrecisionEnum.CASH_PLACES)

    if isinstance(account_type, AccountBalanceType):
        account_type_str = account_type.name
    else:
        account_type_str = account_type
    deal_usd = 0
    if account_type_str in (AccountBalanceType.SPOT.name, 
                            AccountBalanceType.MARGIN.name):
        deal_usd = _get_spot_deal_usd(ts)
    elif account_type_str == AccountBalanceType.PERPETUAL.name:
        deal_usd = _get_perpetual_deal_usd(ts)
    elif account_type_str == TOTAL_ACCOUNT:
        deal_usd = _get_spot_deal_usd(ts) + _get_perpetual_deal_usd(ts)
    
    return dict(deal_usd=deal_usd)


def get_realtime_data(user_id: int, account_type: Union[AccountBalanceType, str]):
    result = RealtimeAccountProfitLossProcessor.get_realtime_user_account_type_data(
        user_id, account_type)
    return dict(
        profit_rate=result["profit_rate"],
        profit_usd=result["profit_usd"],
        balance_usd=result["real_balance_usd"]
    )


def format_profit_snapshot_data(start_date: date, series: List):
    result = []
    fields = ['balance_usd', 'profit_usd', 'profit_rate']
    zero_data = {k: Decimal() for k in fields}
    report_dates = [v["report_date"] for v in series]
    earliest_date = min(report_dates) if len(report_dates) > 0 else start_date
    fix_points = (earliest_date - start_date).days
    for i in range(fix_points):
        # noinspection PyTypeChecker
        result.append(
            dict(
                zero_data,
                report_date=start_date + timedelta(days=i)
            )
        )
    result.extend(series)
    total_profit_usd = Decimal()
    for index, _ in enumerate(result):
        total_profit_usd += result[index]["profit_usd"]
        # noinspection PyTypeChecker
        result[index]["total_profit_usd"] = total_profit_usd
    return result


def format_deal_snapshot_data(start_date: date, end_date: date, series: List):
    result = []
    fields = ['deal_value_usd']
    zero_data = {k: Decimal() for k in fields}
    report_dates = [v["report_date"] for v in series]
    earliest_date = min(report_dates) if report_dates else end_date
    fix_points = (earliest_date - start_date).days
    for i in range(fix_points):
        # noinspection PyTypeChecker
        result.append(
            dict(
                zero_data,
                report_date=start_date + timedelta(days=i)
            )
        )
    result.extend(series)
    return result

def get_account_pl_cache_data(user_id, account_type_str):
    from app.schedules.profit_loss import update_user_realtime_pl_task
    current_ts = current_timestamp(to_int=True)
    cache = UserAccountPLDataCache(user_id, account_type_str)
    cache_data = cache.hgetall()
    cache_ts = int(cache_data.get('ts', 0))
    if current_ts - cache_ts > BusinessSettings.account_pl_user_data_cache_time or not cache_data:
        cache.hset('ts', str(current_ts))
        update_user_realtime_pl_task.delay(user_id, account_type_str)
    return cache_data or {}


def get_account_deal_value_cache_data(user_id, account_type_str):
    from app.schedules.profit_loss import update_user_realtime_deal_value_task
    current_ts = current_timestamp(to_int=True)
    cache = UserAccountDealCache(user_id, account_type_str)
    cache_data = cache.read()
    data = json.loads(cache_data) if cache_data else {}
    cache_ts = int(data.get('ts', 0))
    if current_ts - cache_ts > BusinessSettings.account_pl_user_data_cache_time or not data:
        update_user_realtime_deal_value_task.delay(user_id, account_type_str)
    return data or {}
