from typing import Iterable

from decimal import Decimal

from enum import Enum

from app.config import config

from app.common.onchain import Chain

from app.utils import now
from app.utils.config_ import BaseConfig
from app.utils.config_ import ConfigMode
from app.utils.config_ import ConfigField
from app.utils.onchain import OnchainAddressSOL
from app.utils.onchain import OnchainAddressETH
from app.utils.onchain import OnchainA<PERSON>ressBSC

from app.models import db
from app.models.onchain import OnchainSetting as OnchainSettingModel

from app.caches import OnchainSettingCache


class OnchainAddressHelper:

    def __init__(self, chain: Chain):
        self._cls = {
            Chain.SOL: OnchainAddressSOL,
            Chain.ERC20: OnchainAddressETH,
            Chain.BSC: OnchainAddressBSC,
        }[chain]

    def normalise_address(self, address: str) -> str:
        return self._cls.normalise_address(address)

    def normalise_addresses(self, addresses: Iterable[str]) -> list[str]:
        return [self._cls.normalise_address(address) for address in addresses]

    def validate_address(self, address: str) -> bool:
        return self._cls.validate_address(address)


class _OnchainSettings(BaseConfig):
    F = ConfigField

    class HotLimitKey(Enum):
        AMOUNT_24H = 'amount_24h'
        FDV = 'fdv'
        TOP_POOL_LIQUIDITY = 'top_pool_liquidity'
        HOLD_ADDRESS_COUNT = 'hold_address_count'
        TOP10_PERCENTAGE = 'top10_percentage'

    eth_hot_limit_amount_24h: Decimal = F(Decimal, 'ETH链热门榜-24小时成交额', default=Decimal())
    eth_hot_limit_fdv: Decimal = F(Decimal, 'ETH链热门榜-24小时成交额', default=Decimal())
    eth_hot_limit_top_pool_liquidity: Decimal = F(Decimal, 'ETH链热门榜-24小时成交额', default=Decimal())
    eth_hot_limit_hold_address_count: Decimal = F(Decimal, 'ETH链热门榜-24小时成交额', default=Decimal())
    eth_hot_limit_top10_percentage: Decimal = F(Decimal, 'ETH链热门榜-24小时成交额', default=Decimal())

    eth_quantity_per_order_limit_eth: Decimal = F(Decimal, 'ETH链最大支付数量-ETH', default=Decimal('5'))
    eth_quantity_per_order_limit_usdt: Decimal = F(Decimal, 'ETH链最大支付数量-USDT', default=Decimal('20000'))

    sol_hot_limit_amount_24h: Decimal = F(Decimal, 'SOL链热门榜-24小时成交额', default=Decimal())
    sol_hot_limit_fdv: Decimal = F(Decimal, 'SOL链热门榜-24小时成交额', default=Decimal())
    sol_hot_limit_top_pool_liquidity: Decimal = F(Decimal, 'SOL链热门榜-24小时成交额', default=Decimal())
    sol_hot_limit_hold_address_count: Decimal = F(Decimal, 'SOL链热门榜-24小时成交额', default=Decimal())
    sol_hot_limit_top10_percentage: Decimal = F(Decimal, 'SOL链热门榜-24小时成交额', default=Decimal())

    sol_quantity_per_order_limit_sol: Decimal = F(Decimal, 'SOL链最大支付数量-SOL', default=Decimal('100'))
    sol_quantity_per_order_limit_usdt: Decimal = F(Decimal, 'SOL链最大支付数量-USDT', default=Decimal('20000'))

    bsc_hot_limit_amount_24h: Decimal = F(Decimal, 'BSC链热门榜-24小时成交额', default=Decimal())
    bsc_hot_limit_fdv: Decimal = F(Decimal, 'BSC链热门榜-24小时成交额', default=Decimal())
    bsc_hot_limit_top_pool_liquidity: Decimal = F(Decimal, 'BSC链热门榜-24小时成交额', default=Decimal())
    bsc_hot_limit_hold_address_count: Decimal = F(Decimal, 'BSC链热门榜-24小时成交额', default=Decimal())
    bsc_hot_limit_top10_percentage: Decimal = F(Decimal, 'BSC链热门榜-24小时成交额', default=Decimal())

    bsc_quantity_per_order_limit_bnb: Decimal = F(Decimal, 'BSC链最大支付数量-BNB', default=Decimal('20'))
    bsc_quantity_per_order_limit_usdt: Decimal = F(Decimal, 'BSC链最大支付数量-USDT', default=Decimal('20000'))

    @classmethod
    def get_hot_limit_key(cls, chain: Chain, key: HotLimitKey) -> str:
        return f'{chain.get_display_name().lower()}_hot_limit_{key.value}'

    def get_hot_limit_value(self, chain: Chain, key: HotLimitKey) -> Decimal:
        return getattr(self, self.get_hot_limit_key(chain, key))

    @classmethod
    def get_quantity_per_order_limit_key(cls, chain: Chain, asset: str) -> str:
        return f'{chain.get_display_name().lower()}_quantity_per_order_limit_{asset.lower()}'

    def get_quantity_per_order_limit_value(self, chain: Chain, asset: str) -> Decimal:
        return getattr(self, self.get_quantity_per_order_limit_key(chain, asset))

    @classmethod
    def get_quantity_per_order_limit_keys(cls, chain: Chain) -> set[str]:
        return {
            Chain.SOL: {'sol', 'usdt'},
            Chain.ERC20: {'eth', 'usdt'},
            Chain.BSC: {'bnb', 'usdt'},
        }[chain]

    @classmethod
    def snapshot_mode(cls):
        return _OnchainSettings(ConfigMode.SNAPSHOT)

    def _get_all(self):
        return OnchainSettingCache().get_values()

    def _get_one(self, name: str) -> str:
        return OnchainSettingCache().get_value(name)

    def _set_one(self, name: str, value: str):
        row = OnchainSettingModel.query.filter(OnchainSettingModel.key == name).first()
        if not row:
            row = OnchainSettingModel(key=name)
            db.session.add(row)
        row.value = value
        row.status = OnchainSettingModel.Status.VALID
        db.session.commit()
        OnchainSettingCache().set_value(name, value)

    def _del_one(self, name: str):
        row = OnchainSettingModel.query.filter(OnchainSettingModel.key == name).first()
        if not row:
            return
        row.status = OnchainSettingModel.Status.DELETED
        row.updated_at = now()
        db.session.commit()
        OnchainSettingCache().del_value(name)


OnchainSettings = _OnchainSettings(ConfigMode.REAL_TIME)


def schedule_enable() -> bool:
    return config['ONCHAIN_CONFIGS'].get('schedule_enable', True)
