from app.common.onchain import Chain
from app.models.onchain import OnchainTokenBlocklist


def is_token_tradable(chain: Chain, contract: str):
    block: OnchainTokenBlocklist = OnchainTokenBlocklist.query.filter(
        OnchainTokenBlocklist.chain == chain,
        OnchainTokenBlocklist.contract == contract
    ).first()
    if block and block.block_type in OnchainTokenBlocklist.BlockType.can_not_trade_type():
        return False, block.notice_type
    return True, None
