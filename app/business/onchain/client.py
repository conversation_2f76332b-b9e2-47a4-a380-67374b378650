import time
import json
import hashlib
import inspect
import requests

from urllib.parse import urljoin

from typing import Iterable

from collections import defaultdict

from decimal import Decimal

from dateutil.tz import UTC

from flask import current_app

from app.config import config

from app.common.onchain import Chain
from app.common.onchain import <PERSON>linePeriod
from app.common.onchain import TokenData
from app.common.onchain import TokenInfo
from app.common.onchain import TokenQuote
from app.common.onchain import TokenKline

from app.business.onchain.base import OnchainAddressHelper

from app.models.onchain import OnchainToken

from app.caches.onchain import OnchainTokenQuoteCache
from app.caches.onchain import OnchainCoingeckoTokenIDSOLCache
from app.caches.onchain import OnchainCoingeckoTokenIDETHCache
from app.caches.onchain import OnchainCoingeckoTokenIDBSCCache
from app.caches.onchain import GoPlusLabsAuthorizationCache
from app.caches.onchain import OnchainThirdPartyAPICallsCache
from app.caches.onchain import OnchainCoingeckoLowValueTokenIDCache

from app.exceptions import ServiceUnavailable

from app.utils.iterable import batch_iter
from app.utils.date_ import str_to_datetime
from app.utils.date_ import current_timestamp
from app.utils.onchain import amount_to_str
from app.utils.onchain import quantize_amount
from app.utils.http_client import RESTClient


class OnchainSwapClient:

    def __init__(self):
        conf = config['ONCHAIN_CONFIGS']['coinex_wallet']
        self._client = RESTClient(conf['url'])

    def get(self, path: str, **params):
        data = self._client.get(path, **params)
        if data.get('code') != 0:
            raise ServiceUnavailable(message=data['message'])
        return data['data']

    def coin_all(self):
        """链上交易所有支持Token"""
        path = '/swap/coin/all'
        return self.get(path)

    def coin_search(self, key: str):
        """链上交易根据合约地址查询链上Token信息"""
        path = '/swap/coin/search'
        return self.get(path, key=key)

    def quote(
            self, equipment_no: str, from_coin: str, to_coin: str,
            from_amount: Decimal, from_decimals: int, slippage: Decimal, exchanger_ids: list,
    ):
        """
        链上交易询价
        slippage: 字符串格式的数字，0.5代表 0.5% ，不传默认为2
        """
        path = '/swap/quote'
        params = {
            'equipment_no': equipment_no,
            'from_coin': from_coin,
            'to_coin': to_coin,
            'from_amount': amount_to_str(from_amount, from_decimals),
            'slippage': amount_to_str(slippage),
            'exchanger_ids': ','.join(exchanger_ids)
        }
        return self.get(path, **params)

    def create_order(
            self, equipment_no: str, exchanger_id: str,
            from_coin: str, to_coin: str, from_amount: Decimal, from_decimals: int,
            from_address: str, to_address: str, slippage: Decimal
    ):
        """
        创建链上交易兑换订单
        slippage: 字符串格式的数字，0.5代表 0.5% ，不传默认为2
        """
        path = '/swap/order/create'
        body = {
            'equipment_no': equipment_no,
            'exchanger_id': exchanger_id,
            'from_coin': from_coin,
            'to_coin': to_coin,
            'from_amount': amount_to_str(from_amount, from_decimals),
            'from_address': from_address,
            'to_address': to_address,
            'slippage': amount_to_str(slippage),
        }
        data = self._client.post(path, body)
        if data.get('code') != 0:
            current_app.logger.warning(
                f'OnchainSwapClient create order, code: {data.get("code")}, message: {data.get("message")}'
            )
            raise ServiceUnavailable(message=data['message'])
        return data['data']

    def get_order(
            self, order_id: str
    ):
        """
        查询链上交易兑换订单
        """
        path = '/swap/order/detail'
        return self.get(path, order_id=order_id)


class CoingeckoClient:

    def __init__(self, chain: Chain = None):
        self._chain_id = self.get_chain_id(chain)
        coingecko_config = config['ONCHAIN_CONFIGS']['coingecko']
        self._headers = {
            'accept': 'application/json',
            coingecko_config['headers_key']: coingecko_config['key'],
        }
        self._client = RESTClient(coingecko_config['host'], headers=self._headers, timeout=10)
        self.number_of_calls = 0

    def get_onchain_dex_api(self, url: str) -> str:
        return f'/api/v3/onchain/networks/{self._chain_id}/{url}'

    @classmethod
    def get_chain_id(cls, chain: Chain = None):
        if not chain:
            return ''
        return {
            Chain.SOL: 'solana',
            Chain.ERC20: 'eth',
            Chain.BSC: 'bsc',
        }[chain]

    @classmethod
    def get_kline_timeframe(cls, period: KlinePeriod):
        return {
            KlinePeriod.MIN: 'minute',
            KlinePeriod.HOUR: 'hour',
            KlinePeriod.DAY: 'day',
        }[period]

    @classmethod
    def _handle_response(cls, r) -> dict | list:
        if isinstance(r, list):
            return r
        if 'data' in r:
            return r['data']
        raise ServiceUnavailable(message=json.dumps(r))

    def _get(self, path, **params):
        self.number_of_calls += 1
        path_key = inspect.currentframe().f_back.f_back.f_code.co_name
        OnchainThirdPartyAPICallsCache().add(OnchainThirdPartyAPICallsCache.ThirdParty.COINGECKO, path_key)
        return self._client.get(path, **params)

    def get(self, path, **params):
        r = self._get(path, **params)
        return self._handle_response(r)

    def get_raw(self, path, **params):
        # 部分接口需要获取接口返回的included字段, 该字段与data字段平级
        return self._get(path, **params)

    def get_coins(self):
        """查询支持的所有Token列表"""
        return self.get('/api/v3/coins/list', include_platform='true')

    def get_hot_pools(self, page: int):
        """根据网络查询热门Pool列表"""
        return self.get(self.get_onchain_dex_api('trending_pools'), page=page)

    def get_token(self, token_address: str):
        """查询单个Token"""
        return self.get_raw(self.get_onchain_dex_api(f'tokens/{token_address}'), include='top_pools')

    def get_tokens(self, token_addresses: list[str]):
        """批量查询Token"""
        return self.get_raw(self.get_onchain_dex_api(f'tokens/multi/{",".join(token_addresses)}'), include='top_pools')

    def get_token_by_ids(self, token_ids: list[str]):
        """根据ID批量查询Token"""
        return self.get('/api/v3/coins/markets', vs_currency='usd', ids=",".join(token_ids))

    def get_simple_price_by_ids(self, token_ids: list[str]):
        """根据ID批量查询Token价格"""
        return self.get_raw(
            '/api/v3/simple/price',
            vs_currencies='usd',
            include_24hr_vol='true',
            include_24hr_change='true',
            ids=",".join(token_ids),
        )

    def get_token_info(self, token_address: str):
        """查询Token Info"""
        return self.get(self.get_onchain_dex_api(f'tokens/{token_address}/info'))

    def get_pool(self, pool_address: str):
        """查询Pool信息"""
        return self.get(self.get_onchain_dex_api(f'pools/{pool_address}'))

    def get_pools(self, pool_addresses: list[str]):
        """查询Pool信息"""
        return self.get(self.get_onchain_dex_api(f'pools/multi/{",".join(pool_addresses)}'))

    def get_token_pools(self, token_address: str, page: int = 1):
        """查询Token热门Pool列表"""
        return self.get(self.get_onchain_dex_api(f'tokens/{token_address}/pools'), page=page)

    def get_kline(
            self, pool_address: str, period: KlinePeriod, *,
            before_timestamp: int = None, limit: int = 1000,
            currency: str = 'usd', include_empty_intervals: bool = False,
    ):
        """查询Kline信息"""
        params = dict(
            limit=limit,
            currency=currency,
            include_empty_intervals='false' if not include_empty_intervals else 'true',
        )
        if before_timestamp:
            params['before_timestamp'] = before_timestamp
        return self.get(
            self.get_onchain_dex_api(f'pools/{pool_address}/ohlcv/{self.get_kline_timeframe(period)}'),
            **params
        )


class GoplusClient:
    class SignatureVerificationFailure(Exception):
        # 签名失败
        pass

    class TooManyRequests(Exception):
        # 调用频繁
        pass

    def __init__(self, chain: Chain, force_update: bool = False):
        self._chain = chain
        goplus_config = config['ONCHAIN_CONFIGS']['goplus']
        authorization = GoPlusLabsAuthorizationCache().get_authorization()
        if not authorization or force_update:
            authorization = self.update_authorization(goplus_config)
        self._client = RESTClient(goplus_config['host'], headers={'Authorization': authorization}, timeout=10)

    @classmethod
    def update_authorization(cls, goplus_config: dict[str, str]) -> str:
        host, key, secret = goplus_config['host'], goplus_config['key'], goplus_config['secret']
        t = int(time.time())
        sign_str = f'{key}{t}{secret}'
        sha1 = hashlib.sha1()
        sha1.update(sign_str.encode('utf-8'))
        body = {
            'app_key': key,
            'sign': sha1.hexdigest(),
            'time': t,
        }
        result = cls._handle_response(requests.post(urljoin(host, '/api/v1/token'), data=body).json())
        authorization = result['access_token']
        ttl = result['expires_in'] - 100
        GoPlusLabsAuthorizationCache().set_authorization(authorization, ttl)
        return authorization

    @classmethod
    def _handle_response(cls, r):
        if r['code'] == 4012:
            # 签名失败的异常
            raise cls.SignatureVerificationFailure
        if r['code'] == 4029:
            # 调用频繁
            raise cls.TooManyRequests
        if r['code'] != 1:
            raise ServiceUnavailable(message=json.dumps(r))
        return r['result']

    def get(self, path, **params):
        r = self._client.get(path, **params)
        OnchainThirdPartyAPICallsCache().add(OnchainThirdPartyAPICallsCache.ThirdParty.GO_PLUS, path)
        return self._handle_response(r)

    @classmethod
    def get_security_url(cls, chain: Chain):
        return {
            Chain.SOL: '/api/v1/solana/token_security',
            Chain.ERC20: '/api/v1/token_security/1',
            Chain.BSC: '/api/v1/token_security/56',
        }[chain]

    def get_token_security(self, token_address: str):
        return self.get(self.get_security_url(self._chain), contract_addresses=token_address)


class DataInterfaceHelper:

    def __init__(self, chain: Chain = None):
        self.chain = chain
        self.address_helper = OnchainAddressHelper(chain) if chain else None

    def get_hot_token_address(self, limit: int = 200, whitelist: set[str] = None) -> set[str]:
        raise NotImplementedError

    def sort_all_token_address(self, blocklist: set[str]) -> list[str]:
        raise NotImplementedError

    def get_token_data_list(self, token_addresses: Iterable[str]) -> list[TokenData]:
        raise NotImplementedError

    def get_token_info(self, token_address: str) -> TokenInfo:
        raise NotImplementedError

    def get_token_quote_list(self, token_addresses: Iterable[str]) -> list[TokenQuote]:
        raise NotImplementedError

    def get_token_quote_list_super(self, token_addresses: Iterable[str]) -> list[TokenQuote]:
        """单次查询支持更高Token数量的方法"""
        raise NotImplementedError

    @classmethod
    def kline_prev_timestamp(cls, period: KlinePeriod, ts: int) -> int:
        """计算上一个时间戳"""
        if period == KlinePeriod.DAY:
            return ts - 86400
        elif period == KlinePeriod.HOUR:
            return ts - 3600
        return ts - 60

    @classmethod
    def kline_limit(cls):
        """每次查询K线接口返回的最大条数"""
        raise NotImplementedError

    def get_kline(
            self, pool_address: str, period: KlinePeriod, start_time: int, end_time: int = None
    ) -> list[TokenKline]:
        """根据时间范围查询K线数据, end为None时查询到最新的数据"""
        raise NotImplementedError

    def init_kline(
            self, pool_address: str, period: KlinePeriod, limit: int = None
    ) -> list[TokenKline]:
        """初始化K线时按照固定数量请求"""
        raise NotImplementedError

    @property
    def number_of_calls(self):
        raise NotImplementedError


class CoingeckoHelper(DataInterfaceHelper):

    def __init__(self, chain: Chain = None):
        self.client = CoingeckoClient(chain)
        super().__init__(chain)

    def _get_address_from_id(self, id_str: str) -> str:
        return self.address_helper.normalise_address(id_str.split('_', 1)[-1])

    def get_hot_token_address(self, limit: int = 200, whitelist: set[str] = None) -> set[str]:
        if not whitelist:
            whitelist = {}
        max_page = 30
        token_set = set()
        for page in range(1, max_page + 1):
            for item in self.client.get_hot_pools(page):
                token_address = self._get_address_from_id(item['relationships']['base_token']['data']['id'])
                if not self.address_helper.validate_address(token_address):
                    continue
                token_address = self.address_helper.normalise_address(token_address)
                if token_address not in whitelist:
                    continue
                token_set.add(token_address)
            if len(token_set) >= limit:
                break
        return token_set

    def get_id_cache(self):
        return {
            Chain.SOL: OnchainCoingeckoTokenIDSOLCache,
            Chain.ERC20: OnchainCoingeckoTokenIDETHCache,
            Chain.BSC: OnchainCoingeckoTokenIDBSCCache,
        }[self.chain]()

    def sort_all_token_address(self, blocklist: set[str]) -> list[str]:
        cache = self.get_id_cache()
        low_value_cache = OnchainCoingeckoLowValueTokenIDCache()
        id_to_address_map = {token_id: address for address, token_id in cache.all().items() if address not in blocklist}
        low_value_token_ids = set(low_value_cache.all())
        all_ids = [token_id for token_id in id_to_address_map.keys() if token_id not in low_value_token_ids]

        new_low_value_token_ids = []

        data = []
        for t_ids in batch_iter(all_ids, 500):
            for token_id, item in self.client.get_simple_price_by_ids(t_ids).items():
                amount_24h = self._get_decimal(item.get('usd_24h_vol')) or Decimal()
                if amount_24h < Decimal('100'):
                    new_low_value_token_ids.append(token_id)
                data.append((token_id, amount_24h))
        data.sort(key=lambda x: x[1], reverse=True)

        low_value_cache.set(new_low_value_token_ids)

        return [id_to_address_map[token_id] for token_id, _, in data if token_id in id_to_address_map]

    @classmethod
    def _get_value_from_keys(cls, data: dict, *keys: str, default: str = '') -> str:
        if not data or not keys:
            return default
        d = data
        for key in keys[:-1]:
            if key not in d:
                return default
            d = d[key]
        return d.get(keys[-1], default)

    def _check_pool_id(self, token_address: str, pool_id: str, included_map: dict[str, dict]) -> str | None:
        if pool_id not in included_map:
            return None
        pool_address = self._get_address_from_id(pool_id)
        if not self.address_helper.validate_address(pool_address):  # 存在pool address为tx hash的脏数据
            return None
        pool_data = included_map[pool_id]
        pool_name = self._get_value_from_keys(pool_data, 'attributes', 'name')
        if pool_name.count('/') != 1:
            # 存在Token1 / Token2 / Token3这种奇葩Pool
            return None
        base_id = self._get_value_from_keys(pool_data, 'relationships', 'base_token', 'data', 'id')
        if self._get_address_from_id(base_id) != token_address:
            return None
        return pool_address

    def _get_top_pool(self, token_address: str, relationships: dict, included_map: dict[str, dict]) -> str:
        """优先从relationships中获取, 检查该Token是否为pool中的base token, 未匹配时从token pools列表中遍历获取"""
        top_pools = relationships['top_pools']['data']
        if not top_pools:
            # 不存在top pool的直接返回空
            return ''
        for top_pool in top_pools:
            # 优先从附带返回的relationships中获取top pool
            if pool_address := self._check_pool_id(token_address, top_pool['id'], included_map):
                return pool_address

        # relationships中符合的top pool时从token pools接口中查询, 最多查询3页
        for page in range(1, 4):
            top_pool_data = self.client.get_token_pools(token_address, page=page)
            for top_pool in top_pool_data:
                pool_id = top_pool['id']
                if pool_address := self._check_pool_id(token_address, pool_id, {pool_id: top_pool}):
                    included_map[pool_id] = top_pool    # 获取到新的pool信息填充会included_map中
                    return pool_address
            if len(top_pool_data) < 20:
                break

        return ''

    @classmethod
    def _get_decimal(cls, amount):
        if not amount:
            return None
        try:
            return Decimal(str(amount))
        except:  # noqa
            return None

    @classmethod
    def _get_included_map(cls, included_data: list[dict]) -> dict[str, dict]:
        return {included['id']: included for included in included_data}

    def get_token_data_list(self, token_addresses: Iterable[str]) -> list[TokenData]:
        token_onchain_data = {}
        token_id_to_address_map = {}
        top_pool_token_address_map = {}
        included_map = {}
        for t_addresses in batch_iter(token_addresses, 30):
            resp = self.client.get_tokens(t_addresses)
            _included_map = self._get_included_map(resp['included'])
            for item in resp['data']:
                attributes = item['attributes']
                token_address = self.address_helper.normalise_address(attributes['address'])
                top_pool = self._get_top_pool(token_address, item['relationships'], _included_map)
                attributes['top_pool'] = top_pool
                token_onchain_data[token_address] = attributes
                if attributes['coingecko_coin_id']:
                    token_id_to_address_map[attributes['coingecko_coin_id']] = token_address
                if top_pool:
                    top_pool_token_address_map[top_pool] = token_address
            included_map.update(_included_map)

        token_id_data = {}
        for t_ids in batch_iter(token_id_to_address_map.keys(), 100):
            for item in self.client.get_token_by_ids(t_ids):
                if item['id'] not in token_id_to_address_map:
                    continue
                token_id_data[token_id_to_address_map[item['id']]] = item

        top_pool_data = {}
        for pool_id, pool_data in included_map.items():
            top_pool_data[self._get_address_from_id(pool_id)] = pool_data

        data = []
        for token_address in token_addresses:
            if token_address not in token_onchain_data:
                continue

            onchain_data = token_onchain_data[token_address]
            top_pool = onchain_data['top_pool']
            name = onchain_data['name']
            symbol = onchain_data['symbol']
            decimals = onchain_data['decimals']
            logo = onchain_data['image_url']
            if logo and '?' in logo:
                logo = logo.split('?')[0]
            total_supply = self._get_decimal(onchain_data['normalized_total_supply'])
            amount_24h = self._get_decimal(onchain_data['volume_usd']['h24'])  # 24H成交量选本链中最大pool的数据
            price = self._get_decimal(onchain_data['price_usd'])

            circulating_supply, change_24h, highest_24h, lowest_24h = Decimal(0), Decimal(0), Decimal(0), Decimal(0)
            if token_address in token_id_data:
                id_data = token_id_data[token_address]
                _price = self._get_decimal(id_data['current_price'])
                if not self._is_loss_of_accuracy(_price):
                    # Quote接口中没有精度丢失的price应覆盖Base接口中的price(因为Base接口的price是该链上的价格, 并非全网总和价格)
                    price = _price
                _total_supply = self._get_decimal(id_data['total_supply'])
                if _total_supply:
                    total_supply = _total_supply  # Quote接口中的total_supply是全网数据
                circulating_supply = self._get_decimal(id_data['circulating_supply'])
                change_24h = self._get_decimal(id_data['price_change_percentage_24h'])
                if change_24h:
                    change_24h = quantize_amount(change_24h, 8)
                highest_24h = self._get_decimal(id_data['high_24h'])
                lowest_24h = self._get_decimal(id_data['low_24h'])

            top_pool_launch_time, top_pool_name, top_pool_liquidity = None, None, None
            if top_pool and top_pool in top_pool_data:
                pool_data = top_pool_data[top_pool]['attributes']
                top_pool_launch_time = str_to_datetime(pool_data['pool_created_at'], tz=UTC)
                top_pool_name = pool_data['name']
                top_pool_liquidity = self._get_decimal(pool_data['reserve_in_usd'])
                if _amount_24h := self._get_decimal(self._get_value_from_keys(pool_data, 'volume_usd', 'h24')):
                    amount_24h = _amount_24h

            data.append(TokenData(
                chain=self.chain,
                contract=token_address,
                top_pool=top_pool,
                symbol=symbol,
                name=name,
                decimals=decimals,
                logo=logo,
                total_supply=total_supply,
                circulating_supply=circulating_supply,
                amount_24h=amount_24h,
                price=price,
                change_24h=change_24h,
                highest_24h=highest_24h,
                lowest_24h=lowest_24h,
                top_pool_launch_time=top_pool_launch_time,
                top_pool_name=top_pool_name,
                top_pool_liquidity=top_pool_liquidity,
            ))

        return data

    def get_token_info(self, token_address: str) -> TokenInfo:
        attributes = self.client.get_token_info(token_address)['attributes']
        website = None
        if attributes['websites']:
            website = attributes['websites'][0]
        holders = attributes['holders']
        distribution_percentage = holders['distribution_percentage']
        if distribution_percentage and 'top_10' in distribution_percentage:
            # distribution_percentage可能为None
            top10_percentage = Decimal(distribution_percentage['top_10'])
            if top10_percentage:
                top10_percentage = quantize_amount(top10_percentage, 8)
        else:
            top10_percentage = None
        return TokenInfo(
            chain=self.chain,
            contract=token_address,
            website=website,
            community=dict(
                discord=attributes['discord_url'],
                telegram=attributes['telegram_handle'],
                twitter=attributes['twitter_handle'],
            ),
            about=attributes['description'],
            holders_count=holders['count'],
            top10_percentage=top10_percentage,
        )

    def _get_24h_cache_map(self, token_addresses: Iterable[str]) -> dict[str, dict[str, Decimal]]:
        """因为Coingecko的get_token_by_ids接口返回的价格字段为float, 数量过小时会丢失精度, 因此发生精度丢失时该数据从缓存中获取填充"""
        if not token_addresses:
            return {}
        token_id_to_address_map = {
            token.id: token.contract for token in OnchainToken.query.filter(
                OnchainToken.chain == self.chain,
                OnchainToken.contract.in_(token_addresses),
            ).all()
        }
        quote_data = OnchainTokenQuoteCache().get_many(list(token_id_to_address_map.keys()))
        data = {}
        for token_id, quote in quote_data.items():
            if token_id not in token_id_to_address_map:
                continue
            data[token_id_to_address_map[token_id]] = quote
        return data

    @classmethod
    def _is_loss_of_accuracy(cls, price: Decimal) -> bool:
        """Coingecko通过ID查询接口返回的价格数据是float类型, 价格过低时会造成精度丢失, 此函数用于判断是否发生精度丢失的情况"""
        if not price:
            return True
        if price >= Decimal('0.000001'):
            # 价格低于0.000001的才通过有效位数进行判断
            return False
        # 只有一个有效位数的认为精度丢失
        return len(price.as_tuple().digits) <= 1

    def get_token_quote_list(self, token_addresses: Iterable[str]) -> list[TokenQuote]:
        # 这里的TokenID是指Coingecko的TokenID, 这个函数没有站内TokenID的概念
        cache = self.get_id_cache()
        row_map = cache.get_token_ids(list(token_addresses))
        if not row_map:
            return []
        address_to_token_id_map = {
            address: row_map[address]
            for address in list(row_map.keys())[:6000]
        }  # 这里限制每次最多处理的Token数量, 6000表示最多查询30次接口
        token_id_to_address_map = {token_id: address for address, token_id in address_to_token_id_map.items()}

        cache_data = self._get_24h_cache_map(address_to_token_id_map.keys())

        data = []
        for t_ids in batch_iter(address_to_token_id_map.values(), 100):
            for item in self.client.get_token_by_ids(t_ids):
                if item['id'] not in token_id_to_address_map:
                    continue
                token_address = token_id_to_address_map[item['id']]
                price = self._get_decimal(item.get('current_price', '0'))
                cache_item = cache_data.get(token_address, {})
                if self._is_loss_of_accuracy(price):
                    # 该接口返回的价格字段为float类型, 价格过低时存在精度丢失问题
                    # 精度丢失时取缓存中的价格填充进去(get_token_data_list每小时会更新一次价格信息)
                    price = cache_item.get('price') or price
                price_change_percentage_24h = self._get_decimal(item.get('price_change_percentage_24h', '0'))
                if price_change_percentage_24h:
                    price_change_percentage_24h = quantize_amount(price_change_percentage_24h, 8)
                data.append(TokenQuote(
                    chain=self.chain,
                    contract=token_address,
                    price=price,
                    change_24h=price_change_percentage_24h,
                    highest_24h=self._get_decimal(item.get('high_24h', '0')),
                    lowest_24h=self._get_decimal(item.get('low_24h', '0')),
                    amount_24h=cache_item.get('amount_24h'),
                ))
        return data

    def get_token_quote_list_super(self, token_addresses: Iterable[str]) -> list[TokenQuote]:
        """单次查询支持更高Token数量的方法"""
        # 这里的TokenID是指Coingecko的TokenID, 这个函数没有站内TokenID的概念
        cache = self.get_id_cache()
        address_to_token_id_map = cache.get_token_ids(list(token_addresses))
        if not address_to_token_id_map:
            return []
        token_id_to_address_map = {token_id: address for address, token_id in address_to_token_id_map.items()}

        cache_data = self._get_24h_cache_map(address_to_token_id_map.keys())

        data = []
        for t_ids in batch_iter(address_to_token_id_map.values(), 500):
            for token_id, item in self.client.get_simple_price_by_ids(t_ids).items():
                if token_id not in token_id_to_address_map:
                    continue
                token_address = token_id_to_address_map[token_id]
                if token_address not in cache_data:
                    continue
                cache_item = cache_data[token_address]
                price = self._get_decimal(item.get('usd', '0'))
                if self._is_loss_of_accuracy(price):
                    # 该接口返回的价格字段为float类型, 价格过低时存在精度丢失问题
                    # 精度丢失时取缓存中的价格填充进去(get_token_data_list每小时会更新一次价格信息)
                    price = cache_item['price'] or price
                price_change_percentage_24h = self._get_decimal(item.get('usd_24h_change', '0'))
                if price_change_percentage_24h:
                    price_change_percentage_24h = quantize_amount(price_change_percentage_24h, 8)
                data.append(TokenQuote(
                    chain=self.chain,
                    contract=token_address,
                    price=price,
                    change_24h=price_change_percentage_24h,
                    highest_24h=cache_item['highest_24h'],
                    lowest_24h=cache_item['lowest_24h'],
                    amount_24h=cache_item['amount_24h'],
                ))
        return data

    @classmethod
    def _ohlcv_to_kline(cls, ohlcv: list) -> TokenKline:
        return TokenKline(
            t=ohlcv[0],
            o=cls._get_decimal(ohlcv[1]),
            h=cls._get_decimal(ohlcv[2]),
            l=cls._get_decimal(ohlcv[3]),
            c=cls._get_decimal(ohlcv[4]),
            v=cls._get_decimal(ohlcv[5]),
        )

    @classmethod
    def kline_limit(cls):
        return 1000

    def get_kline(
            self, pool_address: str, period: KlinePeriod, start_time: int, end_time: int = None
    ) -> list[TokenKline]:
        start_time = period.normalise_time(start_time)
        if not end_time:
            before_timestamp = None
        else:
            before_timestamp = self.kline_prev_timestamp(period, period.normalise_time(end_time, is_end_time=True))
        curr_ts = period.normalise_time(current_timestamp(to_int=True))
        stop_flag = False
        data = []
        while True:
            ohlcv_list = self.client.get_kline(
                pool_address,
                period,
                before_timestamp=before_timestamp,
            )['attributes']['ohlcv_list']
            if len(ohlcv_list) == 0:
                break
            for ohlcv in ohlcv_list:
                k = self._ohlcv_to_kline(ohlcv)
                if k.t == curr_ts:
                    # 最新一条数据还在动态变化, 需要等下一个时刻再更新上一个时刻的数据
                    continue
                if k.t <= start_time:
                    stop_flag = True
                    break
                data.append(k)
            if stop_flag:
                break
            before_timestamp = self.kline_prev_timestamp(period, data[-1].t)
        return data

    def init_kline(
            self, pool_address: str, period: KlinePeriod, limit: int = None
    ) -> list[TokenKline]:
        if not limit:
            limit = {
                KlinePeriod.MIN: 10,  # 大概1周数据
                KlinePeriod.HOUR: 2,  # 大概3个月数据
                KlinePeriod.DAY: 1,  # 大概3年数据
            }[period]
        curr_ts = period.normalise_time(current_timestamp(to_int=True))
        data = []
        before_timestamp = None
        for _ in range(limit):
            ohlcv_list = self.client.get_kline(
                pool_address,
                period,
                before_timestamp=before_timestamp,
            )['attributes']['ohlcv_list']
            if len(ohlcv_list) == 0:
                break
            for ohlcv in ohlcv_list:
                k = self._ohlcv_to_kline(ohlcv)
                if k.t == curr_ts:
                    continue
                data.append(k)
            before_timestamp = self.kline_prev_timestamp(period, data[-1].t)
        return data

    @property
    def number_of_calls(self):
        return self.client.number_of_calls

    def get_token_ids(self) -> dict[Chain, dict[str, str]]:
        platform_map = {
            'solana': Chain.SOL,
            'ethereum': Chain.ERC20,
            'binance-smart-chain': Chain.BSC,
        }
        data = defaultdict(dict)
        for item in self.client.get_coins():
            for platform, token_address in item['platforms'].items():
                if platform not in platform_map:
                    continue
                data[platform_map[platform]][token_address] = item['id']
        return data


class GoplusHelper:

    def __init__(self, chain: Chain):
        self.chain = chain
        self.address_helper = OnchainAddressHelper(chain)
        self.client = GoplusClient(chain)

    @classmethod
    def _is_bool(cls, _is: str | None) -> bool | None:
        if _is is None:
            return None
        return _is == '1' or _is == 1

    @classmethod
    def _is_reasonable_tax(cls, data: dict) -> bool:
        if not cls._is_bool(data['is_in_dex']):
            return True
        tax_limit = Decimal(config['ONCHAIN_CONFIGS']['risk']['tax_limit'])
        if Decimal(data.get('buy_tax') or '0') > tax_limit or Decimal(data.get('sell_tax') or '0') > tax_limit:
            return False
        return True

    @classmethod
    def _is_not_owner(cls, data: dict) -> bool | None:
        if 'owner_address' not in data:
            # 代码未开源时不知道owner情况
            return None
        return data['owner_address'] in {None, '', '******************************************'}

    @classmethod
    def _is_lp_locked(cls, data: dict) -> bool | None:
        """LP是否锁定(判定锁定的LP地址是否超过阈值)"""
        if 'lp_holders' not in data or not data['lp_holders']:
            return None
        locked_percent = Decimal()
        for holder in data['lp_holders']:
            if cls._is_bool(holder.get('is_locked')):
                locked_percent += Decimal(holder.get('percent') or '0')
        return locked_percent >= Decimal('0.5')

    @classmethod
    def _get_eth_risk_data(cls, data: dict) -> dict[str, bool]:
        return dict(
            is_not_honeypot=not cls._is_bool(data.get('is_honeypot')),
            is_reasonable_tax=cls._is_reasonable_tax(data),
            is_open_source=cls._is_bool(data.get('is_open_source')),
            is_lp_locked=cls._is_lp_locked(data),
            is_not_owner=cls._is_not_owner(data),
            is_not_mintable=not cls._is_bool(data.get('is_mintable')),
            is_not_blacklisted=not cls._is_bool(data.get('is_blacklisted')),
        )

    @classmethod
    def _is_sol_bool(cls, _is: dict | None) -> bool | None:
        if _is is None:
            return None
        return cls._is_bool(_is.get('status'))

    @classmethod
    def _get_sol_risk_data(cls, data: dict) -> dict[str, bool]:
        return dict(
            is_not_mintable=not cls._is_sol_bool(data.get('mintable')),
            is_not_freezable=not cls._is_sol_bool(data.get('freezable')),
            is_lp_locked=cls._is_lp_locked(data),
            is_not_mutable=not cls._is_sol_bool(data.get('metadata_mutable')),
        )

    def get_token_security(self, token_address: str) -> dict:
        """签名失败时自动更新签名并重试"""
        try:
            result = self.client.get_token_security(token_address)
        except GoplusClient.SignatureVerificationFailure:
            self.client = GoplusClient(self.chain, force_update=True)
            result = self.client.get_token_security(token_address)
        except GoplusClient.TooManyRequests:
            current_app.logger.warning(f'goplus err, too many requests, token: {token_address}')
            return {}
        return result

    def get_risk_data(self, token_address: str) -> dict[str, bool] | None:
        # 定义所有字段为正向含义, True表示该项风险检测通过, False表示该项风险检测不通过, None表示该项风险未知
        # is_reasonable_holder字段通过TokenInfo获取, 接口实时计算top10_percentage > 50%
        result = self.get_token_security(token_address)
        if token_address not in result:
            return None
        data = result[token_address]
        if self.chain == Chain.SOL:
            return self._get_sol_risk_data(data)
        return self._get_eth_risk_data(data)
