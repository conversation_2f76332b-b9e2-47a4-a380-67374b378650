# -*- coding: utf-8 -*-
import json
from collections import defaultdict
from datetime import datetime, timedelta, date
from decimal import Decimal
from functools import cached_property
from typing import Optional, Iterable, List

import requests
from eth_account.messages import encode_defunct
from web3 import Web3
from web3.auto import w3
from sqlalchemy import func

from app.utils.date_ import now, timestamp_to_datetime, datetime_to_time, current_timestamp, yesterday
from . import PriceManager
from .clients import ServerClient, SPOT_ACCOUNT_ID, MAX_ORDER_ACCOUNT_ID
from .external_dbs import ExchangeLogDB, TradeSummaryDB, PerpetualSummaryDB
from .market_maker import MarketMakerHelper
from .sub_account import get_sub_account_balance_map
from .user_status import VipUserChangeType
from .. import config
from ..common import AccountBalanceType, PrecisionEnum
from ..models import (
    SubAccount, BindingAddress, UserTradeSummary, VipUser, db, AppraisalHistory,
    Mar<PERSON><PERSON>oanOrder, User<PERSON>tatus<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Credit<PERSON>ser, <PERSON><PERSON><PERSON>ce,
)
from ..models.pledge import PledgePosition
from app.exceptions import BindingAddressSignatureError
from app.business.fee_constant import VIP_LEVEL_DICT
from ..models.vip import UserVipSource, UserVipSnapshot, VipUserBuff
from ..utils import batch_iter, amount_to_str, g_map


class VipHelper(object):

    MAX_LEVEL = 5

    @classmethod
    def get_vip_level2desc(cls) -> dict:
        vip_levels = {level: f'VIP{level}' for level in VIP_LEVEL_DICT.keys()}
        vip_levels.update({0: 'VIP0'})
        return vip_levels

    @classmethod
    def get_current_all_cet(cls, user_id: int) -> Decimal:
        in_site_cet = cls._get_current_in_site_cet(user_id)
        out_site_cet = cls._get_current_out_site_cet(user_id)
        return in_site_cet + out_site_cet

    @classmethod
    def get_current_level_all(cls, user_id: int) -> (str, str):
        from app.business.account_pl import RealtimeAccountProfitLossProcessor, TOTAL_ACCOUNT

        cet_amount = cls.get_current_all_cet(user_id)
        main_user_id = user_id
        q = SubAccount.query.filter(
            SubAccount.main_user_id == main_user_id,
            SubAccount.status == SubAccount.Status.VALID,
        ).with_entities(SubAccount.user_id).all()
        sub_user_ids = [v.user_id for v in q]
        if sub_user_ids:
            sub_balance_map = get_sub_account_balance_map(main_user_id)
            sub_balance_usd = sum(sub_balance_map.values())
        else:
            sub_balance_usd = Decimal()
        position_usd = RealtimeAccountProfitLossProcessor.get_real_time_account_type_usd(
            main_user_id,
            TOTAL_ACCOUNT,
            False)
        position_usd += sub_balance_usd
        credit_mapping = cls.get_credit_balance_usd(main_user_id=main_user_id)
        position_usd -= credit_mapping[main_user_id]
        return amount_to_str(cet_amount, PrecisionEnum.COIN_PLACES), amount_to_str(position_usd, 2)

    @classmethod
    def get_last_trade_usd(cls, user_id: int) -> (str, str):
        spot, perp = VipLevelCalculator.calculate_trade_amounts(user_id=user_id)
        spot_usd = spot[user_id]
        perpetual_usd = perp[user_id]
        return amount_to_str(spot_usd, 2), amount_to_str(perpetual_usd, 2)

    @classmethod
    def get_vip_snapshot_data(cls, user_id: int, fetch_current=True) -> dict:
        ret = {
            'spot_trade_usd': '0',
            'perpetual_trade_usd': '0',
            'position_usd': '0',
            'cet_amount': '0',
        }
        model = UserVipSnapshot
        last_row = model.query.with_entities(
            model.report_date
        ).order_by(
            model.id.desc()
        ).first()
        if not last_row:
            if not fetch_current:
                return ret
            cet_amount, position_usd = cls.get_current_level_all(user_id)
            spot_trade_usd, perpetual_trade_usd = cls.get_last_trade_usd(user_id)
            return {
                'spot_trade_usd': spot_trade_usd,
                'perpetual_trade_usd': perpetual_trade_usd,
                'position_usd': position_usd,
                'cet_amount': cet_amount,
            }
        last_date = last_row.report_date
        row = model.query.filter(
            model.report_date == last_date,
            model.user_id == user_id
        ).first()
        if row:
            ret.update({
                'spot_trade_usd': amount_to_str(row.spot_trade_usd, PrecisionEnum.CASH_PLACES),
                'perpetual_trade_usd': amount_to_str(row.perpetual_trade_usd, PrecisionEnum.CASH_PLACES),
                'position_usd': amount_to_str(row.position_usd, PrecisionEnum.CASH_PLACES),
                'cet_amount': amount_to_str(row.cet_amount, PrecisionEnum.COIN_PLACES),
            })
        return ret

    @classmethod
    def get_user_cet_snap_shot_balance(cls, ts: int, user_ids: Optional[Iterable] = None) -> dict:
        user_without_margin_data = ExchangeLogDB.get_user_account_type_balances(
            ts, 'CET',
            [AccountBalanceType.SPOT.value,
             AccountBalanceType.PLEDGE.value,
             AccountBalanceType.AMM.value,
             AccountBalanceType.STAKING.value], user_ids)
        margin_user_balance_data = ExchangeLogDB.get_user_account_type_balances(
            ts, 'CET', [AccountBalanceType.MARGIN.value], user_ids)
        dt = timestamp_to_datetime(ts)
        if user_ids:
            q = MarginLoanOrder.query.filter(
                MarginLoanOrder.updated_at <= dt,
                MarginLoanOrder.user_id.in_(user_ids))
        else:
            q = MarginLoanOrder.query.filter(
                MarginLoanOrder.updated_at <= dt
            )
        records = q.filter(
            MarginLoanOrder.asset == "CET",
            MarginLoanOrder.status.in_(
                [MarginLoanOrder.StatusType.PASS,
                 MarginLoanOrder.StatusType.ARREARS,
                 MarginLoanOrder.StatusType.BURST])
        ).group_by(MarginLoanOrder.user_id).with_entities(
            MarginLoanOrder.user_id,
            func.sum(MarginLoanOrder.unflat_amount +
                     MarginLoanOrder.interest_amount).label("total_unflat_amount")
        ).all()
        margin_user_loan_data = {record.user_id: record.total_unflat_amount for record in records}
        user_margin_data = {}
        for user_id, amount in margin_user_balance_data.items():
            user_amount = max(amount - margin_user_loan_data.get(user_id, Decimal()),
                              Decimal())
            user_margin_data[user_id] = user_amount
        if user_ids:
            pledge_q = PledgePosition.query.filter(
                PledgePosition.updated_at < dt,
                PledgePosition.user_id.in_(user_ids),
            )
        else:
            pledge_q = PledgePosition.query.filter(
                PledgePosition.updated_at < dt,
            )
        pledge_unflats = pledge_q.filter(
            PledgePosition.loan_asset == "CET",
            PledgePosition.status.in_(PledgePosition.ACTIVE_STATUSES),
        ).with_entities(
            PledgePosition.user_id,
            func.sum(PledgePosition.debt_amount + PledgePosition.interest_amount).label('total_unflat_amount')
        ).group_by(
            PledgePosition.user_id,
        ).all()
        pledge_unflat_dict = {r.user_id: r.total_unflat_amount for r in pledge_unflats}
        user_ids = set(user_without_margin_data.keys()) | set(user_margin_data.keys())
        result = {}
        for user_id in user_ids:
            amount = user_without_margin_data.get(user_id, Decimal()) + user_margin_data.get(user_id, Decimal())
            amount = max(amount - pledge_unflat_dict.get(user_id, Decimal()), Decimal())
            result[user_id] = amount
        return result

    @classmethod
    def get_user_snapshot_balance_usd(cls, ts: int) -> dict:
        user_without_margin_data = ExchangeLogDB.sum_user_account_type_balances(
            ts,
            [AccountBalanceType.SPOT.value,
             AccountBalanceType.PLEDGE.value,
             AccountBalanceType.PERPETUAL.value,
             AccountBalanceType.INVESTMENT.value,
             AccountBalanceType.AMM.value,
             AccountBalanceType.STAKING.value])
        margin_user_balance_data = ExchangeLogDB.sum_user_account_type_balances(
            ts, [AccountBalanceType.MARGIN.value])
        dt = timestamp_to_datetime(ts)
        q = MarginLoanOrder.query.filter(
            MarginLoanOrder.updated_at <= dt
        )
        records = q.filter(
            MarginLoanOrder.status.in_(
                [MarginLoanOrder.StatusType.PASS,
                 MarginLoanOrder.StatusType.ARREARS,
                 MarginLoanOrder.StatusType.BURST])
        ).group_by(
            MarginLoanOrder.user_id,
            MarginLoanOrder.asset,
        ).with_entities(
            MarginLoanOrder.user_id,
            MarginLoanOrder.asset,
            func.sum(MarginLoanOrder.unflat_amount +
                     MarginLoanOrder.interest_amount).label("total_unflat_amount")
        ).all()

        def _get_price_rates(_asset):
            if _asset not in price_rates:
                rate = PriceManager.asset_to_usd(_asset)
                price_rates[_asset] = rate
            return price_rates[_asset]

        price_rates = {}
        margin_user_loan_data = {}
        for record in records:
            usd = record.total_unflat_amount * _get_price_rates(record.asset)
            margin_user_loan_data[record.user_id] = usd
        user_margin_data = {}
        for user_id, amount_usd in margin_user_balance_data.items():
            user_amount_usd = max(amount_usd - margin_user_loan_data.get(user_id, Decimal()),
                                  Decimal())
            user_margin_data[user_id] = user_amount_usd
        pledge_q = PledgePosition.query.filter(
            PledgePosition.updated_at < dt,
        )
        pledge_unflats = pledge_q.filter(
            PledgePosition.status.in_(PledgePosition.ACTIVE_STATUSES),
        ).with_entities(
            PledgePosition.user_id,
            PledgePosition.loan_asset,
            func.sum(PledgePosition.debt_amount + PledgePosition.interest_amount).label('total_unflat_amount')
        ).group_by(
            PledgePosition.user_id,
            PledgePosition.loan_asset,
        ).all()
        pledge_unflat_dict = {}
        for r in pledge_unflats:
            usd = r.total_unflat_amount * _get_price_rates(r.loan_asset)
            pledge_unflat_dict[r.user_id] = usd
        user_ids = set(user_without_margin_data.keys()) | set(user_margin_data.keys())
        result = {}
        for user_id in user_ids:
            amount_usd = user_without_margin_data.get(user_id, Decimal()) + user_margin_data.get(user_id, Decimal())
            amount_usd = max(amount_usd - pledge_unflat_dict.get(user_id, Decimal()), Decimal())
            result[user_id] = amount_usd
        return result

    @classmethod
    def get_credit_balance_usd(cls, main_user_id: int = None) -> dict:
        ret = defaultdict(Decimal)
        credit_user_query = CreditUser.query.filter(
            CreditUser.status == CreditUser.StatusType.PASS,
        )
        if main_user_id:
            credit_user_query = credit_user_query.filter(
                CreditUser.user_id == main_user_id,
            )
        credit_users = {row.user_id for row in credit_user_query.all()}
        if not credit_users:
            return ret
        model = CreditBalance
        credit_asset_rows = model.query.filter(
            model.user_id.in_(credit_users),
        ).with_entities(
            model.asset,
            model.user_id,
            model.unflat_amount,
            model.interest_amount,
        ).all()
        asset_unflat_dict = defaultdict(lambda: defaultdict(Decimal))
        for row in credit_asset_rows:
            asset_unflat_dict[row.user_id][row.asset] = row.unflat_amount + row.interest_amount
        all_assets = set()
        for value in asset_unflat_dict.values():
            all_assets |= set(value.keys())
        rates_cache = PriceManager.assets_to_usd(all_assets)
        for _user_id, value in asset_unflat_dict.items():
            unflat_usd = sum(
                [Decimal(rates_cache.get(asset, Decimal(0))) * amount for asset, amount in value.items()]
            )
            ret[_user_id] = unflat_usd if unflat_usd > Decimal() else Decimal()
        return ret

    @classmethod
    def _get_users_margin_loan_asset_data(cls, user_ids: Iterable[int], asset: str) -> Decimal:
        q = MarginLoanOrder.query.filter(
            MarginLoanOrder.user_id.in_(user_ids),
            MarginLoanOrder.asset == asset,
            MarginLoanOrder.status.in_(
                [MarginLoanOrder.StatusType.PASS,
                 MarginLoanOrder.StatusType.ARREARS,
                 MarginLoanOrder.StatusType.BURST])
        ).with_entities(
            func.sum(MarginLoanOrder.unflat_amount +
                     MarginLoanOrder.interest_amount).label("total_unflat_amount")
        ).first()
        if q:
            return q.total_unflat_amount or Decimal()
        return Decimal()

    @classmethod
    def _get_current_in_site_cet(cls, user_id: int) -> Decimal:
        """ Vip-站内CET持仓量，不包括杠杆借入、质押借入的CET，包括用于质押的CET """
        from app.business.amm import get_user_amm_assets
        from app.business.pledge.helper import get_user_last_active_position

        main_user_id = user_id
        q = SubAccount.query.filter(
            SubAccount.main_user_id == main_user_id,
            SubAccount.status == SubAccount.Status.VALID,
        ).with_entities(SubAccount.user_id).all()
        user_ids = [main_user_id] + [v.user_id for v in q]

        client = ServerClient()
        margin_total = Decimal()
        without_margin_total = Decimal()
        for user_id in user_ids:
            balances_result = client.get_user_accounts_balances(user_id)
            for account_id, balance_data in balances_result.items():
                if "CET" not in balance_data:
                    continue
                account_id = int(account_id)
                cet_balance_data = balance_data["CET"]
                cet_amount = Decimal(cet_balance_data['available']) + Decimal(cet_balance_data['frozen'])
                if SPOT_ACCOUNT_ID < account_id <= MAX_ORDER_ACCOUNT_ID:
                    margin_total += cet_amount
                else:
                    without_margin_total += cet_amount
            without_margin_total += get_user_amm_assets(user_id, "CET")
        margin_loan_total = cls._get_users_margin_loan_asset_data(user_ids, "CET")
        margin_data = margin_total - margin_loan_total if margin_total > margin_loan_total else Decimal()
        pledge_pos = get_user_last_active_position(main_user_id, "CET")
        pledge_unflat = pledge_pos.total_unflat_amount if pledge_pos else Decimal()
        res = max(without_margin_total + margin_data - pledge_unflat, Decimal())
        return res

    @classmethod
    def _get_current_out_site_cet(cls, user_id: int) -> Decimal:
        q = BindingAddress.query.filter(
                BindingAddress.user_id == user_id,
                BindingAddress.status == BindingAddress.StatusType.PASS,
                ).with_entities(BindingAddress.address)
        addresses = [v.address for v in q]
        return cls.get_cet_balance_from_addresses(addresses)

    @classmethod
    def get_out_site_cet(cls, user_id: int, ts: int) -> Decimal:
        dt = datetime.utcfromtimestamp(ts)
        q = BindingAddress.query.filter(
                BindingAddress.user_id == user_id,
                BindingAddress.status == BindingAddress.StatusType.PASS,
                BindingAddress.binding_time < dt,
                ).with_entities(BindingAddress.address)
        addresses = [v.address for v in q]
        return cls.get_cet_balance_from_addresses(addresses)

    @classmethod
    def get_cet_balance_from_addresses(cls, addresses: List[str]) -> Decimal:
        if not addresses:
            return Decimal()
        url = config["BINDING_ADDRESS_CET_ASSET_URL"]
        amount = Decimal()
        for address in addresses:
            request_url = url.format(address=address)
            try:
                r = requests.get(request_url)
                if r.status_code != 200:
                    continue
                resp = r.json()
            except Exception:
                continue
            balance = Decimal(resp.get("data", {}).get("balance", "0"))
            staking_balances = sum([Decimal(v.get("staking", "0"))
                                    for v in resp.get("data", {}).get("staking", [])])
            amount += Decimal(balance)
            amount += Decimal(staking_balances)
        return amount

    @classmethod
    def get_every_address_cet_balance(cls, addresses: List[str]) -> dict[str, Decimal]:
        data = {}
        if not addresses:
            return {}

        url = config["BINDING_ADDRESS_CET_ASSET_URL"]
        data = {}
        for address in addresses:
            request_url = url.format(address=address)
            try:
                r = requests.get(request_url, timeout=10)
                if r.status_code != 200:
                    continue
                resp = r.json()
            except Exception:
                continue
            balance = Decimal(resp.get("data", {}).get("balance", "0"))
            staking_balances = sum([Decimal(v.get("staking", "0"))
                                    for v in resp.get("data", {}).get("staking", [])])
            amount = Decimal(balance) + Decimal(staking_balances)
            data[address] = amount
        return data

    @classmethod
    def get_binding_cet_data(cls, user_id: int) -> Decimal:
        q = BindingAddress.query.filter(
                BindingAddress.user_id == user_id,
                BindingAddress.status == BindingAddress.StatusType.PASS
                ).order_by(BindingAddress.binding_time.desc()
                           ).with_entities(
                BindingAddress.address, BindingAddress.binding_time
                )
        addresses = [v.address for v in q]
        return cls.get_cet_balance_from_addresses(addresses)

    @classmethod
    def get_user_binding_address_cet_details(cls, user_id: int) -> list:
        q = BindingAddress.query.filter(
                BindingAddress.user_id == user_id,
                BindingAddress.status == BindingAddress.StatusType.PASS
                ).order_by(BindingAddress.binding_time.desc()
                           ).with_entities(
                BindingAddress.address, BindingAddress.binding_time
                )
        addresses = [v.address for v in q]
        addresses_amount_details = cls.get_every_address_cet_balance(addresses)
        result = [
            {
                "address": v.address,
                "amount": addresses_amount_details.get(v.address, Decimal()),
                "time": v.binding_time
                } for v in q]
        return result

    @classmethod
    def get_next_level_data(cls, level: int) -> dict:
        return VIP_LEVEL_DICT.get(level, {}).get("rule", {})

    @classmethod
    def get_trade_detail(cls, user_id: int, start_date: date, end_date: date, system=UserTradeSummary.System.SPOT) -> dict:
        trade_summary = UserTradeSummary.query.filter(
            UserTradeSummary.system == system,
            UserTradeSummary.user_id == user_id,
            UserTradeSummary.report_date >= start_date,
            UserTradeSummary.report_date < end_date
        ).with_entities(
            UserTradeSummary.report_date,
            UserTradeSummary.trade_amount,
            UserTradeSummary.maker_amount,
            UserTradeSummary.taker_amount
        ).all()
        trade_summary = {x[0]: x for x in trade_summary}
        result = {}
        d = start_date
        while d < end_date:
            summary = trade_summary.get(d)
            key = d.strftime('%Y-%m-%d')
            if not summary:
                result[key] = {
                    'trade_amount': "0",
                    'maker_amount': "0",
                    'taker_amount': "0",
                }
            else:
                result[key] = {
                    'trade_amount': summary[1],
                    'maker_amount': summary[2],
                    'taker_amount': summary[3]
                }
            d += timedelta(days=1)
        return result

    @classmethod
    def list_trade_amount(cls, user_ids: Iterable,
                          start_date: date,
                          end_date: date) -> dict:
        rows = UserTradeSummary.query.filter(
                UserTradeSummary.user_id.in_(user_ids),
                UserTradeSummary.report_date <= end_date,
                UserTradeSummary.report_date >= start_date
                ).with_entities(
                UserTradeSummary.user_id,
                UserTradeSummary.system,
                func.sum(UserTradeSummary.trade_amount).label('trade_amount')
                ).group_by(
                UserTradeSummary.user_id,
                UserTradeSummary.system
                )
        ret = defaultdict(dict)
        for row in rows:
            ret[row.user_id][row.system] = row.trade_amount
        return ret

    @classmethod
    def list_vip_user_ids(cls) -> dict:
        return {v.user_id: v.level for v in
                VipUser.query.filter(
                        VipUser.status == VipUser.StatusType.PASS)}

    @classmethod
    def list_lock_vip(cls) -> dict:
        return {v.user_id: v.lock_level for v in
                VipUser.query.filter(
                        VipUser.status == VipUser.StatusType.PASS,
                        VipUser.is_lock.is_(True),
                        VipUser.expired_time >
                        now())}

    @classmethod
    def get_appraisal_detail_desc(cls, user_id: int) -> str:
        snapshot_data = VipHelper.get_vip_snapshot_data(user_id, fetch_current=False)
        desc = f"""
                当前考核数据：CET持仓量 {snapshot_data['cet_amount']}，近30天现货交易量 {snapshot_data['spot_trade_usd']}，
                近30天合约交易量 {snapshot_data['perpetual_trade_usd']}，资产总值(USD) {snapshot_data['position_usd']}
                """
        return desc

    @classmethod
    def get_vip_appraisal_result_desc(cls, user_id: int) -> str:
        report_date = date.today()  # 考核任务在 UTC+0 00:50 开始
        model = AppraisalHistory
        row = model.query.with_entities(
            model.user_id,
            model.result_status
        ).filter(
            model.business_type == model.BusinessType.VIP,
            model.report_date == report_date,
            model.user_id == user_id,
        ).first()
        if not row:
            desc = '-'
            return desc
        if row.result_status is model.ResultStatus.LEVEL_UP:
            desc = '达标升级'
        elif row.result_status is model.ResultStatus.LEVEL_DOWN:
            desc = '不达标降级'
        elif row.result_status is model.ResultStatus.NOT_CHANGE:
            desc = '等级不变'
        else:
            desc = '不满足条件取消'
        return desc

    @classmethod
    def add_appraisal_history(cls,
                              user_id: int,
                              report_date: date,
                              old_level: int,
                              new_level: int,
                              lock_level: int) -> int:
        if new_level < max(old_level, lock_level):
            result_status = AppraisalHistory.ResultStatus.LEVEL_DOWN
        elif new_level > max(old_level, lock_level):
            result_status = AppraisalHistory.ResultStatus.LEVEL_UP
        else:
            result_status = AppraisalHistory.ResultStatus.NOT_CHANGE
        if old_level and new_level == 0:
            result_status = AppraisalHistory.ResultStatus.NOT_LEAST_LEVEL
        r = AppraisalHistory.query.filter(
            AppraisalHistory.user_id == user_id,
            AppraisalHistory.business_type == AppraisalHistory.BusinessType.VIP,
            AppraisalHistory.report_date == report_date
        ).first()
        if not r:
            r = AppraisalHistory(
                user_id=user_id,
                business_type=AppraisalHistory.BusinessType.VIP,
                report_date=report_date,
                result_status=result_status,
                result=json.dumps(
                    dict(
                        user_id=user_id,
                        new_level=new_level,
                        old_level=old_level,
                        lock_level=lock_level
                    )
                )
            )
            db.session_add_and_commit(r)
        else:
            r.result_status = result_status
            r.result = json.dumps(
                dict(
                    user_id=user_id,
                    new_level=new_level,
                    old_level=old_level,
                    lock_level=lock_level
                )
            )
            db.session.commit()
        return r.id

    @classmethod
    def add(cls,
            user_id: int,
            level: int,
            real_level: int = 0) -> None:
        vip = VipUser.query.filter(
                VipUser.user_id == user_id
                ).first()
        if vip:
            vip.status = VipUser.StatusType.PASS
            vip.level = level
            vip.real_level = real_level
        else:
            db.session.add(
                    VipUser(
                            user_id=user_id,
                            level=level,
                            real_level=real_level,
                            status=VipUser.StatusType.PASS,
                            is_lock=False
                            )
                    )
        db.session.commit()

    @classmethod
    def add_lock_level(cls,
                       user_id: int,
                       lock_level: int,
                       expired_time: Optional[datetime]
                       ) -> None:
        vip: VipUser = VipUser.query.filter(
                VipUser.user_id == user_id
                ).first()
        if expired_time is None:
            lock_level = 0
        old_level = old_lock_level = 0
        if vip:
            old_level = vip.level
            old_lock_level = vip.lock_level
            vip.status = VipUser.StatusType.PASS
            if vip.lock_level < lock_level:
                vip.level = max(lock_level, vip.real_level)
                vip.lock_level = lock_level
                vip.expired_time = expired_time
                vip.is_lock = bool(lock_level)
        else:
            vip = VipUser(
                user_id=user_id,
                level=lock_level,
                status=VipUser.StatusType.PASS,
                lock_level=lock_level,
                expired_time=expired_time,
                is_lock=bool(lock_level)
            )
            db.session.add(vip)
        db.session.commit()
        cls.add_change_history(
            user_id=user_id,
            new_level=vip.level,
            old_level=old_level,
            new_lock_level=vip.lock_level,
            old_lock_level=old_lock_level,
            expired_time=expired_time
        )

    @classmethod
    def delete(cls, user_id: int) -> None:
        vip = VipUser.query.filter(
                VipUser.user_id == user_id
                ).first()
        if vip:
            vip.status = VipUser.StatusType.DELETE
            vip.level = 0
            vip.lock_level = 0
            vip.real_level = 0
            vip.expired_time = None
            db.session.commit()

    @classmethod
    def add_change_history(
            cls,
            user_id,
            new_level: int,
            old_level: int,
            new_lock_level: int,
            old_lock_level: int,
            expired_time=None,
            admin_user_id=None,
            buff_level=None,
            real_level=None
    ) -> None:
        action_list = []
        if new_level and not old_level:
            action_list.append(VipUserChangeType.ADD.name)
        if old_level and not new_level:
            action_list.append(VipUserChangeType.REMOVE.name)
        if new_lock_level != old_lock_level:
            action_list.append(VipUserChangeType.ADJUST_LOCK_LEVEL.name)
        if new_level and old_level and new_level != old_level and not buff_level:
            action_list.append(VipUserChangeType.LEVEL_CHANGE.name)
        if buff_level and new_level != old_level:
            action_list.append(VipUserChangeType.BUFF.name)
        history_list = []
        for action in action_list:
            history = UserStatusChangeHistory(
                user_id=user_id,
                type="VIP",
                action=action,
                detail=json.dumps(dict(
                    new_level=new_level if action != VipUserChangeType.ADJUST_LOCK_LEVEL.name else new_lock_level,
                    old_level=old_level if action != VipUserChangeType.ADJUST_LOCK_LEVEL.name else old_lock_level,
                    expired_time=datetime_to_time(expired_time) if expired_time else None,
                    buff_level=buff_level,
                    real_level=real_level,
                    detail=cls.get_appraisal_detail_desc(user_id),
                ))
            )
            if admin_user_id:
                history.admin_user_id = admin_user_id
            history_list.append(history)
        db.session.add_all(history_list)
        db.session.commit()

    @classmethod
    def _get_upgrade_level(cls, user_id):
        return int(VipUserBuff.query.filter(
            VipUserBuff.user_id == user_id,
            VipUserBuff.buff_type == VipUserBuff.BuffType.UPGRADE,
            VipUserBuff.expire_at > now()
        ).with_entities(
            func.sum(VipUserBuff.value)
        ).scalar() or 0)

    @classmethod
    def buff_level(cls, user_id: int, vip_level: int):
        upgrade_level = cls._get_upgrade_level(user_id)
        if not upgrade_level:
            return vip_level, 0
        return min(int(vip_level + upgrade_level), cls.MAX_LEVEL), upgrade_level


class Decoder(object):
    """
    only support eth chain.
    """

    def __init__(self, address: str, message: str, sign: str):
        self.address = address
        self.message = message
        self.sign = sign

    def verify(self):
        message = encode_defunct(text=self.message)
        try:
            address_from_sign = w3.eth.account.recover_message(message, signature=self.sign)
            checksum_address = Web3.to_checksum_address(self.address)
            return checksum_address == address_from_sign
        except Exception:
            raise BindingAddressSignatureError


class VipLevelCalculator:
    RULE = {k: v['rule'] for k, v in VIP_LEVEL_DICT.items()}

    def __init__(self, vip_user_ids=None):
        self.vip_user_ids = vip_user_ids or VipHelper.list_vip_user_ids()
        self.sub_main_mapping = {
            row.user_id: row.main_user_id for row in SubAccount.query.filter(
                SubAccount.status == SubAccount.Status.VALID
            ).with_entities(
                SubAccount.user_id,
                SubAccount.main_user_id
            ).all()
        }
        self.current_ts = current_timestamp(to_int=True)
        self.now_date = now().date()

        self.snapshot_mapping = defaultdict(lambda: {
            'spot_trade_usd': Decimal(),
            'perpetual_trade_usd': Decimal(),
            'position_usd': Decimal(),
            'cet_amount': Decimal(),
        })

    def calculate(self):
        init_levels = self._init_vip_user_levels()
        cet_levels = self._calculate_cet_levels()
        position_levels = self._calculate_position_levels()
        spot_levels, perp_levels = self._calculate_trade_levels()
        self._create_or_update_snapshot()
        merged_ret = defaultdict(list)
        self._merge_levels(merged_ret, levels=init_levels)
        self._merge_levels(merged_ret, levels=cet_levels)
        self._merge_levels(merged_ret, levels=position_levels)
        self._merge_levels(merged_ret, levels=spot_levels)
        self._merge_levels(merged_ret, levels=perp_levels)
        ret = {}
        for uid, levels in merged_ret.items():
            ret[uid] = max(levels)
        return ret

    def _init_vip_user_levels(self):
        ret = defaultdict(list)
        for _uid in self.vip_user_ids:
            _main_user_id = self.sub_main_mapping.get(_uid, _uid)
            # add user_id to balance dict
            ret[_uid] = [0]
        return ret

    def _calculate_cet_levels(self):
        out_site_ret = self.__calculate_out_site_cet()
        in_site_ret = self.__calculate_in_site_cet()
        ret = defaultdict(Decimal)
        self._merge_amount(ret, out_site_ret)
        self._merge_amount(ret, in_site_ret)
        self._update_snapshot_mapping(key='cet_amount', ret=ret)
        return self.calculate_levels(ret, 'cet_amount')

    def __calculate_out_site_cet(self):
        model = BindingAddress
        rows = model.query.filter(
            model.status == model.StatusType.PASS,
            model.binding_time < now(),
        ).with_entities(
            model.user_id,
            model.address
        ).all()
        user_address_dic = defaultdict(set)
        for row in rows:
            user_address_dic[row.user_id].add(row.address)
        cet_results = g_map(self.aggregate_out_site_cet, user_address_dic.items(), size=50)
        ret = defaultdict(Decimal)
        for user_id, amount in cet_results:
            _main_user_id = self.sub_main_mapping.get(user_id, user_id)
            ret[_main_user_id] += amount
        return ret

    @staticmethod
    def aggregate_out_site_cet(user_addresses_pair):
        # noinspection PyBroadException
        user_id, addresses = user_addresses_pair
        try:
            amount = VipHelper.get_cet_balance_from_addresses(list(addresses))
        except Exception:
            amount = Decimal()
        return user_id, amount

    def __calculate_in_site_cet(self):
        ret = defaultdict(Decimal)
        snapshot = VipHelper.get_user_cet_snap_shot_balance(self.current_ts)
        for _user_id, _cet_amount in snapshot.items():
            _main_user_id = self.sub_main_mapping.get(_user_id, _user_id)
            ret[_main_user_id] += _cet_amount
        return ret

    @staticmethod
    def _merge_amount(ret, amounts):
        for uid, amount in amounts.items():
            ret[uid] += amount

    def _calculate_position_levels(self):
        ret = defaultdict(Decimal)
        snapshot = VipHelper.get_user_snapshot_balance_usd(self.current_ts)
        for _user_id, amount_usd in snapshot.items():
            _main_user_id = self.sub_main_mapping.get(_user_id, _user_id)
            ret[_main_user_id] += amount_usd
        credit_mapping = VipHelper.get_credit_balance_usd()
        for _user_id, credit_usd in credit_mapping.items():
            ret[_user_id] -= credit_usd

        self._update_snapshot_mapping(key='position_usd', ret=ret)
        return self.calculate_levels(ret, 'position_usd')

    def _calculate_trade_levels(self):
        spots, perps = self.calculate_trade_amounts()
        self._update_snapshot_mapping(key='spot_trade_usd', ret=spots)
        self._update_snapshot_mapping(key='perpetual_trade_usd', ret=perps)
        return self.calculate_levels(spots, 'spot_usd'), self.calculate_levels(perps, 'perpetual_usd')

    def calculate_trade_amounts(self, user_id=None):
        yesterday_ = yesterday()
        begin_date = self.now_date - timedelta(days=30)
        model = UserTradeSummary
        query = model.query.with_entities(
            model.user_id,
            model.system,
            func.sum(model.trade_amount).label('trade_amount')
        )
        if user_id is not None:
            query = query.filter(model.user_id == user_id)
        rows = query.filter(
            model.report_date >= begin_date,
            model.report_date < yesterday_,
        ).group_by(
            model.user_id,
            model.system,
        ).all()
        spot, perp = defaultdict(Decimal), defaultdict(Decimal)
        all_maker_ids = MarketMakerHelper.list_all_maker_ids()
        for row in rows:
            if row.user_id in all_maker_ids:
                continue
            if row.system is model.System.SPOT:
                spot[row.user_id] = row.trade_amount
            else:
                perp[row.user_id] = row.trade_amount
        # 昨天的交易数据从UserVipSource表获取，防止UserTradeSummary表昨天的数据未生成导致偏差
        yesterday_trade_data = VipSourceBusiness.get_trade_data(yesterday_, user_id)
        for uid, value in yesterday_trade_data.items():
            if uid in all_maker_ids:
                continue
            spot_usd = value.get('spot_trade_usd', Decimal())
            perp_usd = value.get('perpetual_trade_usd', Decimal())
            spot[uid] += spot_usd
            perp[uid] += perp_usd
        return spot, perp

    def calculate_levels(self, user_amount_dic: dict, calculate_field: str):
        rules = [{'level': k, 'amount': v[calculate_field]} for k, v in self.RULE.items()]
        return self._calculate_levels(user_amount_dic, rules)

    @staticmethod
    def _calculate_levels(ret, rules):
        r = defaultdict(list)
        rules.sort(key=lambda x: x['level'], reverse=True)
        for uid, amount in ret.items():
            level = 0
            for item in rules:
                if amount >= item['amount']:
                    level = item['level']
                    break
            r[uid] = [level]
        return r

    @staticmethod
    def _merge_levels(merged_ret, levels):
        for uid, lv in levels.items():
            merged_ret[uid].extend(lv)

    def _update_snapshot_mapping(self, key, ret):
        for uid, amount in ret.items():
            self.snapshot_mapping[uid][key] = amount

    def _create_or_update_snapshot(self):
        model = UserVipSnapshot
        objs = []
        for user_id, mapping in self.snapshot_mapping.items():
            if user_id == 0:
                continue
            row = model.get_or_create(report_date=self.now_date, user_id=user_id)
            row.spot_trade_usd = mapping['spot_trade_usd']
            row.perpetual_trade_usd = mapping['perpetual_trade_usd']
            row.position_usd = mapping['position_usd']
            row.cet_amount = mapping['cet_amount']
            objs.append(row)
        for chunk_objs in batch_iter(objs, 10000):
            db.session.bulk_save_objects(chunk_objs)
            db.session.commit()


class VipSourceBusiness:
    model = UserVipSource

    def __init__(self, dt=None):
        dt = dt or now()
        ts = int(dt.timestamp())
        self.end_ts = ts - ts % (60 * 60)
        last_hour_ts = self.end_ts - 60 * 60
        report_dt = datetime.fromtimestamp(last_hour_ts)
        self.report_date = report_dt.date()
        self.begin_ts = int(report_dt.timestamp()) - int(report_dt.timestamp()) % (60 * 60 * 24)
        self.sub_main_mapping = {
            row.user_id: row.main_user_id for row in SubAccount.query.filter(
                SubAccount.status == SubAccount.Status.VALID
            ).with_entities(
                SubAccount.user_id,
                SubAccount.main_user_id
            ).all()
        }
        self.prices = AssetPrice.get_close_price_map(self.report_date)

    def create(self):
        spots = self._calculate_spot_trade_usd()
        perps = self._calculate_perp_trade_usd()
        user_ids = set(set(spots.keys()) | set(perps.keys()))
        objs = []
        for user_id in user_ids:
            row = self.model.get_or_create(report_date=self.report_date, user_id=user_id)
            row.spot_trade_usd = spots.get(user_id, Decimal())
            row.perpetual_trade_usd = perps.get(user_id, Decimal())
            objs.append(row)
        for chunk_objs in batch_iter(objs, 10000):
            db.session.bulk_save_objects(chunk_objs)
            db.session.commit()

    def _calculate_spot_trade_usd(self):
        records = TradeSummaryDB.group_by_user_asset_by_hours(self.report_date, self.end_ts)
        ret = defaultdict(Decimal)
        for record in records:
            _main_user_id = self.sub_main_mapping.get(record['user_id'], record['user_id'])
            if _main_user_id in self._marker_user_ids:
                continue
            _rate = self.prices.get(record['money_asset'], Decimal())
            ret[_main_user_id] += record['deal_volume'] * _rate
        return ret

    def _calculate_perp_trade_usd(self):
        records = PerpetualSummaryDB.group_by_user_asset_by_hours(self.report_date, self.end_ts)
        ret = defaultdict(Decimal)
        for record in records:
            _main_user_id = self.sub_main_mapping.get(record['user_id'], record['user_id'])
            if _main_user_id in self._marker_user_ids:
                continue
            ret[_main_user_id] += record['deal_amount']
        return ret

    @cached_property
    def _marker_user_ids(self):
        return MarketMakerHelper.list_all_maker_ids()

    @classmethod
    def calculate_vip_source(cls, user_id=None):
        """根据 UserVipSource 计算出当前一些统计值"""
        ret = defaultdict(lambda: dict(
            spot_trade_usd=Decimal(),
            perpetual_trade_usd=Decimal(),
        ))
        model = cls.model
        last_row = model.query.with_entities(
            model.report_date
        ).order_by(
            model.id.desc()
        ).first()
        if not last_row:
            return now().date(), ret
        last_date = last_row.report_date
        query = model.query.filter(model.report_date == last_date)
        if user_id is not None:
            query = query.filter(model.user_id == user_id)
        rows = query.with_entities(
            model.user_id,
            model.spot_trade_usd,
            model.perpetual_trade_usd,
        ).all()
        for row in rows:
            ret[row.user_id]['spot_trade_usd'] = row.spot_trade_usd
            ret[row.user_id]['perpetual_trade_usd'] = row.perpetual_trade_usd
        return last_date, ret

    @classmethod
    def get_trade_data(cls, query_date: date, user_id: int = None):
        """根据 UserVipSource 查询某天成交量的统计值"""
        ret = defaultdict(lambda: dict(
            spot_trade_usd=Decimal(),
            perpetual_trade_usd=Decimal(),
        ))
        model = cls.model
        query = model.query.filter(model.report_date == query_date)
        if user_id is not None:
            query = query.filter(model.user_id == user_id)
        rows = query.with_entities(
            model.user_id,
            model.spot_trade_usd,
            model.perpetual_trade_usd,
        ).all()
        for row in rows:
            ret[row.user_id]['spot_trade_usd'] = row.spot_trade_usd
            ret[row.user_id]['perpetual_trade_usd'] = row.perpetual_trade_usd
        return ret

    @classmethod
    def delete(cls, report_date):
        cls.model.query.filter(
            cls.model.report_date <= report_date
        ).delete(synchronize_session=False)
        db.session.commit()


