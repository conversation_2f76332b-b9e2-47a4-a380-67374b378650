# -*- coding: utf-8 -*-
import re
import time
from decimal import Decimal
from enum import Enum, IntEnum
from math import floor
from logging import getLogger
from typing import Callable

from flask import current_app
from sqlalchemy import func

from .. import config
from ..caches.pre_trading import PreTradingMarketMaxPriceCache
from ..caches.spot import OfflineMarketExecutorCache
from ..exceptions import InvalidMarket, InvalidArgument
from ..models import Market, db
from ..common import FIXED_ASSET_PRICES, OrderSideType, MARKET_PRICE_DIGITS, CeleryQueues
from ..caches import MarketCache, AmmMarketCache, MarketViewCache, InternalMarketViewCache, \
    PreTradingMarketCache
from .prices import PriceManager
from .clients import ServerClient, AmmClient
from .lock import lock_call, CacheLock, LockKeys
from ..utils import now, celery_task

_logger = getLogger(__name__)
_RE_MARKET_CODE = re.compile(r'[A-Za-z0-9_]{1,32}')


_BASE_ASSET_PRECISION = 8
_MAX_QUOTE_ASSET_PRECISION = 10  # 10 + 8 + 6 == 24


def market_info_auto(quote_asset: str, opening_price: Decimal,
                     opening_price_asset: str):
    trading_area = Market.TradingArea(quote_asset)

    base_asset_to_quote = PriceManager.convert_price(
        opening_price_asset, opening_price, quote_asset)
    default_depth = min(
        -floor(base_asset_to_quote.log10()) + MARKET_PRICE_DIGITS - 1,
        _MAX_QUOTE_ASSET_PRECISION)

    min_precision = (2
                     if (quote_asset == 'USDT'
                         or FIXED_ASSET_PRICES.get(quote_asset) == Decimal(1))
                     else 8)
    quote_asset_precision = min(
        max(((-floor((base_asset_to_quote / 3).log10()) + MARKET_PRICE_DIGITS)
             // 2 * 2),
            min_precision),
        _MAX_QUOTE_ASSET_PRECISION)
    min_depth = -floor(base_asset_to_quote.log10()) + 1
    depths = list(range(min_depth, quote_asset_precision + 1))

    base_asset_to_usd = PriceManager.convert_price(
        opening_price_asset, opening_price, 'USD')

    def precision_to_decimal(_p):
        return f'{Decimal(10) ** -_p:f}'

    return dict(
        trading_area=trading_area,
        base_asset_precision=_BASE_ASSET_PRECISION,
        quote_asset=quote_asset,
        quote_asset_precision=quote_asset_precision,
        default_depth=precision_to_decimal(default_depth),
        depths=','.join(map(precision_to_decimal, depths)),
        opening_price_asset=opening_price_asset,
        opening_price=opening_price,
        min_order_amount=PriceManager.price_to_min_order_amount(
            base_asset_to_usd)
    )


def cancel_market_orders(name: str):
    server_cli = ServerClient(_logger)

    for side in OrderSideType:
        while True:
            orders = server_cli.market_book_orders(name, side, 1, 1000)
            for order in orders:
                server_cli.cancel_user_order(order['user'], name, order['id'])
            if not orders.has_next:
                break

    for side in OrderSideType:
        while True:
            orders = server_cli.market_stop_book_orders(name, side, 1, 1000)
            for order in orders:
                server_cli.cancel_user_stop_order(order['user'], name, order['id'])
            if not orders.has_next:
                break


@celery_task(queue=CeleryQueues.MARKET_MAKER)
@lock_call(with_args=True)
def new_cancel_market_orders_task(market: str):
    row = Market.query.filter(Market.name == market).first()
    if row.status != Market.Status.OFFLINE:
        return
    new_cancel_market_orders(market)


def new_cancel_market_orders(market: str):
    limit = 500
    page = 1
    limit_user_ids = set()
    client = ServerClient(_logger)
    while True:
        ret_ = client.market_book_orders_lite(market, page, limit, 0)
        asks = ret_["ask_orders"]
        bids = ret_["bid_orders"]
        if not asks and not bids:
            break
        for order in asks:
            limit_user_ids.add(order['user_id'])
        for order in bids:
            limit_user_ids.add(order['user_id'])
        page += 1

    for _uid in limit_user_ids:
        client.cancel_user_all_order(_uid, -1, market)

    for side in OrderSideType:
        while True:
            orders = client.market_stop_book_orders(market, side, 1, 1000)
            for order in orders:
                client.cancel_user_stop_order(order['user'], market, order['id'])
            if not orders.has_next:
                break

    return

@lock_call(wait=True, ttl=600)
def update_markets():
    _logger.info('update_markets: updating caches...')
    MarketCache.refresh_all()
    AmmMarketCache.refresh()
    PreTradingMarketCache.refresh()
    PreTradingMarketMaxPriceCache.refresh()

    _logger.info('update_markets: refreshing API caches...')
    MarketViewCache.reload()
    InternalMarketViewCache.reload()

    _logger.info('update_markets: informing server...')
    ServerClient().update_market()

    _logger.info('update_markets: informing amm...')
    AmmClient().update_market()

    _logger.info('update_markets: done')


def update_amm_markets():
    AmmMarketCache.refresh()
    _logger.info('update_markets: informing amm...')
    AmmClient().update_market()


def normalise_market(market: str) -> str:
    if not market or not _RE_MARKET_CODE.fullmatch(market):
        raise InvalidMarket
    return market.upper()


def has_market(market: str, allow_offline: bool = False) -> bool:
    market = check_special_market(market)
    if allow_offline:
        all_markets = MarketCache.list_online_markets() + MarketCache.list_offline_markets()
    else:
        all_markets = MarketCache.list_online_markets()
    return market in all_markets


def check_special_market(market):
    special_mark = ('INDEX',)
    market_split = market.split('_')
    if len(market_split) > 1:
        if market_split[1] in special_mark:
            market = market_split[0]
    return market


class MarketRepository:

    @classmethod
    def get_online_market_count(cls):
        ret = Market.query.filter(
            Market.status == Market.Status.ONLINE
        ).with_entities(
            func.count('*')
        ).scalar() or 0
        return ret

    @classmethod
    def get_trading_area_online_market_count(cls):
        ret = Market.query.filter(
            Market.status == Market.Status.ONLINE
        ).with_entities(
            Market.trading_area,
            func.count('*').label('market_count')
        ).group_by(Market.trading_area).all()
        return {i.trading_area.name: i.market_count for i in ret}


class MarketOfflineHelper:
    # 提前三十分钟，开始执行
    AUTO_PROCESS_SECS = 1800
    # 自动处理时间限制
    LIMIT_PROCESS_SECS = 7200
    # 提前2分钟开始执行撤单
    AUTO_CANCEL_SECS = 120
    # 提前五分钟处理兑换
    AUTO_CANCEL_EXCHANGE_SECS = 300
    # process alert end ts
    ALERT_END_SECS = 180
    # alert exception ts
    ALERT_EXCEPTION_SECS = 300

    class Step(IntEnum):
        AMM = 1
        AUTO_INVEST = 2
        SPOT_STRATEGY = 3
        EXCHANGE = 4
        CANCEL_ORDER = 5

        def get_success_msg(self):
            match self:
                case self.AMM:
                    return 'AMM已撤销流动性，业务已关闭'
                case self.AUTO_INVEST:
                    return '定投已撤销策略，业务已关闭'
                case self.SPOT_STRATEGY:
                    return '现货网格已撤销策略，业务已关闭'
                case self.EXCHANGE:
                    return '兑换配置已处理'
                case self.CANCEL_ORDER:
                    return '已撤销市场订单'
                case _:
                    return ''

        def get_fail_msg(self):
            match self:
                case self.AMM:
                    return 'AMM业务关闭失败'
                case self.AUTO_INVEST:
                    return '定投业务关闭失败'
                case self.SPOT_STRATEGY:
                    return '现货网格关闭失败'
                case self.EXCHANGE:
                    return '兑换配置处理失败'
                case self.CANCEL_ORDER:
                    return '市场订单撤销失败'
                case _:
                    return ''

    class BusinessType(Enum):
        AMM = 'amm'
        MARGIN = 'margin'
        PLEDGE = 'pledge'
        AUTO_INVEST = 'auto_invest'
        SPOT_STRATEGY = 'spot_strategy'

    def __init__(self, market: str):
        self.market = market
        row_ = Market.query.filter(Market.name == market).first()
        if not row_:
            raise InvalidMarket(message=f"{market} config not found")
        self.market_row = row_

    @classmethod
    def check_permission(cls, market: str, business: BusinessType):
        market_row = Market.query.filter(Market.name == market).first()

        def get_desc(_business):
            desc_map = {
                cls.BusinessType.AMM: 'amm',
                cls.BusinessType.MARGIN: '杠杆',
                cls.BusinessType.PLEDGE: '借贷',
                cls.BusinessType.AUTO_INVEST: '定投',
                cls.BusinessType.SPOT_STRATEGY: '网格',
            }
            return desc_map.get(_business, '')
        msg = f'相关市场配置了下架时间，不可添加 {get_desc(business)}'
        if market_row.ended_at:
            raise InvalidArgument(message=msg)

    def execute_require_check(self, step: Step, finished_steps: set[Step]):
        requires = {
            self.Step.CANCEL_ORDER: [
                self.Step.AMM,
                self.Step.AUTO_INVEST,
                self.Step.SPOT_STRATEGY,
                self.Step.EXCHANGE,
            ]
        }
        if step not in requires:
            return True
        reqs = set(requires.get(step, set()))
        if reqs & finished_steps == reqs:
            return True
        return False

    def execute_func(self, step: Step) -> Callable:
        step_map = {
            self.Step.AMM: self.execute_amm,
            self.Step.AUTO_INVEST: self.execute_auto_invest,
            self.Step.SPOT_STRATEGY: self.execute_spot_strategy,
            self.Step.EXCHANGE: self.execute_exchange,
            self.Step.CANCEL_ORDER: self.execute_cancel_order
        }
        func_ = step_map.get(step)
        return func_

    def execute(self):
        from app.business.alert import send_alert_notice

        if not self.market_row.status == Market.Status.ONLINE:
            return
        if not self.market_row.ended_at:
            return

        secs = (self.market_row.ended_at - now()).total_seconds()
        if secs > self.AUTO_PROCESS_SECS or secs < - self.LIMIT_PROCESS_SECS:
            return

        cache = OfflineMarketExecutorCache(self.market, self.Step)

        if cache.is_finish():
            return

        cache.expire(cache.ttl_)
        results = []
        undone_results = []
        finish_steps = set()
        for step in self.Step:
            if cache.get_bit(step.value):
                finish_steps.add(step)

        for step in self.Step:
            if step in finish_steps:
                continue
            current_app.logger.warning(f"auto run offline market {self.market} step {step.name}")
            # 撤单需要前置的业务流程走完才能进行
            if not self.execute_require_check(step, finish_steps):
                undone_results.append(step)
                continue
            new_ = self.execute_func(step)()
            if new_:
                cache.set_bit(step.value, True)
                results.append(step)
                finish_steps.add(step)
                current_app.logger.warning(f"auto run offline market {self.market} step {step.name} successful")
            else:
                undone_results.append(step)
        channel_id = config["ADMIN_CONTACTS"].get("auto_offline_market_notice", "")
        if results:
            if cache.is_finish():
                build_msg = f'* {self.market} 市场下架 - 成功 *\n'
                for step in self.Step:
                    build_msg += step.get_success_msg()
                    build_msg += '\n'
                send_alert_notice(build_msg, channel_id)
                return True
            else:
                build_msg = f'* {self.market} 市场下架处理中 *\n'
                for step in results:
                    build_msg += step.get_success_msg()
                    build_msg += '\n'
                send_alert_notice(build_msg, channel_id)
        if secs <= - self.ALERT_EXCEPTION_SECS and undone_results:
            data = cache.multiple_get_bit(list(range(cache.ALERT_BIT_STARTS, cache.ALERT_BIT_STARTS + cache.ALERT_TIMES)))
            if all(data.values()):
                return

            for (offset, _value) in sorted(data.items(), key=lambda x: x[0]):
                if not _value:
                    set_alert_offset = offset
                    # 设置
                    cache.set_bit(set_alert_offset, True)
                    break

            build_msg = f'* {self.market} 市场下架异常，请检查 *\n'
            for step in undone_results:
                build_msg += step.get_fail_msg()
                build_msg += '\n'
            at = config["ADMIN_CONTACTS"]['slack_at'].get("auto_offline_market_notice")
            send_alert_notice(build_msg, channel_id, at=at)

    def execute_amm(self) -> bool:
        from app.business.amm import close_liqudity_pool, UserLiquidity, AmmMarket
        amm_row = AmmMarket.query.filter(AmmMarket.name == self.market).first()
        if not amm_row:
            return True
        if amm_row.status == AmmMarket.Status.OFFLINE:
            return True

        close_liqudity_pool(self.market)
        with CacheLock(LockKeys.amm_liquidity(self.market), wait=True):
            db.session.rollback()
            if UserLiquidity.query.filter(
                    UserLiquidity.market == self.market,
                    UserLiquidity.liquidity > 0
            ).first():
                return False
            amm_row = AmmMarket.query.filter(AmmMarket.name == self.market).first()
            amm_row.status = AmmMarket.Status.OFFLINE
            db.session.commit()
            update_amm_markets()
            return True

    def execute_auto_invest(self) -> bool:
        from app.models.auto_invest import AutoInvestMarket
        from app.business.auto_invest import (AutoInvestPlan, AutoInvestMarketCache,
                                              auto_invest_plan_terminated_task)
        auto_market_row: AutoInvestMarket = AutoInvestMarket.query.filter(
            AutoInvestMarket.market == self.market
        ).first()
        if not auto_market_row:
            return True
        if auto_market_row.status == AutoInvestMarket.Status.CLOSE:
            return True

        if auto_market_row.status == AutoInvestMarket.Status.OPEN:
            auto_market_row.status = AutoInvestMarket.Status.CLOSING
            db.session.commit()

        AutoInvestMarketCache().hdel(self.market)

        pending_plans = AutoInvestPlan.query.filter(
            AutoInvestPlan.market == self.market,
            AutoInvestPlan.status != AutoInvestPlan.Status.TERMINATED,
        ).all()
        for plan in pending_plans:
            auto_invest_plan_terminated_task(plan.id)
        # 等待5s
        time.sleep(5)

        plan_list = AutoInvestPlan.query.filter(
            AutoInvestPlan.market == self.market,
            AutoInvestPlan.status != AutoInvestPlan.Status.TERMINATED,
        ).all()
        if len(plan_list) > 0:
            return False

        auto_market_row.status = AutoInvestMarket.Status.CLOSE
        db.session.commit()
        return True

    def execute_spot_strategy(self) -> bool:
        from app.models.strategy import SpotGridMarket, SpotGridStrategy
        from app.caches.strategy import SpotGridMarketCache
        from app.business.strategy.grid import batch_terminate_grid_strategy_by_market
        spot_grid_row: SpotGridMarket = SpotGridMarket.query.filter(
            SpotGridMarket.name == self.market,
        ).first()
        if not spot_grid_row:
            return True
        if spot_grid_row.status == SpotGridMarket.Status.OFFLINE:
            return True

        SpotGridMarketCache().hdel(self.market)

        if spot_grid_row.status == SpotGridMarket.Status.ONLINE:
            spot_grid_row.status = SpotGridMarket.Status.CLOSING
            db.session.commit()

        sty_list = SpotGridStrategy.query.filter(
            SpotGridStrategy.market == self.market,
            SpotGridStrategy.status.in_(
                [
                    SpotGridStrategy.Status.CREATED,
                    SpotGridStrategy.Status.TRANSFERRED_IN,
                    SpotGridStrategy.Status.RUNNING,
                    SpotGridStrategy.Status.PAUSED,
                ]
            )
        ).with_entities(SpotGridStrategy.id).all()
        if sty_list:
            batch_terminate_grid_strategy_by_market(self.market)
        time.sleep(10)
        db.session.rollback()
        sty_list = SpotGridStrategy.query.filter(
            SpotGridStrategy.market == self.market,
            SpotGridStrategy.status.in_(
                [
                    SpotGridStrategy.Status.CREATED,
                    SpotGridStrategy.Status.TRANSFERRED_IN,
                    SpotGridStrategy.Status.RUNNING,
                    SpotGridStrategy.Status.PAUSED,
                ]
            )
        ).with_entities(SpotGridStrategy.id).all()
        if sty_list:
            return False

        spot_grid_row.status = spot_grid_row.Status.OFFLINE
        db.session.commit()
        return True

    def execute_exchange(self) -> bool:
        from app.business.site import BusinessSettings
        if self.market in BusinessSettings.disabled_exchange_markets:
            return True
        secs = (self.market_row.ended_at - now()).total_seconds()
        if secs < self.AUTO_CANCEL_EXCHANGE_SECS:
            # 简化处理
            BusinessSettings.disabled_exchange_markets.add(self.market)
            return True
        return False

    def execute_cancel_order(self) -> bool:
        # 执行操作并修改状态
        secs = (self.market_row.ended_at - now()).total_seconds()
        if secs < self.AUTO_CANCEL_SECS:
            new_cancel_market_orders(self.market)
            row = Market.query.filter(Market.name == self.market).first()
            row.status = Market.Status.OFFLINE
            db.session.commit()
            MarketCache(row.name).refresh()
            try:
                new_cancel_market_orders(self.market)
            except Exception as e:
                current_app.logger.error(f"cancel_market_orders {row.name} error, {e!r}")
            # 之后再撤一次单,防止api未同步仍有部分订单未撤
            new_cancel_market_orders_task.apply_async((row.name,), countdown=300, expires=600)
            return True
        return False

