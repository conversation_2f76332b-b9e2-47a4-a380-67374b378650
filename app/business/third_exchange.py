from sqlalchemy import func
import hashlib
import hmac
import json
import time
from collections import defaultdict
from decimal import Decimal
from functools import cached_property
from itertools import chain
from typing import Type, Tuple, Dict, List
from websockets.sync.client import connect
from flask import current_app

from app.business import PriceManager
from app.caches import PerpetualMarketCache
from app.caches.third_exchange import BinanceCache, BinanceBlockCache
from app.models import Market
from app.models.market_liquidity import Exchange
from app.models.summary import UserTradeFeeSummary
from app.models.user import SubAccount, User
from app.utils import APIClient, g_map, current_timestamp, amount_to_str, batch_iter, \
    quantize_amount, BaseHTTPClient, safe_div
from app.utils.date_ import today_timestamp_utc, yesterday
from app.utils.parser import JsonEncoder, safe_json_loads


PRIORITY_MARKETS = ['BTCUSDT', 'ETHUSDT', 'CETUSDT', 'DOGEUSDT', 
                    'XRPUSDT', 'SOLUSDT', 'SUIUSDT', 'PEPEUSDT']

class BinanceAPIClient:
    """请求频率限制：500/5min/IP
    现货权重 6000/min；
    合约权重 2400/min
    具体接口权重见币安接口文档
    eg：
    现货深度权重（根据不同的limit）：
    1-100	5
    101-500	25
    501-1000	50
    1001-5000	250
    """

    C_URL = 'https://dapi.binance.com'  # 币本位
    U_URL = 'https://fapi.binance.com'  # U 本位
    CONTRACT_TYPE = 'PERPETUAL'
    WS_U_URL = 'wss://ws-fapi.binance.com/ws-fapi/v1'  # U 本位 WebSocket
    WS_SPOT = 'wss://ws-api.binance.com:443/ws-api/v3'
    SPOT_URL = 'https://api.binance.com'  # 现货/杠杆等
    
    def __init__(self, api_key: str = None, secret: str = None):
        self.api_key = api_key
        self.secret = secret
        self.headers = {
            'X-MBX-APIKEY': self.api_key,
        }
        self.c = APIClient(self.C_URL, headers=self.headers)
        self.u = APIClient(self.U_URL, headers=self.headers)
        self.s = APIClient(self.SPOT_URL, headers=self.headers)
        self.block_cache = BinanceBlockCache()
        
    def get_spot_fee(self, symbol: str, start_time: int, end_time: int):
        api = '/api/v3/myTrades'
        return self.get_fee(self.s, api, symbol, start_time, end_time)
    
    def get_inverse_perpetual_fee(self, symbol: str, start_time: int, end_time: int):
        api = '/dapi/v1/userTrades'
        return self.get_fee(self.c, api, symbol, start_time, end_time)
    
    def get_direct_perpetual_fee(self, symbol: str, start_time: int, end_time: int):
        api = '/fapi/v1/userTrades'
        return self.get_fee(self.u, api, symbol, start_time, end_time)
    
    def get_full_margin_fee(self, symbol: str, start_time: int, end_time: int):
        api = '/sapi/v1/margin/myTrades'
        extras = {
            'isIsolated': 'FALSE',
        }
        return self.get_fee(self.s, api, symbol, start_time, end_time, extras)
    
    def get_isolate_margin_fee(self, symbol: str, start_time: int, end_time: int):
        api = '/sapi/v1/margin/myTrades'
        extras = {
            'isIsolated': 'TRUE',
        }
        return self.get_fee(self.s, api, symbol, start_time, end_time, extras)
    
    
    def get_fee(self, client, api, symbol: str, start_time: int, end_time: int, extras: dict = {}):
        max_limit = 1000
        res = defaultdict(Decimal)
        p = {
            'symbol': symbol,
            'startTime': start_time,
            'endTime': end_time,
            'limit': max_limit,
        }    # 币安不支持startTime+endTime+fromId联合查询
        p.update(extras)
        params = self.sign_params(p)
        resp = client.get(api, **params)
        resp.sort(key=lambda x: x['id'])
        for deal in resp:
            res[deal['commissionAsset']] += Decimal(deal['commission'])
        if len(resp) < max_limit:
            return res
        
        start_id = resp[-1]['id'] + 1

        while True:
            p_ = {
                'symbol': symbol,
                'limit': max_limit,
                'fromId': start_id,
            }
            p_.update(extras)
            params = self.sign_params(p_)
            resp = client.get(api, **params)
            ret = sorted(filter(lambda x: start_time <= x['time'] <= end_time, resp), key=lambda x: x['id'])
            for deal in ret:
                res[deal['commissionAsset']] += Decimal(deal['commission'])
            if len(ret) < max_limit:
                break
            start_id = ret[-1]['id'] + 1
        return res
        
    def fetch_spot_balance(self):
        """
        {
            "balances": [
                {
                    "asset": "BTC",
                    "free": "0.********",
                    "locked": "0.********"
                },
                {
                    "asset": "LDUSDT",   # 理财账户里的币种
                    "free": "0.********",
                    "locked": "0.********"
                }
            ]
        }
        """
        api = '/api/v3/account'
        params = self.sign_params({'omitZeroBalances': 'true'})
        
        resp = self.s.get(api, **params)
        return resp['balances']
    
    def fetch_inverse_balance(self):
        api = '/dapi/v1/balance'
        params = self.sign_params()
        
        resp = self.c.get(api, **params)
        return resp
    
    def fetch_direct_balance(self):
        api = '/fapi/v3/balance'
        params = self.sign_params()
        
        resp = self.u.get(api, **params)
        return resp
    
    def get_full_margin_balance(self):
        api = '/sapi/v1/margin/account'
        params = self.sign_params()
        
        resp = self.s.get(api, **params)
        return resp
    
    def get_isolate_margin_balance(self):
        api = '/sapi/v1/margin/isolated/account'
        params = self.sign_params()
        
        resp = self.s.get(api, **params)
        return resp
    
    def sign_params(self, params: dict = None) -> dict:
        params = params or {}
        params['timestamp'] = int(current_timestamp() * 1000)
        query_string = '&'.join([f'{k}={v}' for k, v in params.items()])
        signature = self.gen_signature(query_string)
        params['signature'] = signature   
        return params
    
    def gen_signature(self, query_string: str) -> str:
        return hmac.new(self.secret.encode(), query_string.encode(), 
                             hashlib.sha256).hexdigest()

    def fetch_u_depths(self, symbols):
        return self._fetch_symbols_depth(symbols, self.WS_U_URL)

    def fetch_c_depths(self, symbols):
        res = dict()
        for symbol in symbols:
            ret = self.fetch_c_depth(symbol)
            res.update(ret)
        return res

    def _fetch_symbols_depth(self, symbols, api):
        max_limit = 100
        with connect(api) as websocket:
            res = dict()
            for symbol in symbols:
                params = dict(
                    symbol=symbol,
                    limit=max_limit,
                )

                data = {'method': 'depth', 'params': params, 'id': symbol}
                payload = json.dumps(data)
                websocket.send(payload)
                message = websocket.recv(timeout=10)
                recv_data = json.loads(message)
                res[symbol] = {
                    'bids': recv_data['result']['bids'],
                    'asks': recv_data['result']['asks']
                }
        return res

    def fetch_s_depths(self, symbols):
        return self._fetch_symbols_depth(symbols, self.WS_SPOT)

    def fetch_u_depth(self, symbol):
        api = '/fapi/v1/depth'
        return self.fetch_depth(symbol, api, self.u)

    def fetch_c_depth(self, symbol):
        api = '/dapi/v1/depth'
        return self.fetch_depth(symbol, api, self.c)

    def fetch_depth(self, symbol, api, client) -> dict:
        max_limit = 1000
        params = {
            'symbol': symbol,
            'limit': max_limit,
        }
        if not self.can_send_req():
            return dict()
        resp = self._get_resp(client, api, params)
        ret = {
            symbol: {
                'bids': resp.get('bids', []),
                'asks': resp.get('asks', []),
            }
        }
        return ret

    def fetch_u_markets(self):
        api = '/fapi/v1/exchangeInfo'
        return self.fetch_markets(api, self.u)

    def fetch_c_markets(self):
        api = '/dapi/v1/exchangeInfo'
        return self.fetch_markets(api, self.c)

    def fetch_markets(self, api, client) -> dict:
        if not self.can_send_req():
            return dict()
        resp = self._get_resp(client, api)
        ret = {}
        for r in resp['symbols']:
            if r['contractType'] != self.CONTRACT_TYPE:
                continue
            ret.update({
                r['symbol']: {
                    'symbol': r['symbol'],
                    'baseAsset': r['baseAsset'],
                    'quoteAsset': r['quoteAsset'],
                    'contractSize': r.get('contractSize', 1),  # 币本位 1张 = 100U
                }
            })
        return ret

    def fetch_u_last_prices(self):
        api = '/fapi/v1/ticker/price'
        return self.fetch_last_prices(api, self.u)

    def fetch_c_last_prices(self):
        api = '/dapi/v1/ticker/price'
        return self.fetch_last_prices(api, self.c)

    def fetch_last_prices(self, api, client):
        if not self.can_send_req():
            return dict()
        resp = self._get_resp(client, api)
        ret = {}
        for r in resp:
            ret.update({r['symbol']: Decimal(r['price'])})
        return ret

    def fetch_spot_markets(self):
        api = '/api/v3/exchangeInfo'
        params = {'symbolStatus': 'TRADING'}
        resp = self.s.get(api, **params)
        symbol_lis = resp['symbols']
        res = dict()
        for r in symbol_lis:
            symbol = r['symbol']
            res[symbol] = {
                'symbol': symbol,
                'baseAsset': r['baseAsset'],
                'quoteAsset': r['quoteAsset'],
            }
        return res
    
    def fetch_isolated_margin_spot_markets(self):
        api = '/sapi/v1/margin/isolated/allPairs'
        params = {
            'timestamp': int(current_timestamp() * 1000),
        }
        resp = self.s.get(api, **params)
        res = dict()
        for r in resp:
            symbol = r['symbol']
            res[symbol] = {
                'symbol': symbol,
                'baseAsset': r['base'],
                'quoteAsset': r['quote'],
            }
        return res

    def fetch_full_margin_spot_markets(self):
        api = '/sapi/v1/margin/allPairs'
        resp = self.s.get(api)
        res = dict()
        for r in resp:
            symbol = r['symbol']
            res[symbol] = {
                'symbol': symbol,
                'baseAsset': r['base'],
                'quoteAsset': r['quote'],
            }
        return res

    def fetch_s_last_prices(self):
        api = '/api/v3/ticker/price'
        resp = self.s.get(api)
        res = dict()
        for r in resp:
            res[r['symbol']] = Decimal(r['price'])
        return res

    def fetch_s_depth(self, symbol: str, max_limit: int = 100):
        api = '/api/v3/depth'
        params = {
            'symbol': symbol,
            'limit': max_limit
        }
        resp = self.s.get(api, **params)
        ret = {
            symbol: {
                    'bids': resp.get('bids', []),
                    'asks': resp.get('asks', []),
            }
        }
        return ret

    def fetch_u_funding_rate_index_sign_prices(self) -> dict:
        api = '/fapi/v1/premiumIndex'
        return self.fetch_funding_rate_index_sign_prices(api, self.u)

    def fetch_c_funding_rate_index_sign_prices(self) -> dict:
        api = '/dapi/v1/premiumIndex'
        return self.fetch_funding_rate_index_sign_prices(api, self.c)

    def fetch_funding_rate_index_sign_prices(self, api, client) -> dict:
        if not self.can_send_req():
            return dict()
        resp = self._get_resp(client, api)
        ret = {}
        for row in resp:
            index_price = Decimal(row['indexPrice'])
            sign_price = Decimal(row['markPrice'])
            funding_rate = row['lastFundingRate']
            ret[row['symbol']] = {
                'index_price': index_price,
                'sign_price': sign_price,
                'funding_rate': funding_rate,
            }
        return ret

    def fetch_u_funding_interval(self, symbol) -> int:
        api = '/fapi/v1/fundingRate'
        return self.fetch_funding_interval(api, self.u, symbol)

    def fetch_c_funding_interval(self, symbol) -> int:
        api = '/dapi/v1/fundingRate'
        return self.fetch_funding_interval(api, self.c, symbol)

    def fetch_funding_interval(self, api, client, symbol) -> int:
        if not self.can_send_req():
            return None
        params = {'symbol': symbol, 'limit': 2}
        resp = self._get_resp(client, api, params)
        if len(resp) != 2:
            interval = 8
        else:
            interval = int(abs(int(resp[1]['fundingTime'])-int(resp[0]['fundingTime']))/(1000*3600))
        return interval

    def get_u_funding_intervals(self) -> dict:
        api = '/fapi/v1/fundingInfo'
        ret = self.get_funding_info(api)
        res = {i['symbol']: i['fundingIntervalHours'] for i in ret}
        return res

    def get_funding_info(self, api) -> list:
        """
        [{
        "symbol": "UNFIUSDT",
        "adjustedFundingRateCap": "0.03000000",
        "adjustedFundingRateFloor": "-0.03000000",
        "fundingIntervalHours": 4,
        "disclaimer": false
    },]
        """
        if not self.can_send_req():
            return []
        return self._get_resp(self.u, api)

    def _get_resp(self, client: APIClient, api: str, params: dict = None):
        try:
            if not params:
                params = dict()
            return client.get(api, **params)
        except Exception as e:
            if isinstance(e, BaseHTTPClient.BadResponse):
                self.block_requests(e.code)
                current_app.logger.error(f'币安访问已被拉黑，code：{e.code}')
            raise e

    def block_requests(self, err_code: int):
        self.block_cache.block_requests(err_code)

    def can_send_req(self):
        return self.block_cache.can_send_req()


class OKXAPIClient:
    """
    产品类型(instType)
        SPOT：币币
        SWAP：永续合约
        FUTURES：交割合约
        OPTION：期权
    """
    URL = 'https://www.okx.com'
    CONTRACT_TYPE = 'SWAP'
    SPOT_TYPE = 'SPOT'
    WS_URL = 'wss://ws.okx.com:8443/ws/v5/public'

    def __init__(self):
        self.c = APIClient(self.URL)

    def fetch_depths(self, symbols):

        with connect(self.WS_URL) as websocket:
            res = dict()
            args = [{"channel": 'books', "instId": symbol} for symbol in symbols]
            subscribe_msg = {
                "op": "subscribe",
                "args": args
            }

            payload = json.dumps(subscribe_msg)
            websocket.send(payload)
            exit_count = len(symbols) * 3
            count = 0
            while True:
                count += 1
                if count >= exit_count:
                    break
                message = websocket.recv(timeout=10)
                data = json.loads(message)
                if data.get("event") == "subscribe" or data.get("event") == "error":
                    continue
                if "data" in data and len(data["data"]) > 0:
                    depth_info = data["data"][0]
                    symbol = data['arg']["instId"]
                    if symbol in res:   # 只需要初始数据，不需要传增量数据(说明初始数据已经传完？)
                        continue
                    res[symbol] = {
                        "bids": depth_info["bids"],
                        "asks": depth_info["asks"],
                    }
                if len(res) == len(symbols):
                    break

        return res

    def fetch_depth(self, symbol):
        api = '/api/v5/market/books'  # 40次/2s
        max_limit = 400
        params = {
            'instId': symbol,
            'sz': max_limit,
        }
        resp = self.c.get(api, **params)['data'][0]
        ret = {
            symbol: {
                'bids': resp.get('bids', []),
                'asks': resp.get('asks', []),
            }
        }
        return ret

    def fetch_s_depths(self, symbol: str):
        return self.fetch_depth(symbol)

    def fetch_markets(self):
        api = '/api/v5/public/instruments'
        params = {'instType': self.CONTRACT_TYPE}
        resp = self.c.get(api, **params)
        ret = {}
        for r in resp['data']:
            base = r['ctValCcy'] if r['ctType'] == 'linear' else r['settleCcy']
            quote = r['settleCcy'] if r['ctType'] == 'linear' else r['ctValCcy']
            ret.update({
                r['instId']: {
                    'symbol': r['instId'],
                    'baseAsset': base,
                    'quoteAsset': quote,
                    'type': r['ctType'],  # linear/inverse
                    'contractSize': r['ctVal'],  # 合约面值
                    'contractMult': r['ctMult'],  # 合约乘数
                }
            })
        return ret

    def fetch_spot_markets(self):
        api = '/api/v5/public/instruments'
        params = {'instType': self.SPOT_TYPE}
        resp = self.c.get(api, **params)
        res = dict()
        for r in resp['data']:
            symbol = r['instId']
            res[symbol] = {
                'symbol': symbol,
                'baseAsset': r['baseCcy'],
                'quoteAsset': r['quoteCcy'],
            }
        return res

    def fetch_last_prices(self):
        params = {'instType': self.CONTRACT_TYPE}
        return self._fetch_last_prices(params)

    def fetch_s_last_prices(self):
        params = {'instType': self.SPOT_TYPE}
        return self._fetch_last_prices(params)

    def _fetch_last_prices(self, params):
        api = '/api/v5/market/tickers'
        resp = self.c.get(api, **params)
        ret = {}
        for r in resp['data']:
            ret.update({r['instId']: Decimal(r['last'] or 0)})
        return ret

    def fetch_sign_prices(self):
        api = '/api/v5/public/mark-price'
        params = {'instType': self.CONTRACT_TYPE}
        resp = self.c.get(api, **params)
        ret = {}
        for r in resp['data']:
            ret.update({
                r['instId']: Decimal(r['markPx'])
            })
        return ret

    def fetch_funding_rate(self, inst_id):
        api = '/api/v5/public/funding-rate'
        params = {'instId': inst_id}
        resp = self.c.get(api, **params)
        data = resp['data'][0]
        funding_rate = data['fundingRate']
        funding_interval = int((int(data['nextFundingTime'])-int(data['fundingTime']))/(1000*3600))
        return {'funding_rate': funding_rate, 'funding_interval': funding_interval}

    def fetch_usdt_index_prices(self):
        quote_ccy = 'USDT'
        return self.fetch_index_prices(quote_ccy)

    def fetch_usdc_index_prices(self):
        quote_ccy = 'USDC'
        return self.fetch_index_prices(quote_ccy)

    def fetch_usd_index_prices(self):
        quote_ccy = 'USD'
        return self.fetch_index_prices(quote_ccy)

    def fetch_btc_index_prices(self):
        quote_ccy = 'BTC'
        return self.fetch_index_prices(quote_ccy)

    def fetch_index_prices(self, quote_ccy: str):
        api = '/api/v5/market/index-tickers'
        params = {'quoteCcy': quote_ccy}
        resp = self.c.get(api, **params)['data']
        ret = {}
        for r in resp:
            ret[r['instId']] = Decimal(r['idxPx'])
        return ret


class HuobiAPIClient:
    URL = 'https://api.hbdm.com'  # all public req 800次/60s
    SPOT_URL = 'https://api.huobi.pro'
    CONTRACT_TYPE = 'swap'
    # 这里取最多档 150
    STEP_MAPPING = {
        '0.0000001': 'step16',  # 'step18' merged 20 cnt
        '0.000001': 'step17',  # 'step19' merged 20 cnt
        '0.00001': 'step1',  # 'step7' merged 20 cnt
        '0.0001': 'step2',  # 'step8' merged 20 cnt
        '0.001': 'step3',  # 'step9' merged 20 cnt
        '0.01': 'step4',  # 'step10' merged 20 cnt
        '0.1': 'step5',  # 'step11' merged 20 cnt
        '1': 'step14',  # 'step12' merged 20 cnt
        '10': 'step15',  # 'step13' merged 20 cnt
    }       #  'step0' 不聚合

    def __init__(self):
        self.c = APIClient(self.URL)
        self.s = APIClient(self.SPOT_URL)

    def fetch_u_depth(self, symbol, step = 'step0'):
        api = '/linear-swap-ex/market/depth'
        return self.fetch_depth(symbol, api, step)

    def fetch_c_depth(self, symbol, step = 'step0'):
        api = '/swap-ex/market/depth'
        return self.fetch_depth(symbol, api, step)

    def fetch_depth(self, symbol, api, step):
        params = {
            'contract_code': symbol,
            'type': step,
        }
        resp = self.c.get(api, **params)['tick']
        bids, asks = resp.get('bids', []), resp.get('asks', [])
        b, a = [], []
        for bid in bids:
            b.append([
                amount_to_str(Decimal(bid[0])),
                amount_to_str(Decimal(bid[1])),
            ])
        for ask in asks:
            a.append([
                amount_to_str(Decimal(ask[0])),
                amount_to_str(Decimal(ask[1])),
            ])
        ret = {
            symbol: {
                'bids': b,
                'asks': a,
            }
        }
        return ret

    def fetch_u_markets(self):
        api = '/linear-swap-api/v1/swap_contract_info'
        params = {'contract_type': self.CONTRACT_TYPE}
        return self.fetch_markets(api, params)

    def fetch_c_markets(self):
        api = '/swap-api/v1/swap_contract_info'
        return self.fetch_markets(api, {})

    def fetch_markets(self, api, params: Dict):
        resp = self.c.get(api, **params)
        ret = {}
        for r in resp['data']:
            contract_code = r['contract_code']  # 未找到 base/quote
            codes = contract_code.split('-')
            base, quote = codes[0], codes[1]
            ret.update({
                r['contract_code']: {
                    'symbol': r['contract_code'],
                    'baseAsset': base,
                    'quoteAsset': quote,
                    'contractSize': amount_to_str(r.get('contract_size', 1)),  # 正反合约同一个参数
                }
            })
        return ret

    def fetch_u_last_prices(self):
        api = '/linear-swap-ex/market/trade'
        return self.fetch_last_prices(api)

    def fetch_c_last_prices(self):
        api = '/swap-ex/market/trade'
        return self.fetch_last_prices(api)

    def fetch_last_prices(self, api):
        resp = self.c.get(api, business_type=self.CONTRACT_TYPE)['tick']['data']
        ret = {}
        for r in resp:
            ret.update({r['contract_code']: Decimal(r['price'])})
        return ret

    def fetch_spot_markets(self):
        api = '/v2/settings/common/symbols'
        resp = self.s.get(api)
        data_lis = resp['data']
        res = dict()
        for r in data_lis:
            if r['state'] != 'online':
                continue
            symbol = r['sc']
            res[symbol] = {
                'symbol': symbol,
                'baseAsset': r['bc'],
                'quoteAsset': r['qc'],
            }
        return res

    def fetch_s_last_prices(self):
        """
        {
    "data": [
        {
            "symbol": "sylousdt",
            "open": 6.37E-4,
            "high": 6.4E-4,
            "low": 6.26E-4,
            "close": 6.38E-4,
            "amount": 4.192971082444E8,
            "vol": 265402.3757604294,
            "count": 7459,
            "bid": 6.35E-4,
            "bidSize": 151140.0,
            "ask": 6.4E-4,
            "askSize": 24317.0
        },
        ]
        }
        """
        api = '/market/tickers'
        resp = self.s.get(api)['data']
        ret = {}
        for r in resp:
            ret[r['symbol']] = r['close']
        return ret

    def fetch_s_depths(self, symbol):
        """"
            {
                "ch": "market.bchusdt.depth.step0",
                "status": "ok",
                "ts": 1739780540250,
                "tick": {
                    "bids": [
                        [
                            326.26,
                            1.08
                        ],
                        ],
                    "asks": [
                        [
                            326.28,
                            0.54
                        ],
                        ],
                    "version": 118415342553,
                    "ts": 1739780539030
                }
            }
        """
        api = '/market/depth'
        params = {
            'symbol': symbol,
            'type': 'step0'  # 不聚合，默认返回150档深度数据
        }
        resp = self.s.get(api, **params)['tick']
        ret = {
            symbol: {
                'bids': resp.get('bids', []),
                'asks': resp.get('asks', []),
            }
        }
        return ret

    def fetch_u_index_prices(self):
        api = '/linear-swap-api/v1/swap_index'
        resp = self.c.get(api)['data']
        ret = {}
        for r in resp:
            ret.update({r['contract_code']: Decimal(r['index_price'])})
        return ret

    def fetch_c_index_prices(self):
        api = '/swap-api/v1/swap_index'
        resp = self.c.get(api)['data']
        ret = {}
        for r in resp:
            ret.update({r['contract_code']: Decimal(r['index_price'])})
        return ret

    def fetch_u_funding_rate_data(self):
        api = '/linear-swap-api/v1/swap_batch_funding_rate'
        return self.fetch_funding_rate_data(api)

    def fetch_c_funding_rate_data(self):
        api = '/swap-api/v1/swap_batch_funding_rate'
        return self.fetch_funding_rate_data(api)

    def fetch_funding_rate_data(self, api):
        resp = self.c.get(api)['data']
        ret = {}
        for record in resp:
            funding_rate = record['funding_rate']
            next_funding_time = record['next_funding_time']
            funding_time = record['funding_time']
            if not funding_rate or not next_funding_time or not funding_time:
                continue
            funding_interval = int((int(next_funding_time)-int(funding_time))/(1000*3600))
            ret[record['contract_code']] = {
                'funding_rate': funding_rate,
                'funding_interval': funding_interval
            }
        return ret

    def fetch_u_sign_price(self, contract_code):
        api = '/index/market/history/linear_swap_mark_price_kline'
        return self.fetch_sign_price(api, contract_code)

    def fetch_c_sign_price(self, contract_code):
        api = '/index/market/history/swap_mark_price_kline'
        return self.fetch_sign_price(api, contract_code)

    def fetch_sign_price(self, api, contract_code):
        params = {
            'contract_code': contract_code,
            'size': 1,
            'period': '1min'
        }
        resp = self.c.get(api, **params)
        return Decimal(resp['data'][0]['close'])


class BybitAPIClient:
    URL = 'https://api.bybit.com'  # GET: 70req/5s,

    def __init__(self):
        self.c = APIClient(self.URL)

    def fetch_c_depth(self, symbol):
        category = 'inverse'
        return self.fetch_depth(symbol, category)

    def fetch_u_depth(self, symbol):
        category = 'linear'
        return self.fetch_depth(symbol, category)

    def fetch_depth(self, symbol, category):
        api = '/v5/market/orderbook'
        max_limit = 100
        params = {
            'category': category,
            'symbol': symbol,
            'limit': max_limit,
        }
        resp = self.c.get(api, **params)['result']
        ret = {
            symbol: {
                'bids': resp.get('b', []),
                'asks': resp.get('a', []),
            }
        }
        return ret

    def fetch_markets(self):
        rows = []
        api = '/v5/market/instruments-info'
        for category in ['linear', 'inverse']:
            data_lis = self.fetch_data_by_cursor(api, category)
            rows.extend(data_lis)
        ret = {}
        for r in rows:
            if r['contractType'] not in ('LinearPerpetual', 'InversePerpetual'):
                continue
            if r['quoteCoin'] == 'USD':
                _type = 'inverse'
            elif r['quoteCoin'] == 'USDT':
                _type = 'linear'
            elif r['quoteCoin'] == 'USDC':
                _type = 'linear'
            else:
                continue
            ret.update({
                r['symbol']: {
                    'symbol': r['symbol'],
                    'baseAsset': r['baseCoin'],
                    'quoteAsset': r['quoteCoin'],
                    'type': _type,  # 无 contractSize 相关字段
                }
            })
        return ret

    def fetch_last_prices(self):
        api = '/v5/market/tickers'
        u_rows = self.fetch_data_by_category(api, 'linear')
        c_rows = self.fetch_data_by_category(api, 'inverse')
        ret = {}
        for r in chain(c_rows, u_rows):
            ret.update({r['symbol']: r['lastPrice']})
        return ret

    def fetch_spot_markets(self):
        api = '/v5/market/instruments-info'
        params = {'category': 'spot'}
        resp = self.c.get(api, **params)
        symbol_lis = resp['result']['list']
        res = dict()
        for r in symbol_lis:
            symbol = r['symbol']
            res[symbol] = {
                'symbol': symbol,
                'baseAsset': r['baseCoin'],
                'quoteAsset': r['quoteCoin'],
            }
        return res

    def fetch_s_depths(self, symbol: str):
        return self.fetch_depth(symbol, 'spot')

    def fetch_s_last_prices(self):
        api = '/v5/market/tickers'
        rows = self.fetch_data_by_category(api, 'spot')
        ret = {}
        for r in rows:
            index_price = r.get('indexPrice') or Decimal()
            ret.update({r['symbol']: {
                'last_price': Decimal(r['lastPrice']),
                'index_price': Decimal(index_price),
            }})
        return ret

    def fetch_ticker_data(self):
        rows = []
        api = '/v5/market/tickers'
        for category in ['linear', 'inverse']:
            data_lis = self.fetch_data_by_category(api, category)
            rows.extend(data_lis)
        res = {}
        for row in rows:
            symbol = row['symbol']
            funding_rate = row['fundingRate']
            last_price = Decimal(row['lastPrice'])
            index_price = Decimal(row['indexPrice'])
            sign_price = Decimal(row['markPrice'])
            index_price_basis_rate = quantize_amount((last_price - sign_price) * 10000 / sign_price, 2)

            res.update({symbol: {
                'last_price': last_price,
                'index_price': index_price,
                'sign_price': sign_price,
                'index_price_basis_rate': index_price_basis_rate,
                'funding_rate': funding_rate,
            }})
        return res

    def fetch_funding_interval(self):
        rows = []
        api = '/v5/market/instruments-info'
        for category in ['linear', 'inverse']:
            data_lis = self.fetch_data_by_cursor(api, category)
            rows.extend(data_lis)
        res = {}
        for row in rows:
            symbol = row['symbol']
            funding_interval = int(row['fundingInterval'] / 60)
            res.update({
                symbol: funding_interval
            })
        return res

    def fetch_data_by_cursor(self, api: str, category: str):
        cursor = ''
        query_limit = 1000
        data_lis = []

        while True:
            params = dict(category=category, limit=query_limit, cursor=cursor)
            resp = self.c.get(api, **params)['result']
            data_lis.extend(resp['list'])
            next_page_cursor = resp['nextPageCursor']
            if not next_page_cursor:
                break
            cursor = next_page_cursor
        return data_lis

    def fetch_data_by_category(self, api: str, category: str):
        params = dict(category=category)
        data_lis = self.c.get(api, **params)['result']['list']
        return data_lis


class KucoinAPIClient:
    URL = 'https://api-futures.kucoin.com'  #
    SPOT_URL = 'https://api.kucoin.com'  # 現貨 & 槓桿

    def __init__(self):
        self.c = APIClient(self.URL)
        self.s = APIClient(self.SPOT_URL)

    def fetch_depth(self, symbol):
        api = '/api/v1/level2/depth100'
        params = {
            'symbol': symbol,
        }
        resp = self.c.get(api, **params)['data']
        bids, asks = resp.get('bids', []), resp.get('asks', [])
        b, a = [], []
        for bid in bids:
            b.append([
                amount_to_str(Decimal(bid[0])),
                amount_to_str(Decimal(bid[1])),
            ])
        for ask in asks:
            a.append([
                amount_to_str(Decimal(ask[0])),
                amount_to_str(Decimal(ask[1])),
            ])
        ret = {
            symbol: {
                'bids': b,
                'asks': a,
            }
        }
        return ret

    def fetch_markets(self):
        api = '/api/v1/contracts/active'
        resp = self.c.get(api)['data']
        ret = {}
        for r in resp:
            if r['type'] != 'FFWCSX':   # 非永续合约直接丢弃
                continue
            base_currency = r['baseCurrency']
            if base_currency == 'XBT':
                base_currency = 'BTC'
            ret.update({
                r['symbol']: {
                    'symbol': r['symbol'],
                    'baseAsset': base_currency,
                    'quoteAsset': r['quoteCurrency'],
                    'type': 'inverse' if r['isInverse'] else 'linear',
                    'last_price': r['lastTradePrice'] or 0,
                    'contractMult': '1' if r['isInverse'] else amount_to_str(r['multiplier']),
                }
            })
        return ret

    def fetch_last_prices(self):
        api = '/api/v1/contracts/active'
        resp = self.c.get(api)['data']
        ret = {}
        for r in resp:
            ret.update({r['symbol']: amount_to_str(r['lastTradePrice'])})
        return ret

    def fetch_s_last_prices(self):
        api = '/api/v1/market/allTickers'
        resp = self.s.get(api)['data']
        ret = {}
        for r in resp['ticker']:
            last = r['last']
            if not last:
                continue
            ret.update({r['symbol']: Decimal(last)})
        return ret

    def fetch_spot_markets(self):
        api = '/api/v2/symbols'
        resp = self.s.get(api)
        symbol_lis = resp['data']
        res = dict()
        for r in symbol_lis:
            symbol = r['symbol']
            res[symbol] = {
                'symbol': symbol,
                'baseAsset': r['baseCurrency'],
                'quoteAsset': r['quoteCurrency'],
            }
        return res

    def fetch_s_depths(self, symbol: str):
        api = '/api/v1/market/orderbook/level2_100'
        params = {
            'symbol': symbol,
        }
        resp = self.s.get(api, **params)['data']
        ret = {
            symbol: {
                    'bids': resp.get('bids', []),
                    'asks': resp.get('asks', []),
            }
        }
        return ret

    def fetch_price_funding_infos(self):
        api = '/api/v1/contracts/active'
        resp = self.c.get(api)['data']
        ret = {}
        for r in resp:
            if r['type'] != 'FFWCSX':
                continue
            last_price = Decimal(r['lastTradePrice'])
            index_price = Decimal(r['indexPrice'])
            sign_price = Decimal(r['markPrice'])
            funding_rate = quantize_amount(r['fundingFeeRate'], 8)
            funding_interval = int(r['fundingRateGranularity'] / (1000*3600))
            index_price_basis_rate = quantize_amount(
                (last_price - index_price) * 10000 / index_price, 2)
            ret[r['symbol']] = {
                'base_asset': r['baseCurrency'],
                'last_price': last_price,
                'index_price': index_price,
                'sign_price': sign_price,
                'index_price_basis_rate': index_price_basis_rate,
                'funding_rate': funding_rate,
                'funding_interval': funding_interval,
            }
        return ret


class GateAPIClient:
    URL = 'https://api.gateio.ws/api/v4'  # perpetual public req: 300r/s
    S_WS_URL = 'wss://ws.gate.io/v3/'

    STEP_MAPPING = {
        '0': '0',  # 默认不合并
        '0.1': '0.1',
        '0.01': '0.01',
    }

    def __init__(self):
        self.c = APIClient(self.URL)

    def fetch_u_depth(self, symbol, step = '0'):
        settle = 'usdt'
        api = f'/futures/{settle}/order_book'
        return self.fetch_depth(symbol, api, step)

    def fetch_c_depth(self, symbol, step = '0'):
        settle = 'btc'
        api = f'/futures/{settle}/order_book'
        return self.fetch_depth(symbol, api, step)

    def fetch_depth(self, symbol, api, step):
        max_limit = 300
        params = {
            'contract': symbol,
            'interval': step,
            'limit': max_limit,
        }
        resp = self.c.get(api, **params)
        ret = {
            symbol: {
                'bids': resp.get('bids', []),
                'asks': resp.get('asks', []),
            }
        }
        return ret

    def fetch_s_depths_ws(self, symbol_depth_dic):
        return self._fetch_s_depths(self.S_WS_URL, symbol_depth_dic)

    def _fetch_s_depths(self, ws_url, symbol_depth_dic):
        with connect(ws_url) as websocket:
            res = dict()
            max_limit = 30
            for symbol, depth in symbol_depth_dic.items():
                payload = {
                    "method": "depth.query",
                    "params": [symbol, max_limit, depth]
                }
                websocket.send(json.dumps(payload, cls=JsonEncoder))
                message = websocket.recv(timeout=10)
                data = json.loads(message)
                res[symbol] = {
                            "bids": data['result']["bids"],
                            "asks": data['result']["asks"],
                        }
        return res

    def fetch_u_markets(self):
        settle = 'usdt'
        api = f'/futures/{settle}/contracts'
        return self.fetch_markets(api)

    def fetch_c_markets(self):
        settle = 'btc'
        api = f'/futures/{settle}/contracts'
        return self.fetch_markets(api)

    def fetch_markets(self, api):
        resp = self.c.get(api)
        ret = {}
        for r in resp:
            codes = r['name'].split('_')
            base, quote = codes[0], codes[1]
            ret.update({
                r['name']: {
                    'symbol': r['name'],
                    'baseAsset': base,
                    'quoteAsset': quote,
                    'type': r['type'],
                    'last_price': r['last_price'],
                    'contractMult': r['quanto_multiplier'],
                }
            })
        return ret

    def fetch_u_last_prices(self):
        settle = 'usdt'
        api = f'/futures/{settle}/contracts'
        return self.fetch_last_prices(api)

    def fetch_c_last_prices(self):
        settle = 'btc'
        api = f'/futures/{settle}/contracts'
        return self.fetch_last_prices(api)

    def fetch_last_prices(self, api):
        resp = self.c.get(api)
        ret = {}
        for r in resp:
            ret.update({r['name']: r['last_price']})
        return ret

    def fetch_c_price_and_funding_info(self):
        settle = 'btc'
        api = f'/futures/{settle}/contracts'
        return self.fetch_price_and_funding_info(api)

    def fetch_u_price_and_funding_info(self):
        settle = 'usdt'
        api = f'/futures/{settle}/contracts'
        return self.fetch_price_and_funding_info(api)

    def fetch_price_and_funding_info(self, api):
        resp: List[Dict] = self.c.get(api)
        ret = {}
        for r in resp:
            last_price = Decimal(r['last_price'])
            index_price = Decimal(r['index_price'])
            sign_price = Decimal(r['mark_price'])
            funding_interval = int(r['funding_interval']/3600)
            funding_rate = r['funding_rate']
            index_price_basis_rate = quantize_amount((last_price-index_price)*10000/index_price, 2)
            ret[r['name']] = {
                'last_price': last_price,
                'index_price': index_price,
                'sign_price': sign_price,
                'index_price_basis_rate': index_price_basis_rate,
                'funding_interval': funding_interval,
                'funding_rate': funding_rate,
            }
        return ret

    def fetch_s_last_prices(self):
        api = '/spot/tickers'
        resp = self.c.get(api)
        ret = {}
        for r in resp:
            ret.update({r['currency_pair']: Decimal(r['last'])})
        return ret

    def fetch_spot_markets(self):
        api = '/spot/currency_pairs'
        resp = self.c.get(api)
        res = dict()
        for r in resp:
            symbol = r['id']
            res[symbol] = {
                'symbol': symbol,
                'baseAsset': r['base'],
                'quoteAsset': r['quote'],
            }
        return res

    def fetch_s_depths(self, symbol):
        api = '/spot/order_book'
        max_limit = 300
        params = {
            'limit': max_limit,
            'currency_pair': symbol
        }
        resp = self.c.get(api, **params)
        ret = {
            symbol: {
                    'bids': resp['bids'],
                    'asks': resp['asks'],
            }
        }
        return ret


class BitgetAPIClient:
    URL = 'https://api.bitget.com'
    U_MARKETS_TYPE = 'umcbl'
    C_MARKETS_TYPE = 'dmcbl'

    def __init__(self):
        self.c = APIClient(self.URL)

    def fetch_u_markets(self):
        api = '/api/mix/v1/market/contracts'
        params = {'productType': self.U_MARKETS_TYPE}
        return self.fetch_markets(api, params)

    def fetch_c_markets(self):
        api = '/api/mix/v1/market/contracts'
        params = {'productType': self.C_MARKETS_TYPE}
        return self.fetch_markets(api, params)

    def fetch_markets(self, api, params: Dict):
        resp = self.c.get(api, **params)['data']
        ret = {}
        for r in resp:
            if r['symbolType'] != 'perpetual':  # 只保留永续合约
                continue
            ret.update({
                r['symbol']: {
                    'symbol': r['symbol'],
                    'baseAsset': r['baseCoin'],
                    'quoteAsset': r['quoteCoin'],
                }
            })
        return ret

    def fetch_depth(self, symbol):
        # 20次/1s
        api = '/api/mix/v1/market/depth'
        max_limit = 100
        params = {
            'symbol': symbol,
            'limit': max_limit,
        }
        resp = self.c.get(api, **params)['data']
        ret = {
            symbol: {
                'bids': resp.get('bids', []),
                'asks': resp.get('asks', []),
            }
        }
        return ret

    def fetch_u_last_prices(self):
        api = '/api/mix/v1/market/tickers'
        params = {'productType': self.U_MARKETS_TYPE}
        return self.fetch_last_prices(api, params)

    def fetch_c_last_prices(self):
        api = '/api/mix/v1/market/tickers'
        params = {'productType': self.C_MARKETS_TYPE}
        return self.fetch_last_prices(api, params)

    def fetch_last_prices(self, api, params: Dict):
        resp = self.c.get(api, **params)['data']
        ret = {}
        for r in resp:
            symbol = r['symbol']
            ret.update({symbol: r['last']})
        return ret

    def fetch_spot_markets(self):
        api = '/api/v2/spot/public/symbols'
        resp = self.c.get(api)
        symbol_lis = resp['data']
        res = dict()
        for r in symbol_lis:
            symbol = r['symbol']
            res[symbol] = {
                'symbol': symbol,
                'baseAsset': r['baseCoin'],
                'quoteAsset': r['quoteCoin'],
            }
        return res

    def fetch_s_last_prices(self):
        api = '/api/v2/spot/market/tickers'
        resp = self.c.get(api)['data']
        res = dict()
        for r in resp:
            res[r['symbol']] = r['lastPr']
        return res

    def fetch_s_depths(self, symbol: str):
        # 20次/1s (IP)
        api = '/api/v2/spot/market/orderbook'
        params = {
            'symbol': symbol,
        }
        resp = self.c.get(api, **params)['data']
        ret = {
            symbol: {
                    'bids': resp.get('bids', []),
                    'asks': resp.get('asks', []),
            }
        }
        return ret


class MexcAPIClient:
    URL = 'https://contract.mexc.com'   # req: 20r/2s
    SPOT_URL = 'https://api.mexc.com'

    def __init__(self):
        self.c = APIClient(self.URL)
        self.s = APIClient(self.SPOT_URL)

    def fetch_funding_rate_data(self, symbol: str):
        api = f'api/v1/contract/funding_rate/{symbol}'
        ret = self.c.get(api)
        return {
            'funding_rate': ret['data']['fundingRate'],
            'funding_interval': ret['data']['collectCycle']
                }

    def fetch_ticker_data(self):
        api = '/api/v1/contract/ticker'
        ret = self.c.get(api)
        res = {}
        for row in ret['data']:
            symbol = row['symbol']
            last_price = Decimal(row.get('lastPrice', 0))
            index_price = Decimal(row.get('indexPrice', 0))
            sign_price = Decimal(row.get('fairPrice', 0))
            index_price_basis_rate = quantize_amount(safe_div((last_price-index_price)*10000, index_price), 2)

            res.update({symbol: {
                'last_price': last_price,
                'index_price': index_price,
                'sign_price': sign_price,
                'index_price_basis_rate': index_price_basis_rate,
            }})
        return res

    def fetch_spot_markets(self):
        api = '/api/v3/exchangeInfo'
        resp = self.s.get(api)
        symbol_lis = resp['symbols']
        res = dict()
        for r in symbol_lis:
            symbol = r['symbol']
            res[symbol] = {
                'symbol': symbol,
                'baseAsset': r['baseAsset'],
                'quoteAsset': r['quoteAsset'],
            }
        return res

    def fetch_s_last_prices(self):
        api = '/api/v3/ticker/price'
        resp = self.s.get(api)
        res = dict()
        for r in resp:
            res[r['symbol']] = r['price']
        return res

    def fetch_s_depths(self, symbol: str, max_limit: int = 1000):
        """限频根据limit计算权重"""
        api = '/api/v3/depth'
        params = {
            'symbol': symbol,
            'limit': max_limit
        }
        resp = self.s.get(api, **params)
        ret = {
            symbol: {
                    'bids': resp.get('bids', []),
                    'asks': resp.get('asks', []),
            }
        }
        return ret


class LbankAPIClient:
    # 200次/10秒
    URL = 'https://api.lbkex.com'

    def __init__(self):
        self.s = APIClient(self.URL)

    def fetch_spot_markets(self):
        api = '/v2/currencyPairs.do'
        resp = self.s.get(api)
        symbol_lis = resp['data']
        res = dict()
        for r in symbol_lis:
            currency_pair = r.split('_')
            base_coin, quote_coin = currency_pair[0], currency_pair[-1]
            res[r] = {
                'symbol': r,
                'baseAsset': base_coin,
                'quoteAsset': quote_coin,
            }
        return res

    def fetch_s_last_prices(self):
        api = '/v2/supplement/ticker/price.do'
        resp = self.s.get(api)['data']
        res = dict()
        for r in resp:
            res[r['symbol']] = r['price']
        return res

    def fetch_s_depths(self, symbol: str):
        api = '/v2/depth.do'
        max_limit = 100
        params = {
            'symbol': symbol,
            'size': max_limit
        }
        resp = self.s.get(api, **params)['data']
        ret = {
            symbol: {
                    'bids': resp.get('bids', []),
                    'asks': resp.get('asks', []),
            }
        }
        return ret


class BitmartAPIClient:
    URL = 'https://api-cloud.bitmart.com'

    def __init__(self):
        self.s = APIClient(self.URL)

    def fetch_spot_markets(self):
        api = '/spot/v1/symbols/details'
        resp = self.s.get(api)
        symbol_lis = resp['data']['symbols']
        res = dict()
        for r in symbol_lis:
            symbol = r['symbol']
            res[symbol] = {
                'symbol': symbol,
                'baseAsset': r['base_currency'],
                'quoteAsset': r['quote_currency'],
            }
        return res

    def fetch_s_last_prices(self):
        api = '/spot/quotation/v3/tickers'
        resp = self.s.get(api)['data']
        res = dict()
        for r in resp:
            res[r[0]] = r[1]
        return res

    def fetch_s_depths(self, symbol: str):
        # 15次/2秒
        api = '/spot/quotation/v3/books'
        max_limit = 50
        params = {
            'symbol': symbol,
            'limit': max_limit
        }
        resp = self.s.get(api, **params)['data']
        ret = {
            symbol: {
                    'bids': resp.get('bids', []),
                    'asks': resp.get('asks', []),
            }
        }
        time.sleep(1)
        return ret


class BingxAPIClient:
    # 100/10s
    URL = 'https://open-api.bingx.com'

    def __init__(self):
        self.s = APIClient(self.URL)

    def fetch_spot_markets(self):
        api = '/openApi/spot/v1/common/symbols'
        resp = self.s.get(api)
        symbol_lis = resp['data']['symbols']
        res = dict()
        for r in symbol_lis:
            symbol = r['symbol']
            status = r['status']
            if status != 1:
                continue
            currency_pair = symbol.split('-')
            base_currency, quote_currency = currency_pair[0], currency_pair[-1]
            res[symbol] = {
                'symbol': symbol,
                'baseAsset': base_currency,
                'quoteAsset': quote_currency,
            }
        return res

    def fetch_s_last_prices(self):
        api = '/openApi/spot/v1/ticker/price'
        resp = self.s.get(api)['data']
        res = dict()
        for r in resp:
            symbol_lis = r['symbol'].split('_')
            symbol = f'{symbol_lis[0]}-{symbol_lis[-1]}'
            last = r['trades'][0]['price']
            res[symbol] = last
        return res

    def fetch_s_depths(self, symbol: str):
        api = '/openApi/spot/v1/market/depth'
        max_limit = 100
        params = {
            'symbol': symbol,
            'limit': max_limit
        }
        resp = self.s.get(api, **params)
        code = resp['code']
        data = resp['data']
        if code != 0:
            ret = {
                symbol: {
                    'bids': [],
                    'asks': [],
                }
            }
        elif isinstance(data, dict):  # 可能存在 resp = 'data is not ready'的情况
            ret = {
                symbol: {
                        'bids': data.get('bids', []),
                        'asks': data.get('asks', []),
                }
            }
        else:
            ret = {
                symbol: {
                        'bids': [],
                        'asks': [],
                }
            }
        return ret
    
    def fetch_u_last_prices(self):
        """U本位永续合约价格
        return:
        {'ACE-USDT': '1.23456', 'AAVE-USDT': '1.23456'}
        """
        api = '/openApi/swap/v1/ticker/price'
        resp = self.s.get(api)['data']
        res = dict()
        for r in resp:
            res[r['symbol']] = r['price']
        return res

    def fetch_c_last_prices(self):
        """币本位永续合约价格
        return:
        {'SUI-USD': '1.23456', 'TONCOIN-USD': '1.23456'}
        """
        api = '/openApi/cswap/v1/market/ticker'
        ts = current_timestamp(to_int=True) * 1000
        params = {
            'timestamp': ts
        }
        resp = self.s.get(api, **params)['data']
        res = dict()
        for r in resp:
            res[r['symbol']] = r['lastPrice']
        return res
    
    def fetch_u_markets(self):
        api = '/openApi/swap/v2/quote/contracts'
        resp = self.s.get(api)['data']
        res = dict()
        for r in resp:
            base_currency = r['asset']
            quote_currency = r['currency']
            symbol = r['symbol']
            res[symbol] = {
                'symbol': symbol,
                'baseAsset': base_currency,
                'quoteAsset': quote_currency,
            }
        return res
    
    def fetch_c_markets(self):
        api = '/openApi/cswap/v1/market/contracts'
        ts = current_timestamp(to_int=True) * 1000
        params = {
            'timestamp': ts
        }
        resp = self.s.get(api, **params)['data']
        res = dict()
        for r in resp:
            symbol = r['symbol']
            split_lis = symbol.split('-')
            if not split_lis or len(split_lis) != 2:
                continue
            res[symbol] = {
                'symbol': symbol,
                'baseAsset': split_lis[0],
                'quoteAsset': split_lis[1],
            }
        return res

    def fetch_u_depth(self, symbol: str):
        api = '/openApi/swap/v2/quote/depth'
        max_limit = 1000
        params = {
            'symbol': symbol,
            'limit': max_limit
        }
        resp = self.s.get(api, **params)['data']
        ret = {
            symbol: {
                'bids': resp.get('bids', []),
                'asks': resp.get('asks', []),
            }
        }
        return ret

    def fetch_c_depth(self, symbol: str):
        api = '/openApi/cswap/v1/market/depth'
        max_limit = 1000
        ts = current_timestamp(to_int=True) * 1000
        params = {
            'symbol': symbol,
            'timestamp': ts,
            'limit': max_limit
        }
        resp = self.s.get(api, **params)['data']
        ret = {
            symbol: {
                'bids': resp.get('bids', []),
                'asks': resp.get('asks', []),
            }
        }
        return ret


class CoinwAPIClient:
    URL = 'https://api.coinw.com'

    def __init__(self):
        self.s = APIClient(self.URL)

    def fetch_spot_markets(self):
        api = '/api/v1/public?command=returnSymbol'
        resp = self.s.get(api)
        symbol_lis = resp['data']
        res = dict()
        for r in symbol_lis:
            symbol = r['currencyPair']
            res[symbol] = {
                'symbol': symbol,
                'baseAsset': r['currencyBase'],
                'quoteAsset': r['currencyQuote'],
            }
        return res

    def fetch_s_last_prices(self):
        api = '/api/v1/public?command=returnTicker'
        resp = self.s.get(api)['data']
        res = dict()
        for symbol, info in resp.items():
            last = info['last']
            res[symbol] = last
        return res

    def fetch_s_depths(self, symbol: str):
        # 5次/s
        api = '/api/v1/public'
        max_limit = 20
        params = {
            'command': 'returnOrderBook',
            'symbol': symbol,
            'size': max_limit
        }
        resp = self.s.get(api, **params)['data']
        ret = {
            symbol: {
                    'bids': resp.get('bids', []),
                    'asks': resp.get('asks', []),
            }
        }
        return ret


class XTComAPIClient:
    SPOT_URL = 'https://sapi.xt.com'

    def __init__(self):
        self.s = APIClient(self.SPOT_URL)

    def fetch_spot_markets(self):
        api = '/v4/public/symbol'
        resp = self.s.get(api)['result']['symbols']
        res = dict()
        for r in resp:
            res[r['symbol']] = {
                'symbol': r['symbol'],
                'baseAsset': r['baseCurrency'].upper(),
                'quoteAsset': r['quoteCurrency'].upper(),
            }
        return res

    def fetch_s_last_prices(self):
        """
        return:
        {'ape_usdt': 1.23456, 'btc_usdt': 1.23456}
        """
        api = '/v4/public/ticker/price'
        resp = self.s.get(api)['result']
        res = dict()
        for r in resp:
            res[r['s']] = r['p']
        return res

    def fetch_s_depth(self, symbol: str):
        api = '/v4/public/depth'
        max_limit = 500
        params = {
            'symbol': symbol,
            'limit': max_limit
        }
        resp = self.s.get(api, **params)['result']
        ret = {
            symbol: {
                'bids': resp.get('bids', []),
                'asks': resp.get('asks', []),
            }
        }
        return ret
    

class BaseSpotDepthHandler:
    CLIENT = None

    def __init__(self):
        self.client = self.CLIENT()
        self.symbol_to_market = dict()
        self.ours_markets = self.get_markets_info()

    @staticmethod
    def get_markets_info():
        recs = Market.query.filter(
            Market.status == Market.Status.ONLINE
        ).with_entities(
            Market.name,
            Market.default_depth,
            Market.base_asset,
            Market.quote_asset,
        ).all()
        res = defaultdict(dict)
        for market, default_depth, base_asset, quote_asset in recs:
            res[market]['default_depth'] = default_depth
            res[market]['base_asset'] = base_asset
            res[market]['quote_asset'] = quote_asset
        return res
    
    def get_market_price_list(self):
        symbol_price_dic = self.get_market_prices()
        symbol_dic = self.get_symbols()
        symbol_to_market = self.transfer_to_our_markets(symbol_dic)
        res = []
        for symbol, price in symbol_price_dic.items():
            if  not (market := symbol_to_market.get(symbol)):
                continue
            res.append([
                market,
                price,
            ])
        return res
     
    def build_spot_depth_data(self) -> list:
        ts = current_timestamp(to_int=True)
        snapshot_ts = ts - ts % (60 * 30)
        market_price_dic = self.get_market_prices()
        symbol_dic = self.get_symbols()
        self.symbol_to_market = self.transfer_to_our_markets(symbol_dic)
        symbols = list(self.symbol_to_market.keys())
        symbols.sort(key=lambda x: PRIORITY_MARKETS.index(x) if x in PRIORITY_MARKETS else 100000)
        market_depth_dic = self.get_market_depths(symbols)
        res = []
        for symbol, bid_ask in market_depth_dic.items():
            last_price = market_price_dic.get(symbol)
            if not last_price:
                continue
            market = self.symbol_to_market[symbol]
            market_info = self.ours_markets[market]
            base_asset_rate = PriceManager.asset_to_usd(market_info['base_asset'])
            quote_asset_rate = PriceManager.asset_to_usd(market_info['quote_asset'])
            default_depth = market_info['default_depth']
            bid_ask.update({
                'last': last_price,
            })
            res.append([
                default_depth,  # 取自己默认，第三方未提供
                market,
                base_asset_rate,
                quote_asset_rate,
                json.dumps(bid_ask, cls=JsonEncoder),
                snapshot_ts
            ])
        return res

    def get_symbols(self) -> dict:
        raise NotImplementedError

    def transfer_to_our_markets(self, symbol_dic):
        res = dict()
        for symbol, info in symbol_dic.items():
            base = info['baseAsset'].upper()
            quote = info['quoteAsset'].upper()
            market = f'{base}{quote}'
            if market not in self.ours_markets:
                continue
            res[symbol] = market
        return res

    def get_market_prices(self) -> dict:
        raise NotImplementedError

    def get_market_depths(self, symbols):
        """
        {"BTCUSDT": {"bids": [], "asks": []}}
        """
        raise NotImplementedError


class BinanceSpotDepthHandler(BaseSpotDepthHandler):
    CLIENT = BinanceAPIClient
    tag = Exchange.Binance.value

    def get_symbols(self) -> dict:
        return self.client.fetch_spot_markets()

    def get_market_prices(self):
        return self.client.fetch_s_last_prices()

    def get_market_depths(self, symbols):
        res = dict()
        for symbol in symbols:
            if symbol in PRIORITY_MARKETS:   # 获取5000条深度数据，权重为250
                sleep_time = 3.5
                max_limit = 2000
            else:
                sleep_time = 1.5    # 获取1000条深度数据，权重为50
                max_limit = 1000
            try:
                ret = self.client.fetch_s_depth(symbol, max_limit)
                res.update(ret)
            except Exception as e:
                current_app.logger.error(f'{self.tag} 现货市场{symbol} 深度获取失败，msg：{e}')
            time.sleep(sleep_time)
        return res


class OKXSpotDepthHandler(BaseSpotDepthHandler):
    CLIENT = OKXAPIClient
    tag = Exchange.OKX.value

    def get_symbols(self) -> dict:
        return self.client.fetch_spot_markets()

    def get_market_prices(self):
        return self.client.fetch_s_last_prices()

    def get_market_depths(self, symbols):

        ret = dict()
        depth_list = []
        for chunk_symbols in batch_iter(symbols, 100):
            chunk_depth_list = g_map(self.client.fetch_s_depths, chunk_symbols, fail_safe={}, size=5)
            time.sleep(1.2)
            depth_list.extend(chunk_depth_list)
        for depth in depth_list:
            ret.update(depth)

        def _to_ours(asks_bids):
            r = []
            for x in asks_bids:
                r.append([x[0], x[1]])
            return r

        depths = {}
        for symbol, v in ret.items():
            bids, asks = v.get('bids', []), v.get('asks', [])
            b, a = _to_ours(bids), _to_ours(asks)
            depths[symbol] = {
                'bids': b,
                'asks': a,
            }
        return depths


class GateSpotDepthHandler(BaseSpotDepthHandler):
    CLIENT = GateAPIClient
    tag = Exchange.GATE.value

    def get_symbols(self) -> dict:
        return self.client.fetch_spot_markets()

    def get_market_prices(self):
        return self.client.fetch_s_last_prices()

    def get_market_depths(self, symbols):
        ret = dict()
        depth_list = []
        for chunk_symbols in batch_iter(symbols, 100):
            chunk_depth_list = g_map(self.client.fetch_s_depths, chunk_symbols, fail_safe={}, size=5)
            time.sleep(1.2)
            depth_list.extend(chunk_depth_list)
        for depth in depth_list:
            ret.update(depth)
        return ret


class HTXSpotDepthHandler(BaseSpotDepthHandler):
    CLIENT = HuobiAPIClient
    tag = Exchange.HTX.value

    def get_symbols(self) -> dict:
        return self.client.fetch_spot_markets()

    def get_market_prices(self):
        return self.client.fetch_s_last_prices()

    def get_market_depths(self, symbols):
        res = dict()
        depth_list = []
        for chunk_symbols in batch_iter(symbols, 100):
            chunk_depth_list = g_map(self.client.fetch_s_depths, chunk_symbols, fail_safe={}, size=5)
            time.sleep(1.2)
            depth_list.extend(chunk_depth_list)
        for depth in depth_list:
            res.update(depth)
        return res


class MEXCSpotDepthHandler(BaseSpotDepthHandler):
    CLIENT = MexcAPIClient
    tag = Exchange.MEXC.value

    def get_symbols(self) -> dict:
        return self.client.fetch_spot_markets()

    def get_market_prices(self):
        return self.client.fetch_s_last_prices()

    def get_market_depths(self, symbols):
        res = dict()
        for symbol in symbols:
            if symbol in PRIORITY_MARKETS:
                sleep_time = 3.5
                max_limit = 2000
            else:
                sleep_time = 0.7
                max_limit = 1000
            try:
                ret = self.client.fetch_s_depths(symbol, max_limit)
                res.update(ret)
            except Exception as e:
                current_app.logger.error(f'{self.tag} 现货市场{symbol} 深度获取失败，msg：{e}')
            time.sleep(sleep_time)
        return res


class BybitSpotDepthHandler(BaseSpotDepthHandler):
    CLIENT = BybitAPIClient
    tag = Exchange.BYBIT.value

    def get_symbols(self) -> dict:
        return self.client.fetch_spot_markets()

    def get_market_prices(self):
        return self.client.fetch_last_prices()

    def get_market_depths(self, symbols):
        res = dict()
        depth_list = []
        for chunk_symbols in batch_iter(symbols, 100):
            chunk_depth_list = g_map(self.client.fetch_s_depths, chunk_symbols, fail_safe={}, size=5)
            time.sleep(1.2)
            depth_list.extend(chunk_depth_list)
        for depth in depth_list:
            res.update(depth)
        return res


class KucoinSpotDepthHandler(BaseSpotDepthHandler):
    CLIENT = KucoinAPIClient
    tag = Exchange.KUCOIN.value

    def get_symbols(self) -> dict:
        return self.client.fetch_spot_markets()

    def get_market_prices(self):
        return self.client.fetch_s_last_prices()

    def get_market_depths(self, symbols):
        res = dict()
        depth_list = []
        for chunk_symbols in batch_iter(symbols, 100):
            chunk_depth_list = g_map(self.client.fetch_s_depths, chunk_symbols, fail_safe={}, size=5)
            time.sleep(1.2)
            depth_list.extend(chunk_depth_list)
        for depth in depth_list:
            res.update(depth)
        return res


class BitgetSpotDepthHandler(BaseSpotDepthHandler):
    CLIENT = BitgetAPIClient
    tag = Exchange.BITGET.value

    def get_symbols(self) -> dict:
        return self.client.fetch_spot_markets()

    def get_market_prices(self):
        return self.client.fetch_s_last_prices()

    def get_market_depths(self, symbols):
        res = dict()
        depth_list = []
        for chunk_symbols in batch_iter(symbols, 100):
            chunk_depth_list = g_map(self.client.fetch_s_depths, chunk_symbols, fail_safe={}, size=5)
            time.sleep(1.2)
            depth_list.extend(chunk_depth_list)
        for depth in depth_list:
            res.update(depth)
        return res


class LbankSpotDepthHandler(BaseSpotDepthHandler):
    CLIENT = LbankAPIClient
    tag = Exchange.LBANK.value

    def get_symbols(self) -> dict:
        return self.client.fetch_spot_markets()

    def get_market_prices(self):
        return self.client.fetch_s_last_prices()

    def get_market_depths(self, symbols):
        res = dict()
        depth_list = []
        for chunk_symbols in batch_iter(symbols, 100):
            chunk_depth_list = g_map(self.client.fetch_s_depths, chunk_symbols, fail_safe={}, size=5)
            time.sleep(1.2)
            depth_list.extend(chunk_depth_list)
        for depth in depth_list:
            res.update(depth)
        return res


class BitmartSpotDepthHandler(BaseSpotDepthHandler):
    CLIENT = BitmartAPIClient
    tag = Exchange.BITMART.value

    def get_symbols(self) -> dict:
        return self.client.fetch_spot_markets()

    def get_market_prices(self):
        return self.client.fetch_s_last_prices()

    def get_market_depths(self, symbols):
        res = dict()
        depth_lis = g_map(self.client.fetch_s_depths, symbols, fail_safe={}, size=7)
        for depth in depth_lis:
            res.update(depth)
        return res


class BingxSpotDepthHandler(BaseSpotDepthHandler):
    CLIENT = BingxAPIClient
    tag = Exchange.BINGX.value

    def get_symbols(self) -> dict:
        return self.client.fetch_spot_markets()

    def get_market_prices(self):
        return self.client.fetch_s_last_prices()

    def get_market_depths(self, symbols):
        res = dict()
        depth_list = []
        for chunk_symbols in batch_iter(symbols, 100):
            chunk_depth_list = g_map(self.client.fetch_s_depths, chunk_symbols, fail_safe={}, size=5)
            time.sleep(1.2)
            depth_list.extend(chunk_depth_list)
        for depth in depth_list:
            res.update(depth)
        return res


class CoinwSpotDepthHandler(BaseSpotDepthHandler):
    CLIENT = CoinwAPIClient
    tag = Exchange.COINW.value

    def get_symbols(self) -> dict:
        return self.client.fetch_spot_markets()

    def get_market_prices(self):
        return self.client.fetch_s_last_prices()

    def get_market_depths(self, symbols):
        res = dict()
        depth_lis = g_map(self.client.fetch_s_depths, symbols, fail_safe={}, size=4)
        for depth in depth_lis:
            res.update(depth)
        return res


class XTComSpotDepthHandler(BaseSpotDepthHandler):
    CLIENT = XTComAPIClient
    tag = Exchange.XTCOM.value

    def get_symbols(self) -> dict:
        return self.client.fetch_spot_markets()

    def get_market_prices(self):
        return self.client.fetch_s_last_prices()
    
    def get_market_depths(self, symbols):
        res = dict()
        depth_list = []
        for chunk_symbols in batch_iter(symbols, 100):
            chunk_depth_list = g_map(self.client.fetch_s_depth, chunk_symbols, fail_safe={}, size=5)
            time.sleep(1.2)
            depth_list.extend(chunk_depth_list)
        for depth in depth_list:
            res.update(depth)
        return res
    

class BaseDepthHandler:
    CLIENT = None

    def __init__(self):
        self.client = self.CLIENT()
        self.ours_markets = PerpetualMarketCache().read_aside()

        self.asset_rates = {}
        self.symbol_to_market = {}

    def build_depth_data(self) -> list:
        ts = current_timestamp(to_int=True)
        snapshot_ts = ts - ts % (60 * 30)
        last_prices = self._merge_last_prices()
        markets = self._calculate_markets()
        c_symbols, u_symbols = markets['symbols']
        if not c_symbols + u_symbols:
            return []
        u_symbols.sort(key=lambda x: PRIORITY_MARKETS.index(x) if x in PRIORITY_MARKETS else 100000)
        depth = self._req_depth_data(c_symbols, u_symbols)
        ret = []
        for symbol, bid_ask in depth.items():
            last_price = last_prices.get(symbol)
            if not last_price:
                continue
            stock_rate = self._get_asset_rate(markets['markets'][symbol]['baseAsset'])
            money_rate = self._get_asset_rate(markets['markets'][symbol]['quoteAsset'])
            market = self.symbol_to_market[symbol]
            default_depth = self.ours_markets[market]['default_merge']
            bid_ask.update({
                'last': last_price,
                'contract_size': markets['markets'][symbol].get('contractSize', 1),
                'contract_mult': markets['markets'][symbol].get('contractMult', 1),
            })
            ret.append([
                default_depth,  # 取自己默认，第三方未提供
                market,
                money_rate,
                stock_rate,
                json.dumps(bid_ask, cls=JsonEncoder),
                snapshot_ts
            ])
        return ret

    def get_market_price_list(self):
        symbol_price_dic = self._merge_last_prices()
        self._calculate_markets()
        res = []
        for symbol, price in symbol_price_dic.items():
            if  not (market := self.symbol_to_market.get(symbol)):
                continue
            res.append([
                market,
                price,
            ])
        return res

    def _calculate_markets(self) -> dict:

        def _get_mapping(_markets):
            _mapping = {}
            for _symbol, info in _markets.items():
                base = info['baseAsset'].upper()
                quote = info['quoteAsset'].upper()
                market = f'{base}{quote}'
                if market not in self.ours_markets:
                    continue
                _mapping.update({_symbol: market})
            return _mapping

        def _set_mapping():
            self.symbol_to_market = {**c_mapping, **u_mapping}

        c_markets, u_markets = self._req_markets()
        c_mapping, u_mapping = _get_mapping(c_markets), _get_mapping(u_markets)
        markets = {}
        for symbol in chain(c_mapping, u_mapping):
            markets.update({
                symbol: c_markets.get(symbol) or u_markets.get(symbol)
            })

        _set_mapping()
        return {
            'markets': markets,
            'symbols': (list(c_mapping.keys()), list(u_mapping.keys())),
        }

    def _req_markets(self) -> Tuple[Dict, Dict]:
        raise NotImplementedError

    def _merge_last_prices(self) -> dict:
        raise NotImplementedError

    def _req_depth_data(self, c_symbols, u_symbols) -> dict:
        raise NotImplementedError

    def _get_asset_rate(self, asset) -> Type[Decimal]:
        if asset not in self.asset_rates:
            asset_rate = PriceManager.asset_to_usd(asset) if asset != "USD" else Decimal('1')
            self.asset_rates.update({asset: asset_rate})
        return self.asset_rates[asset]


class BinanceDepthHandler(BaseDepthHandler):
    CLIENT = BinanceAPIClient
    tag = Exchange.Binance.value
    cache_cls = BinanceCache

    def _req_markets(self) -> Tuple[Dict, Dict]:
        mapping = self._cache_data
        c_markets = mapping.get('c_markets', {})
        u_markets = mapping.get('u_markets', {})
        return c_markets, u_markets

    def _merge_last_prices(self):
        c_prices = self.client.fetch_c_last_prices()
        u_prices = self.client.fetch_u_last_prices()
        return {**c_prices, **u_prices}

    @cached_property
    def _cache_data(self):
        return self.cache_cls.read_aside()

    def _req_depth_data(self, c_symbols, u_symbols):
        res = dict()
        for symbol in c_symbols:
            try:
                ret = self.client.fetch_c_depth(symbol)
                res.update(ret)
            except Exception as e:
                current_app.logger.error(f'{self.tag} 合约市场{symbol} 深度获取失败，msg：{e}')
            time.sleep(1.2)

        for symbol in u_symbols:
            try:
                ret = self.client.fetch_u_depth(symbol)
                res.update(ret)
            except Exception as e:
                current_app.logger.error(f'{self.tag} 合约市场{symbol} 深度获取失败，msg：{e}')
            time.sleep(1.2)

        return res


class OKXDepthHandler(BaseDepthHandler):
    CLIENT = OKXAPIClient
    tag = Exchange.OKX.value

    def _req_markets(self) -> Tuple[Dict, Dict]:
        markets = self.client.fetch_markets()
        c_markets, u_markets = {}, {}
        for symbol, info in markets.items():
            if info['type'] == 'inverse':
                c_markets[symbol] = info
            else:
                u_markets[symbol] = info
        return c_markets, u_markets

    def _merge_last_prices(self):
        prices = self.client.fetch_last_prices()
        return prices

    def _req_depth_data(self, c_symbols, u_symbols):
        symbols = set(c_symbols) | set(u_symbols)
        ret = dict()
        depth_list = []
        for chunk_symbols in batch_iter(symbols, 100):
            chunk_depth_list = g_map(self.client.fetch_s_depths, chunk_symbols, fail_safe={}, size=5)
            time.sleep(1.2)
            depth_list.extend(chunk_depth_list)
        for depth in depth_list:
            ret.update(depth)

        def _to_ours(asks_bids):
            r = []
            for x in asks_bids:
                r.append([x[0], x[1]])
            return r
        depths = {}
        for symbol, v in ret.items():
            bids, asks = v.get('bids', []), v.get('asks', [])
            b, a = _to_ours(bids), _to_ours(asks)
            depths[symbol] = {
                'bids': b,
                'asks': a,
            }
        return depths


class HuobiDepthHandler(BaseDepthHandler):
    CLIENT = HuobiAPIClient
    tag = Exchange.HTX.value

    def _req_markets(self) -> Tuple[Dict, Dict]:
        u_markets = self.client.fetch_u_markets()
        c_markets = self.client.fetch_c_markets()
        return c_markets, u_markets

    def _merge_last_prices(self):
        u_prices = self.client.fetch_u_last_prices()
        c_prices = self.client.fetch_c_last_prices()
        return {**u_prices, **c_prices}

    def _req_depth_data(self, c_symbols, u_symbols):
        depth_list = []
        for chunk_symbols in batch_iter(c_symbols, 100):
            chunk_depth_list = g_map(self.client.fetch_c_depth, chunk_symbols, fail_safe={}, size=5)
            time.sleep(1.2)
            depth_list.extend(chunk_depth_list)

        for chunk_symbols in batch_iter(u_symbols, 100):
            chunk_depth_list = g_map(self.client.fetch_u_depth, chunk_symbols, fail_safe={}, size=5)
            time.sleep(1.2)
            depth_list.extend(chunk_depth_list)

        depths = {}
        for d in depth_list:
            depths.update(d)
        return depths

    def _get_step(self, depth):
        step = self.client.STEP_MAPPING.get(depth, 'step0')
        return step


class BybitDepthHandler(BaseDepthHandler):
    CLIENT = BybitAPIClient
    tag = Exchange.BYBIT.value

    def _req_markets(self) -> Tuple[Dict, Dict]:
        markets = self.client.fetch_markets()
        c_markets, u_markets = {}, {}
        for symbol, info in markets.items():
            if info['type'] == 'inverse':
                c_markets[symbol] = info
            else:
                u_markets[symbol] = info
        return c_markets, u_markets

    def _merge_last_prices(self):
        prices = self.client.fetch_last_prices()
        return prices

    def _req_depth_data(self, c_symbols, u_symbols):
        depth_list = []
        for chunk_symbols in batch_iter(c_symbols, 100):
            chunk_depth_list = g_map(self.client.fetch_c_depth, chunk_symbols, fail_safe={}, size=5)
            time.sleep(1.2)
            depth_list.extend(chunk_depth_list)

        for chunk_symbols in batch_iter(u_symbols, 100):
            chunk_depth_list = g_map(self.client.fetch_u_depth, chunk_symbols, fail_safe={}, size=5)
            time.sleep(1.2)
            depth_list.extend(chunk_depth_list)

        depths = {}
        for d in depth_list:
            depths.update(d)
        return depths


class KucoinDepthHandler(BaseDepthHandler):
    CLIENT = KucoinAPIClient
    tag = Exchange.KUCOIN.value

    def _req_markets(self) -> Tuple[Dict, Dict]:
        markets = self.client.fetch_markets()
        c_markets, u_markets = {}, {}
        for symbol, info in markets.items():
            if info['type'] == 'inverse':
                c_markets[symbol] = info
            else:
                u_markets[symbol] = info
        return c_markets, u_markets

    def _merge_last_prices(self):
        prices = self.client.fetch_last_prices()
        return prices

    def _req_depth_data(self, c_symbols, u_symbols):
        depth_list = []
        for chunk_symbols in batch_iter(c_symbols, 100):
            chunk_depth_list = g_map(self.client.fetch_depth, chunk_symbols, fail_safe={}, size=5)
            time.sleep(1.2)
            depth_list.extend(chunk_depth_list)

        for chunk_symbols in batch_iter(u_symbols, 100):
            chunk_depth_list = g_map(self.client.fetch_depth, chunk_symbols, fail_safe={}, size=5)
            time.sleep(1.2)
            depth_list.extend(chunk_depth_list)

        depths = {}
        for d in depth_list:
            depths.update(d)
        return depths


class GateDepthHandler(BaseDepthHandler):
    CLIENT = GateAPIClient
    tag = Exchange.GATE.value

    def _req_markets(self) -> Tuple[Dict, Dict]:
        u_markets = self.client.fetch_u_markets()
        c_markets = self.client.fetch_c_markets()
        return c_markets, u_markets

    def _merge_last_prices(self):
        u_prices = self.client.fetch_u_last_prices()
        c_prices = self.client.fetch_c_last_prices()
        return {**u_prices, **c_prices}

    def _req_depth_data(self, c_symbols, u_symbols):
        depth_list = []
        for chunk_symbols in batch_iter(c_symbols, 100):

            chunk_depth_list = g_map(self.client.fetch_c_depth, chunk_symbols, fail_safe={}, size=5)
            time.sleep(1.2)
            depth_list.extend(chunk_depth_list)

        for chunk_symbols in batch_iter(u_symbols, 100):

            chunk_depth_list = g_map(self.client.fetch_u_depth, chunk_symbols, fail_safe={}, size=10)
            time.sleep(1.2)
            depth_list.extend(chunk_depth_list)

        def _to_ours(asks_bids):
            r = []
            for x in asks_bids:
                r.append([x['p'], x['s']])
            return r

        depths = {}
        for d in depth_list:
            for k, v in d.items():
                bids, asks = v.get('bids', []), v.get('asks', [])
                b, a = _to_ours(bids), _to_ours(asks)
                depths.update({
                    k: {
                        'bids': b,
                        'asks': a,
                    }
                })
        return depths

    def _get_step(self, depth):
        step = self.client.STEP_MAPPING.get(depth, '0')
        return step


class BitgetDepthHandler(BaseDepthHandler):
    CLIENT = BitgetAPIClient
    tag = Exchange.BITGET.value

    def _req_markets(self) -> Tuple[Dict, Dict]:
        u_markets = self.client.fetch_u_markets()
        c_markets = self.client.fetch_c_markets()
        return c_markets, u_markets

    def _merge_last_prices(self):
        c_prices = self.client.fetch_c_last_prices()
        u_prices = self.client.fetch_u_last_prices()
        return {**c_prices, **u_prices}

    def _req_depth_data(self, c_symbols, u_symbols):
        depth_list = []
        for chunk_symbols in batch_iter(c_symbols, 100):
            chunk_depth_list = g_map(self.client.fetch_depth, chunk_symbols, fail_safe={}, size=5)
            time.sleep(1.2)
            depth_list.extend(chunk_depth_list)

        for chunk_symbols in batch_iter(u_symbols, 100):
            chunk_depth_list = g_map(self.client.fetch_depth, chunk_symbols, fail_safe={}, size=5)
            time.sleep(1.2)
            depth_list.extend(chunk_depth_list)

        depths = {}
        for d in depth_list:
            depths.update(d)
        return depths


class BingxDepthHandler(BaseDepthHandler):
    CLIENT = BingxAPIClient
    tag = Exchange.BINGX.value

    def _req_markets(self) -> Tuple[Dict, Dict]:
        u_markets = self.client.fetch_u_markets()
        c_markets = self.client.fetch_c_markets()
        return c_markets, u_markets

    def _merge_last_prices(self):
        c_prices = self.client.fetch_c_last_prices()
        u_prices = self.client.fetch_u_last_prices()
        return {**c_prices, **u_prices}

    def _req_depth_data(self, c_symbols, u_symbols):
        depth_list = []
        for chunk_symbols in batch_iter(c_symbols, 100):
            chunk_depth_list = g_map(self.client.fetch_c_depth, chunk_symbols, fail_safe={}, size=5)
            time.sleep(1.2)
            depth_list.extend(chunk_depth_list)

        for chunk_symbols in batch_iter(u_symbols, 100):
            chunk_depth_list = g_map(self.client.fetch_u_depth, chunk_symbols, fail_safe={}, size=5)
            time.sleep(1.2)
            depth_list.extend(chunk_depth_list)

        depths = {}
        for d in depth_list:
            depths.update(d)
        return depths


PERPETUAL_HANDLERS = {
    BinanceDepthHandler.tag: BinanceDepthHandler,
    OKXDepthHandler.tag: OKXDepthHandler,
    BingxDepthHandler.tag: BingxDepthHandler,
    BybitDepthHandler.tag: BybitDepthHandler,
    KucoinDepthHandler.tag: KucoinDepthHandler,    # 保留，合约深度报表需要
    GateDepthHandler.tag: GateDepthHandler,
    BitgetDepthHandler.tag: BitgetDepthHandler,
    HuobiDepthHandler.tag: HuobiDepthHandler,   # 保留，合约深度报表需要
}

SPOT_HANDLERS = {
    BinanceSpotDepthHandler.tag: BinanceSpotDepthHandler,
    OKXSpotDepthHandler.tag: OKXSpotDepthHandler,
    GateSpotDepthHandler.tag: GateSpotDepthHandler,
    HTXSpotDepthHandler.tag: HTXSpotDepthHandler,
    MEXCSpotDepthHandler.tag: MEXCSpotDepthHandler,
    BybitSpotDepthHandler.tag: BybitSpotDepthHandler,
    KucoinSpotDepthHandler.tag: KucoinSpotDepthHandler,
    BitgetSpotDepthHandler.tag: BitgetSpotDepthHandler,
    LbankSpotDepthHandler.tag: LbankSpotDepthHandler,
    BingxSpotDepthHandler.tag: BingxSpotDepthHandler,
    XTComSpotDepthHandler.tag: XTComSpotDepthHandler,
}


class SpecialAccountHandler:

    def get_user_balance(self, *args, **kwargs) -> Decimal:
        raise NotImplementedError
    
    def get_user_fee(self, *args, **kwargs) -> Decimal:
        raise NotImplementedError

class CoinExSpecialAccountHandler(SpecialAccountHandler):
    
    def __init__(self, user_id: int, **kwargs):
        self.user_id = user_id
        self.usdt_rate = PriceManager.asset_to_usd('USDT')
        
    def get_user_balance(self) -> Decimal:
        from app.api.admin.users import UserSpotAssetPreviewResource as cls
        user = User.query.get(self.user_id)
        account_balance_sum = cls.get_account_balance_sum(self.user_id, True)
        all_bl = account_balance_sum['all']
        nor_sub_accounts_bl = copy_trading_sub_accounts_bl = Decimal()

        for sub in user.sub_accounts:
            sub_bl = cls.get_account_balance_sum(sub.user_id)
            if sub.type in [SubAccount.Type.COPY_TRADER, SubAccount.Type.COPY_FOLLOWER]:
                copy_trading_sub_accounts_bl += sub_bl['all']
            else:
                nor_sub_accounts_bl += sub_bl['all']
        balance_usd = all_bl + nor_sub_accounts_bl + copy_trading_sub_accounts_bl
        return safe_div(balance_usd, self.usdt_rate)
    
    def get_user_fee(self) -> Decimal:
        report_date = yesterday()
        fee_sum = UserTradeFeeSummary.query.filter(
            UserTradeFeeSummary.user_id == self.user_id,
            UserTradeFeeSummary.report_date == report_date,
        ).with_entities(
            func.sum(UserTradeFeeSummary.trade_fee_amount)
        ).scalar() or Decimal()
        return safe_div(fee_sum, self.usdt_rate)
    
    
class BinanceSpecialAccountHandler(SpecialAccountHandler):
    
    def __init__(self, api_key: str, secret: str, **kwargs):
        self.client = BinanceAPIClient(api_key, secret)
        self.price_rates = PriceManager.assets_to_usd()
        self.usdt_rate = self.price_rates.get('USDT', Decimal())
        
    def get_user_fee(self) -> Decimal:
        spot_fee = self.get_spot_fee()
        inverse_fee = self.get_inverse_perpetual_fee()
        direct_fee = self.get_direct_perpetual_fee()
        full_margin_fee = self.get_full_margin_fee()
        isolate_margin_fee = self.get_isolate_margin_fee()
        return spot_fee + inverse_fee + direct_fee + full_margin_fee + isolate_margin_fee

    def get_spot_fee(self) -> Decimal:
        symbols = self.client.fetch_spot_markets()
        fee = Decimal()
        end_time = int(today_timestamp_utc() * 1000)
        start_time = end_time - 86400 * 1000
        for symbol in symbols:
            try:
                fee_map = self.client.get_spot_fee(symbol, start_time, end_time)
                for asset, amount in fee_map.items():
                    asset_rate = self.price_rates.get(asset, Decimal())
                    usd = amount * asset_rate
                    fee += safe_div(usd, self.usdt_rate)
            except Exception as e:
                ret = safe_json_loads(e.data)
                if not ret:
                    current_app.logger.warning(f'Failed to fetch spot fee for {symbol}: {e}')
                    continue
                if ret.get('code') == -2015:
                    current_app.logger.warning(f'Permission denied for spot fee, breaking: {e}')
                    break
                else:
                    current_app.logger.warning(f'Failed to fetch spot fee for {symbol}: {e}')
            time.sleep(0.21)
        return fee
    
    def get_inverse_perpetual_fee(self) -> Decimal:
        symbols = self.client.fetch_c_markets()
        fee = Decimal()
        end_time = int(today_timestamp_utc() * 1000)
        start_time = end_time - 86400 * 1000
        for symbol in symbols:
            try:
                fee_map = self.client.get_inverse_perpetual_fee(symbol, start_time, end_time)
                for asset, amount in fee_map.items():
                    asset_rate = self.price_rates.get(asset, Decimal())
                    usd = amount * asset_rate
                    fee += safe_div(usd, self.usdt_rate)
            except Exception as e:
                ret = safe_json_loads(e.data)
                if not ret:
                    current_app.logger.warning(f'Failed to fetch inverse perpetual fee for {symbol}: {e}')
                    continue
                if ret.get('code') == -2015:
                    current_app.logger.warning(f'Permission denied for inverse perpetual fee, breaking: {e}')
                    break
                else:
                    current_app.logger.warning(f'Failed to fetch inverse perpetual fee for {symbol}: {e}')
            time.sleep(0.1)
        return fee
    
    def get_direct_perpetual_fee(self) -> Decimal:
        symbols = self.client.fetch_u_markets()
        fee = Decimal()
        end_time = int(today_timestamp_utc() * 1000)
        start_time = end_time - 86400 * 1000
        for symbol in symbols:
            try:
                fee_map = self.client.get_direct_perpetual_fee(symbol, start_time, end_time)
                for asset, amount in fee_map.items():
                    asset_rate = self.price_rates.get(asset, Decimal())
                    usd = amount * asset_rate
                    fee += safe_div(usd, self.usdt_rate)
            except Exception as e:
                ret = safe_json_loads(e.data)
                if not ret:
                    current_app.logger.warning(f'Failed to fetch direct perpetual fee for {symbol}: {e}')
                    continue
                if ret.get('code') == -2015:
                    current_app.logger.warning(f'Permission denied for direct perpetual fee, breaking: {e}')
                    break
                else:
                    current_app.logger.warning(f'Failed to fetch direct perpetual fee for {symbol}: {e}')
            time.sleep(0.1)
        return fee
    
    def get_full_margin_fee(self) -> Decimal:
        symbols = self.client.fetch_full_margin_spot_markets()
        fee = Decimal()
        end_time = int(today_timestamp_utc() * 1000)
        start_time = end_time - 86400 * 1000
        for symbol in symbols:
            try:
                fee_map = self.client.get_full_margin_fee(symbol, start_time, end_time)
                for asset, amount in fee_map.items():
                    asset_rate = self.price_rates.get(asset, Decimal())
                    usd = amount * asset_rate
                    fee += safe_div(usd, self.usdt_rate)
            except Exception as e:
                ret = safe_json_loads(e.data)
                if not ret:
                    current_app.logger.warning(f'Failed to fetch full margin fee for {symbol}: {e}')
                    continue
                if ret.get('code') == -3003:
                    current_app.logger.warning(f'Permission denied for full margin fee, breaking: {e}')
                    break
                else:
                    current_app.logger.warning(f'Failed to fetch full margin fee for {symbol}: {e}')
            time.sleep(0.1)
        return fee
    
    def get_isolate_margin_fee(self) -> Decimal:
        symbols = self.client.fetch_isolated_margin_spot_markets()
        fee = Decimal()
        end_time = int(today_timestamp_utc() * 1000)
        start_time = end_time - 86400 * 1000
        for symbol in symbols:
            try:
                fee_map = self.client.get_isolate_margin_fee(symbol, start_time, end_time)
                for asset, amount in fee_map.items():
                    asset_rate = self.price_rates.get(asset, Decimal())
                    usd = amount * asset_rate
                    fee += safe_div(usd, self.usdt_rate)
            except Exception as e:
                ret = safe_json_loads(e.data)
                if not ret:
                    current_app.logger.warning(f'Failed to fetch isolate margin fee for {symbol}: {e}')
                    continue
                if ret.get('code') == -2015:
                    current_app.logger.warning(f'Permission denied for isolate margin fee, breaking: {e}')
                    break
                else:
                    current_app.logger.warning(f'Failed to fetch isolate margin fee for {symbol}: {e}')
            time.sleep(0.1)
        return fee
    
    def get_user_balance(self) -> Decimal:
        # 现货+合约+全仓杠杆账户+逐仓杠杆账户
        spot_balance = self.get_spot_balance()
        direct_balance = self.get_direct_balance()
        inverse_balance = self.get_inverse_balance()
        full_margin_balance = self.get_full_margin_balance()
        isolate_margin_balance = self.get_isolate_margin_balance()
        return spot_balance + inverse_balance + direct_balance + full_margin_balance + isolate_margin_balance
        
    def get_spot_balance(self) -> Decimal:
        try:
            ret = self.client.fetch_spot_balance()
            res = Decimal()
            for item in ret:
                balance = Decimal(item['free']) + Decimal(item['locked'])
                asset_rate = self.price_rates.get(item['asset'], Decimal())
                usd = balance * asset_rate
                balance = safe_div(usd, self.usdt_rate)
                res += balance
            return res
        except Exception as e:
            current_app.logger.warning(f'Failed to fetch spot balance: {e}')
            return Decimal()
    
    def get_inverse_balance(self) -> Decimal:
        try:
            ret = self.client.fetch_inverse_balance()
            res = Decimal()
            for item in ret:
                balance = Decimal(item['balance']) + Decimal(item['crossUnPnl'])
                asset_rate = self.price_rates.get(item['asset'], Decimal())
                usd = balance * asset_rate
                balance = safe_div(usd, self.usdt_rate)
                res += balance
            return res
        except Exception as e:
            current_app.logger.warning(f'Failed to fetch inverse balance: {e}')
            return Decimal()
    
    def get_direct_balance(self) -> Decimal:
        try:
            ret = self.client.fetch_direct_balance()
            res = Decimal()
            for item in ret:
                balance = Decimal(item['balance']) + Decimal(item['crossUnPnl'])
                asset_rate = self.price_rates.get(item['asset'], Decimal())
                usd = balance * asset_rate
                balance = safe_div(usd, self.usdt_rate)
                res += balance
            return res
        except Exception as e:
            current_app.logger.warning(f'Failed to fetch direct balance: {e}')
            return Decimal()
    
    def get_full_margin_balance(self) -> Decimal:
        try:
            ret = self.client.get_full_margin_balance()
            net_val_of_btc = Decimal(ret.get('totalNetAssetOfBtc', 0))
            btc_rate = self.price_rates.get('BTC', Decimal())
            usd = net_val_of_btc * btc_rate
            balance = safe_div(usd, self.usdt_rate)
            return balance
        except Exception as e:
            current_app.logger.warning(f'Failed to fetch full margin balance: {e}')
            return Decimal()
    
    def get_isolate_margin_balance(self) -> Decimal:
        try:
            ret = self.client.get_isolate_margin_balance()
            net_val_of_btc = Decimal(ret.get('totalNetAssetOfBtc', 0))
            btc_rate = self.price_rates.get('BTC', Decimal())
            usd = net_val_of_btc * btc_rate
            balance = safe_div(usd, self.usdt_rate)
            return balance
        except Exception as e:
            current_app.logger.warning(f'Failed to fetch isolate margin balance: {e}')
            return Decimal()
    
    
class OKXSpecialAccountHandler(SpecialAccountHandler):
    def get_user_balance(self, *args, **kwargs) -> Decimal:
        raise NotImplementedError
    
    def get_user_fee(self, *args, **kwargs) -> Decimal:
        raise NotImplementedError
    
class BybitSpecialAccountHandler(SpecialAccountHandler):
    def get_user_balance(self, *args, **kwargs) -> Decimal:
        raise NotImplementedError
    
    def get_user_fee(self, *args, **kwargs) -> Decimal:
        raise NotImplementedError
    
class GateSpecialAccountHandler(SpecialAccountHandler):
    def get_user_balance(self, *args, **kwargs) -> Decimal:
        raise NotImplementedError
    
    def get_user_fee(self, *args, **kwargs) -> Decimal:
        raise NotImplementedError


SPECIAL_ACCOUNT_HANDLERS = {
    Exchange.CoinEx: CoinExSpecialAccountHandler,
    Exchange.Binance: BinanceSpecialAccountHandler,
    Exchange.OKX: OKXSpecialAccountHandler,
    Exchange.BYBIT: BybitSpecialAccountHandler,
    Exchange.GATE: GateSpecialAccountHandler,
}