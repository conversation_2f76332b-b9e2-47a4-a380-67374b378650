from datetime import <PERSON><PERSON><PERSON>
from collections import defaultdict
from decimal import Decimal
from typing import (<PERSON>Tup<PERSON>, <PERSON><PERSON>, List, Dict, Set, Iterable, Hashable,
                    Type, Optional, Any)
from logging import getLogger, Logger
from sqlalchemy import func as sql_func, tuple_ as sql_tuple

from app import config

from .clients.kyt import (
    CoinfirmClient as KYTCoinfirmClient,
    ChainalysisClient as KYTChainalysisClient,
    UsageQuota as KYTUsageQuota
)
from .risk_control.base import RiskControlGroupConfig
from ..caches import UserDailyDepositAmountCache
from ..caches.config import KytSettingCache
from ..caches.kyt import KYTAbnormalCache, KYTAbnormalNoticedCache
from ..common import CeleryQueues
from ..models import db, Deposit, DepositAudit
from ..models.kyt import KytSetting as KytSettingModel, DepositSenderRiskAssessment, <PERSON>yt<PERSON>hain<PERSON><PERSON>sor, \
    DepositSenderRiskAssessment<PERSON><PERSON>elist, BlacklistedDepositSender, KY<PERSON><PERSON><PERSON>elistUser, DepositRiskAssessmentRequest, \
    KYTAddressPreScreening, UserWithdrawalAddressPreScreening
from ..models.wallet import EDDAudit

from ..utils import (NamedObject, BaseHTTPClient, BaseConfig, ConfigMode,
                     exhaust, now, batch_iter,
                     celery_task, ConfigField, amount_to_str, current_timestamp, quantize_amount)
from .lock import lock_call
from .func_cache import cached
from .prices import PriceManager

_logger = getLogger(__name__)

_EMPTY = NamedObject('Empty')

AssessmentStatus = DepositSenderRiskAssessment.Status
AssessmentAuditStatus = DepositSenderRiskAssessment.AuditStatus
AssessObjectType = DepositSenderRiskAssessment.AssessObjectType


class AssessmentServiceNotAvailable(RuntimeError):
    pass


class AssessmentNotPossible(RuntimeError):
    pass


class KYTErrOrNotSupported(RuntimeError):
    pass


class _KytSettings(BaseConfig):
    _field_class = F = ConfigField

    kyt_enabled: bool = F(bool, '开启 KYT', default=False)
    coinfirm_dangerous_threshold: int \
        = F(int, 'Coinfirm: 危险阈值', default=90, validator=lambda v: 0 < v <= 99)
    coinfirm_dangerous_indicator_check_enabled: bool \
        = F(bool, 'Coinfirm: 是否检查 indicators', default=False)
    coinfirm_excluded_chains: Set[str] \
        = F(set, 'Coinfirm: 排除链', default=())
    coinfirm_enabled: bool = F(bool, '是否开启coinfirm', default=True)
    chainalysis_enabled: bool = F(bool, '是否开启chainalysis', default=True)
    chainalysis_pre_screen_category_ids: Set[int] = F(set, 'Chainalysis: 风险类型', default=())

    def list_chainalysis_pre_screen_category_ids(self) -> set[int]:
        return {int(x) for x in self.chainalysis_pre_screen_category_ids}

    @property
    def identifier(self) -> Hashable:
        return 0

    def as_mode(self, mode: ConfigMode) -> '_KytSettings':
        if mode is self._mode and mode is not ConfigMode.SNAPSHOT:
            return self
        return type(self)(mode)

    def _get_all(self) -> dict:
        return KytSettingCache().get_values()

    def _get_one(self, name: str) -> Optional[str]:
        return KytSettingCache().get_value(name)

    def _set_one(self, name: str, value: str):
        model = KytSettingModel
        row = model.query \
            .filter(model.key == name) \
            .first()
        if not row:
            row = model(key=name)
            db.session.add(row)
        row.value = value
        row.status = model.Status.VALID
        db.session.commit()
        KytSettingCache().set_value(name, value)

    def _del_one(self, name: str):
        model = KytSettingModel
        row = model.query \
            .filter(model.key == name) \
            .first()
        if not row:
            return
        row.status = model.Status.DELETED
        db.session.commit()
        KytSettingCache().del_value(name)


KytSettings = _KytSettings(ConfigMode.REAL_TIME)


class WithdrawalPreScreeningManager:

    _client = KYTChainalysisClient(timeout=30)  # timeout=30 目前主要是用于前端接口

    @classmethod
    def check_user_risk_agreement(cls, id_: int, user_id: int) -> bool:
        model = UserWithdrawalAddressPreScreening
        row = model.query.get(id_)
        if not row:
            return False
        if row.user_id != user_id:
            return False
        if row.category_id not in cls.list_monitored_category_ids():
            return True
        return row.risk_agreement == model.RiskAgreement.AGREED

    @classmethod
    def update_user_pre_screening_risk_agreement(
            cls,
            id_: int,
            user_id: int,
            agreed: bool
    ):
        model = UserWithdrawalAddressPreScreening
        row = model.query.get(id_)
        if not row:
            return
        if row.user_id != user_id:
            return
        if row.risk_agreement != model.RiskAgreement.NOT_OPERATED:
            return
        row.risk_agreement = model.RiskAgreement.AGREED if agreed else model.RiskAgreement.DISAGREED
        db.session.commit()

    @classmethod
    def check_address_risky(
            cls,
            user_id: int,
            asset: str,
            chain: str,
            address: str,
            amount: Decimal,
            memo: str = None
    ) -> (bool, int | None):
        return False, None  # 当前频次太高导致成本问题，后续会调整
        risky = False
        if not cls._is_chain_supported(chain):
            return risky, None
        agreed, r_id = cls._get_user_last_screening_result(user_id, address)
        if agreed:  # 已同意不再筛查
            return risky, r_id
        unsafe, r_id = cls.pre_screening(user_id, asset, chain, address, amount, memo)
        risky = unsafe
        return risky, r_id

    @classmethod
    def _is_chain_supported(cls, chain: str) -> bool:
        return cls._client.is_chain_supported(chain)

    @classmethod
    def _get_user_last_screening_result(cls, user_id: int, address: str) -> (bool, int | None):
        model = UserWithdrawalAddressPreScreening
        row = model.query.with_entities(
            model.id,
            model.category_id,
            model.risk_agreement,
        ).filter(
            model.user_id == user_id,
            model.address == sql_func.binary(address),
        ).order_by(
            model.id.desc()
        ).first()
        if not row:
            return False, None
        agreed = row.risk_agreement == model.RiskAgreement.AGREED
        return agreed, row.id

    @classmethod
    def pre_screening(
            cls,
            user_id: int,
            asset: str,
            chain: str,
            address: str,
            amount: Decimal,
            memo: str = None
    ) -> (bool, int):
        category_id = cls._get_local_screening_result(address)
        if category_id is not None:
            unsafe = category_id in cls.list_monitored_category_ids()
        else:
            category_id = cls._register_withdrawal_address(user_id, asset, chain, address)
            if category_id is not None:
                unsafe = category_id in cls.list_monitored_category_ids()
                if unsafe:
                    _ = cls._create_local_screening_result(address, category_id)
            else:
                unsafe = False
        user_result = cls._create_user_pre_screening(
            unsafe,
            user_id, category_id, asset, chain, address, amount, memo
        )
        db.session.commit()
        return unsafe, user_result.id

    @classmethod
    def _get_local_screening_result(cls, address: str) -> int | None:
        model = KYTAddressPreScreening
        row = model.query.with_entities(
            model.category_id
        ).filter(
            model.address == sql_func.binary(address)
        ).first()
        return row.category_id if row else None

    @classmethod
    def _register_withdrawal_address(cls, user_id: int, asset: str, chain: str, address: str) -> int | None:
        try:
            category_id = cls._client.register_withdrawal_addresses(user_id, asset, chain, address)
        except Exception as e:  # noqa
            _logger.error(f'failed to register user {user_id} withdrawal address {address} on {chain}: {e!r}')
            category_id = cls._client.ERR_CATEGORY_ID
        return category_id

    @classmethod
    def _create_local_screening_result(cls, address: str, category_id: int):
        model = KYTAddressPreScreening
        row = model.get_or_create(address=address, category_id=category_id)
        db.session.add(row)
        return row

    @classmethod
    def _create_user_pre_screening(
            cls,
            unsafe: bool,
            user_id: int,
            category_id: int,
            asset: str,
            chain: str,
            address: str,
            amount: Decimal,
            memo: str = None
    ):
        if category_id not in cls.list_all_categories():
            category_id = None
        model = UserWithdrawalAddressPreScreening
        init_status = model.RiskAgreement.NONE
        if unsafe:
            init_status = model.RiskAgreement.NOT_OPERATED
        amount_usd = amount * PriceManager.asset_to_usd(asset)
        row = model(
            user_id=user_id,
            category_id=category_id,
            asset=asset,
            chain=chain,
            address=address,
            amount=amount,
            amount_usd=quantize_amount(amount_usd, 2),
            memo=memo,
            risk_agreement=init_status,
        )
        db.session.add(row)
        return row

    @classmethod
    def list_monitored_category_ids(cls) -> Set[int]:
        return KytSettings.list_chainalysis_pre_screen_category_ids()

    @classmethod
    def list_all_categories(cls) -> dict:
        return dict(cls._client.CATEGORY_IDs)

    @classmethod
    def list_configurable_categories(cls) -> dict:
        categories = dict(cls._client.CATEGORY_IDs)
        categories.pop(cls._client.ERR_CATEGORY_ID, None)
        return categories

    @classmethod
    def check_db_records_alert(cls):
        from app.business import send_alert_notice

        user_ps_count = UserWithdrawalAddressPreScreening.query.count()
        local_ps_count = KYTAddressPreScreening.query.count()
        send_alert_notice(
            f'''
当前本地提现高风险筛查记录数：{local_ps_count}\n
当前用户提现高风险筛查记录数：{user_ps_count}\n
    ''',
            config["ADMIN_CONTACTS"]["kyt_notice"],
        )


class DepositRiskAssessmentManager:
    Settings = KytSettings
    STATUS_TO_TTL = {
        DepositSenderRiskAssessment.Status.APPROVED: 3600,
        DepositSenderRiskAssessment.Status.DECLINED: 86400 * 15
    }
    MAX_ASSESSMENT_FAILURE_COUNT = 2
    NEW_DEPOSIT_STATUSES = {
        Deposit.Status.TOO_SMALL,
        Deposit.Status.PROCESSING,
    }

    @classmethod
    def is_processing_risk_assessment(cls, deposit_id: int) -> bool:
        model = DepositRiskAssessmentRequest
        req = model.query.filter(
            model.deposit_id == deposit_id
        ).first()
        if req is None:  # 老数据没有
            return False
        else:
            if req.status in [
                model.Status.CREATED,
            ]:
                return True
            model = DepositSenderRiskAssessment
            ass_rows = model.query.filter(
                model.deposit_id == deposit_id
            ).all()
            if not ass_rows:  # 老数据没有
                return False
            # coinfirm 可能存在多条评估记录，多条都需评估完成
            for ass in ass_rows:
                if ass.status == model.Status.PENDING:
                    return True
            return False

    @classmethod
    def new_risk_assessment_request(cls, deposit: Deposit, senders: list[str] = None):
        if not cls.is_kyt_enabled():
            cls._mock_kyt_disabled_assessment(deposit, senders)
            return
        if not cls._is_new_deposit(deposit):
            return
        model = DepositRiskAssessmentRequest
        row = model.get_or_create(deposit_id=deposit.id)
        if row.id:
            return
        row.senders = senders or []
        db.session_add_and_commit(row)

    @classmethod
    def is_kyt_enabled(cls):
        return cls.Settings.kyt_enabled

    @classmethod
    def _mock_kyt_disabled_assessment(cls, deposit: Deposit, senders: list[str] = None):
        model = DepositRiskAssessmentRequest
        row = model.get_or_create(deposit_id=deposit.id)
        if row.id:
            return
        row.senders = senders or []
        row.status = model.Status.COMPLETED
        db.session.add(row)
        db.session.flush()
        ass = DepositSenderRiskAssessment(
            request_id=row.id,
            deposit_id=deposit.id,
            reason=DepositSenderRiskAssessment.Reason.KYT_DISABLED,
            chain=deposit.chain,
            assessed_object='',
            status=DepositSenderRiskAssessment.Status.APPROVED,
            applied_at=now(),
            assessed_at=now(),
            attachment={
                'user_id': deposit.user_id,
                'asset': deposit.asset,
                'recipient': deposit.address,
            }
        )
        db.session.add(ass)
        db.session.commit()

    @classmethod
    def process_assessment_requests(cls, limit: int = 500):
        model = DepositRiskAssessmentRequest
        rows = model.query.with_entities(
            model.id
        ).filter(
            model.status == model.Status.CREATED,
        ).order_by(model.id.desc()).limit(limit).all()
        all_rids = [row.id for row in rows]
        exhaust(map(
            process_one_application_req.delay,
            all_rids
        ))

    @classmethod
    def process_assessment_requests_quickly(cls, limit: int = 500):
        from app.business import yield_query_records_by_time_range

        model = DepositRiskAssessmentRequest
        end_time = now()
        start_time = end_time - timedelta(hours=1)
        for row in yield_query_records_by_time_range(
                table=model,
                start_time=start_time,
                end_time=end_time,
                select_fields=(
                        model.id,
                        model.status,
                ),
                filters={'status': model.Status.CREATED, },
                limit=limit,
                filter_in_query=False,
        ):
            process_one_application_req.delay(row.id)

    @classmethod
    def assess_object_by_system_rules(cls, rid: int):
        row = DepositRiskAssessmentRequest.query.get(rid)
        if not row:
            raise ValueError(f'DepositRiskAssessmentRequest invalid row id {rid}')
        ts = now().timestamp()
        _logger.warning(f'DepositRiskAssessmentRequest<{rid}> start at: {ts}')
        deposit_id = row.deposit_id
        deposit = Deposit.query.get(deposit_id)
        user_id = deposit.user_id
        chain, senders = deposit.chain, row.list_senders()
        senders_in_blacklist, b_details = cls._blacklist_validation(chain, senders)
        ass = None
        if senders_in_blacklist:
            if not cls._is_whitelist_user(user_id):
                in_whitelist, w_details = cls._whitelist_senders_validation(chain, senders)
                blacklisted = [k for k in b_details if b_details[k]]
                # 这里需要按黑名单地址, 去匹配是否白名单地址中
                for b in blacklisted:
                    if not w_details.get(b):
                        ass = DepositSenderRiskAssessment(
                            request_id=row.id,
                            deposit_id=deposit_id,
                            reason=DepositSenderRiskAssessment.Reason.BLACKLISTED,
                            chain=chain,
                            assessed_object='',
                            status=DepositSenderRiskAssessment.Status.DECLINED,
                            applied_at=now(),
                            assessed_at=now(),
                            attachment={
                                'user_id': user_id,
                                'asset': deposit.asset,
                                'recipient': deposit.address,
                            }
                        )
                        db.session.add(ass)
                        cls._new_blacklisted_deposit_audit(deposit)
                        break
        if ass is None:
            ts1 = now().timestamp()
            ass = cls._accumulated_deposit_kyt_rc(deposit, request_id=row.id)
            _logger.warning(f'_accumulated_deposit_kyt_rc <{rid}> cost: {now().timestamp() - ts1}')

        if ass is None:
            ts2 = now().timestamp()
            # 我们自己的 KYT 校验结束时，若没有写入结果，则继续进行第三方 KYT，按评估对象进行评估
            # 此时再按评估方规则，初始化待评估记录
            cls._create_deposit_risk_assessment(deposit, req=row)
            _logger.warning(f'_create_deposit_risk_assessment <{rid}> cost: {now().timestamp() - ts2}')

        row.status = DepositRiskAssessmentRequest.Status.COMPLETED
        db.session.commit()
        _logger.warning(f'assess_object_by_system_rules <{rid}> cost: {now().timestamp() - ts}')

    @classmethod
    def _blacklist_validation(cls, chain: str, senders: list) -> (bool, dict):
        if not senders:
            return False, {}
        model = BlacklistedDepositSender
        rows = model.query.with_entities(
            model.address
        ).filter(
            model.chain == chain,
            model.status == model.Status.VALID,
            model.address.in_([sql_func.binary(sender) for sender in senders])
        ).all()
        matched_senders = [row.address for row in rows]
        blacklist_validation = dict()
        result = bool(matched_senders)
        for sender in senders:
            blacklist_validation[sender] = sender in matched_senders
        return result, blacklist_validation

    @classmethod
    def _is_whitelist_user(cls, user_id: int) -> bool:
        _model = KYTWhitelistUser
        _row = _model.query.with_entities(
            _model.user_id
        ).filter(
            _model.user_id == user_id,
            _model.status == _model.Status.VALID,
        ).first()
        return _row is not None

    @classmethod
    def _whitelist_senders_validation(cls, chain: str, senders: list) -> (bool, dict):
        if not senders:
            return False, {}
        _model = DepositSenderRiskAssessmentWhitelist
        rows = _model.query.with_entities(
            _model.address
        ).filter(
            _model.chain == chain,
            _model.status == _model.Status.VALID,
            _model.address.in_([sql_func.binary(sender) for sender in senders])
        ).all()
        matched_senders = [row.address for row in rows]
        r = bool(matched_senders)
        validation = {}
        for sender in senders:
            validation[sender] = sender in matched_senders
        return r, validation

    @classmethod
    def _accumulated_deposit_kyt_rc(cls, deposit: Deposit, request_id: int) -> DepositSenderRiskAssessment | None:

        def _new_ass(accumulated_value=Decimal()):
            _ass = DepositSenderRiskAssessment(
                request_id=request_id,
                deposit_id=deposit.id,
                reason=DepositSenderRiskAssessment.Reason.VALUE_BELOW_THRESHOLD,
                chain=deposit.chain,
                details=dict(value=amount_to_str(amount_usd), accumulated_value=amount_to_str(accumulated_value)),
                assessed_object='',
                status=DepositSenderRiskAssessment.Status.APPROVED,
                applied_at=now(),
                assessed_at=now(),
                attachment={
                    'user_id': deposit.user_id,
                    'asset': deposit.asset,
                    'recipient': deposit.address,
                }
            )
            db.session.add(_ass)
            return _ass

        conf = RiskControlGroupConfig().kyt_thresholds
        amount_usd = deposit.amount * PriceManager.asset_to_usd(deposit.asset)
        if amount_usd > Decimal(conf['kyt_threshold']):
            return None
        cache_usd = UserDailyDepositAmountCache(deposit.user_id).get_deposit_usd()
        total_usd = cache_usd + amount_usd
        if total_usd > Decimal(conf['kyt_accumulated_threshold']):
            return None
        ass = _new_ass(accumulated_value=total_usd)
        return ass

    @classmethod
    def _new_blacklisted_deposit_audit(cls, deposit: Deposit):
        audit_row = DepositAudit.get_or_create(
            deposit_id=deposit.id,
            type=DepositAudit.Type.KYT_BLACKLISTED_RC,
        )
        if audit_row.id:
            return
        audit_row.user_id = deposit.user_id
        db.session.add(audit_row)

    @classmethod
    def _create_deposit_risk_assessment(cls, deposit: Deposit, req: DepositRiskAssessmentRequest):

        def _new_ass(_reason, _status, _assessor=None, _assess_object_type=None):
            _ass = DepositSenderRiskAssessment(
                request_id=req.id,
                deposit_id=deposit.id,
                reason=_reason,
                chain=deposit.chain,
                assessor=_assessor,
                assessed_object='',
                status=_status,
                applied_at=now(),
                attachment={
                    'user_id': deposit.user_id,
                    'asset': deposit.asset,
                    'recipient': deposit.address,
                }
            )
            _ass.assess_object_type = _assess_object_type or DepositSenderRiskAssessment.AssessObjectType.NONE
            db.session.add(_ass)
            return _ass

        chain = deposit.chain
        if (assessor := cls.get_assessor_by_chain(chain)) is None:
            _ = _new_ass(
                _reason=DepositSenderRiskAssessment.Reason.NO_KYT_ASSESSOR,
                _status=DepositSenderRiskAssessment.Status.APPROVED,
            )
            return
        objects = assessor.get_deposit_assess_objects(deposit, req)
        if not objects:
            _ = _new_ass(
                _reason=DepositSenderRiskAssessment.Reason.NO_ASSESS_OBJECT,
                _status=DepositSenderRiskAssessment.Status.APPROVED,
                _assessor=assessor.name,
                _assess_object_type=assessor.assess_object_type,
            )
            return
        model = DepositSenderRiskAssessment
        for assess_object in objects:
            row = model.query.filter(
                model.chain == chain,
                model.deposit_id == deposit.id,
                model.assessed_object == sql_func.binary(assess_object)
            ).first()
            if not row:
                row = model()
                db.session.add(row)
            row.status = model.Status.PENDING
            row.request_id = req.id
            row.deposit_id = deposit.id
            row.chain = deposit.chain
            row.assessor = assessor.name
            row.assessed_object = assess_object
            row.assess_object_type = assessor.assess_object_type
            row.attachment = {
                'user_id': deposit.user_id,
                'asset': deposit.asset,
                'recipient': deposit.address,
            }
            row.applied_at = now()

    @classmethod
    def process_assessment_applications(
            cls,
            *,
            chain: str = '',
            limit: int = 500,
    ):
        model = DepositSenderRiskAssessment
        statuses = model.Status
        query = model.query
        all_rids = [
            row.id for row in query
            .filter((model.chain == chain) if chain else True,
                    model.status == statuses.PENDING)
            .order_by(model.updated_at)
            .with_entities(model.id)
            .limit(limit)
        ]

        exhaust(map(
            process_one_application.delay,
            all_rids
        ))

    @classmethod
    def process_one_assessment_application(
            cls,
            row_id: int,
    ) -> bool:
        model = DepositSenderRiskAssessment
        row: model = model.query.get(row_id)
        if row is None:
            raise ValueError(f'invalid row id {row}')
        if row.status is not model.Status.PENDING:
            _logger.warning(f'row {row.id} is not in pending status')
            return False
        # 旧数据没有 deposit_id 不做处理
        # 可能需要考虑把旧数据处理中的状态，统一刷为终止态
        if not row.deposit_id or not row.request_id:
            row.status = model.Status.APPROVED
            row.reason = model.Reason.MIGRATED_DATA
            db.session.commit()
            return False
        try:
            prep = cls._prepare_application(row)
        except AssessmentNotPossible:
            row.status = row.Status.APPROVED
            row.reason = model.Reason.MIGRATED_DATA
            db.session.commit()
            return False
        except KYTErrOrNotSupported:
            row.status = row.Status.APPROVED
            row.reason = row.Reason.KYT_NO_RESPONSE
            db.session.commit()
            return False
        except ChainalysisDataUpdatedTimeout as e:
            row.status = model.Status.APPROVED
            row.reason = model.Reason.KYT_NO_RESPONSE
            row.details = dict(row.details or {}, __exception__=repr(e))
            db.session.commit()
            return False
        else:
            if not prep:
                return False

        try:
            # ass: Assessment = safe(cls.assess_object)(row.id)
            _ = cls.assess_object(row.id)
        except Exception as e:  # noqa
            cls._set_abnormal_notice_data(row.assessor)
            row.status = model.Status.APPROVED
            row.reason = model.Reason.KYT_EXCEPTION
            row.details = dict(row.details or {}, __exception__=repr(e))
            db.session.commit()
            return False
        return True

    @classmethod
    def _prepare_application(cls, row: DepositSenderRiskAssessment) -> bool:
        if row.assessor:
            assessor = cls.get_assessor(row.assessor)
        else:
            assessor = cls.get_assessor_by_chain(row.chain)
        if assessor is None:
            _logger.error(f'{row.chain} not found assessor')
            return False
        if assessor.assess_object_type != row.assess_object_type:
            _logger.error(f'row {row.id} doest not match {assessor.name}')
            return False
        return assessor.prepare_assess_object(row)

    @classmethod
    @lock_call(with_args=True)
    def assess_object(cls, rid: int) -> DepositSenderRiskAssessment:
        model = DepositSenderRiskAssessment
        ass = model.query.get(rid)
        chain, object_ = ass.chain, ass.assessed_object
        chain = cls.normalise_chain(chain)

        if (assessor := cls.get_assessor_by_chain(chain)) is None:
            _logger.error(f'assessment on {chain} is not supported')
            ass.status = model.Status.APPROVED
            ass.reason = model.Reason.NO_KYT_ASSESSOR
            db.session.commit()
            return ass

        _logger.info(f'assessing {object_} on {chain} by {assessor.name}...')

        # 钱包几乎每次都会调用 normalise_assess_object
        # web：这里在写入前已经 normalise_assess_object，此时直接使用
        # object_ = assessor.normalise_assess_object(chain, object_)
        t0 = now().timestamp()
        # noinspection PyBroadException
        try:
            is_safe, details = assessor.assess_object(ass)
        except AssessmentServiceNotAvailable as e:
            # 这里异常时，钱包的做法是由下次任务去重试一次，
            # 但重试达到次数后，似乎未更改状态，这样任务会一直找到这条记录执行
            # web 将异常视为安全：即 APPROVED
            _logger.error(f'failed to assess {object_} on {chain}: {e!r}')
            cls._set_abnormal_notice_data(ass.assessor)
            ass.status = model.Status.APPROVED
            ass.reason = model.Reason.KYT_EXCEPTION
            ass.details = dict(ass.details or {}, __exception__=repr(e))
            db.session.commit()
            return ass
        except Exception as e:
            # 同上
            _logger.exception(f'failed to assess {object_} on {chain}: {e!r}')
            cls._set_abnormal_notice_data(ass.assessor)
            ass.status = model.Status.APPROVED
            ass.reason = model.Reason.KYT_EXCEPTION
            ass.details = dict(ass.details or {}, __exception__=repr(e))
            db.session.commit()
            return ass

        _logger.info(
            f'assessed {object_} on {chain} in {now().timestamp() - t0:.1f}s: '
            f'{"not " if not is_safe else ""}safe, {details=}')
        if not is_safe:
            deposit = Deposit.query.get(ass.deposit_id)
            # 这里还需要过一次我们自己的白名单逻辑
            # 此时与单笔评估记录维度无关，与该笔充值有关
            if cls._is_whitelist_user(deposit.user_id):
                status = model.Status.APPROVED
                reason = model.Reason.KYT_RISK_HIGH_BW
            else:
                req = DepositRiskAssessmentRequest.query.get(ass.request_id)
                b, _ = cls._whitelist_senders_validation(chain, req.list_senders())
                if b:
                    status = model.Status.APPROVED
                    reason = model.Reason.KYT_RISK_HIGH_BW
                else:
                    status = model.Status.DECLINED
                    reason = model.Reason.KYT_RISK_HIGH
        else:
            status = model.Status.APPROVED
            reason = model.Reason.KYT_RISK_LOW
        ass.status = status
        ass.reason = reason
        ass.assessed_at = now_ = now()
        ass.expired_at = now_ + timedelta(seconds=cls.STATUS_TO_TTL.get(ass.status, 3600))
        ass.assessment_count += 1
        ori_details = ass.details or {}
        ass.details = {**ori_details, **details}
        if ass.status == model.Status.DECLINED:
            cls._new_kyt_deposit_audit(ass.deposit_id, ass)
        db.session.commit()
        return ass

    @classmethod
    def _set_abnormal_notice_data(cls, assessor: str):
        if assessor := cls.get_assessor(assessor):
            KYTAbnormalCache(assessor.name).update()

    @classmethod
    def _new_kyt_deposit_audit(cls, deposit_id: int, ass: DepositSenderRiskAssessment):
        """生成 KYT 高风险待审核记录"""
        deposit = Deposit.query.get(deposit_id)
        assert cls._is_new_deposit(deposit)
        audit_row = DepositAudit.get_or_create(
            deposit_id=deposit.id,
            type=DepositAudit.Type.KYT_RC,
        )
        if audit_row.id:
            return
        audit_row.status = DepositAudit.Status.INFO_REQUIRED
        audit_row.user_id = deposit.user_id

        edd = EDDAudit.get_or_create(
            deposit_id=deposit.id,
        )
        edd.wallet_deposit_id = deposit.wallet_deposit_id
        edd.source = EDDAudit.Source.DEPOSIT_AUDIT
        edd.user_id = deposit.user_id
        edd.sender_risk_id = ass.id
        edd.assess_object = ass.assessed_object  # coinfirm 是发送方地址；chainalysis 是交易ID+接收地址
        edd.assessor = ass.assessor
        objects = ass.assessed_object.split(':')
        edd.address_from = objects[-1] if len(objects) > 1 else ass.assessed_object  # 冗余 for admin
        exp_details = cls.explain_assessment_details(ass.assessor, ass.details)
        owner_name = None
        if exp_details and exp_details.get('owner'):
            owner_name = exp_details['owner'].get('name')
        edd.exchange_from = owner_name or ''
        edd.risk_score = ass.details.get('risk_score') or 0  # chainalysis 没有 risk_score
        risk_level = ass.details.get('risk_level', 'HIGH')
        try:
            rl = EDDAudit.RiskLevel[risk_level]
        except KeyError:  # 这里综合了我们与第三方判断的场景，如：第三方认为安全，但我们认为危险
            rl = EDDAudit.RiskLevel.HIGH
        edd.risk_level = rl
        db.session.add(edd)
        db.session.flush()
        audit_row.edd_id = edd.id
        db.session.add(audit_row)

    @classmethod
    def normalise_chain(cls, chain: str) -> str:
        from ..assets import normalise_chain_name
        return normalise_chain_name(chain)

    @classmethod
    def normalise_address(cls, chain: str, address: str) -> str:
        from app.business import WalletClient
        r = WalletClient().normalise_addresses([(chain, address)])
        return r[chain]

    @classmethod
    def _is_new_deposit(cls, deposit: Deposit) -> bool:
        return deposit.status in cls.NEW_DEPOSIT_STATUSES

    @classmethod
    def filter_senders_in_whitelist(
            cls, senders: Iterable[Tuple[str, str]],
    ) -> Dict[Tuple[str, str], str]:
        # 写入前已经进行了 normalise_address，使用的时候不再格式化
        model = DepositSenderRiskAssessmentWhitelist
        b = sql_func.binary
        return {
            (c, a): r
            for c, a, r in model.query.filter(
                sql_tuple(
                    model.chain, model.address
                ).in_(
                    [sql_tuple(b(c), b(a)) for c, a in senders]
                ),
                model.status == model.Status.VALID
            ).with_entities(
                model.chain,
                model.address,
                model.reason
            )
        }

    @classmethod
    def list_assessors(cls) -> Tuple[Type['KytBaseAssessor'], ...]:
        return KytBaseAssessor.list_assessors()

    @classmethod
    def get_assessor(cls, name: str) -> Optional[Type['KytBaseAssessor']]:
        return KytBaseAssessor.get_assessor(name)

    @classmethod
    @cached(3600 * 3)
    def list_supported_chains(cls) -> Iterable[str]:
        chains = set()
        for assessor in cls.list_assessors():
            chains.update(assessor.list_supported_chains())
        return chains

    @classmethod
    def get_assessor_by_chain(cls, chain: str, *, assessing: bool = True
                              ) -> Optional[Type['KytBaseAssessor']]:
        record: KytChainAssessor = KytChainAssessor.query.filter(
            KytChainAssessor.chain == chain
        ).first()
        if not record or not record.assessor:
            return None
        assessor = KytBaseAssessor.get_assessor(record.assessor)
        return assessor if not assessing or assessor.is_enabled() else None

    @classmethod
    def preview_assessment_details(cls, assessor: str, details: Optional[dict]
                                   ) -> Optional[str]:
        if not details or (assessor := cls.get_assessor(assessor)) is None:
            return None
        return assessor.preview_assessment_details(details)

    @classmethod
    def explain_assessment_details(cls, assessor: str, details: Optional[dict]
                                   ) -> Optional[Dict[str, Any]]:
        if not details or (assessor := cls.get_assessor(assessor)) is None:
            return None
        return assessor.explain_assessment_details(details)

    @classmethod
    def display_for_deposit_list(cls, deposit_ids: list[int]) -> dict:
        model = DepositSenderRiskAssessment
        tmp = defaultdict(list)
        for chunk_dep_ids in batch_iter(deposit_ids, 5000):
            rows = model.query.with_entities(
                model.deposit_id,
                model.status,
            ).filter(
                model.deposit_id.in_(chunk_dep_ids),
            ).all()
            for row in rows:
                tmp[row.deposit_id].append(row.status)
        sort_values = {
            model.Status.CANCELLED: 0,
            model.Status.PENDING: 1,
            model.Status.APPROVED: 2,
            model.Status.DECLINED: 3,
        }
        ret = {}
        for did, statuses in tmp.items():
            sorted_statuses = sorted(statuses, key=lambda x: sort_values[x])
            take_status = sorted_statuses[-1]
            ret[did] = take_status.name
        return ret

    @classmethod
    def check_db_records_alert(cls):
        from app.business import send_alert_notice

        req_count = DepositRiskAssessmentRequest.query.count()
        risk_count = DepositSenderRiskAssessment.query.count()
        send_alert_notice(
            f'''
当前充值风险请求记录数：{req_count}\n
当前充值风险评估记录数：{risk_count}\n
''',
            config["ADMIN_CONTACTS"]["kyt_notice"],
        )


class KytBaseAssessor:
    name: str
    assess_object_type: AssessObjectType

    _classes_: Dict[str, Type['KytBaseAssessor']] = {}
    _chain_to_assessors_: Dict[str, List[str]] = defaultdict(list)

    def __init_subclass__(cls, **kwargs):
        if not (name := getattr(cls, 'name', None)):
            raise AttributeError(f'{cls.__name__}.name is not defined')
        KytBaseAssessor._classes_[name] = cls
        # chain_to_names = KytBaseAssessor._chain_to_assessors_
        # for chain in cls.list_supported_chains():
        #     chain_to_names[chain].append(name)

    @classmethod
    def get_logger(cls) -> Logger:
        return getLogger(f'{cls.__module__}.{cls.__qualname__}')

    @staticmethod
    def list_assessors() -> Tuple[Type['KytBaseAssessor'], ...]:
        # noinspection PyTypeChecker
        return tuple(KytBaseAssessor._classes_.values())

    @staticmethod
    def get_assessor(name: str) -> Optional[Type['KytBaseAssessor']]:
        return KytBaseAssessor._classes_.get(name)

    @staticmethod
    def list_assessors_for_chain(chain: str
                                 ) -> Tuple[Type['KytBaseAssessor'], ...]:
        # 未用到
        return tuple(map(KytBaseAssessor.get_assessor,
                         KytBaseAssessor._chain_to_assessors_[chain]))

    @staticmethod
    def get_assessor_for_chain(chain: str
                               ) -> Optional[Type['KytBaseAssessor']]:
        # 未用到
        if not (name := KytBaseAssessor._get_assessor_for_chain(chain)):
            return None
        return KytBaseAssessor.get_assessor(name)

    @staticmethod
    @cached(600)
    def _get_assessor_for_chain(chain: str) -> Optional[str]:
        # 未用到
        chain_to_names = KytBaseAssessor._chain_to_assessors_

        target = None
        to_be_removed = []
        names = chain_to_names.get(chain) or []
        for assessor in map(KytBaseAssessor.get_assessor, names):
            name = assessor.name
            if assessor.is_chain_supported(chain):
                target = name
                break
            to_be_removed.append(name)

        exhaust(map(names.remove, to_be_removed))
        if target is not None:
            return target

        for assessor in KytBaseAssessor.list_assessors():
            if assessor.is_chain_supported(chain):
                if not names:
                    chain_to_names[chain] = names
                names.append(target := assessor.name)
                return target

        return None

    @classmethod
    def list_supported_chains(cls) -> Iterable[str]:
        raise NotImplementedError

    @classmethod
    def is_chain_supported(cls, chain: str) -> bool:
        raise NotImplementedError

    @classmethod
    def assess_object(cls, ass: DepositSenderRiskAssessment
                      ) -> Tuple[bool, Optional[dict]]:
        raise NotImplementedError

    @classmethod
    def preview_assessment_details(cls, details: dict) -> str:
        raise NotImplementedError

    @classmethod
    def explain_assessment_details(cls, details: dict
                                   ) -> Optional[Dict[str, Any]]:
        raise NotImplementedError

    @classmethod
    def get_usage_quotas(cls) -> List[KYTUsageQuota]:
        raise NotImplementedError

    @classmethod
    def get_deposit_assess_objects(
            cls,
            deposit: Deposit,
            req: DepositRiskAssessmentRequest,
    ) -> List[str]:
        raise NotImplementedError

    @classmethod
    def normalise_assess_object(cls, chain: str, assess_object: str) -> str:
        raise NotImplementedError

    @classmethod
    def prepare_assess_object(cls, ass: DepositSenderRiskAssessment) -> bool:
        _ = ass
        return True

    @classmethod
    def is_address_assessor(cls) -> bool:
        return cls.assess_object_type is AssessObjectType.ADDRESS

    @classmethod
    def is_enabled(cls) -> bool:
        return True


class CoinfirmAddressAssessor(KytBaseAssessor):
    name = 'coinfirm'
    assess_object_type = AssessObjectType.ADDRESS

    RiskLevel = KYTCoinfirmClient.RiskLevel

    _client = KYTCoinfirmClient(timeout=300)

    @classmethod
    @cached(86400 * 30)
    def list_supported_chains(cls) -> Iterable[str]:
        return cls._client.list_supported_chains()

    @classmethod
    def is_chain_supported(cls, chain: str) -> bool:
        return cls._client.is_chain_supported(chain)

    @classmethod
    def assess_object(cls, ass: DepositSenderRiskAssessment
                      ) -> Tuple[bool, Optional[dict]]:
        chain, object_ = ass.chain, ass.assessed_object
        try:
            assessment = cls._client.assess_address(chain, object_)
        except BaseHTTPClient.BadResponse as e:
            cls.get_logger().exception(
                f'failed to assess {object_} on {chain}')
            raise AssessmentServiceNotAvailable(repr(e))
        return (
            assessment.is_safe,
            dict(
                risk_level=assessment.risk_level,
                risk_score=assessment.risk_score,
                indicators=assessment.indicators,
                belongs_to_exchange=assessment.belongs_to_exchange,
                owner=assessment.owner
            )
        )

    @classmethod
    def preview_assessment_details(cls, details: dict) -> str:
        risk_score = details.get("risk_score", '')
        risk_level = details.get("risk_level", '')
        if risk_score and risk_level:
            return (f'risk_score={risk_score}, '
                    f'risk_level={risk_level}')
        return ''

    @classmethod
    def explain_assessment_details(cls, details: dict
                                   ) -> Optional[Dict[str, Any]]:
        exp = {}
        cli = cls._client
        if owner := details.get('owner'):
            exp['owner'] = dict(
                id=owner,
                **((cli.get_owner(owner) or {}).get('value') or {})
            )
        if indicators := details.get('indicators'):
            indicators_info = cli.get_indicators(indicators)
            exp['indicators'] = [
                dict(
                    id=int(ind),
                    impact=impact,
                    **((indicators_info.get(ind) or {}).get('value') or {})
                ) for ind, impact
                in sorted(indicators.items(), key=lambda x: -x[1])
            ]
        return exp

    @classmethod
    def get_usage_quotas(cls) -> List[KYTUsageQuota]:
        return cls._client.get_usage_quotas()

    @classmethod
    def get_deposit_assess_objects(
            cls,
            deposit: Deposit,
            req: DepositRiskAssessmentRequest,
    ) -> List[str]:
        return req.list_senders()

    @classmethod
    def normalise_assess_object(cls, chain: str, assess_object: str) -> str:
        from app.business import WalletClient
        return WalletClient().normalise_addresses([(chain, assess_object)])[chain]

    @classmethod
    def is_enabled(cls) -> bool:
        return KytSettings.coinfirm_enabled


class ChainalysisDataUpdatedTimeout(RuntimeError):
    pass


class ChainalysisTXAssessor(KytBaseAssessor):
    name = 'chainalysis'
    assess_object_type = AssessObjectType.TX_ID

    _client = KYTChainalysisClient(timeout=300)
    SPLITTER = DepositSenderRiskAssessment.SPLITTER
    MAX_WAITING_SECONDS = 60 * 5  # 有个中间接口可能一直没有更新结果

    @classmethod
    @cached(86400 * 30)
    def list_supported_chains(cls) -> Iterable[str]:
        return cls._client.list_supported_chains()

    @classmethod
    def is_chain_supported(cls, chain: str) -> bool:
        return cls._client.is_chain_supported(chain)

    @classmethod
    def get_deposit_assess_objects(
            cls,
            deposit: Deposit,
            req: DepositRiskAssessmentRequest,
    ) -> List[str]:
        from app.business import WalletClient

        try:
            client = WalletClient()
            r_tx_id = client.normalise_tx_ids([(deposit.chain, deposit.tx_id)])
            tx_id = r_tx_id[deposit.chain]
            r_addr = client.normalise_addresses([(deposit.chain, deposit.address)])
            recipient = r_addr[deposit.chain]
        except Exception as e:
            cls.get_logger().error(f'normalise object failed: {e!r}')
            return []
        return [cls._compose_asset_object(tx_id, recipient)]

    @classmethod
    def _parse_asset_object(cls, asset_object: str) -> Tuple[str, str]:
        result = asset_object.split(':', maxsplit=1)
        if len(result) < 2:
            raise ValueError(f'invalid asset object {asset_object}')
        return result[0], result[1]

    @classmethod
    def _compose_asset_object(
            cls, tx_id: str, recipient: str) -> str:
        return f'{tx_id}:{recipient}'

    @classmethod
    def normalise_assess_object(cls, chain: str, assess_object: str) -> str:
        tx_id, recipient = cls._parse_asset_object(assess_object)
        from app.business import WalletClient
        client = WalletClient()
        tx_id = client.normalise_tx_ids([(chain, tx_id)])[chain]
        recipient = client.normalise_addresses([(chain, recipient)])[chain]
        return cls._compose_asset_object(tx_id, recipient)

    @classmethod
    def assess_object(cls, ass: DepositSenderRiskAssessment
                      ) -> Tuple[bool, Optional[dict]]:
        risk_assessment = ass
        chain, object_ = ass.chain, ass.assessed_object
        if not risk_assessment or not (
                external_id := risk_assessment.details.get('external_id')):
            cls.get_logger().error(f'{object_} of {chain} missing')
            raise Exception(f'{object_} of {chain} missing or not registered')
        try:
            assessment = cls._client.get_transaction_assessment(external_id)
        except BaseHTTPClient.BadResponse as e:
            cls.get_logger().exception(
                f'failed to assess tx({object_}) on {chain}')
            raise AssessmentServiceNotAvailable(repr(e))
        return (
            assessment.is_safe,
            dict(
                external_id=external_id,
                risk_level=assessment.risk_level,
                service=assessment.service,
            )
        )

    @classmethod
    def prepare_assess_object(cls, ass: DepositSenderRiskAssessment) -> bool:
        chain, object_ = ass.chain, ass.assessed_object
        tx_id, address = cls._parse_asset_object(object_)
        if ((deposit := Deposit.query.filter(
                Deposit.chain == chain,
                Deposit.tx_id == tx_id
        ).first()) is not None and deposit.status not in [
            Deposit.Status.TOO_SMALL,
            Deposit.Status.PROCESSING,
        ]):
            raise AssessmentNotPossible

        assessment = ass
        details = assessment.details
        if details and (external_id := details.get('external_id')):
            try:
                b = bool(cls._client.is_transaction_processed(external_id))
            except Exception:
                raise KYTErrOrNotSupported
            else:
                if b is True:
                    return b
                if assessment.updated_at + timedelta(seconds=cls.MAX_WAITING_SECONDS) < now():
                    msgs = []
                    msgs.append(f'updated_at: {assessment.updated_at.timestamp()}')
                    msgs.append(f'timeout_at: {now().timestamp()}')
                    msgs.append(f'max_waiting_seconds: {cls.MAX_WAITING_SECONDS}')
                    raise ChainalysisDataUpdatedTimeout('; '.join(msgs))
                return b
        external_id = cls._register_transaction(assessment)
        if not external_id:
            return False
        assessment.details = dict(details or {}, external_id=external_id)
        db.session.commit()
        return False

    @classmethod
    def _register_transaction(
            cls,
            risk_assessment: DepositSenderRiskAssessment
    ) -> str:
        chain = risk_assessment.chain
        tx_id, recipient = cls._parse_asset_object(
            risk_assessment.assessed_object)
        attachment = risk_assessment.attachment
        user_id = attachment.get('user_id')
        asset = attachment.get('asset')
        if not asset or not recipient:
            raise Exception(f'asset object {tx_id}({chain}) '
                            f'missing asset or recipient')
        try:
            r = cls._client.register_transaction(asset, chain, tx_id, recipient, user_id)
        except Exception:
            raise KYTErrOrNotSupported
        else:
            return r

    @classmethod
    def preview_assessment_details(cls, details: dict) -> str:
        risk_level = details.get("risk_level")
        return '' if not risk_level else f'risk_level={risk_level}'

    @classmethod
    def explain_assessment_details(cls, details: dict
                                   ) -> Optional[Dict[str, Any]]:
        return details

    @classmethod
    def is_enabled(cls) -> bool:
        return KytSettings.chainalysis_enabled


@celery_task(queue=CeleryQueues.WALLET)
@lock_call(with_args=True)
def process_one_application_req(rid: int):
    DepositRiskAssessmentManager.assess_object_by_system_rules(rid)


@celery_task(queue=CeleryQueues.WALLET)
@lock_call(with_args=True)
def process_one_application(rid: int):
    DepositRiskAssessmentManager.process_one_assessment_application(rid)


class KytUsageQuotasMonitor:
    desc = 'KYT 用量监控'

    THRESHOLD: float = .8
    THRESHOLD_CRITICAL = .9

    class AlarmItem(NamedTuple):
        assessor: str
        type: str
        used: int
        total: int
        time_progress: float
        remark: str = ''

        _keys_ = 'assessor', 'type'

        @property
        def used_ratio(self) -> float:
            return self.used / self.total

        @property
        def used_ratio_over_time(self) -> float:
            return self.used_ratio / self.time_progress

        @property
        def is_urgent(self) -> int:
            if self.used_ratio > KytUsageQuotasMonitor.THRESHOLD_CRITICAL:
                return 3
            used_ot = self.used_ratio_over_time
            if used_ot >= KytUsageQuotasMonitor.THRESHOLD_CRITICAL:
                return 2
            if used_ot >= KytUsageQuotasMonitor.THRESHOLD:
                return 1
            return 0

    @classmethod
    def check_send_alerts(cls):
        for assessor in DepositRiskAssessmentManager.list_assessors():
            try:
                alarms = cls.check_items(assessor)
            except NotImplementedError:
                pass
            else:
                if alarms:
                    cls.send_alarms(alarms)

    @classmethod
    def check_items(
            cls,
            item
    ) -> list:
        ret = []
        for quota in item.get_usage_quotas():
            quota_ratio, time_ratio = quota.used_ratio, quota.time_progress
            if quota_ratio < .2 and time_ratio < .1:
                continue
            # 使用量与使用周期保持均衡
            if quota_ratio / time_ratio < cls.THRESHOLD:
                continue
            ret.append(cls.AlarmItem(
                item.name,
                quota.type,
                used=quota.used,
                total=quota.total,
                time_progress=quota.time_progress,
                remark=quota.remark
            ))
        return ret

    @classmethod
    def send_alarms(cls, alarms: list[AlarmItem]):
        from . import send_alert_notice

        def ratio_to_pc(_r):
            return amount_to_str(Decimal(_r) * 100, 1) + '%'

        def bold_if(_s, _if):
            return f'*{_s}*' if _if else _s

        lines = ['KYT 用量报告:']
        for a in sorted(alarms, key=lambda _a: (_a.assessor, _a.type)):
            assessor, type_ = a.assessor, a.type
            used, total = a.used, a.total
            lines.append(
                f'{assessor} ({type_}): \n'
                f'当前周期已经过 {ratio_to_pc(a.time_progress)}, '
                f'当前用量 {used}/{total} '
                f'({bold_if(ratio_to_pc(a.used_ratio), a.is_urgent >= 2)});'
            )
            may_total = int(used / a.time_progress) if a.time_progress > 0 else 0
            if may_total > 0:
                may_total_percent = may_total / total
                lines.append(
                    f'预估本月用量为 {may_total}/{total} '
                    f'({bold_if(ratio_to_pc(may_total_percent), a.is_urgent >= 2)})'
                )
            if a.used_ratio >= Decimal('0.98'):
                lines.append(
                    f'⚠️当前用量不足！！！请注意！！！'
                )
        send_alert_notice(
            '\n'.join(lines),
            config["ADMIN_CONTACTS"]["kyt_notice"],
            at=config["ADMIN_CONTACTS"]["slack_at"].get("kyt_notice")
        )


class KYTAbnormalMonitor:
    desc = 'KYT 报错监控'

    THRESHOLD: float = .1

    @classmethod
    def check_send_alerts(cls):
        from . import send_alert_notice

        ts = current_timestamp(to_int=True)
        for assessor in DepositRiskAssessmentManager.list_assessors():
            name = assessor.name
            notice_cache = KYTAbnormalNoticedCache(name)
            if notice_cache.noticed(ts):
                continue
            cache = KYTAbnormalCache(name)
            value, last_value = cache.read_values()
            if not last_value:
                continue
            if value <= (last_value * cls.THRESHOLD):
                continue

            msg = f'''
                【KYT服务商报错告警】
                服务商{name}接口返回异常：
                上一小时报错次数：{last_value}，
                当前报错次数：{value}，
                请及时处理；
                '''
            send_alert_notice(
                msg,
                config["ADMIN_CONTACTS"]["kyt_notice"],
                at=config["ADMIN_CONTACTS"]["slack_at"].get("kyt_notice")
            )
            # mobiles = RiskControlMobileNoticeConfig.get_mobiles(
            #     RiskControlMobileNoticeConfig.MobileNoticeEventType.KYT_SERVICE_ABNORMAL_NOTICE
            # )
            # for mobile in mobiles:
            #     send_kyt_service_abnormal_notice(mobile, name)
            notice_cache.set_noticed(ts)
