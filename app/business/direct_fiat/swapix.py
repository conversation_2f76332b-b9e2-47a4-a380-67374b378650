import datetime
import hashlib
import hmac
import re
import time
import uuid
from decimal import Decimal
from typing import Any, Dict, Optional

from flask import current_app

from .. import cached, PriceManager
from ...config import config
from ...exceptions import InvalidArgument
from ...models import User, KycVerificationExtend, KycVerification, KYCInstitution, InstitutionCompany
from ...models.direct_fiat import DirectFiatOrder
from ...utils import (AmountType, RESTClient, amount_to_str, quantize_amount, AWSBucketPublic,
                      now, )
from .base import BasePartnerClient, Order, DirectFiatPaymentMethod, Quote, QuoteMethod, SupportType


class SwapixClient(BasePartnerClient):
    name = 'Swapix'
    # TODO: logo required
    logo = AWSBucketPublic.get_file_url('coinex_picture_manage_ic_swapix.png')
    buy_assets = ['USDT', ]
    sell_assets = ['USDT', ]
    buy_fiats = ['BRL', ]
    sell_fiats = ['BRL', ]
    support_types = [SupportType.BUY, SupportType.SELL]
    buy_payment_methods = [DirectFiatPaymentMethod.PIX, ]
    sell_payment_methods = [DirectFiatPaymentMethod.PIX, ]
    buy_quote_methods = [QuoteMethod.BY_FIAT, QuoteMethod.BY_ASSET]
    sell_quote_methods = [QuoteMethod.BY_FIAT, QuoteMethod.BY_ASSET]
    buy_fee_rate = Decimal('0.02')  # Fee was agreed at 2% +0.35 BRL per transaction
    sell_fee_rate = Decimal('0.02')  # Fee was agreed at 2% +0.35 BRL per transaction
    min_fee = Decimal()

    buy_order_limit_min = Decimal(6)
    buy_order_limit_max = Decimal(18000)
    sell_order_limit_max = Decimal(18000)
    sell_order_limit_min = Decimal(6)
    daily_limit = Decimal(18000)  # not sure
    monthly_limit = Decimal(18000)  # No monthly limit: 他们当前只限制单笔交易
    help_url = 'https://swapx.com.br/'

    LOCATION_CODE = 'BRA'
    network_map = {
        'USDT': 'SOL',
    }  # to ours

    withdrawal_addresses = {
        ('USDT', 'SOL'): 'AkV2neDQEKmwPAqLaoGZqncVMNsebrdDLY6rL8DJ6p79'
    }  # 目前钱包侧 memo 支持的最大长度为 128（按一个字节计算）

    _blockchain_map = {
        'SOL': 'sol',
    }  # ours to third

    conv_map = {
        ('USDT', 'SOL'): 'sxusdt'
    }  # third quotes conv

    def __init__(self):
        self._conf = config['SWAPIX_CONFIG']
        self._client = RESTClient(self._conf['url'], headers={
            'api_user': self._conf['api_user'],
        })

    def get_withdrawal_address(self, asset: str, chain: str) -> str:
        return self.withdrawal_addresses[(asset, chain)]

    def asset_to_fiat(self, asset: str, fiat: str, support_type=SupportType.BUY) -> Decimal:
        if support_type is SupportType.BUY:
            assets = self.buy_assets
            fiats = self.buy_fiats
        else:
            assets = self.sell_assets
            fiats = self.sell_fiats
        if asset not in assets or fiat not in fiats:
            raise ValueError(f'invalid asset {asset} or fiat {fiat}')
        price = self._get_price(asset, fiat, support_type.value)
        return Decimal(price)

    @cached(300)
    def _get_price(self, asset: str, fiat: str, support_type: str) -> str:
        fiat_amount = getattr(self, f"{support_type}_order_limit_max") / 4
        if fiat != 'USD':
            fiat_amount = quantize_amount(fiat_amount / PriceManager.fiat_to_usd(fiat), 2)
        data = self._request_quote(
            asset=asset,
            fiat=fiat,
            quote_amount=fiat_amount,
            support_type=SupportType(support_type),
            fiat_amount=fiat_amount,
            asset_amount=Decimal(),
        )
        return amount_to_str(data['price'], self._price_precision(asset))

    def quote(self,
              user: User,
              from_: str,
              to: str,
              amount: AmountType,
              support_type=SupportType.BUY) -> Quote:
        assets = getattr(self, f"{support_type.value}_assets")
        fiats = getattr(self, f"{support_type.value}_fiats")
        asset_amount, fiat_amount = Decimal(), Decimal()
        if from_ in assets and to in fiats:
            asset, fiat = from_, to
            asset_amount = amount
        elif from_ in fiats and to in assets:
            asset, fiat = to, from_
            fiat_amount = amount
        else:
            raise ValueError(f'invalid asset and fiat `{from_}` `{to}`')

        data = self._request_quote(
            asset=asset,
            fiat=fiat,
            quote_amount=amount,
            support_type=support_type,
            fiat_amount=fiat_amount,
            asset_amount=asset_amount,
        )
        return Quote(
            id=str(uuid.uuid4()),
            asset=asset,
            asset_amount=quantize_amount(data['asset_amount'], self._asset_amount_precision(asset)),
            fiat=fiat,
            fiat_amount=quantize_amount(data['fiat_amount'], 2),
            price=quantize_amount(data['price'], 2),
            support_type=support_type,
            ttl=data['timeout']
        )

    def _request_quote(
            self,
            asset: str,
            fiat: str,
            quote_amount: AmountType,
            support_type: SupportType,
            fiat_amount=Decimal(),
            asset_amount=Decimal(),
    ) -> dict:
        if support_type == SupportType.BUY:
            _type = 'sell'
            profile = "payment"
            target = "amount" if asset_amount else "all"
        else:
            _type = 'buy'
            profile = "transfer"
            target = "amount" if fiat_amount else "all"
        conv = self.conv_map[(asset, self.network_map[asset])]
        params = {
            "type": _type,
            "currency": fiat.lower(),
            "profile": profile,
            "conv": conv,
            "target": target,
            "amount": amount_to_str(quote_amount),
            "user": self._conf['api_user'],
        }
        resp = self._client.get('/swapix/swapquote', **params)
        if resp['status'] != 'ok':
            current_app.logger.warning(f"_request_quote resp: {resp}")
            raise ValueError
        if support_type == SupportType.BUY:
            fiat_amount = resp['data']['total_brl']
            key = f'send_{conv}'
            asset_amount = resp['data'][key]
        else:
            fiat_amount = resp['data']['send_brl']
            key = f'total_{conv}'
            asset_amount = resp['data'][key]
        price = Decimal(fiat_amount) / Decimal(asset_amount) if asset_amount else 0
        return {
            'price': price,
            'fiat_amount': fiat_amount,
            'asset_amount': asset_amount,
            'timeout': resp['data']['timeout'],
        }

    def place_order(self,
                    user: User,
                    quote: Quote,
                    address: str) -> Order:
        support_type = SupportType(quote.support_type)
        if support_type == SupportType.BUY:
            return self.place_buy_order(user, quote, address)
        return self.place_sell_order(user, quote, address)

    def place_buy_order(self, user: User, quote: Quote, address: str) -> Order:
        asset = quote.asset
        chain = self._blockchain_map[self.get_asset_chain(asset)]
        conv = self.conv_map[(asset, self.network_map[asset])]
        cpf = self.get_cpf(user)
        if not cpf:
            raise RuntimeError("No CPF number")
        ttl = 600
        params = {
            "chain": chain,
            "currency": conv,
            "address": address,
            "amount_direction": "in",  # "out" for USDT, "in" for BRL. see docs explain
            "expire": ttl,  # time for payment to expire, in seconds
            "amount": quote.fiat_amount,
            "v_taxid": cpf,  # cpf or cnpj of the buyer
            "v_email": user.email,  #
            "v_phone": user.mobile,  #
            "v_country": "br",  #
            "v_zip": "88095700",  # Need to send, but default value
            "v_num": "",  # Need to send, but leave empty
            "v_add": "",  # Need to send, but leave empty
            "bind_code": "1",
            # "callback": "",
        }

        data = self._payment_generation(params)
        # timeout = data['operation']['rate']['timeout']
        expired_at = now() + datetime.timedelta(seconds=ttl)
        data['expired_at'] = expired_at
        return Order(
            id=data['operation']['operation_id'],
            payment_url=data['emv'],
            timeout=ttl,
            extra=data
        )

    def validate_cpf(self, user):
        if not self.get_cpf(user):
            raise InvalidArgument

    @classmethod
    def get_cpf(cls, user) -> str | None:
        user_id = user.id
        if user.kyc_type == User.KYCType.INDIVIDUAL:
            model = KycVerificationExtend
            row = model.query.with_entities(
                model.id_number
            ).filter(
                model.user_id == user_id,
                model.id_type == KycVerification.IDType.CPF,
                model.nationality == cls.LOCATION_CODE,
                model.status == model.Status.PASSED
            ).first()
            if not row:
                return cls._get_kyc_cpf(user_id)
            return row.id_number
        elif user.kyc_type == User.KYCType.INSTITUTION:
            model = KYCInstitution
            row = model.query.with_entities(
                model.id
            ).filter(
                model.user_id == user_id,
                model.status == model.Status.PASSED,
            ).first()
            if not row:
                return None
            ic_model = InstitutionCompany
            ic_row = ic_model.query.with_entities(
                ic_model.register_code
            ).filter(
                ic_model.institution_id == row.id,
                ic_model.location_code == cls.LOCATION_CODE,
            ).first()
            return cls.normalize_cnpj(ic_row.register_code) if ic_row else None

    @classmethod
    def normalize_cnpj(cls, cnpj: str) -> str:
        """将 CNPJ 格式化为纯数字"""
        digits = re.sub(r'\D', '', cnpj)
        if len(digits) != 14:
            raise ValueError(f"CNPJ 长度错误: {digits}")
        return digits

    @classmethod
    def _get_kyc_cpf(cls, user_id: int) -> str | None:
        model = KycVerification
        row = model.query.with_entities(
            model.id_number
        ).filter(
            model.user_id == user_id,
            model.id_type == model.IDType.CPF,
            # model.nationality == cls.LOCATION_CODE,
            model.country == cls.LOCATION_CODE,
            model.status == model.Status.PASSED
        ).first()
        return row.id_number if row else None

    @classmethod
    def get_cpf_status(cls, user) -> dict | None:

        def _check_status_display(_status_name: str | None):
            display_statuses = (
                'CREATED',
                'PASSED',
                'REJECTED',
                'CANCELED',
            )
            if not _status_name:
                return
            if _status_name not in display_statuses:
                raise ValueError('invalid status to display.')

        user_id = user.id
        if user.kyc_type == User.KYCType.INDIVIDUAL:
            model = KycVerificationExtend
            row = model.query.with_entities(
                model.status,
                model.nationality,
            ).filter(
                model.user_id == user_id,
                model.id_type == KycVerification.IDType.CPF,
                model.nationality == cls.LOCATION_CODE,
                model.status != model.Status.CANCELLED,
            ).first()
            if not row:  # 特殊场景处理
                cpf = cls._get_kyc_cpf(user_id)
                status_name = model.Status.PASSED.name if cpf else None
                _check_status_display(_status_name=status_name)
                return dict(
                    status=status_name,
                    location_code=cls.LOCATION_CODE,
                )

            status_name = row.status.name
            _check_status_display(_status_name=status_name)
            return dict(
                status=status_name,
                location_code=row.nationality,
            )

        elif user.kyc_type == User.KYCType.INSTITUTION:
            model = KYCInstitution
            row = model.query.with_entities(
                model.id,
                model.status,
            ).filter(
                model.user_id == user_id,
            ).order_by(model.id.desc()).first()
            if not row:
                return None
            status_name = row.status.name
            _check_status_display(_status_name=status_name)
            ic_model = InstitutionCompany
            ic_row = ic_model.query.with_entities(
                ic_model.location_code
            ).filter(
                ic_model.institution_id == row.id,
            ).first()
            return dict(
                status=status_name,
                location_code=ic_row.location_code,
            )

    def _payment_generation(self, data: dict):
        data["api_ts"] = int(time.time())
        data["api_user"] = self._conf['api_user']
        data["api_sig"] = self.__sign_data(data)
        c = RESTClient(self._conf['url'], headers={
            'User-Agent': 'SPAYAPIP3',
        })
        try:
            resp = c.post('/swapix/pixlinkverify', data=data)
        except RESTClient.BadResponse as e:
            current_app.logger.warning(f"payment_generation error: {e!r}")
            raise
        else:
            if resp['status'] != 'ok':
                current_app.logger.warning(f"payment_generation resp: {resp}")
                raise ValueError
            return resp['data']

    def __sign_data(self, post_data: dict):
        # Sort the data by keys
        sorted_data = sorted(post_data.items())
        post_str = "&".join(f"{key}={value}" for key, value in sorted_data)

        # Create HMAC signature
        sig = hmac.new(
            self._conf['api_secret'].encode('utf-8'),
            post_str.encode('utf-8'),
            hashlib.sha256
        )
        return sig.hexdigest()


    def place_sell_order(self, user: User, quote: Quote, address: str) -> Order:
        pass

    def get_order(self, order: DirectFiatOrder) -> Optional[Dict[str, Any]]:
        if order.order_type == DirectFiatOrder.OrderType.BUY:
            params = {
                'chain': 'smartpay',
                'txid': order.order_id,  # operation_id
            }
        else:
            if not order.tx_id:
                return None
            params = {
                'chain': self._blockchain_map[self.get_asset_chain(order.asset)],
                'txid': order.tx_id,  # blockchain transaction ID
            }
        resp = self._client.get('/swapix/txstatus', **params)
        if resp['status'] != 'ok':
            current_app.logger.warning(f"get_order {order.id} resp: {resp}")
            return None
        return resp['data'] if resp else None
