import json
from collections import defaultdict
from copy import deepcopy
from datetime import <PERSON><PERSON><PERSON>
from decimal import Decimal
from enum import Enum
from typing import Any, Dict, List, Protocol

from flask_babel import gettext as _, force_locale

from app import config
from app.business import UserPreferences
from app.business.clients.server import ServerClient
from app.business.email import send_mission_email
from app.business.mission_center.mission import MissionBiz
from app.business.push import send_mobile_push_by_user_ids, get_user_web_lang, get_user_app_lang
from app.caches.mission import MissionCache, MonitorUserMissionCache, SendNoticeCache, MissionUserNoticeCache
from app.common import WebPushChannelType, MessageTitle, NoticePushType, MessageContent, MessageWebLink, \
    WebPushMessageType
from app.models import Message, db
from app.models.equity_center import EquityType
from app.models.mission_center import UserMission, SceneType
from app.utils import now, url_join, current_timestamp
from app.utils.iterable import batch_iter
from app.utils.parser import JsonEncoder
from app.utils.push import AppPagePath, WebPagePath


class SendType(Enum):
    EMAIL = "邮件"
    PUSH = "push"
    SITE_MAIL = "站内信"
    WEB_PUSH = "web_push"


# ==================== 基础混入类 ====================
class EmailMixin:
    """邮件发送混入类"""
    MISSION_CENTER = "/reward-center"
    REWARD_CENTER = "/reward-center?type=award"

    def get_mission_center_url(self) -> str:
        return url_join(config["SITE_URL"], self.MISSION_CENTER)

    def get_reward_center_url(self) -> str:
        return url_join(config["SITE_URL"], self.REWARD_CENTER)

    @staticmethod
    def format_email_params(user_id, params, fields):
        user_preference = UserPreferences(user_id)
        with force_locale(user_preference.language.value):
            email_params = {**params}
            for field in fields:
                email_params[field] = _(params[field])
        return email_params

    def send_email(self, param: dict[str, Any]):
        user_id = param["user_id"]
        pref = UserPreferences(user_id)
        send_mission_email(
            user_id,
            pref.language.value,
            param['name'],
            json.dumps(param, cls=JsonEncoder, ensure_ascii=False)
        )


class SiteMailMixin:
    """站内信发送混入类"""
    mail_title: MessageTitle
    mail_content: MessageContent
    web_link: MessageWebLink
    display_type: Message.DisplayType = Message.DisplayType.POPUP_WINDOW
    app_link: str

    def get_mail_title_and_content(self):
        return self.mail_title, self.mail_content

    def set_web_link(self, url):
        self.web_link = url

    def set_app_link(self, url):
        self.app_link = url

    def _create_message(self, user_id: int, params: dict[str, Any]) -> Message:
        title, content = self.get_mail_title_and_content()
        message = Message(
            user_id=user_id,
            title=title,
            content=content,
            params=json.dumps(params, cls=JsonEncoder, ensure_ascii=False),
            extra_info=json.dumps(
                dict(
                    web_link=self.web_link,
                    android_link=self.app_link,
                    ios_link=self.app_link,
                )
            ),
            display_type=self.display_type,
            expired_at=now() + timedelta(days=3),
            channel=Message.Channel.SYSTEM,
        )
        return message

    def send_site_mail(self, param: dict[str, Any]):
        tmp_param = deepcopy(param)
        if "site_url" in tmp_param:
            tmp_param.pop('site_url')
        user_id = tmp_param.pop('user_id')
        message = self._create_message(user_id, tmp_param)
        db.session.add(message)
        db.session.commit()


class PushMixin:
    """推送发送混入类"""
    app_push_url: str

    def send_push(self, params: dict[str, Any], title: str, message: str):
        user_id = params['user_id']
        app_lang = get_user_app_lang(user_id).value
        with force_locale(app_lang):
            _title_for_lang = _(title)
            _message_for_lang = _(message)
            send_mobile_push_by_user_ids.delay(
                user_ids=[user_id],
                content=_message_for_lang,
                title=_title_for_lang,
                ttl=0,
                url=self.app_push_url,
                created_at=current_timestamp(to_int=True),
            )


class WebPushMixin:
    """Web推送发送混入类"""
    web_push_url: WebPagePath

    def send_wp(self, params: dict[str, Any], title: str, message: str, timing_type: str):
        client = ServerClient()
        user_id = params['user_id']
        web_lang = get_user_web_lang(user_id).value
        with force_locale(web_lang):
            _trs_title = _(title)
            _trs_message = _(message)
        client.notice_user_message(
            user_id,
            WebPushChannelType.REWARD_CENTER.value,
            dict(
                title=_trs_title,
                content=_trs_message,
                url=params['site_url'],
                type=NoticePushType.SUCCESS,
                timing_type=timing_type,
                msg_type=WebPushMessageType.REWARD_CENTER.value
            )
        )


# ==================== 消息发送器接口 ====================
class MessageSender(Protocol):
    """消息发送器接口"""
    def send_message(self, user_id: int, mission_title: str, title_template: str, message_params: dict) -> None:
        """发送单个消息 - 保持与现有接口一致"""
        ...

    def send_merge_message(self, user_id: int, mis_params_list: list[list[str, str, dict]]) -> None:
        """发送合并消息 - 保持与现有接口一致"""
        ...

    def send_single_expired_message(
        self,
        user_id: int,
        mission_title: str,
        value: Decimal,
        value_type: str,
        reward_type: EquityType,
        title_template: str,
        message_params: dict,
    ) -> None: ...

    def send_merge_expired_message(self, user_id: int) -> None: ...


# ==================== 基础消息发送器 ====================
class BaseMessageSender(EmailMixin, PushMixin, WebPushMixin, SiteMailMixin):
    """基础消息发送器"""
    name: str
    mail_title: MessageTitle
    mail_content: MessageContent
    is_merge: bool

    def __init__(self, scene_type: SceneType):
        self.scene_type = scene_type

    def _prepare_base_params(self, user_id: int, mission_title: str, site_url: str) -> dict:
        """准备基础参数"""
        return {
            "user_id": user_id,
            "title": mission_title,
            "site_url": site_url,
            'name': self.name,
            'scene_type': self.scene_type.name
        }

    def _prepare_translate_params(self, title_template: str, message_params: dict) -> dict:
        """准备翻译参数"""
        return {
            'need_translates': [
                {
                    'name': 'title',
                    'text': title_template,
                    'params': message_params,
                    'is_translate': True
                }
            ]
        }

    def _send_all_channels(self, params: dict, push_title: str, push_message: str,
                           web_link=MessageWebLink.REWARD_CENTER_PAGE.value,
                           app_link=AppPagePath.REWARD_CENTER.value.format(tab="tasks"),
                           is_send_email: bool = True) -> None:
        """发送所有渠道的消息"""
        # Email
        if is_send_email:
            self.send_email(params)

        # Site Mail
        self.set_web_link(web_link)
        self.set_app_link(app_link)
        self.send_site_mail(params)

        # Push
        self.app_push_url = app_link
        self.send_push(params, push_title, push_message)

        # Web Push
        self.send_wp(params, push_title, push_message, self.name)


# ==================== 新任务通知发送器 ====================
class NewMissionSender(BaseMessageSender):
    """新任务通知发送器"""
    name = "new_mission_notice"
    mail_title = MessageTitle.NEW_MISSION_NOTICE
    mail_content = MessageContent.NEW_MISSION_NOTICE
    is_merge = True

    def get_push_title(self) -> str:
        return _("请查收新任务")

    def get_push_message(self, mission_title: str) -> str:
        return _('"%(title)s"已上线，快来奖励中心接受挑战吧！请前往「奖励中心」查看详情。', title=mission_title)

    def send_message(self, user_id: int, mission_title: str, title_template: str, message_params: dict) -> None:
        """发送单个新任务通知 - 使用与现有代码相同的参数组合方式"""
        params = self._prepare_base_params(user_id, mission_title, self.get_mission_center_url())
        params.update(self._prepare_translate_params(title_template, message_params))

        push_title = self.get_push_title()
        push_message = self.get_push_message(mission_title)
        
        self._send_all_channels(params, push_title, push_message)

    def send_merge_message(self, user_id: int, mis_params_list: list[list[str, str, dict]]) -> None:
        """发送合并新任务通知 - 使用与现有代码相同的参数组合方式"""
        if not mis_params_list:
            return
        self.mail_title = MessageTitle.NEW_MISSION_NOTICE
        self.mail_content = MessageContent.MUL_NEW_MISSION_NOTICE
            
        # 取第一个任务作为主要信息
        mission_title, title_template, message_params = mis_params_list[0]

        # Email参数
        email_params = self._prepare_base_params(user_id, mission_title, self.get_mission_center_url())
        email_params['is_mul'] = True
        email_params['scene_type'] = self.scene_type.name
        self.send_email(email_params)

        # 其他渠道参数
        params = self._prepare_base_params(user_id, mission_title, self.get_mission_center_url())
        params.update(self._prepare_translate_params(title_template, message_params))

        push_title = _("请查收新任务")
        push_message = _('"%(title)s"等多个任务已上线，快来奖励中心接受挑战吧！请前往「奖励中心」查看详情。', title=mission_title)
        
        self._send_all_channels(params, push_title, push_message, is_send_email=False)


# ==================== 任务奖励通知发送器 ====================
class MissionRewardSender(BaseMessageSender):
    """任务奖励通知发送器"""
    name = "mission_reward_sent"
    mail_title = MessageTitle.MISSION_REWARD_SENT
    mail_content = MessageContent.MISSION_REWARD_SENT
    is_merge = False

    def get_push_title(self) -> str:
        return _("任务奖励发放")

    def get_push_message(self, mission_title: str) -> str:
        return _("恭喜，你已完成任务！任务名称：%(title)s 请前往「我的奖励」查看详情。", title=mission_title)

    def send_message(self, user_id: int, mission_title: str, title_template: str, message_params: dict) -> None:
        """发送单个任务奖励通知 - 使用与现有代码相同的参数组合方式"""
        params = self._prepare_base_params(user_id, mission_title, self.get_reward_center_url())
        params.update(self._prepare_translate_params(title_template, message_params))

        push_title = self.get_push_title()
        push_message = self.get_push_message(mission_title)
        
        self._send_all_channels(
            params,
            push_title,
            push_message,
            web_link=MessageWebLink.REWARD_CENTER_PAGE.value + "?type=award",
            app_link=AppPagePath.REWARD_CENTER.value.format(tab="rewards")
        )


# ==================== 任务到期通知发送器 ====================
class MissionExpiringSender(BaseMessageSender):
    """任务到期通知发送器"""
    name = "mission_expiring"
    mail_title = MessageTitle.MISSION_EXPIRING
    mail_content = MessageContent.MISSION_EXPIRING
    is_merge = True

    def get_push_title(self) -> str:
        return _("任务即将到期")

    def get_push_message(self, value: Decimal, value_type: str, reward_type: EquityType) -> str:
        params = dict(value=value, value_type=value_type, reward_type=_(reward_type.value))
        if reward_type == EquityType.INVEST_INCREASE:
            return _("你的任务即将到期，完成即可获得 %(value)s%(value_type)s%(reward_type)s奖励！请前往「奖励中心」完成任务。", **params)
        else:
            return _("你的任务即将到期，完成即可获得 %(value)s %(value_type)s%(reward_type)s奖励！请前往「奖励中心」完成任务。", **params)

    def send_single_expired_message(
        self,
        user_id: int,
        mission_title: str,
        value: Decimal,
        value_type: str,
        reward_type: EquityType,
        title_template: str,
        message_params: dict,
    ) -> None:
        """发送单个任务到期通知 - 使用与现有代码相同的参数组合方式"""
        params = self._prepare_base_params(user_id, mission_title, self.get_mission_center_url())
        params.update(
            {
                "value": value,
                "value_type": value_type,
                "reward_type": _(reward_type.value),
            }
        )
        params.update(self._prepare_translate_params(title_template, message_params))

        push_title = self.get_push_title()
        push_message = self.get_push_message(value, value_type, reward_type)
        
        self._send_all_channels(params, push_title, push_message)

    def send_merge_expired_message(self, user_id: int) -> None:
        """发送合并任务到期通知 - 使用与现有代码相同的参数组合方式"""
        params = self._prepare_base_params(user_id, "", self.get_mission_center_url())
        
        # Email
        email_params = dict(params)
        email_params['is_mul'] = True
        email_params['scene_type'] = self.scene_type.name
        self.send_email(email_params)

        # 其他渠道
        push_title = _("任务即将到期")
        push_message = _("你有多个任务即将到期，请前往「奖励中心」查看详情。")
        
        self._send_all_channels(params, push_title, push_message, is_send_email=False)
        
        
class MiddleExpiringSender(BaseMessageSender):
    """中期到期通知发送器"""
    name = "mission_middle_expiring"
    mail_title = MessageTitle.MISSION_MIDDLE_EXPIRING
    mail_content = MessageContent.MISSION_MIDDLE_EXPIRING
    is_merge = True

    def get_push_title(self) -> str:
        return _("你还有丰厚的新手福利待领取")

    def get_push_message(self) -> str:
        return _("你还有丰厚的新手福利待领取，快来「奖励中心」查看吧！")

    def send_single_expired_message(
        self, user_id: int, mission_title: str, value: Decimal, value_type: str, reward_type: str, title_template: str, message_params: dict
    ) -> None:
        params = self._prepare_base_params(user_id, "", self.get_mission_center_url())
        
        push_title = self.get_push_title()
        push_message = self.get_push_message()
        
        self._send_all_channels(params, push_title, push_message)

    def send_merge_message(self, user_id: int) -> None:
        """发送合并任务到期通知 - 使用与现有代码相同的参数组合方式"""
        params = self._prepare_base_params(user_id, "", self.get_mission_center_url())
        
        # Email
        email_params = dict(params)
        email_params['is_mul'] = True
        email_params['scene_type'] = self.scene_type.name
        self.send_email(email_params)

        # 其他渠道
        push_title = _("你还有丰厚的新手福利待领取")
        push_message = _("你还有丰厚的新手福利待领取，快来「奖励中心」查看吧！")
        
        self._send_all_channels(params, push_title, push_message, is_send_email=False)


# ==================== 场景特定发送器 ====================
class NewbieNewMissionSender(NewMissionSender):
    """新手新任务通知发送器"""
    def __init__(self):
        super().__init__(SceneType.NEWBIE)


class RoutineNewMissionSender(NewMissionSender):
    """老用户新任务通知发送器"""
    def __init__(self):
        super().__init__(SceneType.ROUTINE)


class NewbieMissionRewardSender(MissionRewardSender):
    """新手任务奖励通知发送器"""
    mail_content = MessageContent.MISSION_REWARD_SENT

    def __init__(self):
        super().__init__(SceneType.NEWBIE)

    def get_push_message(self, mission_title: str) -> str:
        return _("恭喜，你已完成新用户专属任务！任务名称：%(title)s 请前往「我的奖励」查看详情。", title=mission_title)


class RoutineMissionRewardSender(MissionRewardSender):
    """老用户任务奖励通知发送器"""
    mail_content = MessageContent.ROUTINE_MISSION_REWARD_SENT

    def __init__(self):
        super().__init__(SceneType.ROUTINE)

    def get_push_message(self, mission_title: str) -> str:
        return _("恭喜，你已完成专属任务！任务名称：%(title)s 请前往「我的奖励」查看详情。", title=mission_title)


class NewbieMissionExpiringSender(MissionExpiringSender):
    """新手任务到期通知发送器"""
    mail_content = MessageContent.MISSION_EXPIRING

    def __init__(self):
        super().__init__(SceneType.NEWBIE)

    def get_push_message(self, value: Decimal, value_type: str, reward_type: EquityType) -> str:
        params = dict(value=value, value_type=value_type, reward_type=_(reward_type.value))
        if reward_type == EquityType.INVEST_INCREASE:
            content = _(
                "你的新用户专属任务即将到期，完成即可获得 %(value)s%(value_type)s%(reward_type)s奖励！请前往「奖励中心」完成任务。",
                **params,
            )
        else:
            content = _(
                "你的新用户专属任务即将到期，完成即可获得 %(value)s %(value_type)s%(reward_type)s奖励！请前往「奖励中心」完成任务。",
                **params,
            )
        return content
        
    def send_merge_expired_message(self, user_id: int) -> None:
        self.mail_title = MessageTitle.MISSION_EXPIRING
        self.mail_content = MessageContent.MUL_MISSION_EXPIRING
        super().send_merge_expired_message(user_id)


class RoutineMissionExpiringSender(MissionExpiringSender):
    """老用户任务到期通知发送器"""
    mail_content = MessageContent.ROUTINE_MISSION_EXPIRING

    def __init__(self):
        super().__init__(SceneType.ROUTINE)

    def get_push_message(self, value: Decimal, value_type: str, reward_type: EquityType) -> str:
        params = dict(value=value, value_type=value_type, reward_type=_(reward_type.value))
        if reward_type == EquityType.INVEST_INCREASE:
            content = _(
                "你的专属任务即将到期，完成即可获得 %(value)s%(value_type)s%(reward_type)s奖励！请前往「奖励中心」完成任务。", **params
            )
        else:
            content = _(
                "你的专属任务即将到期，完成即可获得 %(value)s %(value_type)s%(reward_type)s奖励！请前往「奖励中心」完成任务。", **params
            )
        return content
        
    def send_merge_expired_message(self, user_id: int) -> None:
        self.mail_title = MessageTitle.MISSION_EXPIRING
        self.mail_content = MessageContent.MUL_ROUTINE_MISSION_EXPIRING
        super().send_merge_expired_message(user_id)
        

class NewbieMiddleExpiringSender(MiddleExpiringSender):
    """新手中期到期通知发送器"""
    
    def __init__(self):
        super().__init__(SceneType.NEWBIE)


class SenderType(Enum):
    """发送器类型"""
    NEW_MISSION = "new_mission"
    REWARD_SENT = "reward_sent"
    MISSION_EXPIRING = 'mission_expiring'
    MIDDLE_EXPIRING = 'middle_expiring'


# ==================== 消息发送器工厂 ====================
class MessageSenderFactory:
    """消息发送器工厂"""
    
    _SENDERS = {
        SenderType.NEW_MISSION: {
            SceneType.NEWBIE: NewbieNewMissionSender,
            SceneType.ROUTINE: RoutineNewMissionSender,
        },
        SenderType.REWARD_SENT: {
            SceneType.NEWBIE: NewbieMissionRewardSender,
            SceneType.ROUTINE: RoutineMissionRewardSender,
        },
        SenderType.MISSION_EXPIRING: {
            SceneType.NEWBIE: NewbieMissionExpiringSender,
            SceneType.ROUTINE: RoutineMissionExpiringSender,
        },
        SenderType.MIDDLE_EXPIRING: {
            SceneType.NEWBIE: NewbieMiddleExpiringSender,
        }
    }
    
    @classmethod
    def create_sender(cls, sender_type: SenderType, scene_type: SceneType) -> MessageSender:
        """创建消息发送器"""
        sender_class = cls._SENDERS.get(sender_type, {}).get(scene_type)
        if not sender_class:
            raise ValueError(f"Unsupported message type: {sender_type} or scene type: {scene_type}")
        return sender_class()


# ==================== 任务消息处理器 ====================
class MissionMessageProcessor:
    """任务消息处理器"""
    
    def __init__(self, sender_type: SenderType):
        self.sender_type = sender_type
    
    def process_user_missions(self, user_missions: List[UserMission]) -> None:
        """处理用户任务消息"""
        # 按场景类型分组
        missions_by_scene = self._group_missions_by_scene(user_missions)
        
        # 为每个场景类型处理消息
        for scene_type, missions in missions_by_scene.items():
            self._process_scene_missions(scene_type, missions)
    
    def _group_missions_by_scene(self, user_missions: List[UserMission]) -> Dict[SceneType, List[UserMission]]:
        """按场景类型分组任务"""
        missions_by_scene = {}
        for mission in user_missions:
            scene_type = mission.scene_type
            if scene_type not in missions_by_scene:
                missions_by_scene[scene_type] = []
            missions_by_scene[scene_type].append(mission)
        return missions_by_scene
    
    def _get_some_plan_user_missions(self, user_id: int, user_missions: List[UserMission]) -> List[UserMission]:
        """获取同一批发放的任务ID（中期过期同一批只会触发一次，需要把其他用户任务ID也加入）"""
        plan_user_ids = {u.plan_id for u in user_missions}
        user_missions = UserMission.query.filter(
            UserMission.plan_id.in_(plan_user_ids),
            UserMission.status == UserMission.Status.PENDING,
            UserMission.user_id == user_id,
            UserMission.scene_type == SceneType.NEWBIE,
        ).all()
        return [u.id for u in user_missions]
    
    def _process_scene_missions(self, scene_type: SceneType, missions: List[UserMission]) -> None:
        """处理特定场景类型的任务"""
        sender = MessageSenderFactory.create_sender(self.sender_type, scene_type)
        
        # 按用户分组
        missions_by_user = self._group_missions_by_user(missions)
        
        # 处理每个用户的任务
        for user_id, user_missions in missions_by_user.items():
            # 过期任务需要处理 24小时防干扰
            match self.sender_type:
                case SenderType.MISSION_EXPIRING:
                    user_notice_cache = MissionUserNoticeCache(user_id, self.sender_type.value)
                    if user_notice_cache.exists():
                        continue
                    self._process_user_missions(user_id, user_missions, sender)
                    user_notice_cache.gen()
                    self.notice_cache.add_ids([f.id for f in user_missions])
                case SenderType.MIDDLE_EXPIRING:
                    self._process_user_missions(user_id, user_missions, sender)
                    # 任务中期过期同一批只会触发一次，需要把其他用户任务ID也加入
                    plan_user_missions = self._get_some_plan_user_missions(user_id, user_missions)
                    self.notice_cache.add_ids(plan_user_missions)
                case _:
                    self._process_user_missions(user_id, user_missions, sender)
            
    def _group_missions_by_user(self, missions: List[UserMission]) -> Dict[int, List[UserMission]]:
        """按用户分组任务"""
        missions_by_user = defaultdict(list)
        for mission in missions:
            missions_by_user[mission.user_id].append(mission)
        return missions_by_user
    
    def _process_user_missions(self, user_id: int, user_missions: List[UserMission], sender: MessageSender) -> None:
        """处理单个用户的任务"""
        # 获取用户偏好
        user_pref = UserPreferences(user_id)
        
        if sender.is_merge:
            # 按任务ID排序
            user_missions.sort(key=lambda x: x.mission_id)
            if len(user_missions) == 1:
                # 单个任务
                self._send_single_message(user_id, user_missions[0], sender, user_pref)
            else:
                # 多个任务合并发送
                self._send_merge_message(user_id, user_missions, sender, user_pref)
        else:
            for user_mission in user_missions:
                self._send_single_message(user_id, user_mission, sender, user_pref)
        
    def _send_single_message(self, user_id: int, user_mission: UserMission, sender: MessageSender, user_pref: UserPreferences) -> None:
        """发送单个消息 - 使用与现有代码相同的参数获取和组合方式"""
        # 获取任务缓存数据 
        cache_data = MissionCache.get_cache_data_by_ids([user_mission.mission_id])[user_mission.mission_id]
        
        with force_locale(user_pref.language.value):
            # 构建标题 
            title = MissionBiz.build_title(cache_data, is_simple=True)
            # 获取标题模板
            title_template = MissionBiz.get_title_template(cache_data["mission_condition"], is_simple=True)
            # 获取标题参数 
            message_params = MissionBiz.get_build_title_params(cache_data)
            
            if self.sender_type in [SenderType.MISSION_EXPIRING, SenderType.MIDDLE_EXPIRING]:
                # 到期通知需要额外的奖励信息 
                reward_type = cache_data['reward']['reward_type']
                sender.send_single_expired_message(
                    user_id,
                    title,
                    cache_data["reward"]["value"],
                    cache_data["reward"]["value_type"],
                    EquityType[reward_type],
                    title_template,
                    message_params,
                )
            else:
                # 新任务和奖励通知 
                sender.send_message(user_id, title, title_template, message_params)
    
    def _send_merge_message(
            self,
            user_id: int,
            user_missions: List[UserMission],
            sender: MessageSender,
            user_pref: UserPreferences
    ) -> None:
        mis_params_list = []
        
        with force_locale(user_pref.language.value):
            for user_mission in user_missions:
                # 获取任务缓存数据
                cache_data = MissionCache.get_cache_data_by_ids([user_mission.mission_id])[user_mission.mission_id]
                # 构建标题
                title = MissionBiz.build_title(cache_data, is_simple=True)
                # 获取标题模板
                title_template = MissionBiz.get_title_template(cache_data["mission_condition"], is_simple=True)
                # 获取标题参数
                title_params = MissionBiz.get_build_title_params(cache_data)
                mis_params_list.append([title, title_template, title_params])
                
            if self.sender_type in [SenderType.MISSION_EXPIRING, SenderType.MIDDLE_EXPIRING]:
                sender.send_merge_expired_message(user_id)
            else:
                # 新任务通知的合并消息需要参数列表
                sender.send_merge_message(user_id, mis_params_list)
  
    def _filter_notified_missions(self, user_missions: List[UserMission]) -> List[UserMission]:
        """过滤已通知的任务"""
        filtered_missions = []
        if not self.notice_cache:
            raise ValueError("notice_cache is not set")
        for mission in user_missions:
            # 检查全局通知缓存
            if self.notice_cache.has_id(mission.id):
                continue           
            filtered_missions.append(mission)
        
        return filtered_missions


# ==================== 到期通知特殊处理器 ====================
class ExpiringNoticeProcessor(MissionMessageProcessor):
    """到期通知特殊处理器"""
    
    def __init__(self):
        super().__init__(SenderType.MISSION_EXPIRING)
        self.notice_cache = SendNoticeCache("mission_expiring")
    
    def process_expiring_notices(self) -> None:
        """处理到期通知"""
        # 查询即将到期的任务
        user_missions = self._query_expiring_missions()
        
        # 过滤已通知的任务
        user_missions = self._filter_notified_missions(user_missions)
        
        # 处理消息
        self.process_user_missions(user_missions)
    
    def _query_expiring_missions(self) -> List[UserMission]:
        """查询即将到期的任务"""
        min_days = 1
        max_days = 2
        user_missions = UserMission.query.filter(
            UserMission.expired_at >= now() + timedelta(days=min_days),
            UserMission.expired_at <= now() + timedelta(days=max_days),
        ).all()
        return [u for u in user_missions if u.status == UserMission.Status.PENDING]


class MiddleExpiringNoticeProcessor(MissionMessageProcessor):
    """中期到期通知特殊处理器"""
    
    def __init__(self):
        super().__init__(SenderType.MIDDLE_EXPIRING)
        self.notice_cache = SendNoticeCache("mission_middle_expiring")
    
    def process_middle_expiring_notices(self) -> None:
        """处理中期到期通知"""
        # 查询即将到期的任务
        user_missions = self._query_middle_expiring_missions()
        
        # 过滤已通知的任务
        user_missions = self._filter_notified_missions(user_missions)
        
        # 处理消息
        self.process_user_missions(user_missions)
        
    def _query_middle_expiring_missions(self) -> List[UserMission]:
        """查询即将到期的新手任务"""
        user_mission_ids = MonitorUserMissionCache.get_middle_expiring_missions()
        if not user_mission_ids:
            return []
        user_missions = []
        for batch_ids in batch_iter(user_mission_ids, 5000):
            user_missions.extend(UserMission.query.filter(
                UserMission.id.in_(batch_ids),
            ).all())
        newbie_user_missions = [u for u in user_missions if u.scene_type == SceneType.NEWBIE]
        user_ids = {u.user_id for u in newbie_user_missions}
        finished_user_ids = {
            u.user_id for u in UserMission.query.filter(
                UserMission.user_id.in_(user_ids),
                UserMission.status == UserMission.Status.FINISHED,
                UserMission.scene_type == SceneType.NEWBIE
            ).with_entities(UserMission.user_id).all()
        } 
        return [u for u in newbie_user_missions if u.user_id not in finished_user_ids]
        

# ==================== 主业务类 ====================
class MissionMessageBiz:
    """任务消息业务类"""
    
    @classmethod
    def send_new_mission_notice(cls, user_missions: List[UserMission]) -> None:
        """发送新任务通知"""
        processor = MissionMessageProcessor(SenderType.NEW_MISSION)
        processor.process_user_missions(user_missions)
    
    @classmethod
    def send_reward_sent_notice(cls, user_missions: List[UserMission]) -> None:
        """发送任务奖励通知"""
        processor = MissionMessageProcessor(SenderType.REWARD_SENT)
        processor.process_user_missions(user_missions)
    
    @classmethod
    def send_airdrop_reward_sent_notice(cls, business_ids: List[int]) -> None:
        """发送空投奖励通知"""
        user_missions = []
        for batch_ids in batch_iter(business_ids, 5000):
            user_missions.extend(UserMission.query.filter(
                UserMission.id.in_(batch_ids),
            ).all())
        cls.send_reward_sent_notice(user_missions)
    
    @classmethod
    def send_mission_expiring_notice(cls) -> None:
        """发送任务到期通知"""
        processor = ExpiringNoticeProcessor()
        processor.process_expiring_notices()
        
    @classmethod
    def send_newbie_middle_expiring_notice(cls) -> None:
        """新手任务中期到期通知"""
        processor = MiddleExpiringNoticeProcessor()
        processor.process_middle_expiring_notices()
        
