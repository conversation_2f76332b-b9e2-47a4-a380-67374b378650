from bson import ObjectId
from flask import current_app
from sqlalchemy import func

from app import Language
from app.api.common.request import is_old_app_request
from app.business import <PERSON>ache<PERSON><PERSON>, LockKeys, lock_call
from app.business.p2p.message import AddPayChannel
from app.business.user import get_users_kyc_name_map
from app.caches.p2p import PayChannelCache, CountrySuggestPayChannelCache, FiatPayChannelCache
from app.common import CeleryQueues
from app.exceptions import RecordNotFound, InvalidArgument
from app.exceptions.p2p import P2pExceptionMap, P2pExceptionCode
from app.models import db
from app.models.mongo.p2p.pay_channel import Status, UserPayChannelMySQL, P2pUserFeedbackChannelMySQL, \
    P2pPayChannelMySQL, \
    P2pFiatPayChannelMySQL, P2pCountrySuggestPayChannelMySQL, UserPayFormModel, FormModel
from app.utils import celery_task


class PayChannelBus:

    def __init__(self, lang: Language):
        self.lang = lang

    def get_channel_config_by_lang(self, channel_ids: list[ObjectId], inactive=False):
        return PayChannelCache.get_many(list(map(str, channel_ids)), self.lang, inactive=inactive)

    @classmethod
    def get_channel_color_mapper(cls, channel_ids: list[ObjectId]):
        mapper = PayChannelCache().get_many(list(map(str, channel_ids)))
        return {k: v['color'] for k, v in mapper.items()}

    def get_channel_name_by_ids(self, channel_ids: list[ObjectId]) -> dict[str, str]:
        channel_mapper = self.get_channel_config_by_lang(channel_ids, inactive=True)
        return {k: v['name'] for k, v in channel_mapper.items()}

    def get_all(self):
        return PayChannelCache.get_all(self.lang)

    def get_normal_channel_ids(self):
        return [
            v['id'] for k, v in self.get_all().items()
            if v["config_type"] == P2pPayChannelMySQL.ConfigType.NORMAL.name
        ]

    @classmethod
    def new_copy_data(cls, old_channel):
        new_pay_channel = P2pPayChannelMySQL()
        for field in old_channel.__table__.columns:
            field_key = field.key
            if field_key not in ("id", "mongo_id"):
                setattr(new_pay_channel, field_key, getattr(old_channel, field_key))
        new_pay_channel.active_status = P2pPayChannelMySQL.ActiveStatus.PENDING
        new_pay_channel.status = Status.VALID
        db.session.add(new_pay_channel)
        db.session.commit()
        return new_pay_channel

    @classmethod
    def get_inactive_pay_channels(cls, channel_ids: list[ObjectId]):
        active_pay_channels = PayChannelCache().get_many(channel_ids)
        inactive_pay_channels = set(channel_ids) - {i for i in active_pay_channels.keys()}
        return inactive_pay_channels

    @classmethod
    def check_lock(cls, pay_channel):
        if pay_channel.lock:
            raise InvalidArgument(message="请先解锁，锁定/解锁操作目前不对外开放，只开放给产品和测试负责人")


class UserPayChannelBus:
    VAL_LEN_LIMIT = 60

    def __init__(self, user_id: int):
        self.user_id = user_id

    def create(self, channel_id: ObjectId, form: list, alias: str, remark: str):
        """
        创建用户支付渠道
        form = {
            "1": "account_num",
            "2": "这是一个备注"
        }
        """
        # 要求前端做防重点击
        config = PayChannelCache().get_one(str(channel_id))
        if not config:
            raise RecordNotFound
        self.check_form(config, form)
        with CacheLock(LockKeys.create_user_pay_channel(self.user_id)):
            ret = UserPayChannelMySQL(
                user_id=self.user_id,
                pay_channel_id=str(channel_id),
                pay_form=form,
                alias=alias,
                remark=remark,
            )
            db.session.add(ret)
            db.session.commit()
        AddPayChannel().send_message(ret)
        return ret

    def update(self, item_id: ObjectId, form: list, alias: str, remark: str):
        """
        更新用户支付渠道
        """
        with CacheLock(LockKeys.update_user_pay_channel(self.user_id, str(item_id))):
            db.session.rollback()
            item = UserPayChannelMySQL.query.filter(
                UserPayChannelMySQL.mongo_id == str(item_id),
                UserPayChannelMySQL.user_id == self.user_id
            ).first()
            if not item:
                raise RecordNotFound
            config = PayChannelCache().get_one(item.pay_channel_id)
            if not config:
                raise RecordNotFound
            self.check_form(config, form)
            item.pay_form = [UserPayFormModel(**f) for f in form]
            item.alias = alias
            item.remark = remark
            db.session.commit()
            return item

    def delete(self, item_id: ObjectId):
        """
        删除用户支付渠道
        """
        from app.schedules.p2p.advertising import update_advertising_by_user_pay_channel_invalid_task
        with CacheLock(LockKeys.update_user_pay_channel(self.user_id, str(item_id))):
            db.session.rollback()
            item = UserPayChannelMySQL.query.filter(
                UserPayChannelMySQL.mongo_id == str(item_id),
                UserPayChannelMySQL.user_id == self.user_id
            ).first()
            if not item:
                raise RecordNotFound
            item.status = Status.INVALID
            db.session.commit()
            update_advertising_by_user_pay_channel_invalid_task.delay(str(item_id))

    @staticmethod
    def check_channel_active_valid(user_pay_channel):
        model = P2pPayChannelMySQL
        channel = model.query.filter(model.mongo_id == user_pay_channel.pay_channel_id).first()
        if channel.active_status != model.ActiveStatus.ACTIVE or channel.status != Status.VALID:
            raise P2pExceptionMap[P2pExceptionCode.PAY_CHANNEL_INVALID]

    def get_channel_ids_by_user_channel(self, user_channel_ids: list[ObjectId]) -> set[str]:
        user_channels = UserPayChannelMySQL.query.filter(
            UserPayChannelMySQL.mongo_id.in_([str(_id) for _id in user_channel_ids]),
            UserPayChannelMySQL.user_id == self.user_id
        ).with_entities(UserPayChannelMySQL.pay_channel_id).all()
        return {i.pay_channel_id for i in user_channels}

    def get_channel_map_list(self, user_channel_ids: list[ObjectId], is_active=True) -> list[dict[str, str]]:
        inactive_channel_ids = set()
        if is_active:
            inactive_channels = P2pPayChannelMySQL.query.filter(
                P2pPayChannelMySQL.status == Status.INVALID
            ).with_entities(P2pPayChannelMySQL.mongo_id).all()
            inactive_channel_ids = {i.mongo_id for i in inactive_channels}
        
        user_pay_channels = UserPayChannelMySQL.query.filter(
            UserPayChannelMySQL.mongo_id.in_([str(_id) for _id in user_channel_ids]),
            UserPayChannelMySQL.user_id == self.user_id
        ).with_entities(UserPayChannelMySQL.pay_channel_id, UserPayChannelMySQL.mongo_id).all()
        
        return [
            dict(
                pay_channel_id=i.pay_channel_id,
                user_pay_channel_id=i.mongo_id,
            ) for i in user_pay_channels if i.pay_channel_id not in inactive_channel_ids
        ]

    def get_user_pay_channels(self, pay_channel_id: ObjectId) -> set[str]:
        """用户支付渠道映射表"""
        query = UserPayChannelMySQL.query.filter(
            UserPayChannelMySQL.user_id == self.user_id,
            UserPayChannelMySQL.pay_channel_id == str(pay_channel_id)
        ).with_entities(UserPayChannelMySQL.mongo_id).all()
        return {i.mongo_id for i in query}

    def get_user_pay_channel_data(
            self,
            lang: Language,
            inactive=False,
            user_channel_ids=None,
            is_update_scenario=False
    ) -> list:
        """
        获取用户所有支付渠道
        :return:
        [
            {
                "channel_id": "111",
                "name": "visa",
                "form": [{
                    "key": "q213kh123",
                    "field_name": "account",
                    "value": "account-123"
                }]
            }
        ]
        """
        user_name = get_users_kyc_name_map([self.user_id]).get(self.user_id, "")
        new_data = []
        if user_channel_ids:
            items = UserPayChannelMySQL.get_by_ids([str(_id) for _id in user_channel_ids])
        else:
            items = UserPayChannelMySQL.get_user_all_channel(self.user_id)
        if not items:
            return new_data

        channel_ids = [i.pay_channel_id for i in items]
        base_config = PayChannelBus(lang).get_channel_config_by_lang(channel_ids, inactive)
        for item in items:
            cid = item.pay_channel_id
            # TODO 排查问题，待删除
            if not (base_info := base_config.get(str(cid))):
                current_app.logger.error(f"pay channel {str(cid)} not found")
                continue
            if new_item := self.format_channel(
                    base_info,
                    item.to_dict(enum_to_name=True),
                    user_name,
                    is_update_scenario
            ):
                new_item['alias'] = item.alias
                new_item['remark'] = item.remark
                new_data.append(new_item)
        return new_data

    def get_channel_by_id(self, user_channel_id: str, user_name, lang=Language.ZH_HANS_CN):
        item = UserPayChannelMySQL.get_by_id(user_channel_id)
        base_config = PayChannelCache.get_one(item.pay_channel_id, lang)
        return self.format_channel(base_config, item.to_dict(enum_to_name=True), user_name)

    def get_source_by_id(self, user_channel_id):
        return UserPayChannelMySQL.query.filter(
            UserPayChannelMySQL.mongo_id == user_channel_id,
            UserPayChannelMySQL.user_id == self.user_id
        ).first()

    @classmethod
    def format_channel(cls, base_info, item, user_name, is_update_scenario=False):
        if form := cls.format_forms(base_info["form"], item["pay_form"], is_update_scenario):
            is_need_name = base_info.get("is_need_name", False)
            new_item = {
                "channel_id": item["pay_channel_id"],
                "id": item["id"],
                "name": base_info["name"],
                "color": base_info["color"],
                "form": form,
                "is_need_name": is_need_name,
                "user_name": user_name if is_need_name else "",
                "active_status": base_info["active_status"]
            }
            return new_item
        return {}

    @classmethod
    def check_form(cls, config, val_forms: list) -> bool:
        return cls._check_form(cls, config["form"], val_forms)

    @staticmethod
    def _check_form(cls, config_forms, val_forms):
        # 检查是否存在无效字段
        valid_map = {i["key"]: i for i in config_forms if i["status"] == Status.VALID.name}
        form_map = {i["key"]: i["value"] for i in val_forms}
        if not set(form_map.keys()).issubset(valid_map.keys()):
            return False
        # 检查必填字段
        require_keys = [k for k, v in valid_map.items() if v["required"] is True]
        require_vals = [form_map.get(key) for key in require_keys]
        if not all(require_vals):
            return False
        # 检查输入的字符，目前只有一个规则，长度小于60个字符
        for vf in val_forms:
            config_vf = valid_map[vf['key']]
            if config_vf['filed_type'] != FormModel.FiledType.TEXT.name:
                continue
            if len(vf["value"]) > cls.VAL_LEN_LIMIT:
                return False
        return True

    @staticmethod
    def format_forms(base_forms: list, value_forms: list, is_update_scenario=False):
        new_form = []
        value_form_map = {i["key"]: i for i in value_forms}
        for form in base_forms:
            is_qrcode_filed = form['filed_type'] == FormModel.FiledType.QR_CODE.name
            if not (val_form := value_form_map.get(form["key"])):
                continue
            # 历史APP版本不展示二维码字段
            if is_qrcode_filed and is_old_app_request(4010, 102):
                continue
            if not (value := val_form.get("value")):
                continue
            tmp = {**form, "value": value}
            if is_qrcode_filed and is_update_scenario:
                tmp['file_url'] = val_form['file_url']
            new_form.append(tmp)
        return new_form

    def feedback_channel(self, data):
        """
        用户反馈其他渠道
        """
        config = P2pUserFeedbackChannelMySQL(**data)
        config.user_id = self.user_id
        db.session.add(config)
        db.session.commit()
        return config


@celery_task(queue=CeleryQueues.P2P)
@lock_call(with_args=True)
def pay_channel_invalid_remove_dependencies(pay_channel_id: str):
    """
    清理无效的支付渠道，同时清理依赖于该支付渠道的配置
    """
    pay_channel_id_str = str(pay_channel_id)
    
    fiat_config = P2pFiatPayChannelMySQL.query.filter(
        func.json_contains(P2pFiatPayChannelMySQL.pay_channel_ids, f'"{pay_channel_id_str}"')
    ).all()
    for c in list(fiat_config):
        pay_channel_ids = c.pay_channel_ids
        pay_channel_ids.remove(pay_channel_id_str)
        if not pay_channel_ids:
            db.session.delete(c)
        else:
            c.pay_channel_ids = pay_channel_ids
    
    db.session.commit()
    FiatPayChannelCache.reload()
    
    # 处理 P2pCountrySuggestPayChannel
    # 使用自定义SQL查询找到包含特定pay_channel_id的记录
    # 由于SQLAlchemy不支持直接查询JSON数组中的元素，需要使用原生SQL或在应用层过滤
    country_config = []
    all_configs = P2pCountrySuggestPayChannelMySQL.query.all()
    
    for config in all_configs:
        for fiat_data in config.fiat_data:
            if pay_channel_id_str in fiat_data.pay_channel_ids:
                country_config.append(config)
                break
    
    for c in list(country_config):
        new_fiat_data = []
        for fiat_data in c.fiat_data:
            # 只处理包含pay_channel_id的fiat_data项
            if pay_channel_id_str not in fiat_data.pay_channel_ids:
                new_fiat_data.append(fiat_data)
                continue
            pay_channel_ids = fiat_data.pay_channel_ids
            pay_channel_ids.remove(pay_channel_id_str)
            if not pay_channel_ids:
                continue
            fiat_data.pay_channel_ids = pay_channel_ids
            new_fiat_data.append(fiat_data)
        c.fiat_data = new_fiat_data
        if not new_fiat_data:
            db.session.delete(c)
    
    db.session.commit()
    CountrySuggestPayChannelCache.reload()
