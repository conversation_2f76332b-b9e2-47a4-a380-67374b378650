import json
import random
from decimal import Decimal
from enum import Enum
from typing import Any

from app import Language
from app.business import <PERSON>ache<PERSON>ock, LockKeys, UserPreferences
from app.business.p2p.config import p2p_setting
from app.business.p2p.margin import P2pUserMarginHistoryBiz
from app.business.p2p.message import BecomeMerchant, P2pMerchantCancelMessage, P2pMarginShortfallMessage
from app.business.p2p.permission import P2pPermissionManager
from app.business.p2p.utils import P2pUtils, check_margin_enough_balance
from app.business.user import UserRepository, is_user_real_time_active, get_user_kyc_country_map, UserSettings, \
    get_users_kyc_name_map
from app.business.user_status import P2pTPlusTChangeType
from app.common import get_country
from app.exceptions import InvalidArgument, TwoFactorAuthenticationRequired, NoKycQualifications, OperationNotAllowed
from app.exceptions.p2p import P2pExceptionMap, P2pExceptionCode
from app.models import db, User, P2pUserMargin, P2pMarginCountry, P2pOrder, P2pOrderComplaint
from app.models.mongo import AutoOfflineAdvReason
from app.models.user import P2pUser, P2pMerchant, P2pMerchantChangeLog, ImUser, UserStatusChangeHistory, UserExtra
from app.models.p2p import P2pUserTradeSummary, P2pUserTPlusNRecord, P2pFollowRelation
from app.utils import quantize_amount, now, current_timestamp


class P2pUserBatchManager:
    """p2p 用户批量操作"""

    @classmethod
    def batch_get_user_info(cls, user_ids: list[int]) -> dict:
        nickname_map = {
            user.id: user.nickname for user in User.query.filter(
                User.id.in_(user_ids),
            ).all()
        }
        account_name_map = {
            user.user_id: user.account_name for user in UserExtra.query.filter(
                UserExtra.user_id.in_(user_ids),
            ).all()
        }
        biz_user_id_map = P2pUtils.get_biz_user_id_map(user_ids)
        trade_summary_data_map = {
            trade_summary.user_id: dict(
                deal_count=trade_summary.deal_count,
            ) for trade_summary in P2pUserTradeSummary.query.filter(
                P2pUserTradeSummary.user_id.in_(user_ids),
            ).all()
        }
        batch_data = {}
        for user_id in user_ids:
            trade_summary_data = trade_summary_data_map.get(user_id, {})
            batch_data[user_id] = dict(
                nickname=nickname_map[user_id],
                account_name=account_name_map[user_id],
                im_user_id=biz_user_id_map[user_id],
                deal_count=trade_summary_data.get('deal_count', 0),
            )
        return batch_data


class P2pUserManger:
    """p2p 用户管理类，权限托管给 option"""

    def __init__(self, user_id: int):
        self.user = User.query.get(user_id)
        self.p2p_user: P2pUser = P2pUser.query.filter(P2pUser.user_id == user_id).first()
        self._option = self._modeling_option()

    def _modeling_option(self):
        if self.p2p_user and self.p2p_user.is_merchant:
            return P2pMerchantOption
        return P2pUserOption

    def check_is_merchant(self):
        return self.p2p_user and self.p2p_user.is_merchant

    def get_self_info(self, lang: Language):
        """获取用户自己的用户详情"""
        return self._option.get_self_info(self, lang)

    def get_public_user_info(self, lang: Language):
        """获取公共展示的用户信息(他人查看)"""
        return self._option.get_public_user_info(self, lang)

    def check_kyc_and_2fa(self):
        if not self.user.has_2fa:
            raise TwoFactorAuthenticationRequired
        if not (self.user.kyc_status == User.KYCStatus.PASSED or
                self.user.kyc_pro_status == User.KycProStatus.PASSED):
            raise NoKycQualifications

    def update_to_p2p_user(self):
        """升级为p2p用户"""
        return self._option.update_to_p2p_user(self)

    def update_to_merchant(self, kwargs: dict[str, str]):
        """升级为p2p买家"""
        return self._option.update_to_merchant(self, kwargs)

    def update_user_info(self, nickname: str, account_name: str = "", is_check_name=True):
        """
        修改p2p用户详情（暂时支持昵称）
        :param account_name: 账号名称
        :param nickname: 用户名称
        :param is_check_name: 是否检查用户名
        :return:
        """
        return self._option.update_user_info(self, nickname, account_name, is_check_name)

    def open_shop(self):
        """
        开启店铺
        :return:
        """
        return self._option.open_shop(self)

    def close_shop(self):
        """
        关闭店铺
        :return:
        """
        return self._option.close_shop(self)

    def check_cancel_merchant_step(self):
        """
        检查取消商家的步骤
        :return:
        """
        return self._option.check_cancel_merchant_step(self)

    def cancel_merchant(self):
        """
        取消商家身份
        :return:
        """
        return self._option.cancel_merchant(self)

    def payment_margin(self, amount: Decimal):
        """
        商家缴纳保证金
        :return:
        """
        user_id = self.user.id
        model = P2pMerchant
        with CacheLock(LockKeys.update_p2p_user(user_id)):
            db.session.rollback()
            margin = self._option.payment_margin(self, amount)
            row = model.query.filter(model.user_id == user_id).first()
            update_merchant_margin_status(row, margin.margin_pass)
            db.session.commit()

    def set_p2p_user(self, p2p_user: P2pUser):
        self.p2p_user = p2p_user

    def check_kyc_pro(self):
        if self.user.kyc_status != User.KYCStatus.PASSED:
            raise P2pExceptionMap[P2pExceptionCode.KYC_INVALID]
        pro_cond = self.user.kyc_pro_status == User.KycProStatus.PASSED
        ins_cond = self.user.kyc_institution
        if not (pro_cond or ins_cond):
            if not pro_cond:
                raise P2pExceptionMap[P2pExceptionCode.KYC_PRO_INVALID]
            if not ins_cond:
                raise P2pExceptionMap[P2pExceptionCode.KYC_INSTITUTION_INVALID]

    def check_merchant_can_order(self):
        model = P2pMerchant
        p2p_merchant: P2pMerchant = model.query.filter(model.user_id == self.user.id).first()
        if p2p_merchant.is_invalid:
            raise P2pExceptionMap[P2pExceptionCode.MERCHANT_OFFLINE]

    @classmethod
    def generate_nickname(cls):
        """User_ASD02245 User_为固定显示，后面随机三位大写字母和4位数字"""
        random_words = "".join([random.choice('ABCDEFGHIJKLMNOPQRSTUVWXYZ') for _ in range(3)])
        random_number = random.randint(1000, 9999)
        return f"User_{random_words}{random_number}"

    def update_user_permission(self):
        """更新用户权限"""
        P2pPermissionManager(self.p2p_user).update_new_user_permission()

    @classmethod
    def get_merchant_infos(cls, user_ids: list[int]):
        account_map = UserRepository.get_user_account_info_mapper(user_ids)
        model = P2pUser
        p2p_users = model.query.filter(model.user_id.in_(user_ids)).with_entities(
            model.user_id, model.biz_user_id).all()
        im_id_map = {i.user_id: i.biz_user_id for i in p2p_users}

        m_model = P2pUserMargin
        margin_map = {i.user_id: i for i in m_model.query.filter(m_model.user_id.in_(user_ids))}

        data = {}
        for user_id in user_ids:
            account_info = account_map[user_id]
            nickname = account_info['name']
            margin = margin_map[user_id]
            data[user_id] = dict(
                nickname=nickname,
                account_name=account_info['account_name'],
                avatar=account_info.get("avatar", ""),
                name_word=P2pUtils.gen_name_word(nickname),
                id=im_id_map.get(user_id),
                margin_enough=margin.margin_enough,
                paid_margin=margin.paid_margin
            )
        return data

    def get_order_info(self):
        from app.business.p2p.order import P2pOrderBiz
        user_id = self.user.id
        return dict(
            pending_count=P2pOrderBiz.get_user_active_order_count(user_id),
            historical_count=P2pOrderBiz.get_user_finished_order_count(user_id),
        )

    def get_merchant_trade_statistics(self):
        summary: P2pUserTradeSummary = P2pUserTradeSummary.query.filter(
            P2pUserTradeSummary.user_id == self.user.id
        ).first()
        if not summary:
            return {}
        return dict(
            deal_count=summary.deal_count,
            completion_rate=summary.completion_rate,
            acceptance_rate=summary.acceptance_rate,
            avg_payment_time=summary.avg_payment_time,
            avg_release_time=summary.avg_release_time,
        )

    def check_2fa(self):
        if not self.user.has_2fa:
            raise P2pExceptionMap[P2pExceptionCode.TWO_FA_INVALID]

    def check_mer_permission(self):
        setting = UserSettings(self.user.id)
        if not setting.withdrawals_sendable or not setting.login_enabled:
            raise P2pExceptionMap[P2pExceptionCode.PERMISSION_INVALID]

    def check_merchant_valid_status(self, is_margin=True):
        # 商家非 get 接口，需要检查前置要求
        model = P2pMerchant
        row = model.query.filter(model.user_id == self.user.id).first()
        if not row:
            raise P2pExceptionMap[P2pExceptionCode.OPENED_P2P_MERCHANT]
        self._option.check_inactive(row)
        self._option.check_cancel(row)
        self.check_2fa()
        self.check_kyc_pro()
        if not UserSettings(self.user.id).withdrawals_sendable:
            raise P2pExceptionMap[P2pExceptionCode.PERMISSION_INVALID]
        if is_margin:
            self._option.check_margin(row)


class P2pUserBaseOption:

    @staticmethod
    def get_self_info(option, lang: Language):
        raise NotImplementedError

    @staticmethod
    def update_to_p2p_user(option):
        raise NotImplementedError

    @staticmethod
    def update_to_merchant(option, kwargs: dict[str, str]):
        raise NotImplementedError

    @staticmethod
    def update_user_info(option, nickname: str, account_name: str, is_check_name: bool):
        raise NotImplementedError

    @staticmethod
    def open_shop(option):
        raise NotImplementedError

    @staticmethod
    def close_shop(option):
        raise NotImplementedError

    @staticmethod
    def check_cancel_merchant_step(option):
        raise NotImplementedError

    @staticmethod
    def cancel_merchant(option):
        raise NotImplementedError

    @staticmethod
    def payment_margin(option, amount: Decimal):
        raise NotImplementedError

    @classmethod
    def get_finished_order_data(cls, user_id):
        """
        获取用户总成交数据
        - 成交数
        - 成交额
        - 完单率
        - 平均付款时间
        - 平均放币时间
        """
        p2p_user_summary: P2pUserTradeSummary = P2pUserTradeSummary.query.filter(
            P2pUserTradeSummary.user_id == user_id
        ).first()
        decimal_zero = Decimal()
        return dict(
            deal_count=p2p_user_summary.deal_count if p2p_user_summary else 0,
            deal_amount=quantize_amount(p2p_user_summary.deal_amount, 2) if p2p_user_summary else decimal_zero,
            completion_rate=p2p_user_summary.completion_rate if p2p_user_summary else decimal_zero,
            avg_payment_time=p2p_user_summary.avg_payment_time if p2p_user_summary else 0,
            avg_release_time=p2p_user_summary.avg_release_time if p2p_user_summary else 0
        )

    @classmethod
    def _get_location_name(cls, code, lang) -> str:
        country = get_country(code)
        if not country:
            return ""
        if lang == Language.ZH_HANS_CN:
            return country.cn_name
        return country.en_name

    @classmethod
    def _get_user_t_plus_n_days(cls, user: User) -> int:
        t_plus_n_record = P2pUserTPlusNRecord.query.filter(
            P2pUserTPlusNRecord.user_id == user.id,
        ).first()
        t_plus_n_days = t_plus_n_record.real_days if t_plus_n_record else None
        return t_plus_n_days

    @staticmethod
    def get_public_user_info(option, lang: Language) -> dict[str: Any]:
        """
        获取可展示的用户数据（公共部分）
        1. 昵称
        2. 所在地区
        3. 注册日期
        4. 总成交订单数
        5. 总成交额
        6. 平均付款时间
        7. 平均放币时间
        8. 完单率
        """
        p2p_user = option.p2p_user
        user = option.user
        account_info = UserRepository.get_user_account_info(user)
        nickname = account_info['name']
        user_id = user.id
        country = get_user_kyc_country_map([user_id]).get(user_id, "")
        require_margin = P2pUserBaseOption.get_require_margin(user_id, country)
        return dict(
            nickname=nickname,
            account_name=account_info['account_name'],
            avatar=account_info['avatar'],
            name_word=P2pUtils.gen_name_word(nickname),
            location_code=country,
            is_merchant=p2p_user and p2p_user.is_merchant,
            location_name=P2pUserBaseOption._get_location_name(country, lang),
            registration_date=user.created_at.date(),
            im_user_id=p2p_user.biz_user_id,
            p2p_trade_fiat=P2pUtils.get_user_usdt_trade_fiat(user),
            p2p_question=UserPreferences(user.id).p2p_question,
            t_plus_n_days=P2pUserBaseOption._get_user_t_plus_n_days(user),
            require_margin=require_margin,
            **P2pUserBaseOption.get_finished_order_data(user.id)
        )

    @staticmethod
    def get_require_margin(user_id, country):
        margin = P2pUserMargin.get_user_row(user_id)
        if margin:
            return margin.require_margin
        else:
            if country:
                return P2pMarginCountry.get_country_margin(country)
            else:
                return p2p_setting.p2p_margin_amount


class KycLevel(Enum):
    PRIMARY = "primary"  # 初级
    SENIOR = "senior"  # 高级
    INSTITUTION = "institution"  # 机构


class P2pUserOption(P2pUserBaseOption):
    """p2p用户管理组件"""

    @staticmethod
    def update_to_p2p_user(option):
        user_id = option.user.id
        option.check_kyc_and_2fa()
        pref = UserPreferences(user_id)
        if p2p_user := option.p2p_user:
            pref.opening_p2p_function = True
            return p2p_user
        if not option.user.name:
            option.update_user_info(option.user.nickname, is_check_name=False)
        with CacheLock(LockKeys.update_p2p_user(user_id)):
            db.session.rollback()
            model = P2pUser
            if p2p_user := model.query.filter(model.user_id == user_id).first():
                pref.opening_p2p_function = True
                return p2p_user
            p2p_user = P2pUser(user_id=user_id)
            db.session.add(p2p_user)
            im_user_row = ImUser.get_or_create(user_id=user_id, user_type=ImUser.UserType.P2P, is_commit=False)
            p2p_user.biz_user_id = im_user_row.im_user_id
            P2pUserOption.add_t_plus_data(option)
            db.session.commit()
        pref.opening_p2p_function = True
        option.set_p2p_user(p2p_user)
        option.update_user_permission()
        return option.p2p_user

    @staticmethod
    def add_t_plus_data(option):
        t_plus_n_row = P2pUserTPlusNRecord.query.filter(P2pUserTPlusNRecord.user_id == option.user.id).first()
        if not t_plus_n_row:
            t_plus_n_row = P2pUserTPlusNRecord(
                user_id=option.user.id,
                global_setting_days=p2p_setting.t_plus_n_days,
                real_rule=P2pUserTPlusNRecord.GLOBAL_SETTING_RULE_ID,
                real_days=p2p_setting.t_plus_n_days,
            )
            db.session.add(t_plus_n_row)

            current_ts = current_timestamp(to_int=True)
            new = t_plus_n_row.to_dict()
            new['created_at'] = new['updated_at'] = current_ts
            user_history = UserStatusChangeHistory(
                user_id=option.user.id,
                type=UserStatusChangeHistory.Type.P2P_T_PLUS_N.name,
                action=P2pTPlusTChangeType.UPDATE.name,
                detail=json.dumps(dict(
                    old={},
                    new=new
                ))
            )
            db.session.add(user_history)

    @staticmethod
    def get_self_info(option, lang: Language):
        """
        获取用户基础信息
        - 昵称
        - 头像
        - KYC等级
        - 所属地区
        - 注册日期
        - 用户类型
        :return:
        """
        base_info = P2pUserOption.get_public_user_info(option, lang)
        return base_info

    @classmethod
    def _get_user_level(cls, user: User):
        # 依次返回 机构 高级 初级
        is_kyc_pass = user.kyc_status == User.KYCStatus.PASSED
        if user.kyc_institution and is_kyc_pass:
            return KycLevel.INSTITUTION
        if user.kyc_pro_status == User.KycProStatus.PASSED:
            return KycLevel.SENIOR
        if user.kyc_verification or is_kyc_pass:
            return KycLevel.PRIMARY

    @staticmethod
    def get_public_user_info(option, lang: Language) -> dict[str: Any]:
        """
        用户展示详情:
        - kyc 等级
        """
        base_info = super(P2pUserOption, P2pUserOption).get_public_user_info(option, lang)
        base_info['kyc_level'] = P2pUserOption._get_user_level(option.user)
        user_id = option.user.id
        base_info['kyc_name'] = get_users_kyc_name_map([user_id]).get(user_id, "")
        return base_info

    @staticmethod
    def update_user_info(option, nickname: str, account_name: str, is_check_name: bool):
        UserRepository.update_user_name(
            option.user,
            nickname,
            auto_commit=False,
            is_check_name=is_check_name
        )
        if account_name:
            UserRepository.update_user_account_name(option.user, account_name, auto_commit=False)
        db.session.commit()
        return option.p2p_user

    @staticmethod
    def update_to_merchant(option, kwargs: dict[str, str]):
        """
        升级为商家
        :return:
        """
        option.check_kyc_pro()
        p2p_user = option.p2p_user
        if not p2p_user:
            p2p_user = option.update_to_p2p_user()
        m_model = P2pMerchant
        with CacheLock(LockKeys.update_p2p_user(option.user.id)):
            db.session.rollback()
            mer = m_model.query.filter(m_model.user_id == p2p_user.user_id).first()
            P2pMerchantOption.check_update_status(mer)

            margin = P2pUserOption.create_merchant_margin(option.user)
            db.session_add_and_commit(margin)
            new_margin = P2pMerchantOption.payment_margin(option, margin.missing_margin)

            mer = m_model.get_or_create(user_id=p2p_user.user_id)
            update_merchant_margin_status(mer, new_margin)
            db.session_add_and_flush(mer)
            p2p_user_update_merchant(p2p_user, mer)
            db.session.commit()
        option.set_p2p_user(p2p_user)
        BecomeMerchant().send_message(option.user)
        return mer

    @staticmethod
    def create_merchant_margin(user):
        user_id = user.id
        margin = P2pUserMargin.get_or_create(user_id=user_id)
        # 初始化
        margin.grace_source = P2pUserMargin.GraceSource.INCR
        margin.grace_deadline = now()

        country = get_user_kyc_country_map([user_id]).get(user_id)
        if margin.margin_type != P2pUserMargin.MarginType.PERSON:
            if country:
                margin.require_margin = P2pMarginCountry.get_country_margin(country)
            else:
                # admin 直接通过的 kyc 用户，取兜底保证金额度，开启个人保证金模式
                margin.margin_type = P2pUserMargin.MarginType.PERSON
                margin.require_margin = p2p_setting.p2p_margin_amount
        check_margin_enough_balance(user.id, margin.require_margin, P2pMarginCountry.Asset.USDT.name)
        db.session.add(margin)
        return margin


class P2pMerchantOption(P2pUserOption):
    """p2p商家管理组件"""

    @staticmethod
    def get_self_info(option, lang: Language):
        info = P2pMerchantOption.get_public_user_info(option, lang)
        merchant = P2pMerchant.query.filter(
            P2pMerchant.user_id == option.user.id
        ).first()
        info['shop_status'] = merchant.shop_status.name
        info['status'] = merchant.status.name
        info['margin_status'] = merchant.margin_status.name
        return info

    @staticmethod
    def get_margin_info(user_id):
        model = P2pUserMargin
        row = model.query.filter(model.user_id == user_id).first()
        info = dict()
        for field in ["require_margin", "paid_margin", "grace_deadline"]:
            info[field] = getattr(row, field, "")
        info["grace_source"] = row.grace_source.name
        info["margin_enough"] = row.margin_enough
        return info

    @staticmethod
    def get_public_user_info(option, lang: Language) -> dict[str: Any]:
        """
        商家信息:
        1. 成为商家日期
        2. 接单率
        """
        summary = P2pUserTradeSummary.query.filter(
            P2pUserTradeSummary.user_id == option.user.id
        ).first()
        acceptance_rate = summary.acceptance_rate if summary else Decimal()
        updated_merchant_date = P2pMerchant.query.filter(
            P2pMerchant.user_id == option.user.id
        ).with_entities(
            P2pMerchant.created_at
        ).scalar() or None
        follow_count = P2pFollowRelation.query.filter(
            P2pFollowRelation.target_user_id == option.user.id,
        ).count()
        base_info = super(P2pMerchantOption, P2pMerchantOption).get_public_user_info(option, lang)

        base_info["updated_merchant_date"] = updated_merchant_date
        base_info['acceptance_rate'] = acceptance_rate
        base_info['is_active'] = is_user_real_time_active(option.user.id)
        base_info['follow_count'] = follow_count
        base_info.update(
            **P2pMerchantOption.get_margin_info(option.user.id),
        )
        return base_info

    @staticmethod
    def open_shop(option):
        from app.business.p2p.advertising import P2pAdvertisingBiz
        user_id = option.user.id
        with CacheLock(LockKeys.update_p2p_user(option.user.id)):
            db.session.rollback()
            p2p_merchant = P2pMerchant.query.filter(P2pMerchant.user_id == user_id).first()
            if not p2p_merchant or p2p_merchant.shop_status != P2pMerchant.ShopStatus.CLOSED:
                raise InvalidArgument(message="店铺状态错误")
            p2p_merchant.shop_status = P2pMerchant.ShopStatus.OPEN
            P2pMerchantChangeLog.save_shop_status_change_log(
                user_id=user_id,
                change_before=P2pMerchant.ShopStatus.CLOSED.name,
                change_after=P2pMerchant.ShopStatus.OPEN.name
            )
            db.session.commit()
            P2pAdvertisingBiz.flush_advertising_updated_at_by_merchant(user_id)

    @staticmethod
    def close_shop(option):
        user_id = option.user.id
        with CacheLock(LockKeys.update_p2p_user(option.user.id)):
            db.session.rollback()
            p2p_merchant = P2pMerchant.query.filter(P2pMerchant.user_id == user_id).first()
            if not p2p_merchant or p2p_merchant.shop_status != P2pMerchant.ShopStatus.OPEN:
                return
            p2p_merchant.shop_status = P2pMerchant.ShopStatus.CLOSED
            P2pMerchantChangeLog.save_shop_status_change_log(
                user_id=user_id,
                change_before=P2pMerchant.ShopStatus.OPEN.name,
                change_after=P2pMerchant.ShopStatus.CLOSED.name
            )
            db.session.commit()

    @staticmethod
    def cancel_merchant(option):
        # 取消身份，退还保证金
        model = P2pMerchant
        user_id = option.user.id
        """
        1. 检查未完成订单
        2. 检查未完成申诉
        3. 检查未收到风控
        4. 定时任务 48 小时后退款
        """
        check, _ = P2pMerchantOption.check_cancel_merchant_step(option)
        if not check:
            raise OperationNotAllowed
        P2pMerchantOption.close_shop(option)
        P2pUtils.offline_advertising_by_merchant(user_id, reason=AutoOfflineAdvReason.CANCEL_MERCHANT)
        with CacheLock(LockKeys.update_p2p_user(user_id)):
            db.session.rollback()
            row: P2pMerchant = model.query.filter(model.user_id == user_id).first()
            P2pMerchantOption.check_cancel_and_active(row)
            row.status = model.Status.CANCELING
            row.canceling_time = now()
            db.session.commit()
        P2pMerchantCancelMessage().send_message(user_id)

    @staticmethod
    def payment_margin(option, amount):
        user_id = option.user.id
        user_margin = P2pUserMargin.get_user_row(user_id)
        if user_margin.margin_enough and user_margin.require_margin > Decimal():
            raise P2pExceptionMap[P2pExceptionCode.PAID_MARGIN]
        P2pUserMarginHistoryBiz.payment(user_id, amount)
        return user_margin

    @staticmethod
    def check_cancel_and_active(merchant):
        P2pMerchantOption.check_inactive(merchant)
        P2pMerchantOption.check_cancel(merchant)

    @staticmethod
    def check_update_status(mer):
        if not mer or mer.status == P2pMerchant.Status.CANCELED:
            return
        match mer.status:
            case P2pMerchant.Status.CANCELING:
                raise P2pExceptionMap[P2pExceptionCode.MERCHANT_CANCELING]
            case P2pMerchant.Status.ACTIVE:
                raise P2pExceptionMap[P2pExceptionCode.OPENED_P2P_MERCHANT]
        P2pMerchantOption.check_inactive(mer)

    @staticmethod
    def check_inactive(merchant):
        if merchant.is_inactive:
            raise P2pExceptionMap[P2pExceptionCode.FROZEN_MERCHANT]

    @staticmethod
    def check_cancel(merchant):
        if merchant.is_cancel:
            raise P2pExceptionMap[P2pExceptionCode.MERCHANT_CANCELED]

    @staticmethod
    def check_margin(merchant):
        if merchant.margin_status == P2pMerchant.MarginStatus.INVALID:
            raise P2pExceptionMap[P2pExceptionCode.NO_MARGIN]

    @staticmethod
    def check_cancel_merchant_step(option):
        user_id = option.user.id
        model = P2pMerchant
        mer: P2pMerchant = model.query.filter(model.user_id == user_id).first()

        error = {
            "has_order": False,
            "has_complaint": False,
            "has_risk": False,
        }

        o_model = P2pOrder
        o_row = o_model.get_user_query(user_id).filter(
            o_model.status.notin_([
                o_model.Status.FINISHED,
                o_model.Status.CANCELED,
            ])
        ).first()
        if o_row:
            error["has_order"] = True

        c_model = P2pOrderComplaint
        c_row = c_model.get_user_query(user_id).filter(
            c_model.complaint_status.in_([
                c_model.Status.CREATED,
                c_model.Status.PENDING,
            ])
        ).first()
        if c_row:
            error["has_complaint"] = True

        if not P2pPermissionManager.check_all_permission(user_id) or mer.is_inactive:
            error["has_risk"] = True

        # 存在异常
        ret = not any(error.values())
        return ret, error


def update_merchant_margin_status(mer_row, margin_pass: bool):
    new_status = P2pMerchant.trans_margin_status(margin_pass)
    if mer_row.margin_status != new_status:
        mer_row.margin_status = new_status
        return True
    return False


def no_margin_merchant_operate(user_id, grace_deadline):
    P2pUserManger(user_id).close_shop()
    P2pUtils.offline_advertising_by_merchant(user_id, reason=AutoOfflineAdvReason.NO_MARGIN)
    P2pMarginShortfallMessage().send_message(user_id, grace_deadline)


def p2p_user_update_merchant(p2p_user, mer):
    user_id = p2p_user.user_id
    p2p_user.merchant_id = mer.id
    mer.status = P2pMerchant.Status.ACTIVE
    UserPreferences(user_id).opening_p2p_merchant_function = True

