from collections import defaultdict
from datetime import date, timedelta
from decimal import Decimal
from typing import Optional

from flask import current_app
from sqlalchemy import func
from werkzeug.datastructures import MultiDict

from app.business import ExchangeLogDB
from app.business.user_tag import check_user_tag_finished, TagReader
from app.business.user_tag.handlers.base import (
    TagHandler,
    update_single_user_tag_data, SimpleTagSaveMixin, delete_or_update_user_tag_data,
)
from app.business.user_tag.helper import (
    data_dumps, get_sub_account_mapping, get_disabled_user_ids,
    get_all_user_ids, yield_get_user_latest_data, yield_get_user_first_data,
)
from app.business.utils import (
    yield_query_records_by_time_range,
    yield_query_records_slice_from_user_id,
)
from app.common import PrecisionEnum
from app.models import (
    UserTradeSummary, db, MarketMaker, VipUser, Ambassador,
    UserActivenessHistory, ReferralHistory, UserTradeFeeSummary, AssetP<PERSON>, User, UserExchangeSummary,
)
from app.models.base import read_only_session
from app.models.exchange import AssetExchangeOrder
from app.models.referral import Referral
from app.models.user_tag import (
    UserTag
)
from app.utils import batch_iter, quantize_amount, timestamp_to_date, group_by
from app.utils.date_ import date_to_datetime, today_datetime, now


class UserLevelHandler(TagHandler):

    read_slot = frozenset([17])
    write_slot = frozenset([17])

    impl_tags = {
        UserTag.VIP_LEVEL,
        UserTag.AMBASSADOR_LEVEL,
        UserTag.MARKET_MAKER_LEVEL
    }

    def flush(self, report_date: Optional[date]):
        disabled_user_ids = get_disabled_user_ids()
        sub_user_mapping = get_sub_account_mapping()
        all_user_ids = get_all_user_ids()
        insert_user_ids = all_user_ids - disabled_user_ids - set(sub_user_mapping.keys())

        vip_q = VipUser.query.filter(VipUser.level > 0,
                                     VipUser.status == VipUser.StatusType.PASS
                                     ).with_entities(
            VipUser.user_id,
            VipUser.level
        ).all()
        ambassador_q = Ambassador.query.filter(Ambassador.status == Ambassador.Status.VALID).all()
        market_maker_q = MarketMaker.query.filter(MarketMaker.level > 0,
                                                  MarketMaker.status == MarketMaker.StatusType.PASS).with_entities(
            MarketMaker.user_id,
            MarketMaker.level
        ).all()
        flush_data = {
            _tag: {
                _uid: 0
                for _uid in insert_user_ids
            }
            for _tag in (UserTag.VIP_LEVEL, UserTag.MARKET_MAKER_LEVEL)
        }
        flush_data[UserTag.AMBASSADOR_LEVEL] = dict()
        for v in vip_q:
            user_id = sub_user_mapping.get(v.user_id, v.user_id)
            flush_data[UserTag.VIP_LEVEL][user_id] = v.level
        for v in ambassador_q:
            user_id = sub_user_mapping.get(v.user_id, v.user_id)
            flush_data[UserTag.AMBASSADOR_LEVEL][user_id] = v.level.name
        for v in market_maker_q:
            user_id = sub_user_mapping.get(v.user_id, v.user_id)
            flush_data[UserTag.MARKET_MAKER_LEVEL][user_id] = v.level
        # convert tag enum to str
        convert_data = {
            _tag.name: {_user_id: data_dumps(_value) for _user_id, _value in _all_tag_data.items()}
            for _tag, _all_tag_data in flush_data.items()
        }
        return convert_data

    def save(self, report_date: Optional[date]):
        new_data = self.flush(report_date)
        for tag_name, tag_data in new_data.items():
            update_single_user_tag_data(tag_name, tag_data)
        if report_date:
            self.mark_finished(report_date)


class UserActivenessHandler(TagHandler):

    priority = 995

    read_slot = frozenset([18])
    write_slot = frozenset([18])

    impl_tags = {
        UserTag.INACTIVE_DAYS,
        UserTag.USER_30D_ACTIVE_DAYS,
        UserTag.LATEST_ACTIVE_TIME,
    }

    def save(self, report_date: Optional[date]):
        sub_user_mapping = get_sub_account_mapping()

        active_user_data = defaultdict(lambda: UserActivenessHistory.EPOCH)

        def get_recent_30days_index_condition():
            yesterday = now().date() - timedelta(days=1)
            start_date = yesterday - timedelta(days=30)
            tmp_date = start_date
            query_condition = []
            while tmp_date <= yesterday:
                _index, _offset = UserActivenessHistory.date_to_offset(tmp_date)
                query_condition.append((_index, _offset))
                tmp_date += timedelta(days=1)
            return MultiDict(query_condition)
        index_mapping = get_recent_30days_index_condition()
        recent_30days_result = defaultdict(set)
        for _record in yield_query_records_slice_from_user_id(
                UserActivenessHistory,
                [
                    UserActivenessHistory.user_id,
                    UserActivenessHistory.index,
                    UserActivenessHistory.flags],
                UserActivenessHistory.index.in_(list(index_mapping.keys())),
            ):
            main_user_id = sub_user_mapping.get(_record.user_id, _record.user_id)
            offsets = index_mapping.getlist(_record.index)
            for offset in offsets:
                if _record.flags & (1 << offset):
                    recent_30days_result[main_user_id].add(
                        UserActivenessHistory.index_offset_to_date(_record.index, offset))

        inactive_user_data = defaultdict(int)
        for record in yield_get_user_latest_data(
                UserActivenessHistory,
                [UserActivenessHistory.user_id, UserActivenessHistory.index,
                UserActivenessHistory.flags]):
            main_user_id = sub_user_mapping.get(record["user_id"], record["user_id"])
            _date = max(UserActivenessHistory.index_flags_to_dates(record["index"], record["flags"]))
            active_user_data[main_user_id] = max(active_user_data[main_user_id], _date)
            inactive_user_data[main_user_id] = max((report_date - active_user_data[main_user_id]).days, 0)

        insert_records = []
        write_model = self.get_write_model()
        for user_id, data in active_user_data.items():
            insert_records.append(
                write_model(
                    user_id=user_id,
                    tag=UserTag.LATEST_ACTIVE_TIME.name,
                    value=data_dumps(data)
                )
            )
        for user_id, data in inactive_user_data.items():
            insert_records.append(
                write_model(
                    user_id=user_id,
                    tag=UserTag.INACTIVE_DAYS.name,
                    value=data_dumps(data)
                )
            )
        for user_id, data in recent_30days_result.items():
            insert_records.append(
                write_model(
                    user_id=user_id,
                    tag=UserTag.USER_30D_ACTIVE_DAYS.name,
                    value=data_dumps(len(data))
                )
            )
        write_model.query.filter(write_model.tag.in_([_tag.name for _tag in self.impl_tags])).delete(
            synchronize_session=False)
        for objs in batch_iter(insert_records, 5000):
            db.session.bulk_save_objects(objs)
        db.session.commit()
        self.mark_finished(report_date)


class ReferralDataHandler(TagHandler):

    read_slot = frozenset([4])
    write_slot = frozenset([4])

    impl_tags = {
        UserTag.REFER_TRADE_USER_7D_COUNT,
        UserTag.REFER_TRADE_USER_30D_COUNT,
        UserTag.REFER_TRADE_USER_90D_COUNT,
        UserTag.REFER_USER_7D_COUNT,
        UserTag.REFER_USER_30D_COUNT,
        UserTag.REFER_USER_90D_COUNT,
        UserTag.REFER_USER_7D_TRADE_USD,
        UserTag.REFER_USER_30D_TRADE_USD,
        UserTag.REFER_USER_90D_TRADE_USD,
        UserTag.LATEST_REFERER_REGISTER_TIME,
        UserTag.FIRST_REFERER_REGISTER_TIME,
        UserTag.REFERER_ID,
        UserTag.REFERER_CODE,
        UserTag.TOTAL_REFER_USER_COUNT,
        UserTag.TOTAL_REFER_TRADE_USER_COUNT,
    }

    def save(self, report_date: Optional[date]):
        start = report_date - timedelta(days=90)
        reffree_mapping = dict()
        count_data = defaultdict(lambda: defaultdict(int))
        trade_count_data = defaultdict(lambda: defaultdict(set))
        trade_usd_data = defaultdict(lambda: defaultdict(Decimal))
        disabled_user_ids = get_disabled_user_ids()
        for record in yield_query_records_by_time_range(
            ReferralHistory,
            date_to_datetime(start),
            date_to_datetime(report_date),
            [ReferralHistory.created_at,
             ReferralHistory.referrer_id,
             ReferralHistory.referree_id]
        ):
            _date = record.created_at.date()
            reffree_mapping[record.referree_id] = record.referrer_id
            if record.referrer_id in disabled_user_ids:
                continue
            if _date >= report_date - timedelta(days=7):
                count_data[UserTag.REFER_USER_7D_COUNT][record.referrer_id] += 1
            if _date >= report_date - timedelta(days=30):
                count_data[UserTag.REFER_USER_30D_COUNT][record.referrer_id] += 1
            count_data[UserTag.REFER_USER_90D_COUNT][record.referrer_id] += 1

        trade_data = defaultdict(lambda: defaultdict(Decimal))
        for user_ids in batch_iter(list(reffree_mapping.keys()), 3000):
            filter_user_ids = set(user_ids) - disabled_user_ids
            rows = UserTradeSummary.query.filter(
                UserTradeSummary.user_id.in_(filter_user_ids),
                UserTradeSummary.report_date >= report_date - timedelta(days=90),
                UserTradeSummary.report_date <= report_date,
            ).with_entities(UserTradeSummary.user_id,
                            UserTradeSummary.report_date,
                            UserTradeSummary.trade_amount,
                            ).all()
            for row in rows:
                trade_data[row.user_id][row.report_date] += row.trade_amount

        for user_id, _data in trade_data.items():
            for _date, usd in _data.items():
                if _date >= report_date - timedelta(days=7):
                    trade_count_data[UserTag.REFER_TRADE_USER_7D_COUNT][reffree_mapping[user_id]].add(user_id)
                    trade_usd_data[UserTag.REFER_USER_7D_TRADE_USD][reffree_mapping[user_id]] += usd
                if _date >= report_date - timedelta(days=30):
                    trade_count_data[UserTag.REFER_TRADE_USER_30D_COUNT][reffree_mapping[user_id]].add(user_id)
                    trade_usd_data[UserTag.REFER_USER_30D_TRADE_USD][reffree_mapping[user_id]] += usd

                trade_count_data[UserTag.REFER_TRADE_USER_90D_COUNT][reffree_mapping[user_id]].add(user_id)
                trade_usd_data[UserTag.REFER_USER_90D_TRADE_USD][reffree_mapping[user_id]] += usd

        daily_price_map = AssetPrice.get_close_price_range_map(
            start_date=report_date - timedelta(days=90),
            end_date=report_date)
        exchange_datas = defaultdict(Decimal)
        sub_user_mapping = get_sub_account_mapping()
        for _order in yield_query_records_by_time_range(
            AssetExchangeOrder,
            date_to_datetime(report_date - timedelta(days=90)),
            date_to_datetime(report_date),
            [AssetExchangeOrder.created_at,
             AssetExchangeOrder.user_id,
             AssetExchangeOrder.source_asset,
             AssetExchangeOrder.target_asset,
             AssetExchangeOrder.target_asset_exchanged_amount]
        ):

            main_user_id = sub_user_mapping.get(_order.user_id, _order.user_id)
            _date = _order.created_at.date()

            exchange_datas[(main_user_id, f"{_order.source_asset}{_order.target_asset}", _date)] \
                = _order.target_asset_exchanged_amount * \
              daily_price_map[_date].get(_order.target_asset, Decimal())

        for key, usd in exchange_datas.items():
            user_id, market, order_report_date = key
            if user_id not in reffree_mapping.keys() or user_id in disabled_user_ids:
                continue

            if order_report_date >= report_date - timedelta(days=7):
                trade_count_data[UserTag.REFER_TRADE_USER_7D_COUNT][reffree_mapping[user_id]].add(user_id)
                trade_usd_data[UserTag.REFER_USER_7D_TRADE_USD][reffree_mapping[user_id]] += usd
            if order_report_date >= report_date - timedelta(days=30):
                trade_count_data[UserTag.REFER_TRADE_USER_30D_COUNT][reffree_mapping[user_id]].add(user_id)
                trade_usd_data[UserTag.REFER_USER_30D_TRADE_USD][reffree_mapping[user_id]] += usd
            trade_count_data[UserTag.REFER_TRADE_USER_90D_COUNT][reffree_mapping[user_id]].add(
                user_id)
            trade_usd_data[UserTag.REFER_USER_90D_TRADE_USD][reffree_mapping[user_id]] += usd

        all_referee_mapping = dict()
        all_referral_id_mapping = dict()
        all_referral_code_mapping = dict()

        for _record in yield_query_records_by_time_range(
            ReferralHistory,
            None,
            None,
            [ReferralHistory.referrer_id, ReferralHistory.referree_id, ReferralHistory.referral_id],
            None,
            30000
        ):
            all_referee_mapping[_record.referree_id] = _record.referrer_id
            all_referral_id_mapping[_record.referree_id] = _record.referral_id

        for _record in yield_query_records_by_time_range(
            Referral,
            None,
            None,
            [Referral.code, Referral.id],
            None,
            30000
        ):
            all_referral_code_mapping[_record.id] = _record.code

        referee_user_ids_data = defaultdict(set)
        referee_trade_user_ids_data = defaultdict(set)
        all_trade_user_ids = {v.uid
                              for v in UserTradeSummary.query.with_entities(
                                UserTradeSummary.user_id.distinct().label('uid')
                              ).all()}
        all_exchange_user_ids = {
            sub_user_mapping.get(v.uid, v.uid)
            for v in AssetExchangeOrder.query.with_entities(
                AssetExchangeOrder.user_id.distinct().label("uid"))
        }
        referee_trade_user_ids = (all_trade_user_ids | all_exchange_user_ids) & set(all_referee_mapping.keys()) \
                                 - disabled_user_ids
        for _referee_id, _referrer_id in all_referee_mapping.items():
            if _referee_id in disabled_user_ids or _referee_id in disabled_user_ids:
                continue
            referee_user_ids_data[_referrer_id].add(_referee_id)
        for referee_trade_user_id in referee_trade_user_ids:
            _referrer_id = all_referee_mapping[referee_trade_user_id]
            if _referrer_id in disabled_user_ids:
                continue
            referee_trade_user_ids_data[_referrer_id].add(referee_trade_user_id)

        insert_records = []
        write_model = self.get_write_model()


        for _referee_id, _user_ids in referee_user_ids_data.items():
            if _referee_id in disabled_user_ids:
                continue
            insert_records.append(
                write_model(
                    user_id=_referee_id,
                    tag=UserTag.TOTAL_REFER_USER_COUNT.name,
                    value=data_dumps(len(_user_ids))
                )
            )
        for _referrer_id, _user_ids in referee_trade_user_ids_data.items():
            if _referrer_id in disabled_user_ids:
                continue

            insert_records.append(
                write_model(
                    user_id=_referrer_id,
                    tag=UserTag.TOTAL_REFER_TRADE_USER_COUNT.name,
                    value=data_dumps(len(_user_ids))
                )
            )

        for referree_id, referrer_id in all_referee_mapping.items():
            if referrer_id in disabled_user_ids or referree_id in disabled_user_ids:
                continue
            insert_records.append(
                write_model(
                    user_id=referree_id,
                    tag=UserTag.REFERER_ID.name,
                    value=data_dumps(referrer_id)
                )
            )

            insert_records.append(
                write_model(
                    user_id=referree_id,
                    tag=UserTag.REFERER_CODE.name,
                    value=data_dumps(all_referral_code_mapping[all_referral_id_mapping[referree_id]])
                )
            )

        for _record in yield_get_user_latest_data(
            ReferralHistory,
            [ReferralHistory.referrer_id,
             ReferralHistory.created_at],
            None,
            "referrer_id"
        ):
            _uid = _record["referrer_id"]
            if _uid in disabled_user_ids:
                continue
            if len(referee_user_ids_data[_uid]) > 0:
                insert_records.append(
                    write_model(
                        user_id=_uid,
                        tag=UserTag.LATEST_REFERER_REGISTER_TIME.name,
                        value=data_dumps(_record["created_at"].date())
                    )
                )

        for _record in yield_get_user_first_data(
            ReferralHistory,
            [ReferralHistory.referrer_id,
             ReferralHistory.created_at],
            None,
            "referrer_id"
        ):
            _uid = _record["referrer_id"]
            if _uid in disabled_user_ids:
                continue
            if len(referee_user_ids_data[_uid]) > 0:
                insert_records.append(
                    write_model(
                        user_id=_record["referrer_id"],
                        tag=UserTag.FIRST_REFERER_REGISTER_TIME.name,
                        value=data_dumps(_record["created_at"].date())
                    )
                )

        for _tag, tag_data in count_data.items():
            for _user_id, value in tag_data.items():
                insert_records.append(
                    write_model(
                        user_id=_user_id,
                        tag=_tag.name,
                        value=data_dumps(value)
                    )
                )
        for _tag, tag_data in trade_count_data.items():
            for _user_id, value in tag_data.items():
                insert_records.append(
                    write_model(
                        user_id=_user_id,
                        tag=_tag.name,
                        value=data_dumps(len(value))
                    )
                )
        for _tag, tag_data in trade_usd_data.items():
            for _user_id, value in tag_data.items():
                insert_records.append(
                    write_model(
                        user_id=_user_id,
                        tag=_tag.name,
                        value=data_dumps(value)
                    )
                )
        del all_referee_mapping
        del all_referral_id_mapping
        del all_referral_code_mapping
        del trade_data

        write_model.query.filter(write_model.tag.in_([_tag.name for _tag in self.impl_tags])).delete(
            synchronize_session=False)
        for objs in batch_iter(insert_records, 5000):
            db.session.bulk_save_objects(objs)
        db.session.commit()
        self.mark_finished(report_date)


class UserAssetPositionHandler(TagHandler):

    read_slot = frozenset([8])
    write_slot = frozenset([8])

    impl_tags = {
        UserTag.LATEST_POSITION_ASSETS,
        UserTag.MAX_POSITION_USD,
        UserTag.TOTAL_USD,
        UserTag.TOTAL_MARGIN_USD,
        UserTag.AMM_USD,
        UserTag.INVEST_USD,
        UserTag.CET_AMOUNT

    }
    limit_usd = Decimal('1')
    asset_limit_usd = Decimal('1')

    def save(self, report_date: Optional[date]):
        ts = int(date_to_datetime(report_date).timestamp())
        table = ExchangeLogDB.user_account_balance_table(ts)
        if not ExchangeLogDB.daily_table_synced(table):
            current_app.logger.warning(f"{report_date} data not ready, {self.impl_tags}")
            return
        records = table.select("user_id", "asset",
                               where=f"balance_usd > {self.asset_limit_usd} and account_type='spot' and user_id != 0")

        rows = table.select("user_id", "account_type", "sum(balance_usd)", group_by="user_id, account_type")
        total_records = defaultdict(Decimal)
        total_amm_records = {}
        total_margin_records = {}
        total_invest_records = {}
        for user_id, account_type, usd in rows:
            if user_id == 0:
                continue
            total_records[user_id] += usd
            if account_type == "AMM":
                total_amm_records[user_id] = usd
            elif account_type == "MARGIN":
                total_margin_records[user_id] = usd
            elif account_type == "INVESTMENT":
                total_invest_records[user_id] = usd
        total_records = {k: v for k, v in total_records.items() if v >= self.limit_usd}

        user_cet_records = table.select("user_id", "sum(balance)",
                                        where=f"`asset`='CET'"
                                              f" and account_type='SPOT' and user_id != 0",
                                        group_by="user_id")
        sub_user_mapping = get_sub_account_mapping()
        insert_records = []
        write_model = self.get_write_model()
        position_assets_data = defaultdict(set)
        total_usd_data = defaultdict(Decimal)
        total_amm_usd_data = defaultdict(Decimal)
        total_invest_usd_data = defaultdict(Decimal)
        total_margin_usd_data = defaultdict(Decimal)
        user_cet_data = defaultdict(Decimal)
        for record in records:
            user_id, asset = record
            main_user_id = sub_user_mapping.get(user_id, user_id)
            position_assets_data[main_user_id].add(asset)
        for user_id, assets in position_assets_data.items():
            insert_records.append(
                write_model(
                    user_id=user_id,
                    tag=UserTag.LATEST_POSITION_ASSETS.name,
                    value=data_dumps(assets)
                )
            )
        for user_id, usd in total_records.items():
            main_user_id = sub_user_mapping.get(user_id, user_id)
            total_usd_data[main_user_id] += usd
        for user_id, usd in total_amm_records.items():
            main_user_id = sub_user_mapping.get(user_id, user_id)
            total_amm_usd_data[main_user_id] += usd
        for user_id, usd in total_margin_records.items():
            main_user_id = sub_user_mapping.get(user_id, user_id)
            total_margin_usd_data[main_user_id] += usd
        for user_id, usd in total_invest_records.items():
            main_user_id = sub_user_mapping.get(user_id, user_id)
            total_invest_usd_data[main_user_id] += usd
        for _cet_record in user_cet_records:
            user_id, _cet_amount = _cet_record
            main_user_id = sub_user_mapping.get(user_id, user_id)
            user_cet_data[main_user_id] += _cet_amount

        user_max_balance_data = ExchangeLogDB.get_user_max_balance_table_data()

        for user_id, usd in total_usd_data.items():
            _usd = quantize_amount(usd, PrecisionEnum.CASH_PLACES)
            if _usd == Decimal():
                continue
            insert_records.append(
                write_model(
                    user_id=user_id,
                    tag=UserTag.TOTAL_USD.name,
                    value=data_dumps(_usd)
                )
            )
        for user_id, usd in total_amm_usd_data.items():
            _usd = quantize_amount(usd, PrecisionEnum.CASH_PLACES)
            if _usd == Decimal():
                continue
            insert_records.append(
                write_model(
                    user_id=user_id,
                    tag=UserTag.AMM_USD.name,
                    value=data_dumps(_usd)
                )
            )
        for user_id, usd in total_margin_usd_data.items():
            _usd = quantize_amount(usd, PrecisionEnum.CASH_PLACES)
            if _usd == Decimal():
                continue
            insert_records.append(
                write_model(
                    user_id=user_id,
                    tag=UserTag.TOTAL_MARGIN_USD.name,
                    value=data_dumps(_usd)
                )
            )
        for user_id, usd in total_invest_usd_data.items():
            _usd = quantize_amount(usd, PrecisionEnum.CASH_PLACES)
            if _usd == Decimal():
                continue
            insert_records.append(
                write_model(
                    user_id=user_id,
                    tag=UserTag.INVEST_USD.name,
                    value=data_dumps(_usd)
                )
            )
        for user_id, _cet_amount in user_cet_data.items():
            insert_records.append(
                write_model(
                    user_id=user_id,
                    tag=UserTag.CET_AMOUNT.name,
                    value=data_dumps(quantize_amount(_cet_amount, PrecisionEnum.CASH_PLACES))
                )
            )

        for user_id, _usd in user_max_balance_data.items():
            insert_records.append(
                write_model(
                    user_id=user_id,
                    tag=UserTag.MAX_POSITION_USD.name,
                    value=data_dumps(quantize_amount(_usd, PrecisionEnum.CASH_PLACES))
                )
            )
        write_model.query.filter(write_model.tag.in_([_tag.name for _tag in self.impl_tags])).delete(
            synchronize_session=False)
        for objs in batch_iter(insert_records, 5000):
            db.session.bulk_save_objects(objs)
        db.session.commit()
        self.mark_finished(report_date)


class ReferralTotalDataHandler(SimpleTagSaveMixin, TagHandler):

    read_slot = frozenset([4])
    write_slot = frozenset([4])

    impl_tags = {
        UserTag.REFER_USER_TOTAL_TRADE_USD,
        UserTag.REFER_USER_TOTAL_FEE_USD,
    }

    def get_referree_map(self):
        model = ReferralHistory
        rows = ReferralHistory.query.with_entities(
            model.referrer_id, model.referree_id
        )
        ret = {i.referree_id: i.referrer_id for i in rows}
        return ret

    def get_convert_data(self, referree_map):
        trade_data, fee_data = defaultdict(Decimal), defaultdict(Decimal)
        trade_model, fee_model, exchange_fee_model = UserTradeSummary, UserTradeFeeSummary, UserExchangeSummary
        for user_ids in batch_iter(list(referree_map.keys()), 5000):
            trade_rows = trade_model.query.filter(
                trade_model.user_id.in_(user_ids),
            ).with_entities(
                trade_model.user_id,
                func.sum(trade_model.trade_amount).label("trade_amount"),
            ).group_by(
                trade_model.user_id
            )
            for row in trade_rows:
                trade_data[referree_map[row.user_id]] += row.trade_amount

            fee_rows = fee_model.query.filter(
                fee_model.user_id.in_(user_ids),
            ).with_entities(
                fee_model.user_id,
                func.sum(fee_model.trade_fee_amount).label("trade_amount"),
            ).group_by(
                fee_model.user_id
            )
            for row in fee_rows:
                fee_data[referree_map[row.user_id]] += row.trade_amount

            exchange_fee_rows = exchange_fee_model.query.filter(
                exchange_fee_model.user_id.in_(user_ids),
            ).with_entities(
                exchange_fee_model.user_id,
                func.sum(exchange_fee_model.web_fee_amount).label("web_fee_amount"),
                func.sum(exchange_fee_model.server_fee_amount).label("server_fee_amount"),
            ).group_by(
                exchange_fee_model.user_id
            )
            for row in exchange_fee_rows:
                fee_data[referree_map[row.user_id]] += row.web_fee_amount
                fee_data[referree_map[row.user_id]] += row.server_fee_amount

        tag_map = {
            UserTag.REFER_USER_TOTAL_TRADE_USD: trade_data,
            UserTag.REFER_USER_TOTAL_FEE_USD: fee_data,
        }
        convert_data = {
            tag.name: {_user_id: data_dumps(_value) for _user_id, _value in data.items()}
            for tag, data in tag_map.items()
        }

        return convert_data

    def flush(self, report_date: Optional[date]):
        referree_map = self.get_referree_map()
        convert_data = self.get_convert_data(referree_map)
        return convert_data


class FirstDealDaysHandler(SimpleTagSaveMixin, TagHandler):

    read_slot = frozenset([11])
    write_slot = frozenset([11])

    impl_tags = {
        UserTag.FIRST_PERPETUAL_7D_TRADE_USD,
        UserTag.FIRST_PERPETUAL_30D_TRADE_USD,
        UserTag.FIRST_PERPETUAL_7D_FEE_USD,
        UserTag.FIRST_PERPETUAL_30D_FEE_USD,
        UserTag.RECENT_30D_FEE_USD,
        UserTag.RECENT_1Y_FEE_USD,
        UserTag.RECENT_7D_FEE_USD,
    }
    require_tag = UserTag.FIRST_PERPETUAL_TRADE_TIME

    @classmethod
    def get_total_fee(cls, report_date: Optional[date], days: int):
        # sql:458s
        with read_only_session() as ro_session:
            _query = ro_session.query(UserTradeFeeSummary).filter(
                UserTradeFeeSummary.report_date > report_date - timedelta(days=days)
            ).with_entities(UserTradeFeeSummary.user_id,
                            func.sum(UserTradeFeeSummary.trade_fee_amount).label("total")
                            ).group_by(
                UserTradeFeeSummary.user_id
            ).all()
            _fee_query = ro_session.query(UserExchangeSummary).filter(
                UserExchangeSummary.report_date > report_date - timedelta(days=days)
            ).with_entities(
                UserExchangeSummary.user_id,
                func.sum(UserExchangeSummary.web_fee_amount).label("web_fee_amount"),
                func.sum(UserExchangeSummary.server_fee_amount).label("server_fee_amount"),
            ).group_by(
                UserExchangeSummary.user_id
            ).all()

        result = defaultdict(Decimal)
        for v in _query:
            result[v.user_id] += quantize_amount(v.total, PrecisionEnum.CASH_PLACES)
        for v in _fee_query:
            result[v.user_id] += quantize_amount(v.web_fee_amount, PrecisionEnum.CASH_PLACES)
            result[v.user_id] += quantize_amount(v.server_fee_amount, PrecisionEnum.CASH_PLACES)
        return result

    def get_first_trade_stamp(self, need_all=False):
        require_tag_data = TagReader.get_single_tag_data(self.require_tag)
        last_day_stamp = 0
        if not need_all:
            # 增量更新只需统计30天内的数据
            last_day_stamp = today_datetime().timestamp() - 86400 * 30
        first_trade_stamp = {k: v for k, v in require_tag_data.items() if v >= last_day_stamp}
        return first_trade_stamp

    def get_convert_data(self, first_trade_stamp):
        trade_model, fee_model = UserTradeSummary, UserTradeFeeSummary
        tag_usd_date = defaultdict(lambda: defaultdict(Decimal))
        for _date, user_ids in group_by(lambda x: timestamp_to_date(first_trade_stamp[x]), first_trade_stamp).items():
            for batch_ids in batch_iter(user_ids, 2000):
                rows = trade_model.query.filter(
                    trade_model.user_id.in_(batch_ids),
                    trade_model.system == trade_model.System.PERPETUAL,
                    trade_model.report_date >= _date,
                    trade_model.report_date < _date + timedelta(days=30)
                ).with_entities(
                    trade_model.user_id,
                    trade_model.report_date,
                    trade_model.trade_amount
                )
                for row in rows:
                    tag_usd_date[UserTag.FIRST_PERPETUAL_30D_TRADE_USD][row.user_id] += row.trade_amount
                    if row.report_date < _date + timedelta(days=7):
                        tag_usd_date[UserTag.FIRST_PERPETUAL_7D_TRADE_USD][row.user_id] += row.trade_amount

                rows = fee_model.query.filter(
                    fee_model.user_id.in_(batch_ids),
                    fee_model.system == fee_model.System.PERPETUAL,
                    fee_model.report_date >= _date,
                    fee_model.report_date < _date + timedelta(days=30)
                ).with_entities(
                    fee_model.user_id,
                    fee_model.report_date,
                    fee_model.trade_fee_amount
                )
                for row in rows:
                    tag_usd_date[UserTag.FIRST_PERPETUAL_30D_FEE_USD][row.user_id] += row.trade_fee_amount
                    if row.report_date < _date + timedelta(days=7):
                        tag_usd_date[UserTag.FIRST_PERPETUAL_7D_FEE_USD][row.user_id] += row.trade_fee_amount
        convert_data = {
            tag.name: {_user_id: data_dumps(_value) for _user_id, _value in sorted(_all_tag_data.items())}
            for tag, _all_tag_data in tag_usd_date.items()
        }
        return convert_data

    def flush(self, report_date: Optional[date]):
        # 每日增量更新函数
        first_trade_stamp = self.get_first_trade_stamp()
        convert_data = self.get_convert_data(first_trade_stamp)
        return convert_data

    def check_require_tag(self, report_date):
        ret, _ = check_user_tag_finished([self.require_tag], report_date)
        if not ret:
            current_app.logger.warning(f"{self.require_tag} {report_date} data not ready")
        return ret

    def flush_all(self, report_date: Optional[date]):
        # 用于脚本刷全量数据
        if self.check_require_tag(report_date):
            first_trade_stamp = self.get_first_trade_stamp(need_all=True)
            convert_data = self.get_convert_data(first_trade_stamp)
            delete_or_update_user_tag_data(self.impl_tags, convert_data)

    def save_fee_data(self, report_date: Optional[date]):
        _7d_fee_result = self.get_total_fee(report_date, 7)
        _30d_fee_result = self.get_total_fee(report_date, 30)
        _1y_fee_result = self.get_total_fee(report_date, 365)
        result = {
            UserTag.RECENT_7D_FEE_USD: _7d_fee_result,
            UserTag.RECENT_30D_FEE_USD: _30d_fee_result,
            UserTag.RECENT_1Y_FEE_USD: _1y_fee_result,
        }
        write_model = self.get_write_model()
        insert_records = []
        for _tag, _data in result.items():
            for _user_id, _usd in _data.items():
                if _tag == UserTag.RECENT_1Y_FEE_USD and _usd <= Decimal("5"):
                    continue
                insert_records.append(
                    write_model(
                        user_id=_user_id,
                        tag=_tag.name,
                        value=data_dumps(_usd)
                    )
                )
        write_model.query.filter(
            write_model.tag.in_([v.name for v in result.keys()])).delete(
            synchronize_session=False)
        for objs in batch_iter(insert_records, 5000):
            db.session.bulk_save_objects(objs)
        db.session.commit()

    def save(self, report_date: Optional[date]):
        if self.check_require_tag(report_date):
            new_data = self.flush(report_date)
            delete_or_update_user_tag_data(self.impl_tags - {UserTag.RECENT_1Y_FEE_USD}, new_data)

            self.save_fee_data(report_date)
            if report_date:
                self.mark_finished(report_date)


class RecentTotalUSDHandler(TagHandler):

    read_slot = frozenset([10])
    write_slot = frozenset([10])

    impl_tags = {
        UserTag.RECENT_7D_TOTAL_USD_AVG,
        UserTag.RECENT_30D_TOTAL_USD_AVG,
        UserTag.RECENT_90D_TOTAL_USD_AVG,
    }

    limit_usd = Decimal('10')

    def save(self, report_date: Optional[date]):
        ts = int(date_to_datetime(report_date).timestamp())
        table = ExchangeLogDB.user_account_balance_sum_table(ts)
        if not ExchangeLogDB.daily_table_synced(table):
            current_app.logger.warning(f"{report_date} data not ready, {self.impl_tags}")
            return
        total_usd_data = defaultdict(lambda: defaultdict(Decimal))
        sub_user_mapping = get_sub_account_mapping()
        for i in range(90):
            cur_date = report_date - timedelta(days=i)
            ts = int(date_to_datetime(cur_date).timestamp())
            table = ExchangeLogDB.user_account_balance_sum_table(ts)
            if not table.exists():
                continue
            records = table.select(
                "user_id", "balance_usd",
                where=f"balance_usd > {self.limit_usd} and user_id != 0"
            )
            for user_id, usd in records:
                main_user_id = sub_user_mapping.get(user_id, user_id)
                if cur_date > report_date - timedelta(days=7):
                    total_usd_data[UserTag.RECENT_7D_TOTAL_USD_AVG][main_user_id] += usd
                if cur_date > report_date - timedelta(days=30):
                    total_usd_data[UserTag.RECENT_30D_TOTAL_USD_AVG][main_user_id] += usd
                total_usd_data[UserTag.RECENT_90D_TOTAL_USD_AVG][main_user_id] += usd

        user_ids = list(total_usd_data[UserTag.RECENT_90D_TOTAL_USD_AVG].keys())
        user_mapping = {}
        for chunk_user_ids in batch_iter(user_ids, 2000):
            rows = User.query.with_entities(
                User.id,
                User.created_at,
            ).filter(
                User.id.in_(chunk_user_ids)
            ).all()
            user_mapping.update(dict(rows))
        insert_records = []
        write_model = self.get_write_model()
        for _tag, tag_data in total_usd_data.items():
            for _user_id, value in tag_data.items():
                if _tag is UserTag.RECENT_7D_TOTAL_USD_AVG:
                    default_days = 7
                elif _tag is UserTag.RECENT_30D_TOTAL_USD_AVG:
                    default_days = 30
                else:
                    default_days = 90
                days = default_days
                created_at = user_mapping.get(_user_id)
                if created_at and (report_date - created_at.date()).days < default_days:
                    days = (report_date - created_at.date()).days
                if days <= 0:
                    continue
                avg_usd = quantize_amount(value / days, PrecisionEnum.CASH_PLACES)
                if avg_usd == Decimal():
                    continue
                insert_records.append(
                    write_model(
                        user_id=_user_id,
                        tag=_tag.name,
                        value=data_dumps(avg_usd)
                    )
                )

        del total_usd_data

        write_model.query.filter(write_model.tag.in_([_tag.name for _tag in self.impl_tags])).delete(
            synchronize_session=False)
        for objs in batch_iter(insert_records, 5000):
            db.session.bulk_save_objects(objs)
        db.session.commit()
        self.mark_finished(report_date)
