# -*- coding: utf-8 -*-
from collections import defaultdict
from decimal import Decimal
from datetime import timedelta

from flask_babel import gettext as _

from typing import Optional, Callable, Tuple, Set

from sqlalchemy import func

from app.business.utils import query_records_by_time_range
from app.models.equity_center import (
    UserInvestIncreaseEquity,
    db,
    EquityType,
    EquityBaseInfo,
    EquitySetting,
    UserEquity,
    UserAirdropEquity,
    UserCashbackEquity, EquityDailyConsumption, UserAirdropEquityHistory, UserCashbackEquityTransferHistory,
    EquityDailyConsumptionDetail,
)
from app.models.user import SubAccount, UserBizTag, User
from app.config import config
from app.business import ServerClient, SPOT_ACCOUNT_ID
from app.caches.config import EquitySettingCache
from app.utils import BaseConfig, ConfigField, ConfigMode, batch_iter, validate_email, now
from app.utils.amount import amount_to_str
from app.utils.date_ import date_to_datetime, today


class _EquitySettings(BaseConfig):
    F = ConfigField
    model = EquitySetting
    cache = EquitySettingCache

    # 新增的静态配置字段
    cet_balance_warning_amount = F(Decimal, "系统结算账户CET预警余额", default=20000,
                                   remark="当账户余额低于当前值时，触发Slack预警")
    usdt_balance_warning_amount = F(Decimal, "系统结算账户USDT预警余额", default=20000,
                                    remark="当账户余额低于当前值时，触发Slack预警")
    usdc_balance_warning_amount = F(Decimal, "系统结算账户USDC预警余额", default=20000,
                                    remark="当账户余额低于当前值时，触发Slack预警")
    same_currency_issuance_ratio_warning = F(Decimal, "同币种发放数量对比昨日倍率预警(%)", default=100,
                                             remark="当日【同币种发放数量对比昨日倍率】高于以下数值时，触发Slack预警")
    slack_warning_users = F(set, "Slack预警时@用户", default=(),
                            remark="触发Slack预警时，自动@用户")
    all_currency_transfer_enabled = F(bool, "开启系统结算账户全币种划转", default=True,
                                      remark="关闭后，空投权益停止发放、返现权益停止返现")
    cashback_expired_notice_hours = F(int, "返现权益到期触达小时(H)", default=24,
                                      remark="可配置所有权益在xx小时到期前，给用户发送到期通知。")

    def __init__(self, model: ConfigMode = ConfigMode.REAL_TIME):
        super().__init__(model)

    def _get_all(self) -> dict[str, str]:
        pass

    def _get_one(self, name: str) -> Optional[str]:
        return self.cache().get_value(name)

    def _set_one(self, name: str, value: str):
        row = self.model.query.filter(self.model.key == name).first()
        if not row:
            row = self.model(key=name)
            db.session.add(row)
        row.value = value
        row.status = self.model.Status.VALID
        db.session.commit()
        self.cache().set_value(name, value)

    def _del_one(self, name: str):
        row = self.model.query.filter(self.model.key == name).first()
        if not row:
            return
        row.status = self.model.Status.DELETED
        db.session.commit()
        self.cache().del_value(name)

    def get_balance_warning_configs(self) -> dict[str, Decimal]:
        return {
            asset: getattr(self, f'{asset.lower()}_balance_warning_amount', Decimal())
            for asset in EquitySetting.BALANCE_ASSETS
        }


EquitySettings = _EquitySettings(model=ConfigMode.REAL_TIME)


def check_sys_balance_enough(asset_warning_configs: dict[str, Decimal]) -> dict[str, dict]:
    """ 检查系统帐号，某个币种资产是否足够 """
    sys_user_id = config["EQUITY_CENTER_ADMIN_USER_ID"]
    balances = ServerClient().get_user_balances(user_id=sys_user_id, account_id=SPOT_ACCOUNT_ID)
    result = {}
    for asset in asset_warning_configs.keys():
        warning_amount = asset_warning_configs[asset]
        if not warning_amount:
            continue
        avl_amount = balances.get(asset, {}).get('available', Decimal())
        if avl_amount < warning_amount:
            result[asset] = {
                'available': avl_amount,
                'warning_amount': warning_amount,
            }
    return result


class EquityCenterService:
    """ 权益中心对外逻辑，提供给其他模块（任务中心）调用 """

    # 暂时 所有状态以及类型都可以调用，后续控制调用细节
    BizTypes = UserEquity.BusinessType
    UserEquityStatus = UserEquity.Status

    FAILED_STATUS = {
        UserEquity.Status.FAILED,
        UserEquity.Status.CREATED
    }

    @classmethod
    def equity_display_str(cls, equity_data: dict) -> str:
        equity_type = equity_data['type']
        trans_equity_type = _(equity_type.value)
        return f"{amount_to_str(equity_data['cost_amount'])} {equity_data['cost_asset']} {trans_equity_type}"

    @classmethod
    def batch_query_equity_basic_info(cls, eq_ids: set[int]) -> dict[int, dict]:
        eq_base_info_rows = EquityBaseInfo.query.filter(
            EquityBaseInfo.id.in_(eq_ids),
        ).with_entities(
            EquityBaseInfo.id,
            EquityBaseInfo.type,
            EquityBaseInfo.cost_asset,
            EquityBaseInfo.cost_amount,
            EquityBaseInfo.status,
            EquityBaseInfo.extra_data
        ).all()
        res = {
            i.id: {
                "type": i.type,
                "cost_asset": i.cost_asset,
                "cost_amount": i.cost_amount,
                "status": i.status,
                "cashback_asset": i.extra_data.get('cashback_asset') if i.extra_data else None,
                "extra_data": i.extra_data
            }
            for i in eq_base_info_rows
        }
        return res

    @classmethod
    def get_or_create_airdrop_base_equity(
            cls,
            airdrop_asset: str,
            airdrop_amount: Decimal,
            creator: int,
            remark: str = "",
    ) -> int:
        """查询或创建一个空投权益配置"""
        assert airdrop_amount > 0
        remark = remark[:512]
        value_asset = "USDT"
        extra_data = dict(
            value_asset=value_asset,
        )
        base_eq: EquityBaseInfo = EquityBaseInfo.query.filter(
            EquityBaseInfo.type == EquityType.AIRDROP,
            EquityBaseInfo.cost_asset == airdrop_asset,
            EquityBaseInfo.cost_amount == airdrop_amount,
        ).first()
        if base_eq and base_eq.extra_data == extra_data:
            base_eq.status = EquityBaseInfo.Status.OPEN
            base_eq.creator = creator
            base_eq.remark = remark
        else:
            base_eq = EquityBaseInfo(
                type=EquityType.AIRDROP,
                creator=creator,
                remark=remark,
                cost_asset=airdrop_asset,
                cost_amount=airdrop_amount,
                extra_data=extra_data,
            )
        db.session.add(base_eq)
        db.session.commit()
        return base_eq.id

    # #######################################################################################

    @classmethod
    def _create_user_airdrop_equity(cls, eq_base_info: EquityBaseInfo, user_eq: UserEquity) -> UserAirdropEquity:
        air_status = UserAirdropEquity.Status.CREATED if user_eq.status == UserEquity.Status.CREATED else UserAirdropEquity.Status.FAILED
        air_equity = UserAirdropEquity(
            user_id=user_eq.user_id,
            user_equity_id=user_eq.id,
            status=air_status,
            airdrop_asset=eq_base_info.cost_asset,
            airdrop_amount=eq_base_info.cost_amount,
            value_asset=eq_base_info.extra_data["value_asset"],
            value_amount=0,
        )
        return air_equity

    @classmethod
    def _create_user_cashback_equity(cls, eq_base_info: EquityBaseInfo, user_eq: UserEquity) -> UserCashbackEquity:
        start_time = user_eq.created_at
        end_time = start_time + timedelta(days=eq_base_info.extra_data["effective_days"])
        if user_eq.status == UserEquity.Status.CREATED:
            user_eq.status = UserEquity.Status.USING
            eq_status = UserCashbackEquity.Status.USING
        else:
            eq_status = UserCashbackEquity.Status.FAILED
        cb_equity = UserCashbackEquity(
            user_id=user_eq.user_id,
            user_equity_id=user_eq.id,
            start_time=start_time,
            end_time=end_time,
            status=eq_status,
            cost_asset=eq_base_info.cost_asset,
            cost_amount=eq_base_info.cost_amount,
            cashback_scope=UserCashbackEquity.CashbackScope[eq_base_info.extra_data["cashback_scope"]],
            cashback_asset=eq_base_info.extra_data["cashback_asset"],
            cashback_ratio=eq_base_info.extra_data["cashback_ratio"]
        )
        return cb_equity

    @classmethod
    def _create_user_invest_increase_equity(cls, eq_base_info: EquityBaseInfo, user_eq: UserEquity) -> UserInvestIncreaseEquity:
        """创建用户理财加息权益"""

        # 从权益基础信息的extra_data中获取理财加息相关配置
        extra_data = eq_base_info.extra_data or {}
        start_time = user_eq.created_at
        end_time = start_time + timedelta(days=extra_data.get("activation_days", 0))
        if user_eq.status == UserEquity.Status.CREATED:
            user_eq.status = UserEquity.Status.PENDING
        else:
            # 风控失败等情况
            end_time = now()
        user_eq.finished_at = end_time
        invest_equity = UserInvestIncreaseEquity(
            user_id=user_eq.user_id,
            user_equity_id=user_eq.id,
            finished_at=end_time,  # 加息权益结束时间
            increase_rate=extra_data.get("increase_rate", Decimal(0)),  # 加息比例
            principal_asset=extra_data.get("principal_asset", UserInvestIncreaseEquity.DEFAULT_PRINCIPAL_ASSET),  # 本金币种
            principal_amount_limit=extra_data.get("principal_amount_limit", Decimal(0)),  # 加息本金上限
            assets=extra_data.get("assets", []),  # 适用币种
            usable_days=extra_data.get("usable_days", 0),  # 加息有效天数
            activation_days=extra_data.get("activation_days", 0),  # 激活有效天数
        )
        return invest_equity

    @classmethod
    def get_create_specific_equity_func(cls, eq_type: EquityType) -> Callable:
        equity_type_specific_func_map = {
            EquityType.AIRDROP: cls._create_user_airdrop_equity,
            EquityType.CASHBACK: cls._create_user_cashback_equity,
            EquityType.INVEST_INCREASE: cls._create_user_invest_increase_equity,
        }
        return equity_type_specific_func_map[eq_type]

    @classmethod
    def batch_get_or_create_user_equity(cls, params: list[dict], is_commit: bool) -> dict:
        """批量查询 or 创建多个用户权益 (过滤子账户)
        param keys: biz_id, biz_type, user_id, equity_id, status
        (biz_id, biz_type, user_id) 唯一
        """
        bus_ids = set()
        bus_types = set()
        equity_ids = set()
        user_ids = set()
        for param in params:
            bus_ids.add(param['biz_id'])
            bus_types.add(param['biz_type'])
            equity_ids.add(param['equity_id'])
            user_ids.add(param['user_id'])
            assert param['status'] in [
                UserEquity.Status.CREATED,
                UserEquity.Status.FAILED,
            ]
        if not bus_types:
            return {}

        assert len(bus_types) <= 1  # BusinessType必须相同
        bus_type = list(bus_types)[0]

        batch_size = 5000
        eq_base_info_rows = []
        for ch_eq_ids in batch_iter(equity_ids, batch_size):
            ch_eq_base_info_rows = EquityBaseInfo.query.filter(
                EquityBaseInfo.id.in_(ch_eq_ids),
            ).all()
            eq_base_info_rows.extend(ch_eq_base_info_rows)
        eq_base_info_map: dict[int, EquityBaseInfo] = {i.id: i for i in eq_base_info_rows}

        user_eq_rows = []
        for ch_bus_ids in batch_iter(bus_ids, batch_size):
            ch_user_eq_rows: list[UserEquity] = UserEquity.query.filter(
                UserEquity.business_id.in_(ch_bus_ids),
                UserEquity.business_type == bus_type,
                UserEquity.user_id.in_(user_ids),
            ).all()
            user_eq_rows.extend(ch_user_eq_rows)
        bus_id_type_user_eq_map = {}
        for u_eq in user_eq_rows:
            bus_id_type_user_eq_map[(u_eq.business_id, u_eq.business_type, u_eq.user_id)] = u_eq

        cashback_eq_user_ids = set()
        result_map = {}

        sub_user_ids = set()
        for batch_ids in batch_iter(user_ids, batch_size):
            sub_user_ids.update(
                {i.user_id for i in SubAccount.query.filter(SubAccount.user_id.in_(batch_ids)).with_entities(SubAccount.user_id).all()}
            )

        for param in params:
            user_id = param['user_id']
            # 过滤子账号
            if user_id in sub_user_ids:
                continue
            equity_id = param['equity_id']
            status = param['status']
            eq_base_info = eq_base_info_map[equity_id]

            biz_id = param['biz_id']
            biz_type = param['biz_type']
            key_ = (biz_id, biz_type, user_id)
            if key_ in bus_id_type_user_eq_map:
                result_map[key_] = bus_id_type_user_eq_map[key_]
                continue

            user_eq = UserEquity(
                user_id=user_id,
                equity_id=equity_id,
                type=eq_base_info.type,
                business_id=biz_id,
                business_type=biz_type,
                status=status,
            )
            db.session.add(user_eq)
            db.session.flush()

            new_specific_func = cls.get_create_specific_equity_func(eq_base_info.type)
            user_specific_eq = new_specific_func(eq_base_info, user_eq)
            db.session.add(user_specific_eq)
            result_map[key_] = user_eq
            if eq_base_info.type == EquityType.CASHBACK and status == UserEquity.Status.CREATED:
                cashback_eq_user_ids.add(user_id)

        # 增加返佣标签
        for ch_eq_user_ids in batch_iter(cashback_eq_user_ids, batch_size):
            UserBizTag.batch_add_user_tag(
                biz_tag=UserBizTag.BizTag.EE_NOT_REFERRAL,
                source=UserBizTag.Source.CASHBACK_EQUITY,
                user_ids=set(ch_eq_user_ids),
            )

        if is_commit:
            db.session.commit()
        return result_map

    @classmethod
    def batch_query_user_eq_info(cls, biz_type: UserEquity.BusinessType, biz_ids: set[int]) -> dict:
        """ biz_ids + biz_type 需要唯一，目前仅MISSION使用 """
        user_eq_rows = UserEquity.query.filter(
            UserEquity.business_id.in_(biz_ids),
            UserEquity.business_type == biz_type,
        ).with_entities(
            UserEquity.id,
            UserEquity.business_id,
            UserEquity.type,
            UserEquity.status,
            UserEquity.created_at,
        ).all()
        air_user_eq_ids = [i.id for i in user_eq_rows if i.type == EquityType.AIRDROP]
        air_eq_row_map = {}
        if air_user_eq_ids:
            air_eq_rows = UserAirdropEquity.query.filter(
                UserAirdropEquity.user_equity_id.in_(air_user_eq_ids),
            ).with_entities(
                UserAirdropEquity.user_equity_id,
                UserAirdropEquity.airdrop_asset,
                UserAirdropEquity.airdrop_amount,
            ).all()
            air_eq_row_map = {i.user_equity_id: i for i in air_eq_rows}
        cb_user_eq_ids = [i.id for i in user_eq_rows if i.type == EquityType.CASHBACK]
        cb_eq_row_map = {}
        if cb_user_eq_ids:
            cb_eq_rows = UserCashbackEquity.query.filter(
                UserCashbackEquity.user_equity_id.in_(cb_user_eq_ids),
            ).with_entities(
                UserCashbackEquity.user_equity_id,
                UserCashbackEquity.cost_asset,
                UserCashbackEquity.cost_amount,
                UserCashbackEquity.cashback_amount,
                UserCashbackEquity.used_cost_amount
            ).all()
            cb_eq_row_map = {i.user_equity_id: i for i in cb_eq_rows}

        # 查询理财加息权益
        invest_user_eq_ids = [i.id for i in user_eq_rows if i.type == EquityType.INVEST_INCREASE]
        invest_eq_row_map = {}
        if invest_user_eq_ids:
            invest_eq_rows = (
                UserInvestIncreaseEquity.query.filter(
                    UserInvestIncreaseEquity.user_equity_id.in_(invest_user_eq_ids),
                )
                .with_entities(
                    UserInvestIncreaseEquity.user_equity_id,
                    UserInvestIncreaseEquity.increase_rate,
                    UserInvestIncreaseEquity.increase_usd,
                    UserInvestIncreaseEquity.principal_asset,
                    UserInvestIncreaseEquity.principal_amount_limit,
                )
                .all()
            )
            invest_eq_row_map = {i.user_equity_id: i for i in invest_eq_rows}

        result = dict()
        for eq_row in user_eq_rows:
            if eq_row.type == EquityType.AIRDROP:
                eq_detail: UserAirdropEquity = air_eq_row_map[eq_row.id]
                cost_asset = eq_detail.airdrop_asset
                cost_amount = eq_detail.airdrop_amount
                real_amount = Decimal() if eq_row.status in cls.FAILED_STATUS \
                    else eq_detail.airdrop_amount
            elif eq_row.type == EquityType.CASHBACK:
                eq_detail: UserCashbackEquity = cb_eq_row_map[eq_row.id]
                cost_asset = eq_detail.cost_asset
                cost_amount = eq_detail.cost_amount
                real_amount = eq_detail.used_cost_amount
            elif eq_row.type == EquityType.INVEST_INCREASE:
                eq_detail: UserInvestIncreaseEquity = invest_eq_row_map[eq_row.id]
                cost_asset = eq_detail.principal_asset
                cost_amount = eq_detail.principal_amount_limit
                real_amount = eq_detail.increase_usd  # 实发是理财币种，转换为 usdt
                increase_rate = eq_detail.increase_rate
            else:
                continue
            result[eq_row.business_id] = {
                "type": eq_row.type,
                "status": eq_row.status,
                "cost_asset": cost_asset,
                "cost_amount": cost_amount,
                "real_amount": real_amount,
                "created_at": eq_row.created_at,
                "increase_rate": increase_rate if eq_row.type == EquityType.INVEST_INCREASE else Decimal(),
            }
        return result

    @classmethod
    def async_send_airdrop_equity(cls):
        from app.business.equity_center.airdrop import send_user_airdrop_equity_task

        send_user_airdrop_equity_task.delay()

    @classmethod
    def format_equity_base_info(cls, row: EquityBaseInfo) -> str:
        if row.type == EquityType.CASHBACK:
            cashback_scope_str = UserCashbackEquity.CashbackScope[row.extra_data["cashback_scope"]].value
            cashback_ratio = amount_to_str(Decimal(row.extra_data['cashback_ratio']) * Decimal(100))
            s = (f'{amount_to_str(row.cost_amount)} {row.cost_asset} 返 {row.extra_data["cashback_asset"]} '
                 f'{cashback_scope_str}({row.id} {cashback_ratio}%)')
        elif row.type == EquityType.INVEST_INCREASE:
            increase_rate = amount_to_str(Decimal(row.extra_data.get("increase_rate", 0)) * Decimal("100"), 2)
            principal_asset = row.extra_data.get("principal_asset", UserInvestIncreaseEquity.DEFAULT_PRINCIPAL_ASSET)
            principal_amount_limit = amount_to_str(row.extra_data.get("principal_amount_limit", 0))
            s = f"{row.id}  {'/'.join(row.extra_data['assets'])}  {increase_rate}%  {principal_amount_limit}{principal_asset}"
        elif row.type == EquityType.AIRDROP:
            s = f"{amount_to_str(row.cost_amount)} {row.cost_asset} 空投({row.id})"
        else:
            s = f"{amount_to_str(row.cost_amount)} {row.cost_asset} 未知类型({row.id})"
        return s

    @classmethod
    def get_open_equity_query(cls):
        model = EquityBaseInfo
        query = model.query.filter(model.status == model.Status.OPEN).with_entities(
            model.id,
            model.cost_asset,
            model.cost_amount,
            model.extra_data,
            model.type,
        )
        return query

    @classmethod
    def get_all_equity_dict(cls, equity_type=None) -> dict[int, str]:
        query = cls.get_open_equity_query()
        if equity_type:
            query = query.filter(EquityBaseInfo.type == equity_type)
        rows = query.all()
        all_equity_dict = {}
        for row in rows:
            all_equity_dict.update({row.id: cls.format_equity_base_info(row)})
        return all_equity_dict

    @classmethod
    def get_reward_type_equity_dict(cls) -> dict[int, str]:
        rows = cls.get_open_equity_query().all()
        ret = defaultdict(dict)
        for row in rows:
            ret[row.type.name][row.id] = cls.format_equity_base_info(row)
        return ret

    @classmethod
    def check_biz_equity_finished(cls, biz_type: UserEquity.BusinessType, biz_ids: set[int]) -> bool:
        """检查业务权益是否完成，biz_ids + biz_type 需要唯一，目前仅MISSION使用"""
        user_eq_rows = UserEquity.query.filter(
            UserEquity.business_id.in_(biz_ids),
            UserEquity.business_type == biz_type,
        ).with_entities(
            UserEquity.id,
            UserEquity.status,
        ).all()
        if not user_eq_rows:
            return True
        status_set = {i.status for i in user_eq_rows}
        return not bool(status_set - {UserEquity.Status.FINISHED, UserEquity.Status.FAILED, UserEquity.Status.EXPIRED})

    @classmethod
    def query_biz_id_by_biz_type_status(cls, biz_type: UserEquity.BusinessType, equity_status: UserEquity.Status) -> set[int]:
        """ business_id + biz_type 需要唯一，目前仅MISSION使用 """
        return {
            ue.business_id for ue in UserEquity.query.filter(
                UserEquity.business_type == biz_type,
                UserEquity.status == equity_status
            ).with_entities(
                UserEquity.business_id
            ).all()
        }


def parse_manual_users(manual_users_str: str) -> Tuple[Set[int], list[str]]:
    if not manual_users_str or not manual_users_str.strip():
        return set(), []

    user_inputs = [item.strip() for item in manual_users_str.split(',') if item.strip()]
    if not user_inputs:
        return set(), []

    user_ids_to_check = []
    emails_to_check = []
    invalid_users = []

    for user_input in user_inputs:
        if user_input.isdigit():
            user_ids_to_check.append(int(user_input))
        elif validate_email(user_input):
            emails_to_check.append(user_input)
        else:
            invalid_users.append(user_input)

    valid_user_ids = set()
    batch_size = 1000

    # 批量查询UID
    if user_ids_to_check:
        for batch_user_ids in batch_iter(user_ids_to_check, batch_size):
            users = User.query.filter(User.id.in_(batch_user_ids)).with_entities(User.id).all()
            found_user_ids = {user.id for user in users}
            valid_user_ids.update(found_user_ids)

            not_found_user_ids = set(batch_user_ids) - found_user_ids
            invalid_users.extend([str(uid) for uid in not_found_user_ids])

    # 批量查询邮箱
    if emails_to_check:
        for batch_emails in batch_iter(emails_to_check, batch_size):
            users = User.query.filter(User.email.in_(batch_emails)).with_entities(User.id, User.email).all()
            found_emails = {user.email for user in users}
            valid_user_ids.update({user.id for user in users})

            not_found_emails = set(batch_emails) - found_emails
            invalid_users.extend(list(not_found_emails))
    return valid_user_ids, invalid_users


class EquityDailyConsumptionService:
    """发奖账户每日消耗统计服务"""

    @classmethod
    def query_last_consumption_date(cls):
        return EquityDailyConsumption.query.with_entities(
            func.max(EquityDailyConsumption.issue_date)
        ).scalar()

    @classmethod
    def _query_airdrop_info(cls, biz_ids: list[int]):
        from app.models.operation import AirdropActivityRewardHistory
        return {
            i.id: i.airdrop_activity_id for i in
            AirdropActivityRewardHistory.query.filter(AirdropActivityRewardHistory.id.in_(biz_ids)).with_entities(
                AirdropActivityRewardHistory.airdrop_activity_id,
                AirdropActivityRewardHistory.id
            ).all()
        }

    @classmethod
    def _query_deposit_bonus_info(cls, biz_ids: list[int]):
        from app.models.operation import DepositBonusActivityUserGiftRow, DepositBonusActivityUserInfo
        biz_user_info_mapper = {
            i.id: i.user_info_id for i in
            DepositBonusActivityUserGiftRow.query.filter(DepositBonusActivityUserGiftRow.id.in_(biz_ids)).with_entities(
                DepositBonusActivityUserGiftRow.user_info_id,
                DepositBonusActivityUserGiftRow.id
            ).all()
        }
        user_info_deposit_bonus_mapper = {
            i.id: i.deposit_bonus_id for i in DepositBonusActivityUserInfo.query.filter(
                DepositBonusActivityUserInfo.id.in_(biz_user_info_mapper.values())
            ).with_entities(
                DepositBonusActivityUserInfo.deposit_bonus_id,
                DepositBonusActivityUserInfo.id
            ).all()
        }
        return {
            biz_id: user_info_deposit_bonus_mapper[user_info_id] for biz_id, user_info_id in
            biz_user_info_mapper.items()
        }

    @classmethod
    def _query_mission_info(cls, biz_ids: list[int]):
        from app.models.mission_center import UserMission
        return {
            i.id: i.mission_id for i in UserMission.query.filter(UserMission.id.in_(biz_ids)).with_entities(
                UserMission.id,
                UserMission.mission_id,
            ).all()
        }

    @classmethod
    def query_biz_source_id_mapper(cls, biz_type_ids_mapper: dict[UserEquity.BusinessType, list[int]]):
        biz_source_id_mapper = {}
        for biz_type, biz_ids in biz_type_ids_mapper.items():
            match biz_type:
                case UserEquity.BusinessType.MISSION:
                    biz_source_id_mapper.update(cls._query_mission_info(biz_ids))
                case UserEquity.BusinessType.AIRDROP_ACTIVITY:
                    biz_source_id_mapper.update(cls._query_airdrop_info(biz_ids))
                case UserEquity.BusinessType.DEPOSIT_BONUS_ACTIVITY:
                    biz_source_id_mapper.update(cls._query_deposit_bonus_info(biz_ids))
                case UserEquity.BusinessType.PLATFORM_SEND:
                    biz_source_id_mapper.update({
                        biz_id: biz_id for biz_id in biz_ids
                    })
        return biz_source_id_mapper

    @classmethod
    def calculate_daily_consumption(cls, target_date=None):
        """计算指定日期的发奖账户消耗统计"""
        from app.business.alert import send_alert_notice

        if target_date is None:
            target_date = today() - timedelta(days=1)  # 默认统计昨天

        # 获取所有支持的币种
        assets = EquitySetting.BALANCE_ASSETS

        query_start_time = date_to_datetime(target_date)
        query_end_time = date_to_datetime(target_date + timedelta(days=1))
        asset_yesterday_consumption_mapper = {}
        asset_not_send_consumption_mapper = {}
        # 统计空投权益发放
        airdrop_rows = query_records_by_time_range(
            UserAirdropEquityHistory,
            query_start_time,
            query_end_time,
        )
        # 统计返现权益发放
        cashback_rows = query_records_by_time_range(
            UserCashbackEquityTransferHistory,
            query_start_time,
            query_end_time,
        )
        for asset in assets:
            # 计算该币种当日的发放总量
            total_amount = Decimal(0)

            user_eq_id_amount_map = defaultdict(Decimal)

            for row in airdrop_rows:
                if row.airdrop_asset != asset or row.status != UserAirdropEquityHistory.Status.FINISHED:
                    continue
                total_amount += row.airdrop_amount
                user_eq_id_amount_map[row.user_equity_id] += row.airdrop_amount
            for row in cashback_rows:
                if row.asset != asset or row.status != UserCashbackEquityTransferHistory.Status.FINISHED:
                    continue
                total_amount += row.amount
                user_eq_id_amount_map[row.user_equity_id] += row.amount

            # 计算对比昨日的倍率
            yesterday_date = target_date - timedelta(days=1)
            yesterday_consumption = EquityDailyConsumption.query.filter(
                EquityDailyConsumption.issue_date == yesterday_date,
                EquityDailyConsumption.issue_asset == asset
            ).first()

            ratio_to_yesterday = None
            if yesterday_consumption and yesterday_consumption.issue_amount > 0:
                ratio_to_yesterday = (
                    (total_amount - yesterday_consumption.issue_amount) / yesterday_consumption.issue_amount
                ) * 100

            # 更新或创建记录
            consumption = EquityDailyConsumption.get_or_create(
                issue_date=target_date,
                issue_asset=asset
            )
            db.session.add(consumption)
            consumption.issue_amount = total_amount
            consumption.ratio_to_yesterday = ratio_to_yesterday

            if ratio_to_yesterday and ratio_to_yesterday >= EquitySettings.same_currency_issuance_ratio_warning:
                asset_yesterday_consumption_mapper[asset] = {
                    "yesterday_amount": yesterday_consumption.issue_amount,
                    "ratio_to_yesterday": ratio_to_yesterday,
                    "today_amount": total_amount,
                    "yesterday_date": yesterday_date,
                    "today_date": target_date
                }

            if yesterday_consumption and not yesterday_consumption.issue_amount and total_amount > 0:
                asset_not_send_consumption_mapper[asset] = {
                    "yesterday_amount": yesterday_consumption.issue_amount,
                    "today_amount": total_amount,
                    "yesterday_date": yesterday_date,
                    "today_date": target_date
                }

            user_eq_rows = UserEquity.query.filter(
                UserEquity.id.in_(user_eq_id_amount_map.keys()),
            ).with_entities(
                UserEquity.id,
                UserEquity.business_type,
                UserEquity.business_id,
            ).all()
            biz_type_ids_mapper = defaultdict(list)
            for user_eq_row in user_eq_rows:
                biz_type_ids_mapper[user_eq_row.business_type].append(user_eq_row.business_id)

            source_id_mapper = cls.query_biz_source_id_mapper(biz_type_ids_mapper)
            source_type_id_amount = defaultdict(lambda: defaultdict(Decimal))
            detail_rows = []
            for user_eq in user_eq_rows:
                source_id = source_id_mapper.get(user_eq.business_id)
                if not source_id:
                    print(f"No source id for {user_eq.id} {user_eq.business_type} {user_eq.business_id}")
                    continue
                source_type_id_amount[user_eq.business_type][source_id] += user_eq_id_amount_map[user_eq.id]

            for biz_type, biz_amount_mapper in source_type_id_amount.items():
                for source_id, amount in biz_amount_mapper.items():
                    detail = EquityDailyConsumptionDetail.get_or_create(
                        consumption_id=consumption.id,
                        issue_date=target_date,
                        business_type=biz_type,
                        business_id=source_id,
                    )
                    detail.issue_amount = amount
                    detail.issue_asset = asset
                    detail_rows.append(detail)
            db.session.bulk_save_objects(detail_rows)
        db.session.commit()
        slack_users = EquitySettings.slack_warning_users
        if asset_yesterday_consumption_mapper:
            content = "【奖励中心】发奖账户今日消耗异常波动\n"
            for asset, waring_data in asset_yesterday_consumption_mapper.items():
                today_date_str = str(waring_data['today_date'])
                yesterday_date_str = str(waring_data['yesterday_date'])

                content += (
                    f"{today_date_str}  {asset}发奖账户总消耗已超过前日"
                    f"{amount_to_str(waring_data['ratio_to_yesterday'], 2)}%，请检查是否异常！\n"
                    f"{today_date_str}：{amount_to_str(waring_data['today_amount'])} {asset}\n"
                    f"{yesterday_date_str}：{amount_to_str(waring_data['yesterday_amount'])} {asset}\n"
                )
            send_alert_notice(
                content,
                config["ADMIN_CONTACTS"]["equity_notice"],
                at=",".join(slack_users) if slack_users else None,
            )
        if asset_not_send_consumption_mapper:
            not_send_content = ""
            for asset, waring_data in asset_not_send_consumption_mapper.items():
                today_date_str = str(waring_data['today_date'])
                yesterday_date_str = str(waring_data['yesterday_date'])
                not_send_content += (
                    f"{yesterday_date_str} {asset}发放数量为0，无法对比消耗波动，请检查{today_date_str}发放数量是否异常！\n"
                    f"{today_date_str}：{amount_to_str(waring_data['today_amount'])} {asset}\n"
                    f"{yesterday_date_str}：{amount_to_str(waring_data['yesterday_amount'])} {asset}\n"
                )
            send_alert_notice(
                not_send_content,
                config["ADMIN_CONTACTS"]["equity_notice"],
                at=",".join(slack_users) if slack_users else None,
            )


class EquityContentHelper:
    """ 权益奖励内容助手 """
    
    @classmethod
    def get_reward_content(cls, equity_type, value, value_type) -> str:
        content = _("%(value)s %(value_type)s%(reward_type)s奖励")
        if equity_type == EquityType.INVEST_INCREASE:
            # eg: 获得+10.00%理财加息权益奖励,value=+10%
            content = _("%(value)s%(value_type)s%(reward_type)s奖励")
        params = dict(
            value=value,
            value_type=value_type,
            reward_type=_(equity_type.value),
        )
        return _(content, **params)
