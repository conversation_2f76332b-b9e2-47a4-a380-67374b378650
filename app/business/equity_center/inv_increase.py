# -*- coding: utf-8 -*-
from collections import Counter, defaultdict
from decimal import Decimal
from datetime import timedelta, date, datetime
from itertools import chain
from typing import Dict, List

from flask import current_app
from sqlalchemy import func

from app.business.investment import (
    InvestmentDailyBase,
    InvestmentDailyPayoutBase,
    InvestmentDataProc,
    InvestmentHourlyBase,
    InvestmentScheduleBase,
)
from app.common.constants import Currency, PrecisionEnum
from app.models.equity_center import (
    db,
    UserEquity,
    UserInvestIncreaseEquity,
    EquityType,
    UserHourIncEquityHistory,
    UserDailyIncEquityHistory,
)
from app import config
from app.common import CeleryQueues
from app.business import ServerClient, BalanceBusiness, SPOT_ACCOUNT_ID, PriceManager, send_alert_notice
from app.models.investment import AssetInvestmentConfig, InterestStatisticTime
from app.utils import now, quantize_amount, route_module_to_celery_queue, today, amount_to_str
from app.utils.date_ import date_to_datetime, datetime_to_str
from app.utils.http_client import BaseHTTPClient
from app.utils.iterable import batch_iter


route_module_to_celery_queue(__name__, CeleryQueues.REWARD_CENTER)


class IncreaseEquityHelper:
    """理财加息权益帮助类"""
    MODEL = UserEquity
    END_STATUS_MAP = {
        MODEL.Status.PENDING: MODEL.Status.EXPIRED,
        MODEL.Status.USING: MODEL.Status.FINISHED,
    }
    
    @classmethod
    def format_value_and_type(cls, increase_rate: Decimal) -> tuple[str, str]:
        """格式化加息权益显示值和类型"""
        increase_rate = Decimal(increase_rate)
        increase_rate = increase_rate if increase_rate > 0 else 0
        value = f"+{amount_to_str(increase_rate * Decimal('100'), 2)}"
        value_type = "%"
        return value, value_type

    @classmethod
    def get_user_show_inc_info(cls, user_id: int) -> tuple[Dict, set[str]]:
        """返回已激活币种数据，待激活币种"""
        ue_model = UserEquity
        detail_model = UserInvestIncreaseEquity
        user_equities = (
            detail_model.query.join(ue_model, detail_model.user_equity_id == ue_model.id)
            .filter(
                detail_model.user_id == user_id,
                ue_model.type == EquityType.INVEST_INCREASE,
                ue_model.status.in_(
                    [
                        ue_model.Status.PENDING,
                        ue_model.Status.USING,
                    ]
                ),
            )
            .with_entities(
                detail_model.user_id,
                detail_model.user_equity_id,
                detail_model.investment_asset,
                detail_model.assets,
                detail_model.principal_asset,
                detail_model.principal_amount_limit,
                detail_model.increase_rate,
                ue_model.status.label("status"),
            )
            .all()
        )

        # 用户只能激活一个加息权益
        ret = dict()
        eq = next((eq for eq in user_equities if eq.status == ue_model.Status.USING), None)
        if eq:
            ret[eq.investment_asset] = dict(
                status=eq.status,
                info=dict(
                    principal_asset=eq.principal_asset,
                    principal_amount_limit=eq.principal_amount_limit,
                    increase_rate=eq.increase_rate,
                ),
            )
        else:
            eqs = [eq for eq in user_equities if eq.status == ue_model.Status.PENDING]
            assets = set(chain(*[eq.assets for eq in eqs]))
            for asset in assets:
                ret[asset] = dict(
                    status=ue_model.Status.PENDING,
                )
        return ret

    @classmethod
    def unpack_user_asset_incr_info(cls, asset, user_eq_info: Dict) -> Dict:
        eq_info = user_eq_info.get(asset, {})
        return dict(
            inc_eq_status=eq_info.get("status", UserEquity.Status.FAILED).name,
            inc_eq_info=eq_info.get("info"),
        )

    @classmethod
    def get_user_using_inc_info(cls, user_id: int) -> Dict:
        ue_model = UserEquity
        ue_detail_model = UserInvestIncreaseEquity
        user_eq = (
            ue_model.query.join(ue_detail_model, ue_model.id == ue_detail_model.user_equity_id)
            .filter(
                ue_model.user_id == user_id,
                ue_model.type == EquityType.INVEST_INCREASE,
                ue_model.status == ue_model.Status.USING,
            )
            .with_entities(
                ue_model.user_id,
                ue_detail_model.investment_asset.label("investment_asset"),
                ue_detail_model.increase_rate.label("increase_rate"),
                ue_detail_model.principal_asset.label("principal_asset"),
                ue_detail_model.principal_amount_limit.label("principal_amount_limit"),
            )
        ).first()
        return user_eq
    
    
    @classmethod
    def update_equities_to_finished_status(cls):
        """ 将到期的权益状态改为结束态 """
        now_ = now()
        model = cls.MODEL
        rows = model.query.filter(
            model.type == EquityType.INVEST_INCREASE,
            model.status.in_([
                model.Status.PENDING,
                model.Status.USING,
            ]),
            model.finished_at < now_,
        ).all()
        for row in rows:
            row.status = cls.END_STATUS_MAP[row.status]
        db.session.commit()
        

class IncreaseEquityHourlyProc(InvestmentHourlyBase):
    """理财加息权益小时利息计算器"""

    BUS_TYPE = InterestStatisticTime.BusType.INCREASE
    HOUR_MODEL = UserHourIncEquityHistory

    def process_hourly_interest(self, report_hour: datetime):
        current_app.logger.info(f"计算 {datetime_to_str(report_hour)} 小时的理财加息权益利息")
        ue_model = UserEquity
        ue_detail_model = UserInvestIncreaseEquity
        user_equities = (
            ue_model.query.join(ue_detail_model, ue_model.id == ue_detail_model.user_equity_id)
            .filter(
                ue_model.type == EquityType.INVEST_INCREASE,
                ue_model.status == ue_model.Status.USING,
                ue_model.finished_at >= report_hour + timedelta(hours=1),  # 结束时间需要大于理财小时
                ue_detail_model.active_at <= report_hour,
            )
            .with_entities(
                ue_model.user_id,
                ue_detail_model.user_equity_id,
                ue_detail_model.investment_asset,
                ue_detail_model.increase_rate,
                ue_detail_model.principal_asset,
                ue_detail_model.principal_amount_limit,
                ue_detail_model.activation_days,
                ue_detail_model.usable_days,
            )
            .all()
        )

        # 一个用户只能激活一张卡券，如果检测到用户激活多张卡券，过滤该用户+告警
        counter = Counter(ue.user_id for ue in user_equities)
        dup_user_ids = {user_id for user_id, count in counter.items() if count > 1}
        # 重复激活告警
        if dup_user_ids:
            send_alert_notice(
                f'发现重复激活理财加息权益用户: {dup_user_ids}',
                config["ADMIN_CONTACTS"]["invest_notice"],
            )

        asset_user_balance = InvestmentDataProc.get_dt_asset_user_snap_map(report_hour)
        valid_assets = AssetInvestmentConfig.get_valid_assets()
        prices = PriceManager.assets_to_usd(valid_assets)
        # 过滤掉无效的权益
        valid_eq = []
        for ue in user_equities:
            # 重复用户
            if ue.user_id in dup_user_ids:
                continue
            asset = ue.investment_asset
            if asset not in valid_assets:
                continue
            balance = asset_user_balance.get(asset, {}).get(ue.user_id, Decimal())
            if balance <= 0:
                continue
            valid_eq.append(ue)

        user_ids = {ue.user_id for ue in valid_eq}
        exist_set = self.get_exists_set(user_ids, report_hour, ("user_id", "user_equity_id"))

        rows = []
        for ue in valid_eq:
            if (ue.user_id, ue.user_equity_id) in exist_set:
                continue
            # 获取快照数量
            balance = asset_user_balance[ue.investment_asset][ue.user_id]
            # 计算加息最大数量
            interest_data = self.calculate_user_hourly_interest(balance, ue, prices)
            if not interest_data:
                continue
            row = UserHourIncEquityHistory(
                report_hour=report_hour,
                user_id=ue.user_id,
                user_equity_id=ue.user_equity_id,
                asset=ue.investment_asset,
                balance=interest_data["balance"],
                interest_amount=interest_data["amount"],
                rate=interest_data["rate"],
            )
            rows.append(row)

        self.batch_save_rows(rows)

    @classmethod
    def calculate_user_hourly_interest(cls, balance, ue, prices) -> Dict:
        max_amount = cls._calc_max_interest_amount(ue.investment_asset, ue.principal_asset, ue.principal_amount_limit, prices)
        use_balance = min(balance, max_amount)
        if not use_balance:
            return {}
        # 计算利息
        interest_amount = cls._calc_interest_amount(use_balance, ue.increase_rate)
        return {
            "amount": interest_amount,
            "balance": use_balance,
            "rate": ue.increase_rate,
        }

    @classmethod
    def _calc_max_interest_amount(
        cls, inv_asset: str, prin_asset: Decimal, prin_amount_limit: Decimal, prices: Dict[str, Decimal]
    ) -> Decimal:
        zero = Decimal()
        prin_rate = Decimal("1") if prin_asset == Currency.USD else prices.get(prin_asset, zero)
        inv_price = prices.get(inv_asset, Decimal())
        if not inv_price:
            return zero
        amount = prin_amount_limit * prin_rate / inv_price
        return amount

    @classmethod
    def _calc_interest_amount(cls, amount, rate):
        hour_rate = rate / cls.YEAR_HOURS
        if hour_rate == 0:
            return 0
        return quantize_amount(amount * hour_rate, PrecisionEnum.COIN_PLACES)

    def batch_save_rows(self, rows: List[UserHourIncEquityHistory]):
        for batch_rows in batch_iter(rows, 2000):
            db.session.bulk_save_objects(batch_rows)
            db.session.commit()


class IncreaseEquityDailyProc(InvestmentDailyBase):
    """理财加息权益日利息计算器"""

    BUS_TYPE = InterestStatisticTime.BusType.INCREASE
    DAY_MODEL = UserDailyIncEquityHistory

    def process_daily_interest(self, report_date: date):
        current_app.logger.info(f"聚合 {report_date} 日的所有理财加息权益利息")

        snap_date = report_date + timedelta(days=1)
        asset_user_snap_map = InvestmentDataProc.get_dt_asset_user_snap_map(date_to_datetime(snap_date))

        asset_user_interests = self._aggregate_daily_interest(report_date)
        valid_interests = self.filter_zero_balance_record(asset_user_interests, asset_user_snap_map)

        self._save_daily_interest(valid_interests, report_date)

    def _save_daily_interest(self, user_asset_interest: Dict, report_date: date):
        batch_size = 2000
        all_user_ids = set()
        for user_interests in user_asset_interest.values():
            all_user_ids.update(user_interests.keys())
        exist_set = self.get_exists_daily_set(all_user_ids, report_date, ("user_id", "user_equity_id"))

        for asset, user_interests in user_asset_interest.items():
            user_ids = list(user_interests.keys())
            for batch_ids in batch_iter(user_ids, batch_size):
                insert_rows = []
                for user_id in batch_ids:
                    interest = user_interests[user_id]["total_interest"]
                    user_equity_id = user_interests[user_id]["user_equity_id"]
                    if interest > 0 and (user_id, user_equity_id) not in exist_set:
                        # 创建日利息记录
                        daily_record = UserDailyIncEquityHistory(
                            report_date=report_date,
                            user_id=user_id,
                            user_equity_id=user_equity_id,
                            asset=asset,
                            interest_amount=interest,
                            system_user_id=config["EQUITY_CENTER_ADMIN_USER_ID"],
                        )
                        insert_rows.append(daily_record)
                if insert_rows:
                    db.session.bulk_save_objects(insert_rows)
                    db.session.commit()

    def _aggregate_daily_interest(self, report_date: date):
        start_hour = date_to_datetime(report_date)
        end_hour = start_hour + timedelta(hours=24)
        hour_model = UserHourIncEquityHistory
        # 汇总24小时利息详情
        hour_records = (
            hour_model.query.filter(hour_model.report_hour >= start_hour, hour_model.report_hour < end_hour)
            .group_by(
                hour_model.user_id,
                hour_model.asset,
                hour_model.user_equity_id,
            )
            .with_entities(
                hour_model.user_id,
                hour_model.asset,
                hour_model.user_equity_id,
                func.sum(hour_model.interest_amount).label("total_interest"),
            )
            .all()
        )

        # 用户每日利息
        user_asset_interest = defaultdict(lambda: defaultdict(Decimal))
        for row in hour_records:
            user_asset_interest[row.asset][row.user_id] = {
                "user_equity_id": row.user_equity_id,
                "total_interest": row.total_interest,
            }

        return user_asset_interest

    @classmethod
    def update_user_equity_summary_data(cls, report_date: date):
        current_app.logger.info(f"更新用户累计利息权益数据: {report_date}")
        int_model = UserDailyIncEquityHistory
        # 获取用户今日有加息权益发放的记录
        int_records = int_model.query.filter(
            int_model.report_date == report_date,
            int_model.status == int_model.Status.FINISHED,
        ).all()
        if not int_records:
            return
        # 找到对应的权益记录
        price_map = PriceManager.assets_to_usd()
        user_equity_ids = {row.user_equity_id for row in int_records}
        equ_model = UserInvestIncreaseEquity
        equ_rows = equ_model.query.filter(equ_model.user_equity_id.in_(user_equity_ids)).all()
        equ_map = {row.user_equity_id: row for row in equ_rows}
        for row in int_records:
            user_equity: UserInvestIncreaseEquity = equ_map.get(row.user_equity_id)
            if not user_equity or user_equity.payout_date == report_date:
                continue
            user_equity.increase_amount += row.interest_amount
            # 换算成 usd 再换回 usdt
            usdt_price = price_map.get(user_equity.principal_asset, Decimal(1))
            user_equity.increase_usd += row.interest_amount * price_map.get(row.asset, Decimal()) / usdt_price
            user_equity.payout_date = report_date
        db.session.commit()


class IncreaseEquityPayoutProc(InvestmentDailyPayoutBase):
    BUS_TYPE = InterestStatisticTime.BusType.INCREASE
    DAY_MODEL = UserDailyIncEquityHistory

    def process_daily_interest_payout(self, report_date: date) -> bool:
        # 聚合日利息
        current_app.logger.info(f"发放 {report_date} 日的所有用户理财加息权益")
        interest_results = self.get_day_interest_pending_rows(report_date)
        self._payout_interest(interest_results)

        current_app.logger.info(f"日利息处理完成: {report_date}, 处理条数: {len(interest_results)}")
        return True

    def get_day_interest_pending_rows(self, report_date: date) -> List[UserDailyIncEquityHistory]:
        return self.DAY_MODEL.query.filter(
            self.DAY_MODEL.report_date == report_date,
            self.DAY_MODEL.status.in_(
                [
                    self.DAY_MODEL.Status.CREATED,
                    self.DAY_MODEL.Status.DEDUCTED,
                ]
            ),
        ).all()

    def _payout_interest(self, interest_rows: List[UserDailyIncEquityHistory]):
        """发放利息"""
        client = ServerClient()
        # 性能优化：解除 sqlalchemy 跟踪
        db.session.expunge_all()
        for row in interest_rows:
            if row.status == self.DAY_MODEL.Status.CREATED and row.interest_amount > 0:
                if self._deduct_system_balance(client, row):
                    self._success_deduct(row)
                    # expunge_all 需要手动更新状态
                    row.status = self.DAY_MODEL.Status.DEDUCTED

            if row.status == self.DAY_MODEL.Status.DEDUCTED:
                if self._add_user_balance(client, row):
                    self._success_add(row)

    def _deduct_system_balance(self, client, row: UserDailyIncEquityHistory):
        remark = f"invest increase deduct for {row.id}"
        error = f"{remark} {row.user_id} {row.asset} {row.interest_amount}"
        retry = BaseHTTPClient.retry(3)
        try:
            retry(client.add_user_balance)(
                user_id=row.system_user_id,
                asset=row.asset,
                amount=str(-row.interest_amount),
                business=BalanceBusiness.EQUITY_INVEST_INCREASE,
                business_id=row.id,
                detail={"remark": remark},
                account_id=SPOT_ACCOUNT_ID,
            )
            return True
        except Exception as e:
            current_app.logger.error(f"扣减理财加息权益失败 {error} error: {e}")
            return False

    def _add_user_balance(self, client, row: UserDailyIncEquityHistory):
        remark = f"invest increase add for {row.id}"
        error = f"{remark} {row.user_id} {row.asset} {row.interest_amount}"
        retry = BaseHTTPClient.retry(3)
        try:
            retry(client.add_user_balance)(
                user_id=row.user_id,
                asset=row.asset,
                amount=str(row.interest_amount),
                business=BalanceBusiness.EQUITY_INVEST_INCREASE,
                business_id=row.id,
                detail={"remark": remark},
                account_id=AssetInvestmentConfig.ACCOUNT_ID,
            )
            return True
        except Exception as e:
            current_app.logger.error(f"增加理财加息权益失败 {error} error: {e}")
            return False

    def _success_deduct(self, row: UserDailyIncEquityHistory):
        self.DAY_MODEL.query.filter(self.DAY_MODEL.id == row.id).update(
            {
                self.DAY_MODEL.status: self.DAY_MODEL.Status.DEDUCTED,
                self.DAY_MODEL.deducted_at: now(),
            },
            synchronize_session=False,
        )
        db.session.commit()

    def _success_add(self, row: UserDailyIncEquityHistory):
        self.DAY_MODEL.query.filter(self.DAY_MODEL.id == row.id).update(
            {
                self.DAY_MODEL.status: self.DAY_MODEL.Status.FINISHED,
                self.DAY_MODEL.finished_at: now(),
            },
            synchronize_session=False,
        )
        db.session.commit()


class IncreaseEquitySchedule(InvestmentScheduleBase):
    """理财加息权益任务"""

    BUS_TYPE = InterestStatisticTime.BusType.INCREASE
    HOUR_PROC = IncreaseEquityHourlyProc
    DAY_PROC = IncreaseEquityDailyProc
    PAYOUT_PROC = IncreaseEquityPayoutProc

    def get_payout_pending_date(self) -> date:
        """获取日利息未发放的最大日期"""
        model = UserDailyIncEquityHistory
        row = model.query.filter(
            model.status.in_(
                [
                    model.Status.CREATED,
                    model.Status.DEDUCTED,
                ]
            ),
        ).order_by(model.report_date).first()
        if not row:
            raise ValueError("没有待发放的日利息")
        return row.report_date

    def day_payout_schedule(self):
        """日利息发放任务"""
        processor = self.PAYOUT_PROC()
        row = InterestStatisticTime.query.filter(InterestStatisticTime.bus_type == self.BUS_TYPE).first()
        if row.day_payout_date:
            start_date = row.day_payout_date + timedelta(days=1)
        else:
            # 获取日利息未发放的最大日期
            start_date = self.get_payout_pending_date()
        while start_date < today():
            processor.run(start_date)
            IncreaseEquityDailyProc.update_user_equity_summary_data(start_date)
            start_date += timedelta(days=1)

