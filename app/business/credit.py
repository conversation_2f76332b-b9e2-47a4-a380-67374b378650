#!/usr/bin/python
# -*- coding: utf-8 -*-
import json
import time
from collections import defaultdict
from datetime import timed<PERSON><PERSON>
from decimal import Decimal
from typing import Optional, <PERSON>, Dict, Tuple

from flask import current_app
from sqlalchemy import func, or_

from app.caches.operation import CreditRiskNoticeCache
from app.common import MessageTitle, MessageContent, MessageWebLink, PrecisionEnum
from app.models import (
    CreditAssetHistory,
    User,
    LendableAssetChangeHistory,
    CreditAssetRate,
    CreditAssetUserRate,
    CreditAssetInterestReceivableHistory, MarginAssetRule,
    Message,
)
from app.models.mongo.op_log import AdminOperationLogMySQL as AdminOperationLog, OPNamespaceObjectWallet
from app.models import CreditBalance
from app.models import CreditUser
from app.models import MarketMaker
from app.models import db
from app.models.pledge import LoanAsset
from app.utils import now, celery_task, quantize_amount, amount_to_str
from .clients import ServerClient, BalanceBusiness
from .lendable import LendableAmountProcessor
from .lock import <PERSON>acheLock, <PERSON><PERSON><PERSON><PERSON>, lock_call
from .margin.helper import MarginUserAccountInfo
from .prices import PriceManager
from .user import UserSettings, get_credit_user_and_sub_balance_to_usd
from ..assets import list_all_assets, get_asset_config
from ..business.alert import send_alert_notice
from ..business.market_maker import MarketMakerHelper
from ..caches.prices import InvisibleAssetsCache
from ..common import CeleryQueues
from ..config import config
from ..exceptions import (
    FlatAmountBiggerThanBalance,
    FlatAmountBiggerThanUnflatAmount, FlatFail,
    InternalServerError, InvalidArgument,
)
from ..models.spot import SystemAssetLiability
from ..utils.amount import format_amount_with_precision

WITHDRAW_RATE_PRE_WARNING_DELTA = Decimal("0.05")  # 可提现风险率-触发预警


def credit_user_has_unflat_asset(user: User):
    main_user_id = user.main_user_id if user.is_sub_account else user.id
    credit_user = CreditUser.query.filter(
        CreditUser.user_id == main_user_id,
        CreditUser.status == CreditUser.StatusType.PASS
    ).with_entities(CreditUser.credit_grade).first()
    credit_grade_normal_user = credit_user and credit_user.credit_grade is CreditUser.CreditGrade.NORMAL

    if credit_grade_normal_user and has_credit_unflat_asset(user):
        return True
    return False


def is_credit_user(user: User):
    """
    判断用户是否是授信用户
    :param user:
    :return:
    """
    main_user_id = user.main_user_id if user.is_sub_account else user.id
    credit_user = CreditUser.query.filter(
        CreditUser.user_id == main_user_id,
        CreditUser.status == CreditUser.StatusType.PASS,
    ).with_entities(CreditUser.user_id).first()
    if credit_user:
        return True
    else:
        return False


def list_credit_users() -> set:
    records = CreditUser.query.filter(
        CreditUser.status == CreditUser.StatusType.PASS,
    ).with_entities(CreditUser.user_id).all()
    return {i.user_id for i in records}


def has_credit_unflat_asset(user: User):
    """ 判断用户是否有未还授信 """
    main_user_id = user.main_user_id if user.is_sub_account else user.id
    credit_asset = CreditBalance.query.filter(
        CreditBalance.user_id == main_user_id,
        (CreditBalance.unflat_amount + CreditBalance.interest_amount) > 0,
    ).first()
    if credit_asset:
        return True
    else:
        return False


def get_credit_user_real_time_can_withdraw_usd(main_user_id: int) -> Tuple[bool, Decimal]:
    """ 获取授信用户实时的可提现市值, 避免超额提现, return (is_credit_user, can_withdraw_usd) """
    credit_user_record = CreditUser.query.filter(
        CreditUser.user_id == main_user_id,
        CreditUser.status == CreditUser.StatusType.PASS,
    ).first()
    if not credit_user_record:
        return False, Decimal()

    credit_asset_rows = CreditBalance.query.filter(CreditBalance.user_id == main_user_id,).with_entities(
        CreditBalance.asset,
        CreditBalance.unflat_amount,
        CreditBalance.interest_amount,
    ).all()
    asset_unflat_dict = defaultdict(lambda: Decimal())
    include_assets = set(list_all_assets()) - InvisibleAssetsCache().read()
    for balance_record in credit_asset_rows:
        if balance_record.asset not in include_assets:
            continue
        asset_unflat_dict[balance_record.asset] = balance_record.unflat_amount + balance_record.interest_amount

    current_usd_balance = get_credit_user_and_sub_balance_to_usd(main_user_id)

    if sum(asset_unflat_dict.values()) <= Decimal():
        can_withdraw_balance = current_usd_balance
    else:
        all_assets = asset_unflat_dict.keys()
        rates_cache = PriceManager.assets_to_usd(all_assets)
        unflat_usd_amount = sum(
            [Decimal(rates_cache.get(asset, Decimal(0))) * amount for asset, amount in asset_unflat_dict.items()]
        )
        unflat_usd_amount = quantize_amount(unflat_usd_amount, 8)
        assert unflat_usd_amount > Decimal()

        if credit_user_record.credit_grade is CreditUser.CreditGrade.NORMAL:
            can_withdraw_balance = current_usd_balance - credit_user_record.withdraw_rate * unflat_usd_amount
        else:
            # 超级授信用户不做限制
            can_withdraw_balance = current_usd_balance

    return True, quantize_amount(max(can_withdraw_balance, Decimal()), 8)


def get_user_old_balance_history(user_id, asset):
    """
    根据历史推算用户更新前资产
    :param user_id:
    :param asset:
    :return:
    """
    credit_histories = CreditAssetHistory.query.filter(
        CreditAssetHistory.user_id == user_id,
        CreditAssetHistory.asset == asset,
        CreditAssetHistory.status == CreditAssetHistory.StatusType.FINISH
    ).with_entities(
        CreditAssetHistory.credit_type,
        func.sum(CreditAssetHistory.amount).label('amount')
    ).group_by(CreditAssetHistory.credit_type)

    credit_type_map = {item.credit_type: item.amount for item in credit_histories}

    balance = credit_type_map.get(CreditAssetHistory.CreditType.CREDIT, Decimal('0')) - \
              credit_type_map.get(CreditAssetHistory.CreditType.FLAT, Decimal('0'))

    return balance


class CreditFlatOperation(object):

    def __init__(self, user_id: int, asset: str, flat_amount: Decimal, operate_user_id: Optional[int]):
        self.user_id = user_id
        self.asset = asset
        self.flat_amount = flat_amount
        self.server_client = ServerClient(current_app.logger)
        self._validate()
        self.operate_user_id = operate_user_id
        # flat_amount = _flat_balance_amount + _flat_interest_amount
        self._flat_balance_amount = 0  # 还余额数
        self._flat_interest_amount = 0  # 还利息数
        self.flat_history = self._flat_interest_and_create_flat_record()

    def _validate(self):
        result = self.server_client.get_user_balances(self.user_id)
        balance = Decimal(result.get(self.asset, {}).get('available', Decimal('0')))
        if self.flat_amount > balance:
            raise FlatAmountBiggerThanBalance

    def _flat_interest_and_create_flat_record(self):
        credit_balance = CreditBalance.query.filter(
            CreditBalance.user_id == self.user_id,
            CreditBalance.asset == self.asset
        ).first()
        old_total_amount = (
            credit_balance.unflat_amount + credit_balance.interest_amount
        )
        if old_total_amount < self.flat_amount:
            raise FlatAmountBiggerThanUnflatAmount

        #
        if credit_balance.interest_amount >= self.flat_amount:
            # 还款小于等于未还利息, 只还利息
            self._flat_interest_amount = self.flat_amount
        else:
            # 还款大于未还利息总额, 还全部利息和余额 or 利息为0
            self._flat_interest_amount = credit_balance.interest_amount
        self._flat_balance_amount = (
            self.flat_amount - self._flat_interest_amount
        )
        assert self._flat_balance_amount >= 0

        old_unflat_amount = credit_balance.unflat_amount
        new_balance = old_unflat_amount - self._flat_balance_amount
        credit_history = CreditAssetHistory()
        credit_history.asset = self.asset
        credit_history.credit_type = CreditAssetHistory.CreditType.FLAT
        credit_history.amount = self._flat_balance_amount  # 还币金额(不包括利息)
        credit_history.interest_amount = self._flat_interest_amount  # 还利息金额
        credit_history.unflat_amount = new_balance
        credit_history.user_id = self.user_id
        credit_history.oper_user_id = self.operate_user_id if self.operate_user_id else self.user_id
        credit_history.status = CreditAssetHistory.StatusType.CREATE
        credit_history.remark = 'user flat'
        db.session.add(credit_history)
        db.session.commit()
        return credit_history

    def flat(self):
        with CacheLock(
                LockKeys.credit_update_balance(self.user_id, self.asset),
                wait=False
        ):
            db.session.rollback()
            try:
                if self.operate_user_id:
                    AdminOperationLog.new_flat(
                        user_id=self.operate_user_id,
                        ns_obj=OPNamespaceObjectWallet.CreditRecord,
                        detail={
                            "asset": self.asset,
                            "flat_amount": str(self.flat_amount)
                        },
                        target_user_id=self.user_id,
                    )
                result = self.server_client.add_user_balance(
                    self.user_id,
                    self.asset,
                    amount=str(-self.flat_amount),
                    business=BalanceBusiness.CREDIT_REPAYMENT,
                    business_id=self.flat_history.id,
                    detail={
                        'remark': f"add credit flat 'credit_history_id':{self.flat_history.id}"
                    }
                )
                if not result:
                    current_app.logger.warning(
                        f"{self.user_id} {self.asset} {self.flat_history.id=} {self.flat_amount=} "
                        f"credit flat DUPLICATE_BALANCE_UPDATE"
                    )
                self.flat_history.finished_at = now()
                self.flat_history.status = CreditAssetHistory.StatusType.FINISH
                balance_record = CreditBalance.query.filter(
                    CreditBalance.user_id == self.user_id,
                    CreditBalance.asset == self.asset
                ).first()
                balance_record.unflat_amount = (
                    balance_record.unflat_amount - self._flat_balance_amount
                )
                balance_record.interest_amount = (
                    balance_record.interest_amount - self._flat_interest_amount
                )
                db.session.commit()
                if self._flat_balance_amount:
                    LendableAmountProcessor(
                        self.asset,
                        abs(self._flat_balance_amount),
                        LendableAssetChangeHistory.BusinessType.CREDIT_FLAT,
                    ).process_new_record()
                update_credit_user_risk_record_task.delay(self.user_id)

            except ServerClient.BadResponse:
                error_msg = f'credit flat failed user_id:{self.user_id}, asset:{self.asset}, amount:{-self.flat_amount}'
                self.flat_history.status = CreditAssetHistory.StatusType.FAIL
                db.session.commit()
                current_app.logger.error(error_msg)
                send_alert_notice(error_msg, config["ADMIN_CONTACTS"]["web_notice"])
                raise FlatFail


class CreditInterestHelper(object):
    @classmethod
    def get_day_rate_from_map(cls, day_rate_map: Dict, asset: str) -> Decimal:
        if asset in day_rate_map:
            return day_rate_map[asset]
        if CreditAssetUserRate.ALL_ASSET in day_rate_map:
            return day_rate_map[CreditAssetUserRate.ALL_ASSET]
        return Decimal()

    @classmethod
    def get_asset_day_rate(
        cls, asset: str, user_id: int = None
    ) -> Union[Decimal, None]:
        """
        获取某个币种的日利率, 同时配置了授信币种利率和用户利率，则优先取授信用户利率
        如果配置了ALL，再配置了单个币种，则该币种为单独利率，其余币种为ALL的利率。
        """
        if user_id is not None:
            # 超级授信用户不收取授信利息
            credit_user = CreditUser.query.filter(
                CreditUser.user_id == user_id,
                CreditUser.status == CreditUser.StatusType.PASS
            ).first()
            if not credit_user:
                raise RuntimeError(f'{user_id}不是授信用户')
            if credit_user.credit_grade is CreditUser.CreditGrade.SUPER:
                return None
            user_rates = CreditAssetUserRate.query.filter(
                CreditAssetUserRate.user_id == user_id,
                CreditAssetUserRate.asset.in_(
                    [asset, CreditAssetUserRate.ALL_ASSET]
                ),
                CreditAssetUserRate.status
                == CreditAssetUserRate.StatusType.PASSED,
                or_(
                    CreditAssetUserRate.expired_at > now(),
                    CreditAssetUserRate.expired_at.is_(None),
                ),
            ).all()
            user_rate_map = {i.asset: i.day_rate for i in user_rates}
            if asset in user_rate_map:
                return user_rate_map[asset]
            if CreditAssetUserRate.ALL_ASSET in user_rate_map:
                return user_rate_map[CreditAssetUserRate.ALL_ASSET]

        asset_rate = CreditAssetRate.query.filter(
            CreditAssetRate.asset == asset,
            CreditAssetRate.status == CreditAssetRate.StatusType.PASSED,
        ).first()
        if asset_rate:
            return asset_rate.day_rate

    @classmethod
    def get_user_asset_day_rate_map(cls, user_id: int) -> Dict[str, Decimal]:
        """
        获取某个用户下所有币种的日利率Map, 优先用户的利率, 其次币种的利率
        """
        user_rates = CreditAssetUserRate.query.filter(
            CreditAssetUserRate.user_id == user_id,
            CreditAssetUserRate.status
            == CreditAssetUserRate.StatusType.PASSED,
            or_(
                CreditAssetUserRate.expired_at > now(),
                CreditAssetUserRate.expired_at.is_(None),
            ),
        )
        user_rate_map = {i.asset: i.day_rate for i in user_rates}
        if CreditAssetUserRate.ALL_ASSET in user_rate_map:
            # 配置了ALL
            return user_rate_map

        all_asset_rates = CreditAssetRate.query.filter(
            CreditAssetRate.status == CreditAssetRate.StatusType.PASSED,
        )
        day_rate_map = {i.asset: i.day_rate for i in all_asset_rates}
        day_rate_map.update(user_rate_map)
        return day_rate_map

    @classmethod
    def add_new_credit_first_interest(
        cls, balance_record: CreditBalance, new_credit_amount: Decimal
    ) -> None:
        # 授信时就产生一笔(新增授信的)利息, 有设置授信利率 就插入 应收授信利息记录
        asset = balance_record.asset
        user_id = balance_record.user_id
        day_rate = CreditInterestHelper.get_asset_day_rate(
            asset=asset, user_id=user_id
        )
        if not day_rate:
            return

        interest_amount = quantize_amount(
            Decimal(new_credit_amount)  # 用新增的授信来算
            * Decimal(day_rate)
            / Decimal("24"),
            8,
        )
        balance_record.interest_amount += interest_amount
        cls.insert_interest_receivable_history(
            balance_record, interest_amount, day_rate
        )

    @classmethod
    def insert_interest_receivable_history(
        cls,
        balance_record: CreditBalance,
        receivable_amount: Decimal,
        day_rate: Decimal,
    ) -> None:
        # 插入应收利息记录，注意 新增授信时的利息不满足：
        # unflat_amount * day_rate * hour / 24 == receivable_amount
        receivable_history = CreditAssetInterestReceivableHistory(
            user_id=balance_record.user_id,
            asset=balance_record.asset,
            unflat_amount=balance_record.unflat_amount,
            receivable_amount=receivable_amount,
            day_rate=day_rate,
        )
        db.session.add(receivable_history)
        db.session.flush()


def check_and_set_credit_user_status(
    master_id: int,
    user_warn_rate: Optional[Decimal],
    withdraw_rate: Decimal,
):
    """
    检查用户的风险率，设置对应的限制, 包含划转功能
    :param master_id: master user_id
    :param user_warn_rate: user_warn_rate
    :param withdraw_rate: 提现风险率
    :return:
    """
    # 提现开关只设置主账号
    main_setting = UserSettings(master_id)
    if user_warn_rate is None:
        main_setting.withdrawals_disabled_due_to_credit_risk = False
    else:
        main_setting.withdrawals_disabled_due_to_credit_risk = user_warn_rate <= withdraw_rate


def check_credit_record(record, admin_user_id):
    """
    复审授信记录
    :param record: 授信记录
    :param admin_user_id: admin操作用户id
    :return:
    """
    from ..assets import get_asset_config
    operated_user = record.user_id
    asset = record.asset
    with CacheLock(
            LockKeys.credit_update_balance(operated_user, asset),
            wait=False):
        db.session.rollback()
        if record.status != CreditAssetHistory.StatusType.AUDIT:
            return
        # 内部授信数量不受限
        amount = record.amount
        record.check_user_id = admin_user_id
        credit_user = CreditUser.query.filter(
            CreditUser.user_id == operated_user,
            CreditUser.status == CreditUser.StatusType.PASS,
        ).first()
        if not credit_user:
            raise InvalidArgument(message='授信用户不存在')
        if credit_user.credit_grade is CreditUser.CreditGrade.NORMAL:
            lendable_amount = get_asset_config(asset).lendable_amount
            if not lendable_amount or lendable_amount < amount:
                raise InvalidArgument(message="剩余可借余额不足")
        # 先增加未还，然后再去加资产
        balance_record = CreditBalance.query.filter(
            CreditBalance.user_id == operated_user,
            CreditBalance.asset == asset,
        ).first()
        if not balance_record:
            balance_record = CreditBalance(
                asset=asset,
                user_id=operated_user,
                unflat_amount=amount,
                interest_at=now(),
            )
            db.session.add(balance_record)
        else:
            balance_record.unflat_amount += Decimal(amount)
        CreditInterestHelper.add_new_credit_first_interest(
            balance_record, amount
        )
        record.unflat_amount = balance_record.unflat_amount
        db.session.commit()

        balance_server = ServerClient(logger=current_app.logger)
        try:
            balance_server.add_user_balance(
                operated_user,
                asset=asset,
                business=BalanceBusiness.CREDIT,
                business_id=record.id,
                amount=str(record.amount),
                detail={
                    'remark': f"add credit 'credit_asset_history_id':{record.id}"
                }
            )
            record.finished_at = now()
            record.status = CreditAssetHistory.StatusType.FINISH
            db.session.commit()
            LendableAmountProcessor(
                asset, -amount,
                LendableAssetChangeHistory.BusinessType.CREDIT_LOAN
            ).process_new_record()
            update_credit_user_risk_record_task.delay(operated_user)
        except Exception as e:
            error_msg = f"add credit id:{record.id} user_id {operated_user} asset {asset} add amount {amount} error: {e!r}"
            current_app.logger.error(error_msg)
            record.status = CreditAssetHistory.StatusType.FAIL
            db.session.commit()
            raise InternalServerError(message=f"授信失败, 授信记录id为{record.id}")


@celery_task(queue=CeleryQueues.CREDIT)
@lock_call(with_args=["user_id"], wait=False)
def update_credit_user_risk_record_task(user_id):
    user = User.query.filter(User.id == user_id).first()
    if user.is_sub_account:
        return
    credit_user_record = CreditUser.query.filter(
        CreditUser.user_id == user_id,
        CreditUser.status == CreditUser.StatusType.PASS).first()
    if not credit_user_record:
        return
    credit_record_query = CreditBalance.query.filter(
        CreditBalance.user_id == user_id,
    ).with_entities(
        CreditBalance.asset,
        CreditBalance.unflat_amount,
        CreditBalance.interest_amount,
    )
    user_unflat_dict = defaultdict(lambda: Decimal())
    include_assets = set(list_all_assets()) - InvisibleAssetsCache().read()
    for balance_record in credit_record_query:
        if balance_record.asset not in include_assets:
            continue
        # 计算授信风险率时`未还授信市值`是本金加利息
        user_unflat_dict[balance_record.asset] = (
            balance_record.unflat_amount + balance_record.interest_amount
        )

    current_usd_balance = get_credit_user_and_sub_balance_to_usd(user_id)
    if current_usd_balance == Decimal():
        # 可能授信用户刚好审核通过，但余额还没读到
        time.sleep(1)
        current_usd_balance = get_credit_user_and_sub_balance_to_usd(user_id)

    if sum(user_unflat_dict.values()) <= Decimal():
        # 全部都已经还清
        check_and_set_credit_user_status(
            user_id,
            None,
            withdraw_rate=credit_user_record.withdraw_rate,
        )
        credit_user_record.unfinished_credit_balance = Decimal()
        credit_user_record.current_balance = current_usd_balance
        credit_user_record.warn_rate = None
        credit_user_record.can_withdraw_balance = current_usd_balance
        credit_user_record.can_withdraw = True
        db.session.commit()
    else:
        # 获取所有币种对usd价格
        all_assets = user_unflat_dict.keys()
        rates_cache = PriceManager.assets_to_usd(all_assets)
        unflat_usd_amount = sum([Decimal(rates_cache.get(asset, Decimal(0))) * amount
                                 for asset, amount in user_unflat_dict.items()])
        if unflat_usd_amount == Decimal():
            raise InternalServerError(message=f"asset rate error for check {user_id=} credit warn risk rate")

        unflat_usd_amount = quantize_amount(unflat_usd_amount, 8)
        user_warn_rate = current_usd_balance / unflat_usd_amount

        if credit_user_record.credit_grade is CreditUser.CreditGrade.NORMAL:
            check_and_set_credit_user_status(user_id, user_warn_rate,
                                             withdraw_rate=credit_user_record.withdraw_rate)
            can_withdraw_balance = quantize_amount(current_usd_balance - credit_user_record.withdraw_rate * unflat_usd_amount, 8)
            can_withdraw_balance = max(can_withdraw_balance, Decimal())
            can_withdraw = True if can_withdraw_balance > Decimal() else False
        else:
            # 超级授信用户不做限制
            can_withdraw_balance = current_usd_balance
            can_withdraw = True
        credit_user_record.unfinished_credit_balance = unflat_usd_amount
        credit_user_record.current_balance = current_usd_balance
        credit_user_record.warn_rate = user_warn_rate
        credit_user_record.can_withdraw_balance = can_withdraw_balance
        credit_user_record.can_withdraw = can_withdraw
        db.session.commit()
        if credit_user_record.credit_grade is CreditUser.CreditGrade.NORMAL:
            send_credit_user_risk_notice(credit_user_record)


def send_credit_user_risk_notice(credit_user_row: CreditUser):
    """ 发授信风险邮件给对应用户，发消息到微信群 """
    from app.business.email import send_credit_user_risk_notice_email

    withdraw_delta_rate = credit_user_row.warn_rate - credit_user_row.withdraw_rate
    if Decimal("0.0001") <= withdraw_delta_rate <= WITHDRAW_RATE_PRE_WARNING_DELTA:
        withdraw_notice_type = "withdraw_pre_warning"
    elif withdraw_delta_rate <= Decimal("0"):
        withdraw_notice_type = "withdraw_close"
    else:
        return

    user_id = credit_user_row.user_id
    notice_cache = CreditRiskNoticeCache(withdraw_notice_type, user_id)
    if notice_cache.exists():
        return

    if withdraw_notice_type == "withdraw_close":
        message_popup_expired_at = now() + timedelta(days=3)
        message_params = dict(
            warn_rate=f"{amount_to_str(credit_user_row.warn_rate * Decimal('100'))}%",
            withdraw_rate=f"{amount_to_str(credit_user_row.withdraw_rate * Decimal('100'))}%",
            trade_rate="",
        )
        db.session.add(
            Message(
                user_id=user_id,
                title=MessageTitle.CREDIT_RISK_WITHDRAW_CLOSE.name,
                content=MessageContent.CREDIT_RISK_WITHDRAW_CLOSE.name,
                params=json.dumps(message_params),
                extra_info=json.dumps(
                    dict(
                        web_link=MessageWebLink.ASSET_DASHBOARD_PAGE.value,
                        android_link="",
                        ios_link="",
                    )
                ),
                display_type=Message.DisplayType.POPUP_WINDOW,
                expired_at=message_popup_expired_at,
                channel=Message.Channel.ACCOUNT_SECURITY,
            )
        )
        db.session.commit()
    send_credit_user_risk_notice_email(
        notice_type=withdraw_notice_type,
        user_id=user_id,
        cur_warn_rate=credit_user_row.warn_rate,
        withdraw_rate=credit_user_row.withdraw_rate,
    )

    #
    user = User.query.filter(User.id == user_id).first()
    market_maker = MarketMaker.query.filter(MarketMaker.user_id == user_id).first()
    market_maker_remark = market_maker.remark if market_maker else ""
    wx_notice_type_desc_map = {
        "withdraw_pre_warning": "提现关闭预警",
        "withdraw_close": "提现关闭",
    }
    content_lines = [
        f"用户名：{user.communication_name}",
        f"备注：{market_maker_remark}",
        f"风险类型：{wx_notice_type_desc_map[withdraw_notice_type]}",
        f"应还授信市值：{quantize_amount(credit_user_row.unfinished_credit_balance, 2)} USD",
        f"账户总资产市值：{quantize_amount(credit_user_row.current_balance, 2)} USD",
    ]
    content = "\n".join(content_lines)
    send_alert_notice(content, config["ADMIN_CONTACTS"].get("credit_notice"))
    notice_cache.set(value="1", ex=notice_cache.ttl)


def get_loan_asset_report(asset='', in_margin=None, in_pledge=None):
    # 提供给内部做市商的币种借贷报表信息，与借贷资产报表数据一致

    def get_asset_liabilities(assets):
        result = {}
        records = SystemAssetLiability.query.order_by(SystemAssetLiability.id.desc()).limit(len(assets) * 2)
        for row in records:
            if row.asset not in result:
                result[row.asset] = row
        return result

    def calc_reserve_ratio(_liability: SystemAssetLiability):
        total_balance = (_liability.cold_wallet + _liability.hot_wallet + _liability.deposit_wallet +
                         _liability.onchain_wallet + _liability.staking_amount + _liability.staking_pending_reward -
                         _liability.processing_withdrawal)
        total_debt = (_liability.spot + _liability.margin + _liability.investment + _liability.perpetual +
                      _liability.margin_insurance + _liability.perpetual_insurance + _liability.pledge)
        if total_debt == 0:
            return 0
        return total_balance / total_debt

    leverage_base_asset = 'USDT'
    asset_list = list_all_assets()
    if asset:
        assets = [asset]
    else:
        assets = asset_list
    if in_margin is not None:
        model = MarginAssetRule
        margin_assets = model.query.filter(
            model.status == model.StatusType.OPEN
        ).with_entities(model.asset).all()
        margin_assets = [margin_asset.asset for margin_asset in margin_assets]
        if in_margin:
            assets = [asset for asset in assets if asset in margin_assets]
        else:
            assets = [asset for asset in assets if asset not in margin_assets]
    if in_pledge is not None:
        loan_assets = {i.asset for i in LoanAsset.query_open_rows()}
        if in_pledge:
            assets = [asset for asset in assets if asset in loan_assets]
        else:
            assets = [asset for asset in assets if asset not in loan_assets]

    # get latest records by asset order.
    asset_liabilities = get_asset_liabilities(asset_list)
    liabilities = [asset_liabilities[asset] for asset in asset_list if asset_liabilities.get(asset)]
    asset_liability_map = defaultdict(lambda: defaultdict(Decimal))

    for item in liabilities:
        asset_liability_map[item.asset]['system_asset'] += \
            item.cold_wallet + item.hot_wallet + \
            item.deposit_wallet + item.margin_unflat + item.credit_unflat + item.pledge_unflat + \
            item.staking_amount + item.staking_pending_reward

        asset_liability_map[item.asset]['system_debts'] += item.sys_debt

        asset_liability_map[item.asset]['system_equity'] += \
            asset_liability_map[item.asset]['system_asset'] - asset_liability_map[item.asset]['system_debts']

        asset_liability_map[item.asset]['wallet_asset'] += \
            (item.cold_wallet + item.hot_wallet + item.deposit_wallet + item.onchain_wallet + item.staking_amount +
             item.staking_pending_reward - item.processing_withdrawal)

    inner_maker_ids = MarketMakerHelper.list_inner_maker_ids(include_sub_account=False)
    query_result = CreditBalance.query.filter(
        CreditBalance.user_id.in_(inner_maker_ids)).group_by(
        CreditBalance.asset).with_entities(
        CreditBalance.asset,
        func.sum(CreditBalance.unflat_amount).label('total_amount')
    ).all()
    inner_maker_result = defaultdict(Decimal)
    for r in query_result:
        if r and r.total_amount:
            inner_maker_result[r.asset] = r.total_amount
    result = []
    asset_leverage_map = dict()
    account_infos = MarginUserAccountInfo(None).all_account_info

    for info in account_infos.values():
        if info['buy_asset_type'] == leverage_base_asset:
            asset_leverage_map[info['sell_asset_type']] = info['leverage']

    prices = PriceManager.assets_to_usd()
    for asset in assets:
        liability = asset_liabilities.get(asset)
        if not liability:
            current_reserve_ratio = 0
            margin_unflat_amount = 0
            credit_amount = 0
            pledge_unflat_amount = 0
        else:
            margin_unflat_amount = liability.margin_unflat
            credit_amount = liability.credit_unflat
            pledge_unflat_amount = liability.pledge_unflat
            current_reserve_ratio = calc_reserve_ratio(liability)
        config = get_asset_config(asset)

        item = {
            'asset': asset,
            'wallet_asset': format_amount_with_precision(asset_liability_map[asset]['wallet_asset']),
            'reserve_ratio': amount_to_str(config.reserve_ratio * 100, 2) + '%',
            'current_reserve_ratio': amount_to_str(current_reserve_ratio * 100, 2) + '%',
            'min_reserve_ratio': amount_to_str(config.reserve_ratio * 100, 2) + '%',
            'inner_credit_amount': format_amount_with_precision(inner_maker_result[asset]),
            'outer_credit_amount': format_amount_with_precision(credit_amount - inner_maker_result[asset]),
            'lendable_amount': format_amount_with_precision(config.lendable_amount),
            'lent_amount': format_amount_with_precision(credit_amount + margin_unflat_amount + pledge_unflat_amount),
            'credit_amount': format_amount_with_precision(credit_amount),
            'margin_unflat_amount': format_amount_with_precision(margin_unflat_amount),
            'pledge_unflat_amount': format_amount_with_precision(pledge_unflat_amount),
            'system_asset': format_amount_with_precision(asset_liability_map[asset]['system_asset']),
            'system_asset_usd': amount_to_str(
                asset_liability_map[asset]['system_asset'] * prices.get(asset, Decimal()),
                PrecisionEnum.CASH_PLACES),
            'system_debts': format_amount_with_precision(asset_liability_map[asset]['system_debts']),
            'system_debts_usd': amount_to_str(
                asset_liability_map[asset]['system_debts'] * prices.get(asset, Decimal()),
                PrecisionEnum.CASH_PLACES),
            'system_equity': format_amount_with_precision(asset_liability_map[asset]['system_equity']),
            'margin_renew_enabled': config.margin_renew_enabled,
        }
        result.append(item)
    return {'assets': asset_list, 'data': result}
