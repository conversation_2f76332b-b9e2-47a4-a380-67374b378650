import copy
from collections import defaultdict
from datetime import timed<PERSON><PERSON>
from itertools import chain
from decimal import Decimal
from typing import List, Dict
from flask import current_app
from pyroaring import BitMap
from sqlalchemy import func

from app.assets import try_get_asset_config, list_all_assets
from app.business import (
    TradeSummaryDB, PriceManager, ExchangeLogDB,
    ServerClient, UserSettings, filter_active_users, SPOT_ACCOUNT_ID, MAX_ORDER_ACCOUNT_ID,
)
from app.business.amm import LiquidityService, get_all_user_amm_assets
from app.business.external_dbs import (PerpetualSummaryDB, TradeLogDB, 
    PerpetualLogDB)
from app.business.margin.helper import (
    Mar<PERSON><PERSON>elper, MarginUserAccountInfo,
    SELL_TYPE, BUY_TYPE,
)
from app.business.pledge.helper import is_pledge_account
from app.caches import MarginAccountIdCache, amount_to_str, MarketCache, IndexPriceCache, MarginAccountNameCache
from app.caches.prices import InvisibleAssetsCache
from app.common import PerpetualMarketType, AccountBalanceType
from app.models import User, UserBusinessRecord, \
    DailyAssetExchangeSiteReport, AssetInvestmentConfig
from app.models.staking import StakingAccount
from app.utils import quantize_amount


def get_user_fee_statistic(user_id, market, start_time, end_time):

    start_time_str = start_time.strftime('%Y-%m-%d')
    end_time_str = end_time.strftime('%Y-%m-%d')

    all_records = []
    for table_name in TradeSummaryDB.get_user_fee_summary_tables(start_time, end_time):
        sql = "select user_id, asset, sum(fee) from {table_name} where '{start_time}' <= trade_date" \
              " and trade_date <= '{end_time}' and market = '{market}' and user_id = '{user_id}' group by user_id, asset;".format(
            table_name=table_name,
            start_time=start_time_str,
            end_time=end_time_str,
            market=market,
            user_id=user_id
        )
        trade_summary_db = TradeSummaryDB()
        cursor = trade_summary_db.cursor()
        # noinspection PyBroadException
        try:
            cursor.execute(sql)
        except Exception:
            continue
        records = cursor.fetchall()
        all_records += records

    coin_fee = 0
    for record in all_records:
        coin_rate = PriceManager.asset_to_usd(record[1])
        coin_fee += Decimal(record[2]) * coin_rate

    return coin_fee


def get_user_perpetual_fee_statistic(user_id, market, start_time, end_time):
    start_time_str = start_time.strftime('%Y-%m-%d')
    end_time_str = end_time.strftime('%Y-%m-%d')

    all_records = []
    for table_name in PerpetualSummaryDB.get_user_fee_summary_tables(start_time, end_time):
        sql = "select user_id, asset, sum(fee) from {table_name} where '{start_time}' <= trade_date" \
              " and trade_date <= '{end_time}' and market = '{market}' and user_id = '{user_id}' group by user_id, asset;".format(
            table_name=table_name,
            start_time=start_time_str,
            end_time=end_time_str,
            market=market,
            user_id=user_id
        )
        trade_summary_db = PerpetualSummaryDB()
        cursor = trade_summary_db.cursor()
        # noinspection PyBroadException
        try:
            cursor.execute(sql)
        except Exception:
            continue
        records = cursor.fetchall()
        all_records += records

    coin_fee = 0
    for record in all_records:
        coin_rate = PriceManager.asset_to_usd(record[1])
        coin_fee += Decimal(record[2]) * coin_rate

    return coin_fee


def get_user_summary_statistic(user_id, start_time, end_time, coin_type=None, market_type=None):
    """
    获取指定用户成交
    :param user_id:
    :param start_time:
    :param end_time:
    :param coin_type:
    :param market_type:
    :return:
    """
    table_names = TradeSummaryDB.get_user_trade_summary_tables(start_time, end_time)
    start_time = start_time.strftime('%Y-%m-%d')
    end_time = end_time.strftime('%Y-%m-%d')
    all_records = []
    if coin_type and not market_type:
        for table_name in table_names:
            sql = "select market, sum(deal_count), sum(deal_amount), sum(deal_volume), sum(deal_buy_count)," \
                  " sum(buy_amount), sum(deal_sell_count), sum(sell_amount) from {table_name} where " \
                  "'{start_time}' <= trade_date and trade_date <= '{end_time}' and stock_asset='{coin_type}' and user_id='{user_id}' " \
                  "group by market;".format(
                table_name=table_name,
                start_time=start_time,
                end_time=end_time,
                coin_type=coin_type,
                user_id=user_id
            )
            trade_summary_db = TradeSummaryDB()
            cursor = trade_summary_db.cursor()
            # noinspection PyBroadException
            try:
                cursor.execute(sql)
            except Exception:
                continue
            records = cursor.fetchall()
            all_records += records

    elif market_type:
        for table_name in table_names:
            sql = "select market, sum(deal_count), sum(deal_amount), sum(deal_volume), sum(deal_buy_count), sum(buy_amount), " \
                  "sum(deal_sell_count), sum(sell_amount) from {table_name} where " \
                  "'{start_time}' <= trade_date and trade_date <= '{end_time}' and market='{market_type}' and user_id='{user_id}' " \
                  "group by market;".format(
                table_name=table_name,
                start_time=start_time,
                end_time=end_time,
                market_type=market_type,
                user_id=user_id
            )
            trade_summary_db = TradeSummaryDB()
            cursor = trade_summary_db.cursor()
            # noinspection PyBroadException
            try:
                cursor.execute(sql)
            except Exception:
                continue
            records = cursor.fetchall()
            all_records += records

    else:
        for table_name in table_names:
            sql = "select market, sum(deal_count), sum(deal_amount), sum(deal_volume), sum(deal_buy_count)," \
                  " sum(buy_amount), sum(deal_sell_count), sum(sell_amount) from {table_name} where " \
                  "'{start_time}' <= trade_date and trade_date <= '{end_time}' and user_id='{user_id}' group by market;".format(
                table_name=table_name,
                start_time=start_time,
                end_time=end_time,
                user_id=user_id
            )
            trade_summary_db = TradeSummaryDB()
            cursor = trade_summary_db.cursor()
            # noinspection PyBroadException
            try:
                cursor.execute(sql)
            except Exception:
                continue
            records = cursor.fetchall()
            all_records += records
    return all_records


def get_user_perpetual_summary_statistic(user_id, start_time, end_time, asset=None):
    """
    获取指定用户合约成交
    :param user_id:
    :param start_time:
    :param end_time:
    :param asset:
    :return:
    """
    start_time_str = start_time.strftime('%Y-%m-%d')
    end_time_str = end_time.strftime('%Y-%m-%d')

    all_records = []
    asset_price_map = PriceManager.assets_to_usd()
    asset_price_map['USD'] = 1
    columns = ('market', 'deal_count', 'deal_amount',
               'deal_volume', 'buy_count', 'buy_amount', 'buy_volume', 'sell_count', 'sell_amount', 'sell_volume', 'asset')
    for table_name in PerpetualSummaryDB.get_user_trade_summary_tables(start_time, end_time):
        trade_summary_db = PerpetualSummaryDB()

        # 正向合约交易
        direct_trade_select_list = \
            "select market, sum(deal_count), " \
            "sum(deal_volume) as deal_amount, " \
            "sum(deal_amount) as deal_volume, " \
            "sum(deal_buy_count), " \
            "sum(buy_volume) as buy_amount, " \
            "sum(buy_amount) as buy_volume, " \
            "sum(deal_sell_count), " \
            "sum(sell_volume) as sell_amount, " \
            "sum(sell_amount) as sell_volume, " \
            "money_asset "
        where = f"from {table_name} where " \
                  f"'{start_time_str}' <= trade_date and trade_date <= '{end_time_str}' " \
                f"and user_id='{user_id}' and market_type = '{PerpetualMarketType.DIRECT.value}' "
        if asset:
            where += f" and market='{asset}' "
        group_by = "group by market, money_asset;"
        sql = direct_trade_select_list + where + group_by

        cursor = trade_summary_db.cursor()
        # noinspection PyBroadException
        try:
            cursor.execute(sql)
        except Exception:
            pass
        direct_records = cursor.fetchall()
        direct_records = list(dict(zip(columns, r)) for r in direct_records)

        # 反向合约交易
        inverse_trade_select_list = \
            "select market, sum(deal_count), " \
            "sum(deal_amount) as deal_amount, " \
            "sum(deal_volume) as deal_volume, " \
            "sum(deal_buy_count), " \
            "sum(buy_amount) as buy_amount, " \
            "sum(buy_volume) as buy_volume, " \
            "sum(deal_sell_count), " \
            "sum(sell_amount) as sell_amount, " \
            "sum(sell_volume) as sell_volume, " \
            "money_asset "
        where = f"from {table_name} where " \
                  f"'{start_time_str}' <= trade_date and trade_date <= '{end_time_str}' " \
                f"and user_id='{user_id}' and market_type = '{PerpetualMarketType.INVERSE.value}' "
        if asset:
            where += f" and market='{asset}' "
        group_by = "group by market, money_asset;"
        sql = inverse_trade_select_list + where + group_by
        cursor = trade_summary_db.cursor()
        # noinspection PyBroadException
        try:
            cursor.execute(sql)
        except Exception:
            continue
        inverse_records = cursor.fetchall()
        inverse_records = list(dict(zip(columns, r)) for r in inverse_records)

        all_records += direct_records + inverse_records

        for r in all_records:
            r['deal_amount_usd'] = asset_price_map.get(r['asset'], 0) * r['deal_amount']
            r['buy_amount_usd'] = asset_price_map.get(r['asset'], 0) * r['buy_amount']
            r['sell_amount_usd'] = asset_price_map.get(r['asset'], 0) * r['sell_amount']

    return all_records


def user_margin_asset(user_id):
    record_list = []
    record_total_assets = dict(
        interest_balance=Decimal(),
        loan_volume=Decimal(),
        net_volume=Decimal(),
        total_volume=Decimal(),
        margin_loan_enabled=UserSettings(user_id).margin_loan_enabled
    )
    helper = MarginUserAccountInfo(user_id)
    status_result = helper.get_all_except_status()
    account_result = helper.get_all_account_data()
    balance_result = helper.get_user_margin_balance_data()
    index_prices = IndexPriceCache().get_all_price()
    assets_rate = PriceManager.assets_to_usd()
    for account_id, data in helper.all_account_info.items():
        if data['market_type'] in index_prices:
            index_price = index_prices[data['market_type']]
        else:
            index_price = IndexPriceCache().get_price(data['market_type'])

        market_data = copy.copy(data)
        market_data['balance_data'] = balance_result[account_id]
        market_data['status'] = status_result[account_id]
        market_data["loan"] = account_result[account_id]["loan"]
        market_data["interest"] = account_result[account_id]["interest"]
        sell_type_rate = assets_rate.get(market_data["sell_asset_type"], Decimal())
        buy_type_rate = assets_rate.get(market_data["buy_asset_type"], Decimal())
        market_data["available_balance_usd"] = \
            balance_result[account_id][SELL_TYPE]["available"] * sell_type_rate + \
            balance_result[account_id][BUY_TYPE]["available"] * buy_type_rate
        market_data["frozen_balance_usd"] = \
            balance_result[account_id][SELL_TYPE]["frozen"] * sell_type_rate + \
            balance_result[account_id][BUY_TYPE]["frozen"] * buy_type_rate
        market_data["loan_usd"] = market_data["loan"][SELL_TYPE] * sell_type_rate + \
                                  market_data["loan"][BUY_TYPE] * buy_type_rate
        market_data["interest_usd"] = market_data["interest"][SELL_TYPE] * sell_type_rate + \
                                      market_data["interest"][BUY_TYPE] * buy_type_rate
        market_data['net_usd'] = market_data["available_balance_usd"] + \
                                 market_data["frozen_balance_usd"] - \
                                 market_data["loan_usd"] - \
                                 market_data["interest_usd"]

        if sum(market_data["loan"].values()) > 0:
            buy_balance = sum(balance_result[account_id][BUY_TYPE].values())
            sell_balance = sum(balance_result[account_id][SELL_TYPE].values())
            buy_loan_amount = account_result[account_id]["loan"][BUY_TYPE]
            sell_loan_amount = account_result[account_id]["loan"][SELL_TYPE]
            buy_interest_amount = account_result[account_id]["interest"][BUY_TYPE]
            sell_interest_amount = account_result[account_id]["interest"][SELL_TYPE]
            x = buy_balance / index_price - buy_interest_amount / index_price + \
                sell_balance - sell_interest_amount
            y = buy_loan_amount / index_price + sell_loan_amount
            warn_rate = Decimal(x / y)
            market_data['rate'] = warn_rate
            all_to_flat_amount = (buy_loan_amount + buy_interest_amount) / index_price \
                                 + sell_loan_amount + sell_interest_amount
            burst_rate = helper.get_liquidation_rate(account_id, all_to_flat_amount)
            x1 = buy_loan_amount * burst_rate + buy_interest_amount - buy_balance
            y1 = sell_balance - sell_interest_amount - sell_loan_amount * burst_rate
            burst_price = Decimal(x1 / y1) if y1 != Decimal() else Decimal()
            if burst_price > 0:
                market_data['burst_price'] = burst_price
            else:
                market_data['burst_price'] = ''
        else:
            market_data['rate'] = ''
            market_data['burst_price'] = ''
        record_total_assets["interest_balance"] += market_data["interest_usd"]
        record_total_assets["loan_volume"] += market_data['loan_usd']
        record_total_assets["net_volume"] += market_data['net_usd']
        record_total_assets["total_volume"] += market_data["available_balance_usd"] + \
                                              market_data["frozen_balance_usd"]
        record_list.append(market_data)
    record_list.sort(key=lambda _x: _x['account_id'], reverse=False)
    return record_list, record_total_assets


def get_user_deal_order(user_id, start_date,
                        end_date, market, market_type, side, page, limit=50):
    from app.business.order import get_market_price_prices
    market_type_list = MarketCache.list_online_markets() + MarketCache.list_offline_markets()

    if market == 'margin':
        market_type_list = MarginHelper.market_type_list()
        account_id = int(
            list(MarginAccountIdCache.list_online_markets().keys())[0])
        if market_type:
            account_id = MarginAccountNameCache(market_type).dict["id"]
        market_type = market_type or MarginHelper.get_market_type_by_account_id(account_id)
    elif market == 'spot':
        account_id = 0
    else:
        account_id = -1

    if start_date:
        start_date = start_date
    else:
        start_date = 0
    if end_date:
        end_date = end_date
    else:
        end_date = 0

    market_server_client = ServerClient()

    result_records = market_server_client.market_user_deals(
        user_id=user_id,
        market=market_type,
        account_id=account_id,
        start_time=start_date,
        end_time=end_date,
        page=page,
        limit=limit,
        side=side,
    )

    records = []
    for item in result_records:
        total = Decimal(item['price']) * Decimal(item['amount'])
        precision = get_market_price_prices(item['market'])
        data = {
            'id': item['id'],
            'order_id': item['order_id'],
            'account_id': item['account'],
            'time': item['time'],
            'market': item['market'],
            'side': '卖出' if item['side'] == 1 else '买入',
            'price': item['price'],
            'amount': item['amount'],
            'total': amount_to_str(total, precision),
            'fee': amount_to_str(item['fee'], 8),
            'fee_asset': item['fee_asset'],
            'role': 'Maker' if item['role'] == 1 else 'Taker',
            'market_type': 'spot' if item['account'] == 0 else 'margin',
        }
        records.append(data)
    return records, result_records.has_next, market_type_list, result_records.page


def get_user_asset(user_id, filter_small_assets=False, hide_offline_assets=False):
    from app.business.p2p.order_factor import get_user_p2p_lock_amount_map
    from app.assets import list_all_assets

    client = ServerClient(current_app.logger)
    balance_data = client.get_user_balances(user_id)

    data = []
    lock_balance_data = client.get_user_locked_assets(user_id)
    prices = PriceManager.assets_to_usd()
    p2p_lock_map = get_user_p2p_lock_amount_map(user_id)
    invisible_assets = InvisibleAssetsCache().read()
    for item in list_all_assets():

        if item not in balance_data:
            continue

        item_usd_rate = prices.get(item, Decimal())
        frozen_amount = Decimal(balance_data[item].get('frozen', '0'))
        available_amount = balance_data[item].get('available', '0')
        market_value_usd = (Decimal(frozen_amount) + Decimal(available_amount)) * item_usd_rate
        if filter_small_assets and market_value_usd < Decimal("1"):
            continue
        if hide_offline_assets and item in invisible_assets:
            continue

        lock_amount = Decimal(lock_balance_data.get(item, '0'))
        p2p_lock_amount = p2p_lock_map.get(item, Decimal())
        data.append({
            'frozen': frozen_amount,
            'available': available_amount,
            'total': (Decimal(balance_data[item]['available'])
                      + Decimal(balance_data[item]['frozen'])),
            'lock': lock_amount - p2p_lock_amount,
            'p2p_lock': p2p_lock_amount,
            'p2p_real_lock': p2p_lock_map,
            'market_value_usd': quantize_amount(market_value_usd, 2),
            'asset': item
        })
    return data


def group_asset_users(timestamp: int = None) -> Dict[str, List[int]]:
    balances = get_asset_user_balances(timestamp)
    result = {k: list(v.keys()) for k,v in balances.items()}
    result['ALL'] = list(set(sum(result.values(), [])))
    return result


def get_asset_user_balances(timestamp: int) -> Dict[str, Dict[int, Decimal]]:
    all_assets = get_user_balances(timestamp)
    if not all_assets:
        return {}
    result = defaultdict(lambda: defaultdict(Decimal))
    for asset, users in all_assets.items():
        config = try_get_asset_config(asset)
        if not config:
            continue
        min_amount = config.asset_user_min_amount
        if min_amount is None:
            continue
        for user_id, balance in users.items():
            if balance >= min_amount:
                result[asset][user_id] = balance
    return result


def get_main_user_balance_usd_map(timestamp: int, sub_main_map: dict = None) -> dict[int, Decimal]:
    """主账号维度总资产市值"""
    from app.business.sub_account import get_all_sub_main_info

    table = ExchangeLogDB.user_account_balance_sum_table(timestamp)
    main_usd_map = defaultdict(Decimal)
    if table.exists():
        if not sub_main_map:
            sub_main_map, _main_subs_map = get_all_sub_main_info()
        records = table.select(
            'user_id',
            'balance_usd',
        )
        main_usd_map = defaultdict(Decimal)
        for r in records:
            mid = sub_main_map.get(r[0], r[0])
            main_usd_map[mid] += r[1]
    return main_usd_map


def get_user_balances(timestamp: int) -> Dict[str, Dict[int, Decimal]]:
    spot_table = TradeLogDB.slice_balance_table(timestamp)
    perpetual_table = PerpetualLogDB.slice_balance_table(timestamp)
    if not spot_table or not perpetual_table:
        return {}
    spot_assets = spot_table.select("user_id, asset, sum(balance)", group_by="user_id, asset")
    perpetual_assets = PerpetualLogDB.get_user_balances(timestamp=timestamp)
    amm_system_user_ids = set(LiquidityService.list_amm_system_user_ids())
    all_assets = defaultdict(lambda: defaultdict(Decimal))
    for user_id, asset, balance in chain(spot_assets, perpetual_assets):
        if user_id in amm_system_user_ids:  # 移除系统 amm 用户资产
            continue
        all_assets[asset][user_id] += balance
    # AMM 按实际用户归集资产
    for user_id, amm_balance_dict in get_all_user_amm_assets().items():
        for asset, balance in amm_balance_dict.items():
            all_assets[asset][user_id] += balance
    return all_assets


def get_user_account_balances(excluded_user_ids: set = None) -> (defaultdict, defaultdict):
    # excluded_user_id包含做市商及一些内部账号
    # ALL-用户资产
    asset_user_balance = defaultdict(lambda: defaultdict(Decimal))
    # 帐户-用户资产
    account_asset_user_balance = defaultdict(lambda: defaultdict(lambda: defaultdict(Decimal)))
    assets = list_all_assets()
    slice_time = TradeLogDB.get_slice_history_timestamp()
    amm_system_user_ids = set(LiquidityService.list_amm_system_user_ids())
    for item in TradeLogDB.get_user_balances(slice_time):
        user_id, asset, account, balance = item
        if asset not in assets:
            continue
        if excluded_user_ids and user_id in excluded_user_ids:
            continue
        if user_id not in amm_system_user_ids:  # 非系统 amm 用户资产
            asset_user_balance[asset][user_id] += balance
        if account == SPOT_ACCOUNT_ID:
            acc = AccountBalanceType.SPOT.name
        elif account == AssetInvestmentConfig.ACCOUNT_ID:
            acc = AccountBalanceType.INVESTMENT.name
        elif SPOT_ACCOUNT_ID < account <= MAX_ORDER_ACCOUNT_ID:
            acc = AccountBalanceType.MARGIN.name
        elif is_pledge_account(account):
            acc = AccountBalanceType.PLEDGE.name
        elif account == StakingAccount.ACCOUNT_ID:
            acc = AccountBalanceType.STAKING.name
        else:
            continue  # unknown account
        account_asset_user_balance[acc][asset][user_id] += balance

    perpetual_slice_time = PerpetualLogDB.get_slice_history_timestamp()
    for item in PerpetualLogDB.get_user_balances(perpetual_slice_time):
        user_id, asset, balance = item
        if asset not in assets:
            continue
        if excluded_user_ids and user_id in excluded_user_ids:
            continue
        asset_user_balance[asset][user_id] += balance
        acc = AccountBalanceType.PERPETUAL.name
        account_asset_user_balance[acc][asset][user_id] += balance

    # AMM
    for user_id, amm_balance_dict in get_all_user_amm_assets().items():
        for asset, balance in amm_balance_dict.items():
            if asset not in assets:
                continue
            if excluded_user_ids and user_id in excluded_user_ids:
                continue
            asset_user_balance[asset][user_id] += balance
            acc = AccountBalanceType.AMM.name
            account_asset_user_balance[acc][asset][user_id] += balance
    return asset_user_balance, account_asset_user_balance


def get_user_trade_value(start_date, end_date, user_ids):
    # 统计从server指定用户的交易额USD
    spot_mapping = TradeSummaryDB.get_assets_usd_group_by_user_sum(start_date, end_date, None, user_ids)
    perpetual_mapping = PerpetualSummaryDB.get_assets_usd_group_by_user_sum(start_date, end_date, None, user_ids)
    return sum(spot_mapping.values()) + sum(perpetual_mapping.values())


def get_active_count_map_by_interval(interval_list, end, sub_map):
    new_end = end
    active_set_map = dict()
    for idx, interval in enumerate(interval_list):
        start = end - timedelta(interval - 1)
        # 区间内活跃用户数
        active_user_ids = filter_active_users(start, new_end)
        main_user_ids = sub_to_main(active_user_ids, sub_map)
        active_set_map[interval] = calc_interval_val(main_user_ids, idx, interval_list, active_set_map)
        active_set_map[interval] = main_user_ids
        new_end = start - timedelta(1)

    active_count_map = {
        k: len(v) for k, v in active_set_map.items()
    }
    # 总用户人数
    active_count_map[-2] = User.query.filter(
        User.user_type != User.UserType.SUB_ACCOUNT
    ).with_entities(func.count(User.id)).scalar()
    active_count_map[-1] = active_count_map[-2] - sum([len(i) for i in active_set_map.values()])
    return active_count_map


def get_business_bitmap_map_by_interval(interval_list, end, business: UserBusinessRecord.Business):
    bitmap_map = dict()
    model = UserBusinessRecord
    new_end = end
    for idx, interval in enumerate(interval_list):
        start = end - timedelta(interval - 1)
        # 基于 user_trade_summary, 子账号已经统计到主账号里
        rows = model.query.filter(
            model.report_at >= start,
            model.report_at <= new_end,
            model.business == business,
        ).with_entities(
            model.new_user_bit_map,
        )
        user_bitmap = BitMap()
        for row in rows:
            user_bitmap.update(BitMap.deserialize(row.new_user_bit_map))
        bitmap_map[interval] = calc_interval_val(user_bitmap, idx, interval_list, bitmap_map)
        new_end = start - timedelta(1)

    newest_row = model.query.filter(model.business == business).order_by(model.report_at.desc()).first()
    history_bitmap = BitMap.deserialize(newest_row.history_user_bit_map)
    bitmap_map[-1] = calc_last_interval_bitmap(history_bitmap, bitmap_map)
    bitmap_map[-2] = history_bitmap
    return bitmap_map


def get_exchange_bitmap_map_by_interval(interval_list, end, sub_map):
    bitmap_map = dict()
    model = DailyAssetExchangeSiteReport
    new_end = end
    for idx, interval in enumerate(interval_list):
        start = end - timedelta(interval - 1)
        rows = model.query.filter(
            model.report_date >= start,
            model.report_date <= new_end,
        ).with_entities(
            model.user_bitmap
        )
        user_bitmap = BitMap()
        for row in rows:
            user_bitmap.update(BitMap.deserialize(row.user_bitmap))
        user_bitmap = BitMap(sub_to_main(set(user_bitmap), sub_map))
        bitmap_map[interval] = calc_interval_val(user_bitmap, idx, interval_list, bitmap_map)
        new_end = start - timedelta(1)

    newest_row = model.query.order_by(model.report_date.desc()).first()
    history_bitmap = BitMap.deserialize(newest_row.history_user_bitmap)
    history_bitmap = BitMap(sub_to_main(set(history_bitmap), sub_map))
    bitmap_map[-1] = calc_last_interval_bitmap(history_bitmap, bitmap_map)
    bitmap_map[-2] = history_bitmap
    return bitmap_map


def sub_to_main(ids: set, sub_map):
    for user_id in list(ids):
        if user_id in sub_map:
            ids.remove(user_id)
            ids.add(sub_map[user_id])
    return ids


def calc_interval_val(data, idx, interval_list, data_map):
    # 去除已经统计过的用户
    for interval in interval_list[:idx]:
        data -= data_map[interval]
    return data


def calc_last_interval_bitmap(history_bitmap, bitmap_map):
    last_interval_bitmap = copy.deepcopy(history_bitmap)
    for tmp_bitmap in bitmap_map.values():
        last_interval_bitmap = last_interval_bitmap - tmp_bitmap
    return last_interval_bitmap


def get_trade_bitmap_map(interval_list, spot_map, perpetual_map, exchange_map):
    trade_map = dict()
    map_list = [spot_map, perpetual_map, exchange_map]
    _list = [*interval_list, -1]
    for idx, interval in enumerate(_list):
        tmp_bitmap = BitMap.union(*[i[interval] for i in map_list])
        trade_map[interval] = calc_interval_val(tmp_bitmap, idx, _list, trade_map)
    trade_map[-2] = BitMap.union(*[i[-2] for i in map_list])
    return trade_map
