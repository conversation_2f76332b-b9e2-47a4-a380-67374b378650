from decimal import Decimal
from enum import Enum, auto
from hashlib import sha256
from hmac import HMAC
import json
from typing import Dict
import uuid

from flask import g
from app import config
from app.business.fiat.base import BasePartnerClient, Order, PaymentMethod, Quote, QuoteMethod, SupportType
from app.caches.fiat import FiatAuthTokenCache, FiatPriceCache
from app.common.constants import PrecisionEnum
from app.exceptions.basic import InvalidArgument
from app.models.base import db
from app.models.fiat import FiatOrder
from app.utils.amount import AmountType, amount_to_str, quantize_amount
from app.utils.date_ import now
from app.utils.files import AWSBucketPublic
from app.utils.http_client import RESTClient
from app.utils.parser import JsonEncoder
from app.utils.push import WebPagePath


class BTCDirectClient(BasePartnerClient):

    name = 'BTCDirect'
    logo = AWSBucketPublic.get_file_url('coinex_picture_manage_ic_btcdirect.png')
    buy_assets = ['ETH', 'BTC', 'USDT']
    buy_fiats = ['EUR']
    sell_fiats = []
    sell_assets = []
    exclude_buy_pairs = ('USDT-EUR',)
    exclude_sell_pairs = ('USDT-EUR',)
    support_types = [SupportType.BUY, ]
    buy_payment_methods = sell_payment_methods = [
        PaymentMethod.IDEAL,
        PaymentMethod.VISA,
        PaymentMethod.MASTER,
        PaymentMethod.BANK,
    ]
    buy_quote_methods = [QuoteMethod.BY_FIAT,]
    sell_quote_methods = [QuoteMethod.BY_ASSET]
    buy_fee_rate = Decimal('0.015')
    sell_fee_rate = Decimal('0.0042')
    min_fee = Decimal()

    buy_order_limit_min = Decimal(100)
    buy_order_limit_max = Decimal(5000)
    sell_order_limit_max = Decimal(1500)
    sell_order_limit_min = Decimal(200)
    daily_limit = Decimal(2600)
    monthly_limit = Decimal(78000)
    help_url = 'https://btcdirect.eu/en-gb'

    network_map = {
        'BTC': 'BTC',
        'USDT': 'ERC20',
        'USDC': 'ERC20',
        'ETH': 'ERC20',
    }

    class _UserStatus(Enum):
        NOT_EXISTS = auto()
        NOT_VERIFIED = auto()
        VERIFIED = auto()


    def __init__(self):
        self._conf = config['BTCDIRECT_CONFIG']
        self._client = RESTClient(self._conf['url'], headers={
            'Authorization': f'Bearer {self._get_authenticate_token()}',
            'X-Forwarded-For': getattr(g, 'request_ip', None)
        })

        # 传入email
        self._auth_client = lambda x: RESTClient(self._conf['url'], headers={
            'Authorization': f'Bearer {self._get_authenticate_token()}',
            'X-Forwarded-For': getattr(g, 'request_ip', None),
            'User-Identifier': self._get_user_identifier(x)
        })


    def _get_authenticate_token(self):
        cache = FiatAuthTokenCache(self.name)
        if token:= cache.read():
            return token
        resp = RESTClient(self._conf['url']).post('/api/v1/authenticate', json=dict(
            username=self._conf['client'],
            password=self._conf['password']
        ))
        cache.save(resp['token'])
        cache.expire(cache.ttl)
        return resp['token']

    def _get_user_identifier(self, email):
            sorted_email = ''.join(sorted(email))
            return HMAC(
                email.encode(),
                sorted_email.encode(),
                sha256
            ).hexdigest()

    def _check_user_status(self, user) -> _UserStatus:
        email = user.main_user.email
        try:
            resp = self._auth_client(email).get('/api/v2/user/verification-status', **dict(
                emailAddress=email
            ))
            return self._UserStatus.VERIFIED if len(resp) == 0 else self._UserStatus.NOT_VERIFIED
        except Exception as e:
            if e.code == 404:
                return self._UserStatus.NOT_EXISTS
            raise e

    def _register(self, user):
        email = user.main_user.email
        if not email:
            raise InvalidArgument
        user_identifier = self._get_user_identifier(email)
        if self._check_user_status(user) is self._UserStatus.NOT_EXISTS:
            resp = self._client.post('/api/v2/user', json=dict(
                identifier=user_identifier,
                email=email,
                # country=country.iso_2,
                country='DE',  # 默认 DE，让用户去服务商页面自己处理
                returnUrl=WebPagePath.FIAT_ORDER.value
            ))
            return resp


    def asset_to_fiat(self, asset: str, fiat: str, support_type=SupportType.BUY) -> Decimal:
        if support_type is SupportType.BUY:
            assets = self.buy_assets
            fiats = self.buy_fiats
        else:
            assets = self.sell_assets
            fiats = self.sell_fiats
        if asset not in assets or fiat not in fiats:
            raise ValueError(f'invalid asset {asset} or fiat {fiat}')
        cache = FiatPriceCache(self.name.lower())
        return quantize_amount(cache.get_price(asset, fiat, support_type), self._price_precision(asset))

    
    def fiat_to_asset_amount(self, fiat: str, asset: str, fiat_amount: AmountType) -> Decimal:
        price = self.asset_to_fiat(asset, fiat)
        if (fee := fiat_amount * self.buy_fee_rate) < self.min_fee:
            fiat_amount -= (self.min_fee - fee)
            if fiat_amount <= 0:
                return Decimal()
        asset_amount = fiat_amount / price
        return quantize_amount(asset_amount, 8)

    def quote(self, user, from_: str, to: str, amount: AmountType, support_type: SupportType) -> Quote:
        self._register(user)
        if support_type == SupportType.BUY:
            assets = self.buy_assets
            fiats = self.buy_fiats
        else:
            assets = self.sell_assets
            fiats = self.sell_fiats
        if from_ in assets and to in fiats:
            asset, fiat = from_, to
        elif from_ in fiats and to in assets:
            asset, fiat = to, from_
        else:
            raise ValueError(f'invalid asset {from_} or fiat {to}')

        resp = self._auth_client(user.email).post(f'/api/v1/{support_type.name.lower()}/quote',
                                json=dict(
                                    currencyPair=f'{asset}-{fiat}',
                                    fiatAmount=str(amount),
                                    fee=str(self.buy_fee_rate),
                                    paymentMethod='creditCard',
                                ))
        
        asset_amount, fiat_amount = resp['cryptoAmount'], resp['fiatAmount']
        if support_type == SupportType.BUY:
            price = quantize_amount(fiat_amount / asset_amount, self._price_precision(asset))    
        else:
            price = quantize_amount(asset_amount / fiat_amount, self._price_precision(asset))
        return Quote(
            id=resp['quote'] or str(uuid.uuid4()),
            asset=asset,
            asset_amount=quantize_amount(asset_amount, self._asset_amount_precision(asset)),
            fiat=fiat,
            fiat_amount=quantize_amount(fiat_amount, 2),
            price=price,
            support_type=support_type
        )

    def place_order(self, user, quote: Quote, address: str) -> Order:
        # 用户需要在btcdirect注册验证才可下单
        # 出金时需要提供用户银行账户信息才可下单，暂不支持
        # self._register(user)
        order_identifier = str(uuid.uuid4())
        resp = self._auth_client(user.email).post(f'/api/v2/{quote.support_type.lower()}/checkout',
                                                  json=dict(
                                                      baseCurrency=quote.asset,
                                                      quoteCurrency=quote.fiat,
                                                      quoteCurrencyAmount=quote.fiat_amount,
                                                      partnerOrderIdentifier=order_identifier,
                                                      returnUrl=WebPagePath.FIAT_HISTORY.value,
                                                      walletAddress=address
                                                  ))
        url, order_id = resp['checkoutUrl'], order_identifier
        return Order(
            id=order_id,
            payment_url=url,
            extra={}
        )

    def get_prices(self):
        buy_resp = self._client.get('/api/v1/prices', **dict(
            fee=str(self.buy_fee_rate * 100),
        ))
        sell_resp = self._client.get('/api/v1/prices', **dict(
            fee=str(self.sell_fee_rate * 100),
        ))
        result = {}

        infos = (
            (SupportType.BUY, buy_resp),
            (SupportType.SELL, sell_resp),
        )
        for support_type, resp in infos:
            for pair, price in resp[support_type.name.lower()].items():
                asset, fiat = pair.split('-')
                result[f"{support_type.value}-{asset}-{fiat}"] = amount_to_str(price, 
                                                                               self._price_precision(asset)) 
        return result
    
    def get_order(self, order_id, email, order_type):
        return self._auth_client(email).get(f'/api/v1/user/{order_type.lower()}/orders/{order_id}')

    def get_supported_fiats(self):
        data = self._client.get('/api/v1/system/currency-pairs')
        result = set()
        for item in data:
            pair = item['currencyPair']
            result.add(pair.split('-')[1])
        return list(result)

    def get_supported_assets(self):
        data = self._client.get('/api/v1/system/currency-pairs')
        result = set()
        for item in data:
            pair = item['currencyPair']
            result.add(pair.split('-')[0])
        return list(result)

    def update_order(data: Dict):
        _status_map = {
            'cancelled': FiatOrder.StatusType.DECLINED,
            'pending': FiatOrder.StatusType.PENDING,
            'in progress': FiatOrder.StatusType.PENDING,
            'blocked': FiatOrder.StatusType.DECLINED,
            'completed': FiatOrder.StatusType.APPROVED,
            'refunded': FiatOrder.StatusType.REFUNDED,
        }

        order = FiatOrder.query.filter(
            FiatOrder.order_id == data['partnerOrderIdentifier']
        ).first()
        if not order:
            return
        asset, asset_amount = data['value']['currencyCode'], data['value']['amount']
        fiat, fiat_amount = data['price']['currencyCode'], data['price']['amount']
        
        order.asset = asset
        order.coin_amount = quantize_amount(asset_amount, PrecisionEnum.COIN_PLACES)
        order.fiat_currency = fiat
        order.fiat_total_amount = quantize_amount(fiat_amount, PrecisionEnum.FIAT_PLACES)
        order.status = _status_map[data['status']]
        order.deposit_address = data['walletAddress']
        if order.status == FiatOrder.StatusType.APPROVED:
            order.approved_at = now()
        order.event = json.dumps(data, cls=JsonEncoder)
        db.session.commit()

    def get_tx_info(self, order_detail, asset: str, order_type: FiatOrder.OrderType):
        """仅当订单完成时，才有 TX 信息"""
        chain = tx_id = tx_url = None
        if order_type is FiatOrder.OrderType.BUY:
            chain = self.get_asset_chain(asset)  # to ours
            tx_id = order_detail['blockchainInfo']['transactionId']
            tx_url = order_detail['blockchainInfo']['transactionExplorer']
        return chain, tx_id, tx_url
