#!/usr/bin/env python3
import uuid
from decimal import Decimal

import typing
from flask import current_app
from itertools import product
from app import config
from app.business import PriceManager
from app.business.fiat import Quote, Order
from app.business.fiat.base import BasePartnerClient, PaymentMethod, QuoteMethod, SupportType
from app.caches.fiat import FiatPriceCache
from app.utils import AmountType, now, amount_to_str, quantize_amount, url_join, g_map
from app.utils.files import AWSBucketPublic
import xml.etree.ElementTree as ET
import hashlib
import requests


class VoletRequestXmlHelper:
    def __init__(self, api_name, token, account_email, api_password):
        self._api_name = api_name
        self._token = token
        self._account_email = account_email
        self._api_password = api_password
        self._base_request_xml = """
        <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:wsm="http://wsm.advcash/">
        <soapenv:Header/>
        <soapenv:Body>
            <wsm:{request_name}>
                {auth}
                {body}
            </wsm:{request_name}>
        </soapenv:Body>
        </soapenv:Envelope>"""

    def _get_request_header(self):
        return f"""
        <arg0>
            <apiName>{self._api_name}</apiName>
            <authenticationToken>{self._token}</authenticationToken>
            <accountEmail>{self._account_email}</accountEmail>
        </arg0>
        """

    def _get_request_body(self, args: typing.Union[dict, str]):
        if isinstance(args, dict):
            args = "".join([f"<{key}>{value}</{key}>" for key, value in args.items()])
        return f"""<arg1>{args}</arg1>"""

    def get_request_xml(self, request_name, args):
        auth = self._get_request_header()
        body = self._get_request_body(args)
        return self._base_request_xml.format(
            request_name=request_name,
            auth=auth,
            body=body
        )


class VoletClient(BasePartnerClient):
    name = "Volet.com"
    logo = AWSBucketPublic.get_file_url("coinex_picture_manage_ic_volet.png")
    buy_assets = ["BTC", "LTC", "BCH", "ETH", "TRX", "USDT"]
    buy_fiats = ['USD', 'EUR', 'GBP', 'BRL', "KZT"]
    sell_assets = []
    sell_fiats = []
    support_types = [SupportType.BUY]
    buy_payment_methods = [PaymentMethod.VISA, PaymentMethod.MASTER]
    buy_quote_methods = [QuoteMethod.BY_ASSET, QuoteMethod.BY_FIAT]
    buy_fee_rate = Decimal('0.05')
    min_fee = Decimal()

    buy_order_limit_min = Decimal(100)
    buy_order_limit_max = Decimal(8000)
    daily_limit = Decimal(11800)
    monthly_limit = Decimal(29500)
    help_url = 'https://volet.com/contacts/'
    
    network_map = {
        "USDT": "TRC20",
    }
    
    asset_currency_mapping = {
        "BTC": "BITCOIN",
        "LTC": "LITECOIN",
        "BCH": "BITCOIN_CASH",
        "ETH": "ETHEREUM",
        "USDT": "TETHER_TRC20",
        "TRX": "TETHER_TRC20",
    }

    def __init__(self):
        self._conf = config['VOLET_CONFIG']
        token = self._get_authentication_token()
        self._helper = VoletRequestXmlHelper(
            api_name=self._conf["apiName"],
            token=token,
            account_email=self._conf["accountEmail"],
            api_password=self._conf["apiPassword"]
        )

    def _get_authentication_token(self):
        utc_time = now()
        sign_str = f'{self._conf["apiPassword"]}:{utc_time.date().strftime("%Y%m%d")}:{utc_time.time().strftime("%H")}'
        return hashlib.sha256(sign_str.encode()).hexdigest().upper()

    def _parse_request_handler(self, response):
        if response.status_code // 100 != 2:
            current_app.logger.error(
                f"xml request error, status_code={response.status_code},"
                f"resp_context={response.content.decode()},"
                f"elapsed_seconds={response.elapsed.total_seconds()}"
            )
            raise RuntimeError(f"volet request api error, info: {response.text}")
        root = ET.fromstring(response.text)
        result = {}
        return_data = root.find(".//return")
        if return_data:
            for item in return_data:
                result[item.tag] = item.text
        return result

    def _request_xml_api(self, request_xml) -> dict:
        headers = {'Content-Type': 'application/soap+xml'}
        r = requests.post(f'{self._conf["url"]}/merchantWebService?wsdl', request_xml, headers=headers)
        r = self._parse_request_handler(r)
        return r

    def _get_quote_response(self, from_, to, action, amount):
        if from_ == 'USDT':
            from_ = 'USDT_TRC20'
        if to == 'USDT':
            to = 'USDT_TRC20'
        request_xml = self._helper.get_request_xml("checkCurrencyExchange", {
            "from": from_,
            "to": to,
            "action": action,
            "amount": amount,
            "depositMethod": "VISA"
        })
        r = self._request_xml_api(request_xml)
        return r

    def get_prices(self):
        assets = []
        fiats = []
        for asset, fiat in product(self.buy_assets, self.buy_fiats):
            assets.append(asset)
            fiats.append(fiat)
        prices = g_map(self._get_price, assets, fiats, ordered=True, fail_safe=Decimal(), size=5)
        return {
            f"buy-{asset}-{fiat}": amount_to_str(price, 2)
            for asset, fiat, price in zip(assets, fiats, prices) if price
        }

    def _get_price(self, asset: str, fiat: str) -> str:
        fiat_amount = self.buy_order_limit_max / 2
        if fiat_amount != 'USD':
            fiat_amount = amount_to_str(fiat_amount / PriceManager.fiat_to_usd(fiat), 2)
        r = self._get_quote_response(fiat, asset, "SELL", fiat_amount)
        price = r.get("rate", 0)
        if amount_exchanged := r.get("amountExchanged", 0):
            price = amount_to_str(Decimal(fiat_amount) / Decimal(amount_exchanged), self._price_precision(asset))
        return price

    def quote(self, user, from_: str, to: str, amount: AmountType, support_type=SupportType.BUY) -> Quote:
        if from_ in self.buy_assets and to in self.buy_fiats:
            asset, fiat = from_, to
            price = self.asset_to_fiat(asset, fiat)
            asset_amount = amount
            fiat_amount = quantize_amount(amount * price, 2)
        elif from_ in self.buy_fiats and to in self.buy_assets:
            asset, fiat = to, from_
            price = self.asset_to_fiat(asset, fiat)
            fiat_amount = amount
            asset_amount = quantize_amount(amount / price, self._asset_amount_precision(asset))
        else:
            raise ValueError(f'invalid asset and fiat `{from_}` `{to}`')
        return Quote(
            id=str(uuid.uuid4()),
            asset=asset,
            fiat=fiat,
            asset_amount=asset_amount,
            fiat_amount=fiat_amount,
            price=price,
            support_type=support_type
        )

    def fiat_to_asset_amount(self, fiat: str, asset: str, fiat_amount: AmountType) -> Decimal:
        price = self.asset_to_fiat(asset, fiat)
        if (fee := fiat_amount * self.buy_fee_rate) < self.min_fee:
            fiat_amount -= (self.min_fee - fee)
            if fiat_amount <= 0:
                return Decimal()
        asset_amount = fiat_amount / price
        return quantize_amount(asset_amount, 8)

    def asset_to_fiat(self, asset: str, fiat: str, support_type=SupportType.BUY) -> Decimal:
        if asset not in self.buy_assets or fiat not in self.buy_fiats:
            raise ValueError(f'invalid asset {asset} or fiat {fiat}')
        cache = FiatPriceCache(self.name.lower())
        return cache.get_price(asset, fiat)

    def place_order(self, user, quote: Quote, address: str) -> Order:
        ac_account_email = self._conf["accountEmail"]
        ac_sci_name = self._conf["sciName"]
        ac_amount = quantize_amount(quote.fiat_amount, 2)
        ac_currency = quote.fiat
        sci_password = self._conf["sciPassword"]
        order_id = str(uuid.uuid4())
        if quote.asset == "BCH":
            address = f"bitcoincash:{address}"
        request_xml = self._helper.get_request_xml(
            "createCryptoCurrencyWithdrawalInvoice",
            {
                "amount": ac_amount,
                "currency": ac_currency,
                "ecurrency": self.asset_currency_mapping[quote.asset],
                "depositMethod": "VISA",
                "orderId": order_id,
                "receiver": address
            }
        )
        response = self._request_xml_api(request_xml)
        ac_sign = f"{ac_account_email}:{ac_sci_name}:{ac_amount:.2f}:{ac_currency}:{sci_password}:{order_id}"
        ac_sign = hashlib.sha256(ac_sign.encode()).hexdigest()
        invoice_id = response.get("id", None)
        data = {
            "ac_account_email": ac_account_email,
            "ac_sci_name": ac_sci_name,
            "ac_crypto_currency_withdrawal_invoice_id": invoice_id,
            "ac_sign": ac_sign
        }
        payment_url = url_join(self._conf["payment_url"], 'crypto-exchange', **data)
        return Order(
            id=order_id,
            payment_url=payment_url,
            extra={
                "invoice_id": invoice_id
            }
        )

    def get_order(self, order_id: str):
        request_xml = self._helper.get_request_xml(
            "findCryptoCurrencyWithdrawalInvoiceByOrderId",
            order_id
        )
        r = self._request_xml_api(request_xml)
        return r