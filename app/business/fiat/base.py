#!/usr/bin/env python3
from collections import defaultdict
from enum import Enum
from decimal import Decimal
from typing import Any, Dict, List, NamedTuple, Optional

from sqlalchemy import func, text
from flask_babel import gettext as _
from app.assets.asset import asset_to_default_chain
from ...common import Currency
from ...common.fiat import PaymentMethod

from ...utils import AmountType
from ...utils import AWSBucketPublic


class _PartnerClientMeta(type):
    _TOP_FIATS = ["USD", "EUR", "JPY", "KRW"]
    _TOP_ASSETS = ['USDT', 'USDC', 'BTC', 'ETH']
    _partners = {}
    _assets = defaultdict(list)
    _fiats = defaultdict(list)

    @classmethod
    def _sort_top_list(mcs, sort_list: List[str], top_list: List[str]) -> List[str]:
        sort_list.sort(key=lambda x: (top_list.index(x) if x in top_list else len(top_list), x))
        return sort_list

    def __new__(mcs, *args, **kwargs):
        cls = super().__new__(mcs, *args, **kwargs)
        name = getattr(cls, 'name', None)
        if name is None:    # base class
            return cls
        mcs._partners[name.lower()] = cls
        for support_type in SupportType:
            if support_type not in cls.support_types:
                continue
            for fiat in getattr(cls, f"{support_type.value}_fiats", []):
                if fiat not in mcs._fiats[support_type]:
                    if not getattr(Currency, fiat.upper(), None):
                        raise ValueError(f'{fiat!r} does not in currency')
                    mcs._fiats[support_type].append(fiat)
            for asset in getattr(cls, f"{support_type.value}_assets", []):
                if asset not in mcs._assets[support_type] and asset in mcs._TOP_ASSETS:
                    mcs._assets[support_type].append(asset)
            mcs._fiats[support_type] = mcs._sort_top_list(mcs._fiats[support_type], mcs._TOP_FIATS)
            mcs._assets[support_type] = mcs._sort_top_list(mcs._assets[support_type], mcs._TOP_ASSETS)
        return cls


class Order(NamedTuple):
    id: str
    payment_url: str
    extra: Any


class QuoteMethod(Enum):
    BY_ASSET = 'by_asset'
    BY_FIAT = 'by_fiat'


class SupportType(Enum):
    SELL = 'sell'
    BUY = 'buy'


class Quote(NamedTuple):
    id: str
    asset: str
    asset_amount: AmountType
    fiat: str
    fiat_amount: AmountType
    price: AmountType
    support_type: SupportType


class BasePartnerClient(metaclass=_PartnerClientMeta):

    name: str
    logo: str
    sell_assets: List[str]
    buy_assets: List[str]
    sell_fiats: List[str]
    buy_fiats: List[str]
    support_types: List[SupportType]
    buy_payment_methods: List[PaymentMethod]
    sell_payment_methods: List[PaymentMethod]
    buy_quote_methods: List[QuoteMethod]
    sell_quote_methods: List[QuoteMethod]
    buy_fee_rate: Decimal
    sell_fee_rate: Decimal
    min_fee: Decimal
    sell_order_limit_min: Decimal
    sell_order_limit_max: Decimal
    buy_order_limit_min: Decimal
    buy_order_limit_max: Decimal
    daily_limit: Decimal
    monthly_limit: Decimal
    help_url: str
    network_map: Dict = {}
    exclude_buy_pairs: tuple[str] = ()
    exclude_sell_pairs: tuple[str] = ()

    def get_asset_chain(self, asset: str, user=None) -> str:
        return self.network_map.get(asset) or asset_to_default_chain(asset)

    def asset_to_fiat(self, asset: str, fiat: str, support_type: SupportType) -> Decimal:
        """get price of asset/fiat, with fee."""
        raise NotImplementedError

    def fiat_to_asset_amount(self, fiat: str, asset: str,
                             fiat_amount: AmountType) -> Decimal:
        """
        how many asset amount can xx fiat buy
        仅兼容 app，不再他用
        """
        raise NotImplementedError

    def quote(self, user, from_: str, to: str, amount: AmountType, support_type: SupportType) -> Quote:
        """quote to partner"""
        raise NotImplementedError

    def place_order(self, user, quote: Quote, address: str) -> Order:
        """place an order by quote result"""
        raise NotImplementedError

    def _price_precision(self, asset):
        if asset == 'TRX':
            return 4
        return 2

    def _asset_amount_precision(self, asset):
        if asset in ['USDC', 'USDT']:
            return 2
        return 8

    def get_prices(self):
        """get all assets to all fiats prices to cache"""
        raise NotImplementedError

    def get_supported_fiats(self):
        raise NotImplementedError

    def get_supported_assets(self):
        raise NotImplementedError
    


def get_fiat_partners(support_type: SupportType = None) -> List[str]:
    partners = _PartnerClientMeta._partners.keys()
    if not support_type:
        return list(partners)

    res: List[str] = []
    for name in list(partners):
        _cls = _PartnerClientMeta._partners.get(name.lower())
        if _cls and support_type in _cls.support_types:
            res.append(name)
    return res


def get_fiat_partner_client(name: str, support_type=SupportType.BUY) -> Optional[BasePartnerClient]:
    _cls = _PartnerClientMeta._partners.get(name.lower())
    if not _cls or (support_type not in _cls.support_types):
        return None
    return _cls()


def get_fiat_partner_asset_chain(client: BasePartnerClient, asset: str) -> Optional[str]:
    net_work_map = getattr(client, 'networks', {})
    return net_work_map.get(asset)


def get_fiat_assets(support_type=SupportType.BUY) -> List[str]:
    return _PartnerClientMeta._assets[support_type]


def get_fiat_currencies(support_type=SupportType.BUY) -> List[str]:
    return _PartnerClientMeta._fiats[support_type]


def get_fast_approved_partner(support_type=None):
    from app.models.fiat import FiatOrder
    query = FiatOrder.query.filter(
        FiatOrder.status == FiatOrder.StatusType.APPROVED,
        FiatOrder.approved_at.isnot(None)
    )
    if support_type:
        query = query.filter(
            FiatOrder.support_type == support_type,
        )
    approved_diff_times = query.group_by(
        FiatOrder.third_party
    ).with_entities(
        func.avg(func.TIMESTAMPDIFF(text('second'), FiatOrder.created_at, FiatOrder.approved_at)).label('avg_diff_time'),
        FiatOrder.third_party
    ).all()
    party_mapper = {diff_time: party for diff_time, party in approved_diff_times}
    min_time = min(party_mapper.keys())
    return party_mapper[min_time]


def first_use_fiat_tag_fmt(third_party):
    return f"FIRST_USED_FIAT_{third_party.upper()}_TIME"


class PartnerActivity(Enum):
    ZERO_FEE = '0 手续费'
    NEW = '上新'
    NEW_USER = '新用户'
    FEE_REDUCTION = "折扣"

    @property
    def icon(self):
        icon_map = {
            self.ZERO_FEE: AWSBucketPublic.get_file_url('coinex_picture_manage_ic_zero_fee.png'),
            self.NEW: AWSBucketPublic.get_file_url('coinex_picture_manage_ic_new.png'),
            self.NEW_USER: AWSBucketPublic.get_file_url('coinex_picture_manage_ic_new_user.png'),
            self.FEE_REDUCTION: AWSBucketPublic.get_file_url('coinex_picture_manage_ic_fee_reduction.png'),
        }
        return icon_map.get(self, "")

    def get_description(self, partner, value):
        description_map = {
            self.NEW_USER: _("%(partner)s 新用户专享减免%(activity_value)s手续费，具体费用以实际支付为准。"),
            self.FEE_REDUCTION: _("%(partner)s 用户专享减免%(activity_value)s手续费，具体费用以实际支付为准。")
        }
        if description := description_map.get(self):
            return _(description, partner=partner, activity_value='{}%'.format(value))
        else:
            return ""
