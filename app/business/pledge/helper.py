# -*- coding: utf-8 -*-
from collections import defaultdict
from decimal import Decimal
from datetime import timed<PERSON><PERSON>
from typing import Dict, Op<PERSON>, Tu<PERSON>, List

from sqlalchemy import func

from app.models.mongo.margin import AssetFloatingRateMySQL
from app.models.pledge import (
    LoanAsset,
    LoanAssetLiquidationRate,
    PledgeAsset,
    PledgeAssetStageConfig,
    PledgePosition,
    MIN_PLEDGE_ACCOUNT_ID,
    MAX_PLEDGE_ACCOUNT_ID,
)
from app.common import PrecisionEnum, ReportType
from app.business import ServerClient, cached
from app.caches.pledge import LoanAssetViewCache, PledgeAssetViewCache, PledgeAccountViewCache
from app.utils import quantize_amount, today


USDT_ASSET = "USDT"


class PledgeValueHelper:
    """ 计算质押资产价值（按指数价格换算为usdt） """

    @classmethod
    def get_asset_index_price(cls, asset: str, market_index_price_dict: Dict[str, Decimal]) -> Decimal:
        if asset == USDT_ASSET:
            return Decimal("1")
        m = f"{asset}{USDT_ASSET}"
        p = Decimal(market_index_price_dict[m])  # must exist
        return p

    @classmethod
    def get_pledge_asset_info_dict(cls, assets) -> Dict[str, Dict]:
        res = {}
        pledge_asset_infos = get_pledge_assets_infos(assets)
        stages: List[PledgeAssetStageConfig] = PledgeAssetStageConfig.query.all()
        stage_info_map = {i.stage: i for i in stages}
        for info in pledge_asset_infos:
            stage_info = stage_info_map[info.stage]
            res[info.asset] = {
                "stage": stage_info.stage,
                "collateral_ratio": stage_info.collateral_ratio,
                "max_pledge_usd": info.max_pledge_usd,
                "is_valid": info.status in (PledgeAsset.Status.OPEN, 
                                            PledgeAsset.Status.TEMP_OFFLINE),
            }
        return res

    @classmethod
    def calc_asset_pledge_values(
        cls,
        asset_amount_dict: Dict[str, Decimal],
        market_index_price_dict: Dict[str, Decimal],
        within_max: bool = True,
    ) -> Dict[str, Decimal]:
        asset_value_dict = {}
        pledge_asset_info_dict = cls.get_pledge_asset_info_dict(list(asset_amount_dict))
        for asset, amount in asset_amount_dict.items():
            pledge_asset_info = pledge_asset_info_dict.get(asset)
            if not pledge_asset_info or not pledge_asset_info["is_valid"]:
                # 计算可借数、减少质押币时，不算下架的质押币的价值
                continue
            index_price = cls.get_asset_index_price(asset, market_index_price_dict)
            collateral_ratio = Decimal(pledge_asset_info["collateral_ratio"])
            pledge_value = amount * index_price * collateral_ratio
            if within_max:
                # 计算可借数、借币时 市值不能超过最大市值；减少质押币时不做限制
                pledge_value = min(pledge_value, Decimal(pledge_asset_info["max_pledge_usd"]))
            asset_value_dict[asset] = quantize_amount(pledge_value, PrecisionEnum.COIN_PLACES)
        return asset_value_dict

    @classmethod
    def calc_pledge_value(
        cls,
        asset_amount_dict: Dict[str, Decimal],
        market_index_price_dict: Dict[str, Decimal],
    ) -> Decimal:
        """ 计算质押资产价值，计算可借数时(借币)调用 """
        if not asset_amount_dict:
            return Decimal()

        asset_value_dict = cls.calc_asset_pledge_values(asset_amount_dict, market_index_price_dict, within_max=True)
        return sum(asset_value_dict.values())

    @classmethod
    def calc_pledge_value_for_remove(
        cls,
        remove_asset: str,
        asset_amount_dict: Dict[str, Decimal],
        market_index_price_dict: Dict[str, Decimal],
    ) -> Decimal:
        """ 计算质押资产价值，减少质押币时调用 """
        if not asset_amount_dict:
            return Decimal()

        remove_asset_amount_dict = {}
        other_asset_amount_dict = {}
        for asset_, amount_ in asset_amount_dict.items():
            if asset_ == remove_asset:
                # 当前要减少币种的质押总价值：按实际质押价值算，即使超出质押市值上限，超出部分也要算
                remove_asset_amount_dict[asset_] = amount_
            else:
                # 其他币种有效质押总价值：按有效质押价值算，即如果超出质押市值上限，超出部分不算
                other_asset_amount_dict[asset_] = amount_
        remove_asset_value_dict = cls.calc_asset_pledge_values(
            remove_asset_amount_dict, market_index_price_dict, within_max=False
        )
        other_asset_value_dict = cls.calc_asset_pledge_values(
            other_asset_amount_dict, market_index_price_dict, within_max=True
        )
        pledge_value = sum(remove_asset_value_dict.values()) + sum(other_asset_value_dict.values())
        return pledge_value

    @classmethod
    def calc_loan_and_pledge_value(
        cls,
        loan_asset: str,
        loan_amount: Decimal,
        pledge_asset_amount_dict: Dict[str, Decimal],
        market_index_price_dict: Dict[str, Decimal],
    ) -> Tuple[Decimal, Dict[str, Decimal]]:
        """ 计算 待还资产价值（借币+利息）和 质押资产价值，计算ltv使用 """
        pledge_asset_value_dict = {}
        pledge_asset_info_dict = cls.get_pledge_asset_info_dict(list(pledge_asset_amount_dict))
        max_collateral_ratio = max([i["collateral_ratio"] for i in pledge_asset_info_dict.values()] or [Decimal("1")])
        for asset, amount in pledge_asset_amount_dict.items():
            index_price = cls.get_asset_index_price(asset, market_index_price_dict)
            if asset == loan_asset:
                # 质押币种和借币币种相同：兑换交易得到的借币币种, 不用乘以折价率
                collateral_ratio = Decimal("1")
                pledge_value = amount * index_price * collateral_ratio
            else:
                pledge_asset_info = pledge_asset_info_dict.get(asset)
                if pledge_asset_info:
                    collateral_ratio = Decimal(pledge_asset_info["collateral_ratio"])
                    pledge_value = amount * index_price * collateral_ratio
                    pledge_value = min(pledge_value, Decimal(pledge_asset_info["max_pledge_usd"]))
                else:
                    # 可能是中间币种，无质押币配置信息，取折价率最高
                    pledge_value = amount * index_price * max_collateral_ratio
            pledge_value = quantize_amount(pledge_value, PrecisionEnum.COIN_PLACES)
            pledge_asset_value_dict[asset] = quantize_amount(pledge_value, PrecisionEnum.COIN_PLACES)

        total_loan_value = loan_amount * cls.get_asset_index_price(loan_asset, market_index_price_dict)
        return total_loan_value, pledge_asset_value_dict

    @classmethod
    def sort_pledge_assets(
        cls,
        pledge_asset_amount_dict: Dict[str, Decimal],
        market_index_price_dict: Dict[str, Decimal] = None,
    ):
        """ 质押币排序, 档位低的强平顺序优先，同一档位的则按质押价值由高到低，用于强平兑换交易和手续费 """

        def _sort_func(_asset: str):
            if _asset == USDT_ASSET:
                # USDT 最优先
                _stage = -1
            else:
                _stage = pledge_asset_info_dict.get(_asset, {}).get("stage", float("inf"))
            _value = asset_pledge_value_dict.get(_asset, Decimal())
            return _stage, -_value

        if not market_index_price_dict:
            market_index_price_dict = get_cached_market_index_prices()

        asset_pledge_value_dict = {}
        pledge_asset_info_dict = cls.get_pledge_asset_info_dict(list(pledge_asset_amount_dict))
        max_collateral_ratio = max([i["collateral_ratio"] for i in pledge_asset_info_dict.values()] or [Decimal("1")])
        for asset, amount in pledge_asset_amount_dict.items():
            index_price = cls.get_asset_index_price(asset, market_index_price_dict)
            pledge_asset_info = pledge_asset_info_dict.get(asset)
            if pledge_asset_info:
                collateral_ratio = Decimal(pledge_asset_info["collateral_ratio"])
                pledge_value = amount * index_price * collateral_ratio
                pledge_value = min(pledge_value, Decimal(pledge_asset_info["max_pledge_usd"]))
            else:
                # 可能是中间币种，无质押币配置信息，取折价率最高
                pledge_value = amount * index_price * max_collateral_ratio
            asset_pledge_value_dict[asset] = quantize_amount(pledge_value, PrecisionEnum.COIN_PLACES)

        return list(sorted(list(pledge_asset_amount_dict), key=_sort_func))


def is_pledge_account(account_id: int) -> bool:
    # 可能会传None
    return account_id and MIN_PLEDGE_ACCOUNT_ID <= int(account_id) <= MAX_PLEDGE_ACCOUNT_ID


def get_special_asset_rate(asset: str, origin_day_rate: Decimal) -> Decimal:
    if asset != USDT_ASSET:
        return origin_day_rate
    f_config = AssetFloatingRateMySQL.query.filter(
        AssetFloatingRateMySQL.asset == asset
    ).first()
    _rate = f_config.rate if f_config else None
    return max([_rate or Decimal(), origin_day_rate])


def get_loan_asset_info(asset: str, only_open: bool = False) -> LoanAsset:
    row: LoanAsset = LoanAsset.query.filter(LoanAsset.asset == asset).first()
    if not row:
        raise ValueError(f"loan_asset:{asset} not found")
    if only_open and row.status != LoanAsset.Status.OPEN:
        raise ValueError(f"loan_asset:{asset} not open")
    return row


def get_loan_assets_infos(assets: List[str]) -> List[LoanAsset]:
    rows: List[LoanAsset] = LoanAsset.query.filter(
        LoanAsset.asset.in_(assets),
    ).all()
    return rows


def get_pledge_loan_asset_account_id_dict() -> Dict[str, int]:
    rows = LoanAsset.query.with_entities(LoanAsset.asset, LoanAsset.account_id).all()
    return dict(rows)


def get_pledge_account_id_assets_dict(all_assets: bool = False) -> Dict[int, List[str]]:
    """ 返回 {质押账户ID: 账户支持的币种列表 }
    all_assets为True时返回包括已失效的币种"""

    if not all_assets:
        pledge_rows = PledgeAsset.query.filter(
            PledgeAsset.status.in_((PledgeAsset.Status.OPEN, PledgeAsset.Status.TEMP_OFFLINE)),
        ).with_entities(PledgeAsset.asset).all()
    else:
        pledge_rows = PledgeAsset.query.filter().with_entities(PledgeAsset.asset).all()
    pledge_assets = [i.asset for i in pledge_rows]
    
    loan_asset_rows = LoanAsset.query.with_entities(LoanAsset.account_id, LoanAsset.asset).all()
    
    account_id_assets_dict = {}
    for loan_asset_row in loan_asset_rows:
        # 质押账户里可能有的资产: 借币币种 + usdt + 全部的质押币种
        _p_assets = {loan_asset_row.asset, USDT_ASSET} | set(pledge_assets)
        account_id_assets_dict[loan_asset_row.account_id] = list(_p_assets)
    return account_id_assets_dict


def get_loan_asset_liq_rates(asset: str) -> List[LoanAssetLiquidationRate]:
    """ 借币币种的阶梯强平率 """
    rates = LoanAssetLiquidationRate.query.filter(
        LoanAssetLiquidationRate.asset == asset,
        LoanAssetLiquidationRate.status == LoanAssetLiquidationRate.Status.VALID,
    ).order_by(LoanAssetLiquidationRate.liquidation_amount.asc()).all()
    return rates


def get_pledge_assets_infos(pledge_assets: List[str], only_open: bool = False) -> List[PledgeAsset]:
    rows: List[PledgePosition] = PledgeAsset.query.filter(
        PledgeAsset.asset.in_(pledge_assets),
    ).all()
    if miss_assets := set(pledge_assets) - set([i.asset for i in rows]):
        raise ValueError(f"pledge_asset:{miss_assets} not found")
    if only_open:
        for row in rows:
            if row.status != PledgeAsset.Status.OPEN:
                raise ValueError(f"pledge_asset:{row.asset} not open")
    return rows


@cached(10)
def get_cached_market_index_prices() -> Dict[str, Decimal]:
    result = ServerClient().get_all_indices()
    price_records = PledgeAsset.query.filter(
        PledgeAsset.status == PledgeAsset.Status.TEMP_OFFLINE,
    ).with_entities(
        PledgeAsset.asset,
        PledgeAsset.locked_index_price
    ).all()
    for asset, price in price_records:
        if price:
            m = f"{asset}{USDT_ASSET}"
            result[m] = price
    return result


def get_user_last_active_position(user_id: int, loan_asset: str) -> Optional[PledgePosition]:
    """ 用户最新可用仓位 """
    rows = PledgePosition.query.filter(
        PledgePosition.user_id == user_id,
        PledgePosition.loan_asset == loan_asset,
        PledgePosition.status.in_(PledgePosition.ACTIVE_STATUSES),
    ).all()
    assert len(rows) <= 1
    if rows:
        return rows[0]
    return None


def get_user_pledge_unflat_amount_dict(user_id: int) -> Dict[str, Decimal]:
    """ 获取用户质押账户的未还数目（借币+利息） """
    rows = PledgePosition.query.filter(
        PledgePosition.user_id == user_id,
        PledgePosition.status.in_(PledgePosition.ACTIVE_STATUSES),
    ).with_entities(
        PledgePosition.loan_asset,
        PledgePosition.debt_amount,
        PledgePosition.interest_amount,
    ).all()
    result = {}
    for v in rows:
        _total = v.debt_amount + v.interest_amount
        result[v.loan_asset] = result.get(v.loan_asset, Decimal()) + _total
    return result


def group_pledge_total_unflat() -> dict:
    """ 按用户和质押账户 统计未还数 """
    pledge_unflats = PledgePosition.query.filter(
        PledgePosition.status.in_(PledgePosition.ACTIVE_STATUSES),
    ).with_entities(
        PledgePosition.user_id,
        PledgePosition.loan_asset,
        func.sum(PledgePosition.debt_amount + PledgePosition.interest_amount).label('total_unflat_amount'),
    ).group_by(
        PledgePosition.user_id,
        PledgePosition.loan_asset,
    ).all()
    loan_asset_account_id_dict = get_pledge_loan_asset_account_id_dict()
    pledge_unflat_dict = defaultdict(Decimal)
    for _v in pledge_unflats:
        key_ = (_v.user_id, loan_asset_account_id_dict[_v.loan_asset], _v.loan_asset)
        pledge_unflat_dict[key_] += _v.total_unflat_amount
    return pledge_unflat_dict


def group_pledge_unflat_debt_interest() -> dict:
    """ 按用户和质押账户 统计未还数（返回本金和利息） """
    pledge_unflats = PledgePosition.query.filter(
        PledgePosition.status.in_(PledgePosition.ACTIVE_STATUSES),
    ).with_entities(
        PledgePosition.user_id,
        PledgePosition.loan_asset,
        func.sum(PledgePosition.debt_amount).label('total_debt_amount'),
        func.sum(PledgePosition.interest_amount).label('total_interest_amount'),
    ).group_by(
        PledgePosition.user_id,
        PledgePosition.loan_asset,
    ).all()
    loan_asset_account_id_dict = get_pledge_loan_asset_account_id_dict()
    pledge_unflat_dict = {}
    for _v in pledge_unflats:
        key_ = (_v.user_id, loan_asset_account_id_dict[_v.loan_asset], _v.loan_asset)
        pledge_unflat_dict[key_] = [_v.total_debt_amount, _v.total_interest_amount]
    return pledge_unflat_dict


def update_loan_asset_caches():
    """ 更新和借币币种相关的缓存 """
    LoanAssetViewCache.reload()
    PledgeAccountViewCache.reload()


def update_pledge_asset_caches():
    """ 更新和质押币种相关的缓存 """
    PledgeAssetViewCache.reload()


def calculate_avg_daily_interest_rate(total_interest_usd, avg_loan_balance_usd, report_type, report_date=None):
    """
    计算平均日利率
    日报平均利率=当日借贷总利息/当日平均借贷余额*100%
    月报平均利率=（当月借贷总利息/当月天数）/当月平均借贷余额*100%
    """
    total_interest_usd = Decimal(total_interest_usd or 0)
    avg_loan_balance_usd = Decimal(avg_loan_balance_usd or 0)

    if avg_loan_balance_usd == 0:
        return 0

    if report_type == ReportType.DAILY:
        days = 1
    else:
        if report_date:
            # 计算该月总天数
            next_month_first = (report_date.replace(day=28) + timedelta(days=4)).replace(day=1)
            last_day_of_month = next_month_first - timedelta(days=1)
            # 如果是当月，则天数为当月已过天数，否则，则是整月天数
            _today = today()
            if _today.year == report_date.year and _today.month == report_date.month:
                days = _today.day - 1
            else:
                days = last_day_of_month.day
        else:
            days = 30  # fallback

    # 统一计算利率：(总利息 / 天数) / 平均借贷余额 * 100%
    daily_interest = total_interest_usd / days
    rate = (daily_interest / avg_loan_balance_usd) * 100
    return rate
