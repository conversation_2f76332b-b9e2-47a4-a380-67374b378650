# -*- coding: utf-8 -*-

from .base import ErrorWithResponseCode
from flask_babel import gettext as _


class InvalidUsernameOrPassword(ErrorWithResponseCode):

    response_code = 205
    message_template = _('用户名或密码错误')

    def __init__(self, count: int = None, message: str = None, **kwargs):
        super().__init__(count, message, **kwargs)

    @property
    def message(self):
        if isinstance((remaining := self._data), int):
            return _(
                '用户名或密码错误，还有%(count)s次机会。',
                count=max(remaining, 0))
        return super().message


class WithdrawPasswordError(ErrorWithResponseCode):

    response_code = 247
    message_template = _('提现密码输入有误')

    @property
    def message(self):
        if isinstance((count := self._data.get("count")), int):
            return _(
                '提现密码输入有误，你还有%(count)s次机会。',
                count=max(count, 0))
        return super().message


class TradePasswordError(ErrorWithResponseCode):

    response_code = 249
    message_template = _('交易密码输入有误')

    @property
    def message(self):
        if isinstance((count := self._data.get("count")), int):
            return _(
                '交易密码输入有误，你还有%(count)s次机会。',
                count=max(count, 0))
        return super().message


class WithdrawPasswordAlreadyExists(ErrorWithResponseCode):

    response_code = 245
    message_template = _('提现密码已存在')


class TradePasswordAlreadyExists(ErrorWithResponseCode):

    response_code = 246
    message_template = _('交易密码已存在')

class InvalidWithdrawPassword(ErrorWithResponseCode):
    response_code = 250
    message_template = _('请设置6位数字密码')


class InvalidTradePassword(ErrorWithResponseCode):
    response_code = 253
    message_template = _('请设置6位数字密码')


class WithdrawPasswordVersionError(ErrorWithResponseCode):
    response_code = 251
    message_template = _('你已开启提现密码，请升级到最新版本后继续操作')


class TradePasswordVersionError(ErrorWithResponseCode):
    response_code = 252
    message_template = _('请前往WEB下单或在WEB端关闭交易密码')


class AccountNameToLong(ErrorWithResponseCode):
    response_code = 260
    message_template = _('账户名不得超出20个字符')


class AccountNameExists(ErrorWithResponseCode):
    response_code = 261
    message_template = _('账户名已存在')


class FrequencyExceeded(ErrorWithResponseCode):

    response_code = 213
    message_template = _("操作太频繁")


class EmailAlreadyExists(ErrorWithResponseCode):

    response_code = 215
    message_template = _('邮箱已存在')


class EmailDoesNotExist(ErrorWithResponseCode):

    response_code = 216
    message_template = _('邮箱不存在')


class MobileAlreadyExists(ErrorWithResponseCode):

    response_code = 217
    message_template = _('手机号已经使用')


class EmailNotAllowed(ErrorWithResponseCode):

    response_code = 240
    message_template = _("不能使用该邮箱")


class UserHasSignedOff(ErrorWithResponseCode):

    response_code = 242
    message_template = _("该账号已注销")


class UserSignOffFailed(ErrorWithResponseCode):
    response_code = 243
    message_template = _("账号注销失败")


class LoginQRCodeExpired(ErrorWithResponseCode):

    response_code: int = 244
    message_template = _("二维码已失效")


class DuplicateReferralCode(ErrorWithResponseCode):

    response_code = 803
    message_template = _('推荐码重复，请重新设置。')


class AccountBoundByAnOther(ErrorWithResponseCode):
    response_code = 4003
    message_template = _('The account has been bound by another user.')


class InvalidPassword(ErrorWithResponseCode):
    response_code = 4004
    message_template = _('密码至少10位字符，必须包含1位大写字母及1位数字')


class MarketPriceNoticeLimit(ErrorWithResponseCode):
    response_code = 4005
    message_template = _("单个市场的价格提醒上限为%(count)s个，目前已达上限。")


class TotalMarketPriceNoticeLimit(ErrorWithResponseCode):
    response_code = 4006
    message_template = _("全部市场的价格提醒上限为%(count)s个，目前已达上限。")


class LoginTimeOutFailed(ErrorWithResponseCode):
    response_code = 241
    message_template = _("登录超时")


class SubAccountBeyondAmountLimit(ErrorWithResponseCode):

    response_code = 3507
    message_template = _('超出数量限制，最多只能添加%(num)s个子账号')


class UsingCouponLimit(ErrorWithResponseCode):
    response_code = 4501
    message_template = _("当前你已有使用中的卡券，交易达标或到期后才可以使用新的卡券。")


class CouponCodeNotExist(ErrorWithResponseCode):
    response_code = 4502
    message_template = _("兑换失败，兑换码不存在")


class CouponCodeExpired(ErrorWithResponseCode):
    response_code = 4503
    message_template = _("兑换失败，兑换码已过期")


class CouponCodeUsed(ErrorWithResponseCode):
    response_code = 4504
    message_template = _("兑换失败，当前账号已领取此兑换券")


class CouponCodeOutOfTimeRange(ErrorWithResponseCode):
    response_code = 4505
    message_template = _("兑换失败，不在兑换时间范围内")


class CouponHasRunOut(ErrorWithResponseCode):
    response_code = 4506
    message_template = _("兑换失败，兑换券已抢光")


class UsingCouponObtained(ErrorWithResponseCode):
    response_code = 4507
    message_template = _("当前卡券已经被领取")


class CouponPoolExpired(ErrorWithResponseCode):
    response_code = 4508
    message_template = _("抱歉，已超出领取有效期啦～")


class CouponSendEnd(ErrorWithResponseCode):
    response_code = 4509
    message_template = _("抱歉，卡券被抢光了。")


class UsingCouponSameInvestmentAsset(ErrorWithResponseCode):
    response_code = 4510
    message_template = _("你已经有一张加息券正在使用中，到期后才可以继续使用新的理财加息券")


class UsingCouponExpired(ErrorWithResponseCode):
    response_code = 4511
    message_template = _("卡券已过期")


class UsingCouponStatusInvalid(ErrorWithResponseCode):
    response_code = 4512
    message_template = _("卡券激活失败，请刷新后重试")


class UsingCouponInvalid(ErrorWithResponseCode):
    response_code = 4513
    message_template = _("卡券已失效")


class NoviceRegisterInvalid(ErrorWithResponseCode):
    response_code = 4514
    message_template = _("活动期间注册用户才可领取卡券。")


class NoviceDepositInvalid(ErrorWithResponseCode):
    response_code = 4515
    message_template = _("活动期间首次充值用户才可领取卡券。(站内转账、空投等不符合领取条件)")


class NoviceTradeInvalid(ErrorWithResponseCode):
    response_code = 4516
    message_template = _("活动期间首次交易(兑换、币币、合约)用户才可领取卡券。")


class NoviceValidateInvalid(ErrorWithResponseCode):
    response_code = 4517
    message_template = _("领取失败")


class UsingPerpetualSubsidyCouponLimit(ErrorWithResponseCode):
    response_code = 4518
    message_template = _("您已有使用中的合约补贴金，卡券使用完毕或到期后才可以继续激活新的合约补贴金。")


class NoviceRiskControl(ErrorWithResponseCode):
    response_code = 4519
    message_template = _("抱歉，由于风险控制，你不具备领取资格")


class ApiExtendInvalid(ErrorWithResponseCode):

    response_code = 3508
    message_template = _('API续期请求已失效')


class WithdrawalAddressOverLimit(ErrorWithResponseCode):

    response_code = 3509
    message_template = _('最多只能添加%(num)s个提现地址')


class WithdrawalApproverOverLimit(ErrorWithResponseCode):

    response_code = 3510
    message_template = _('最多只能添加%(num)s个提现审核人')


class BeyondMaxExportTimesException(ErrorWithResponseCode):

    response_code = 3511
    message_template = _('超出导出次数限制')


class BoxGrabEnd(ErrorWithResponseCode):
    response_code = 3550
    message_template = _("盲盒已抢光")


class UserAlreadyOpenBox(ErrorWithResponseCode):
    response_code = 3551
    message_template = _("此盲盒已打开，无法重复打开")


class UserAlreadyOpenOtherBox(ErrorWithResponseCode):
    response_code = 3552
    message_template = _("盲盒2选1，仅可开启一个盲盒")


class BoxTriggerRiskControl(ErrorWithResponseCode):
    response_code = 3553
    message_template = _("已触发风控规则，无法领取盲盒")


class FrozenManagedSubAccount(ErrorWithResponseCode):

    response_code = 3512
    message_template = _("当前子账号存在授权关系，禁用前请先解除授权。")


class SubAccountPermissionRequire(ErrorWithResponseCode):

    response_code = 3513

    template_map = {
        "MARGIN": _("当前子账号无杠杆交易权限"),
        "PERPETUAL": _("当前子账号无合约交易权限"),
        "AMM": _("当前子账号无参与做市权限"),
        "API": _("当前子账号无API管理权限"),
    }

    @classmethod
    def from_name(cls, name: str):
        return cls(message=cls.template_map[name])


class SubAccountManagerBeyondAmountLimit(ErrorWithResponseCode):

    response_code = 3514
    message_template = _("超过授权上限，1个子账号最多可授权%(num)s个账号共同管理，请先解除部分生效中的授权，再新增授权")


class EditManagedSubAccountName(ErrorWithResponseCode):

    response_code = 3515
    message_template = _('当前子账号存在授权关系，修改账号名前请先解除授权。')


class SubAccountResourceNotAllowed(ErrorWithResponseCode):

    response_code = 3516
    message_template = _('当前子账号存在托管关系，该资源不允许操作/访问。')


class FrozenHostingSubAccount(ErrorWithResponseCode):

    response_code = 3517
    message_template = _("当前子账号存在托管关系，禁用前请先解除托管。")


class CBoxCodeAlreadyUsed(ErrorWithResponseCode):

    response_code = 3316
    message_template = _('该口令不可用')


class CBoxNotAllowed(ErrorWithResponseCode):

    response_code = 3317
    message_template = _("禁止发送C-Box")


class CoinNotSupportCBox(ErrorWithResponseCode):

    response_code = 3318
    message_template = _('该币种不支持发送C-Box')


class CBoxCountLimit(ErrorWithResponseCode):

    response_code = 3319
    message_template = _('C-Box数量不能大于%(count)s')


class CBoxAmountLimit(ErrorWithResponseCode):

    response_code = 3320
    message_template = _('单个C-Box金额不能小于%(per_amount)s%(coin_type)s')


class CBoxAmountNotEnough(ErrorWithResponseCode):

    response_code = 3321
    message_template = _('C-Box每日发送金额上限为%(limit_usd)sUSD，今日已发送%(send_usd)sUSD，超出金额请明日再发。')


class InvalidCBoxCode(ErrorWithResponseCode):

    response_code = 3322
    message_template = _('口令错误，已输错%(fail_times)s次，再输错%(remain_times)s次将禁止输入'
                         '口令%(failure_ttl_hour)s小时')


class CBoxHasExpired(ErrorWithResponseCode):

    response_code = 3323
    message_template = _('C-Box已过期')


class CBoxUserHasReceived(ErrorWithResponseCode):

    response_code = 3324
    message_template = _('用户已领过此C-Box')


class CBoxOnlyNewUser(ErrorWithResponseCode):

    response_code = 3325
    message_template = _('您已经是CoinEx用户，该C-Box仅限新用户领取')


class CBoxHasFinished(ErrorWithResponseCode):

    response_code = 3326
    message_template = _('C-Box已领完')


class CBoxStatusError(ErrorWithResponseCode):

    response_code = 3327
    message_template = _('C-Box状态错误')


class CBoxNotFoundOrNotExists(ErrorWithResponseCode):

    response_code = 3328
    message_template = _('C-Box过期或不存在')


class CBoxRateError(ErrorWithResponseCode):

    response_code = 3329
    message_template = _('C-Box汇率有误')


class AccountDoesNotExist(ErrorWithResponseCode):

    response_code = 3330
    message_template = _('该账号未注册')


class ThirdPartyAccountDoesNotExist(ErrorWithResponseCode):

    response_code = 3331
    message_template = _('该邮箱尚未绑定%(source)s账户，暂无法支持快捷登录')


class ThirdPartyAccountExists(ErrorWithResponseCode):

    response_code = 3332
    message_template = _('该%(source)s账户已被其他CoinEx账号绑定')


class UnbindThirdPartyAccountNotAllowed(ErrorWithResponseCode):

    response_code = 3333
    message_template = _('需要设置账户密码后解绑')

class AccountHasBeenBinded(ErrorWithResponseCode):

    response_code = 3359
    message_template = _('CoinEx账号已绑定其他%(source)s账号，无法重复绑定')

class ActivityAbnormalUserError(ErrorWithResponseCode):
    response_code = 3334
    message_template = _("不符合要求，领取失败，详情请联系客服。")


class CBoxCheatedUser(ErrorWithResponseCode):
    response_code = 3335
    message_template = _('该账号无法领取')


class PledgePositionOperationFailed(ErrorWithResponseCode):
    response_code = 3336
    message_template = _("操作失败")


class PledgeLoanAmountMinLimit(ErrorWithResponseCode):
    response_code = 3337
    message_template = _('低于最小可借数量%(amount)s %(asset)s')


class PledgeMaxLoanAmountLimit(ErrorWithResponseCode):
    response_code = 3338
    message_template = _('最多可借%(amount)s %(asset)s')


class PledgeMaxRemoveAmountLimit(ErrorWithResponseCode):
    response_code = 3339
    message_template = _("最多可减少%(amount)s %(asset)s")


class AlreadyKolError(ErrorWithResponseCode):
    response_code = 3340
    message_template = _("你已经是KOL")


class AlreadySubmitKolApplyError(ErrorWithResponseCode):
    response_code = 3341
    message_template = _("你已经提交过KOL申请")


class WithdrawalPrivacyAssetRequireKyc(ErrorWithResponseCode):
    response_code = 3342
    message_template = _("基于CoinEx的AML政策，提现隐私币必须通过实名认证，请先完成认证。")


class DepositPrivacyAssetRequireKyc(ErrorWithResponseCode):
    response_code = 3343
    message_template = _("基于CoinEx的AML政策，充值隐私币必须通过实名认证，请先完成认证。")


class WithdrawalFeeAssetNotCustomizable(ErrorWithResponseCode):
    response_code = 3344
    message_template = _("当前币种没有价格，只允许设置该币种作为提现手续费。")


class AlreadyBindReferrer(ErrorWithResponseCode):
    response_code = 3345
    message_template = _("已绑定邀请人")


class BindReferrerTimeout(ErrorWithResponseCode):
    response_code = 3346
    message_template = _("已超时，不允许绑定")


class UserRiskScreenFailed(ErrorWithResponseCode):
    response_code = 3347
    message_template = _('由于监管要求，无法为你提供更多服务')


class FollowFullCopyTraderError(ErrorWithResponseCode):
    response_code = 3348
    message_template = _("此交易员跟单人数已达上限，请稍后再进行跟单")


class CopyTraderEditNameTooFrequentError(ErrorWithResponseCode):
    response_code = 3349
    message_template = _("由于你近期已修改昵称，请于%(days)s天后再试")


class CopyTraderOpenPositionOverLimit(ErrorWithResponseCode):
    response_code = 3350
    message_template = _("超过每天开仓次数上限，请明天再试")


class CopyTraderAdjustLeverageError(ErrorWithResponseCode):
    response_code = 3351
    message_template = _("带单生效时，请先关闭生效中的仓位才能切换保证金模式")


class CopyTraderNameAlreadyExists(ErrorWithResponseCode):
    response_code = 3352
    message_template = _("昵称已被使用，请修改后再提交")


class CopyTraderApplyTooFrequentError(ErrorWithResponseCode):
    response_code = 3353
    message_template = _("由于你最近主动取消带单，请于%(days)s天后重新申请")


class CopyTradingForbiddenError(ErrorWithResponseCode):
    response_code = 3354
    message_template = _("暂无法使用跟单交易，如需更多帮助请提交工单咨询")


class FollowInactiveCopyTraderError(ErrorWithResponseCode):
    response_code = 3355
    message_template = _("此交易员已结束带单，请于交易员重新带单后再进行跟单")


class CopyTraderNotExists(ErrorWithResponseCode):
    response_code = 3356
    message_template = _("交易员不存在")


class CopyTraderCopyAmountChanged(ErrorWithResponseCode):
    response_code = 3357
    message_template = _("交易员已调整跟单金额范围，请刷新页面")


class BindReferrerYounger(ErrorWithResponseCode):
    response_code = 3358
    # message_template 前端文案提示


class CountryNotSupportKycPro(ErrorWithResponseCode):
    response_code = 3360
    message_template = _("暂不支持")


class KycSupportIdDocsChanged(ErrorWithResponseCode):
    response_code = 3361
    message_template = _("%(id_type)s验证错误次数达到上限，请选择其他证件类型")


class UserNotAmbassador(ErrorWithResponseCode):
    response_code = 3362
    message_template = 'you are not an ambassador'


class UserEmailRepeat(ErrorWithResponseCode):
    response_code = 3363
    message_template = _("填写邮箱不可与旧邮箱相同，请使用新邮箱填写")


class CBoxWithdrawalApprover(ErrorWithResponseCode):
    response_code = 3364
    message_template = _("你设置了提现多人审核，无法使用C-Box功能。")


class IDTypeReachLimit(ErrorWithResponseCode):
    response_code = 3365
    message_template = _("验证错误次数达到上限")


class InvIncreaseAssetUsed(ErrorWithResponseCode):
    response_code = 3366
    message = _("已有活期理财权益使用中，无法激活此权益")

