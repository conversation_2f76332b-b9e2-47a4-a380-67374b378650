"""empty message

Revision ID: 8e277531835a
Revises: 80a5c4585ee3
Create Date: 2025-09-09 17:04:37.978697

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql
import app.models

# revision identifiers, used by Alembic.
revision = '8e277531835a'
down_revision = '80a5c4585ee3'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('onchain_token_info', schema=None) as batch_op:
        batch_op.add_column(sa.Column('top_pool_liquidity', sa.String(length=78), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('onchain_token_info', schema=None) as batch_op:
        batch_op.drop_column('top_pool_liquidity')
    # ### end Alembic commands ###
