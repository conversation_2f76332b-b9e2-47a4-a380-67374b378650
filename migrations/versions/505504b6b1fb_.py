"""empty message

Revision ID: 505504b6b1fb
Revises: 49a7e696078f
Create Date: 2025-09-15 15:24:37.978697

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql
import app.models

# revision identifiers, used by Alembic.
revision = '505504b6b1fb'
down_revision = '49a7e696078f'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('onchain_token_about_source',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', app.models.base.DateTimeUTC(fsp=6), nullable=True),
    sa.Column('updated_at', app.models.base.DateTimeUTC(fsp=6), nullable=True),
    sa.Column('token_id', sa.Integer(), nullable=False),
    sa.Column('lang',
              app.models.base.StringEnum('EN_US', 'DEFAULT', 'ZH_HANS_CN', 'ZH_HANT_HK', 'JA_JP',
                                         'RU_KZ', 'KO_KP', 'ID_ID', 'ES_ES', 'FA_IR', 'TR_TR', 'VI_VN',
                                         'AR_AE', 'FR_FR', 'PT_PT', 'DE_DE', 'TH_TH', 'IT_IT', 'PL_PL'),
              nullable=False),
    sa.Column('about', sa.TEXT(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('onchain_token_about_source', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_onchain_token_about_source_token_id'), ['token_id'], unique=False)

    op.create_table('onchain_token_about_translation',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', app.models.base.DateTimeUTC(fsp=6), nullable=True),
    sa.Column('updated_at', app.models.base.DateTimeUTC(fsp=6), nullable=True),
    sa.Column('token_id', sa.Integer(), nullable=False),
    sa.Column('source_id', sa.Integer(), nullable=False),
    sa.Column('lang',
              app.models.base.StringEnum('EN_US', 'DEFAULT', 'ZH_HANS_CN', 'ZH_HANT_HK', 'JA_JP',
                                         'RU_KZ', 'KO_KP', 'ID_ID', 'ES_ES', 'FA_IR', 'TR_TR', 'VI_VN',
                                         'AR_AE', 'FR_FR', 'PT_PT', 'DE_DE', 'TH_TH', 'IT_IT', 'PL_PL'),
              nullable=False),
    sa.Column('about', sa.TEXT(), nullable=False),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('token_id', 'lang', name='token_id_lang_uniq')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('onchain_token_about_translation')

    with op.batch_alter_table('onchain_token_about_source', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_onchain_token_about_source_token_id'))
    op.drop_table('onchain_token_about_source')
    # ### end Alembic commands ###
