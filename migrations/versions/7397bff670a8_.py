"""empty message

Revision ID: 7397bff670a8
Revises: 505504b6b1fb
Create Date: 2025-09-02 16:35:28.773260

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

import app.models

# revision identifiers, used by Alembic.
revision = '7397bff670a8'
down_revision = '505504b6b1fb'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('loan_statistic',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', app.models.base.DateTimeUTC(fsp=6), nullable=True),
    sa.Column('updated_at', app.models.base.DateTimeUTC(fsp=6), nullable=True),
    sa.Column('asset', sa.String(length=32), nullable=False),
    sa.Column('loan_type', app.models.base.StringEnum('ALL', '杠杆', '借贷'), nullable=False),
    sa.Column('borrower_count', sa.Integer(), nullable=False),
    sa.Column('borrowed_usd', mysql.DECIMAL(precision=26, scale=8), nullable=False),
    sa.Column('collateral_usd', mysql.DECIMAL(precision=26, scale=8), nullable=False),
    sa.Column('effective_collateral_usd', mysql.DECIMAL(precision=26, scale=8), nullable=True),
    sa.Column('interest_7d_usd', mysql.DECIMAL(precision=26, scale=8), nullable=False),
    sa.Column('interest_30d_usd', mysql.DECIMAL(precision=26, scale=8), nullable=False),
    sa.Column('interest_90d_usd', mysql.DECIMAL(precision=26, scale=8), nullable=False),
    sa.Column('interest_180d_usd', mysql.DECIMAL(precision=26, scale=8), nullable=False),
    sa.Column('interest_365d_usd', mysql.DECIMAL(precision=26, scale=8), nullable=False),
    sa.Column('report_date', sa.Date(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    with op.batch_alter_table('loan_statistic', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_loan_statistic_asset'), ['asset'], unique=False)
        batch_op.create_index(batch_op.f('ix_loan_statistic_report_date'), ['report_date'], unique=False)
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('loan_statistic', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_loan_statistic_report_date'))
        batch_op.drop_index(batch_op.f('ix_loan_statistic_asset'))
    op.drop_table('loan_statistic')
    # ### end Alembic commands ###

