"""empty message

Revision ID: 49a7e696078f
Revises: 43b7aa03edca
Create Date: 2025-09-03 11:04:37.978697

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql
import app.models

# revision identifiers, used by Alembic.
revision = '49a7e696078f'
down_revision = '43b7aa03edca'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('p2p_user_pay_channel', schema=None) as batch_op:
        batch_op.add_column(sa.Column('alias', sa.String(length=512), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('p2p_user_pay_channel', schema=None) as batch_op:
        batch_op.drop_column('alias')
    # ### end Alembic commands ###
