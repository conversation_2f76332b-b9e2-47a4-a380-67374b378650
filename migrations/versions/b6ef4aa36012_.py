"""empty message

Revision ID: b6ef4aa36012
Revises: cffeb5c5aeb3
Create Date: 2025-09-05 10:09:19.580921

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql
import app.models

# revision identifiers, used by Alembic.
revision = 'b6ef4aa36012'
down_revision = 'aff4aae18e3f'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('user_daily_inc_equity_history',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', app.models.base.DateTimeUTC(fsp=6), nullable=True),
    sa.Column('updated_at', app.models.base.DateTimeUTC(fsp=6), nullable=True),
    sa.Column('user_equity_id', sa.Integer(), nullable=False, comment='用户权益ID'),
    sa.Column('report_date', sa.Date(), nullable=False, comment='加息发放日'),
    sa.Column('user_id', sa.Integer(), nullable=False, comment='用户ID'),
    sa.Column('asset', sa.String(length=32), nullable=False, comment='计息币种'),
    sa.Column('interest_amount', mysql.DECIMAL(precision=26, scale=8), nullable=False, comment='发息数量'),
    sa.Column('status', app.models.base.StringEnum('CREATED', 'DEDUCTED', 'FINISHED', 'FAILED'), nullable=False, comment='发放状态'),
    sa.Column('system_user_id', sa.Integer(), nullable=False, comment='系统用户ID'),
    sa.Column('deducted_at', app.models.base.DateTimeUTC(fsp=6), nullable=True, comment='扣减时间'),
    sa.Column('finished_at', app.models.base.DateTimeUTC(fsp=6), nullable=True, comment='发放完成时间'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_equity_id', 'report_date', name='uni_user_equity_report_date')
    )
    with op.batch_alter_table('user_daily_inc_equity_history', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_user_daily_inc_equity_history_asset'), ['asset'], unique=False)
        batch_op.create_index(batch_op.f('ix_user_daily_inc_equity_history_report_date'), ['report_date'], unique=False)
        batch_op.create_index(batch_op.f('ix_user_daily_inc_equity_history_user_id'), ['user_id'], unique=False)

    op.create_table('user_hour_inc_equity_history',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', app.models.base.DateTimeUTC(fsp=6), nullable=True),
    sa.Column('updated_at', app.models.base.DateTimeUTC(fsp=6), nullable=True),
    sa.Column('user_equity_id', sa.Integer(), nullable=False, comment='用户权益ID'),
    sa.Column('report_hour', app.models.base.DateTimeUTC(fsp=6), nullable=False, comment='计息小时'),
    sa.Column('user_id', sa.Integer(), nullable=False, comment='用户ID'),
    sa.Column('asset', sa.String(length=32), nullable=False, comment='计息币种'),
    sa.Column('balance', mysql.DECIMAL(precision=26, scale=8), nullable=False, comment='计息数量'),
    sa.Column('interest_amount', mysql.DECIMAL(precision=26, scale=8), nullable=False, comment='发息数量'),
    sa.Column('rate', mysql.DECIMAL(precision=26, scale=8), nullable=False, comment='加息利率'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_equity_id', 'report_hour', name='uni_user_equity_report_hour')
    )
    with op.batch_alter_table('user_hour_inc_equity_history', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_user_hour_inc_equity_history_asset'), ['asset'], unique=False)
        batch_op.create_index(batch_op.f('ix_user_hour_inc_equity_history_report_hour'), ['report_hour'], unique=False)
        batch_op.create_index(batch_op.f('ix_user_hour_inc_equity_history_user_id'), ['user_id'], unique=False)

    op.create_table('user_invest_increase_equity',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('created_at', app.models.base.DateTimeUTC(fsp=6), nullable=True),
    sa.Column('updated_at', app.models.base.DateTimeUTC(fsp=6), nullable=True),
    sa.Column('user_id', sa.Integer(), nullable=False, comment='用户ID'),
    sa.Column('user_equity_id', sa.Integer(), nullable=False, comment='用户权益ID'),
    sa.Column('investment_asset', sa.String(length=32), nullable=True, comment='选择的加息币种'),
    sa.Column('increase_amount', mysql.DECIMAL(precision=26, scale=8), nullable=True, comment='累计加息收益'),
    sa.Column('increase_usd', mysql.DECIMAL(precision=26, scale=8), nullable=True, comment='累计加息收益USDT'),
    sa.Column('active_at', app.models.base.DateTimeUTC(fsp=6), nullable=True, comment='加息权益激活时间'),
    sa.Column('finished_at', app.models.base.DateTimeUTC(fsp=6), nullable=True, comment='加息权益结束时间'),
    sa.Column('payout_date', sa.Date(), nullable=True, comment='最新权益发放时间'),
    sa.Column('increase_rate', mysql.DECIMAL(precision=26, scale=8), nullable=True, comment='加息比例'),
    sa.Column('principal_asset', sa.String(length=32), nullable=True, comment='本金币种'),
    sa.Column('principal_amount_limit', mysql.DECIMAL(precision=26, scale=8), nullable=True, comment='加息本金上限'),
    sa.Column('assets', app.models.base.RichJSON(), nullable=True, comment='适用币种'),
    sa.Column('usable_days', sa.Integer(), nullable=True, comment='加息有效天数'),
    sa.Column('activation_days', sa.Integer(), nullable=True, comment='激活有效天数'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('user_equity_id')
    )
    with op.batch_alter_table('user_invest_increase_equity', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_user_invest_increase_equity_user_id'), ['user_id'], unique=False)

    with op.batch_alter_table('interest_statistic_time', schema=None) as batch_op:
        batch_op.add_column(sa.Column('bus_type', app.models.base.StringEnum('SITE', 'INCREASE'), nullable=False, comment='业务类型'))
    op.execute("UPDATE interest_statistic_time SET bus_type='SITE'")

    with op.batch_alter_table('user_equity', schema=None) as batch_op:
        batch_op.create_index(batch_op.f('ix_user_equity_status'), ['status'], unique=False)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('user_equity', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_user_equity_status'))

    with op.batch_alter_table('interest_statistic_time', schema=None) as batch_op:
        batch_op.drop_column('bus_type')

    with op.batch_alter_table('user_invest_increase_equity', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_user_invest_increase_equity_user_id'))

    op.drop_table('user_invest_increase_equity')
    with op.batch_alter_table('user_hour_inc_equity_history', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_user_hour_inc_equity_history_user_id'))
        batch_op.drop_index(batch_op.f('ix_user_hour_inc_equity_history_report_hour'))
        batch_op.drop_index(batch_op.f('ix_user_hour_inc_equity_history_asset'))

    op.drop_table('user_hour_inc_equity_history')
    with op.batch_alter_table('user_daily_inc_equity_history', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_user_daily_inc_equity_history_user_id'))
        batch_op.drop_index(batch_op.f('ix_user_daily_inc_equity_history_report_date'))
        batch_op.drop_index(batch_op.f('ix_user_daily_inc_equity_history_asset'))

    op.drop_table('user_daily_inc_equity_history')
    # ### end Alembic commands ###
