"""empty message

Revision ID: 80a5c4585ee3
Revises: 7397bff670a8
Create Date: 2025-09-24 17:49:46.601354

"""
from alembic import op
import sqlalchemy as sa

import app.models

# revision identifiers, used by Alembic.
revision = '80a5c4585ee3'
down_revision = '7397bff670a8'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('p2p_activity_banner', schema=None) as batch_op:
        batch_op.add_column(sa.Column('content', sa.String(length=256), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('p2p_activity_banner', schema=None) as batch_op:
        batch_op.drop_column('content')

    # ### end Alembic commands ###
