"""empty message

Revision ID: aff4aae18e3f
Revises: 8e277531835a
Create Date: 2025-09-17 09:37:33.323429

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql
import app.models

# revision identifiers, used by Alembic.
revision = 'aff4aae18e3f'
down_revision = '8e277531835a'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('ambassador_statistics', schema=None) as batch_op:
        batch_op.add_column(sa.Column('refer_user_balance_usd', mysql.DECIMAL(precision=26, scale=8), nullable=False))
        batch_op.add_column(sa.Column('cur_refer_user_balance_usd', mysql.DECIMAL(precision=26, scale=8), nullable=False))
        batch_op.add_column(sa.Column('refer_active_user_count', sa.Integer(), nullable=False))
        batch_op.add_column(sa.Column('cur_refer_active_user_count', sa.Integer(), nullable=False))

    with op.batch_alter_table('business_ambassador_statistics', schema=None) as batch_op:
        batch_op.add_column(sa.Column('amb_refer_user_balance_usd', mysql.DECIMAL(precision=26, scale=8), nullable=False))
        batch_op.add_column(sa.Column('new_refer_user_balance_usd', mysql.DECIMAL(precision=26, scale=8), nullable=False))
        batch_op.add_column(sa.Column('amb_refer_active_user_count', sa.Integer(), nullable=False))
        batch_op.add_column(sa.Column('new_refer_active_user_count', sa.Integer(), nullable=False))

    with op.batch_alter_table('business_user_statistics', schema=None) as batch_op:
        batch_op.add_column(sa.Column('refer_user_balance_usd', mysql.DECIMAL(precision=26, scale=8), nullable=False))
        batch_op.add_column(sa.Column('refer_active_user_count', sa.Integer(), nullable=False))

    with op.batch_alter_table('daily_ambassador_referral_report', schema=None) as batch_op:
        batch_op.add_column(sa.Column('refer_user_balance_usd', mysql.DECIMAL(precision=26, scale=8), nullable=False))
        batch_op.add_column(sa.Column('refer_active_user_count', sa.Integer(), nullable=False))

    with op.batch_alter_table('daily_business_ambassador_total_referral_report', schema=None) as batch_op:
        batch_op.add_column(sa.Column('refer_user_balance_usd', mysql.DECIMAL(precision=26, scale=8), nullable=False))
        batch_op.add_column(sa.Column('refer_active_user_count', sa.Integer(), nullable=False))

    with op.batch_alter_table('daily_refer_type_report', schema=None) as batch_op:
        batch_op.add_column(sa.Column('refer_user_balance_usd', mysql.DECIMAL(precision=26, scale=8), nullable=False))
        batch_op.add_column(sa.Column('refer_active_user_count', sa.Integer(), nullable=False))

    with op.batch_alter_table('monthly_ambassador_referral_report', schema=None) as batch_op:
        batch_op.add_column(sa.Column('refer_user_balance_usd', mysql.DECIMAL(precision=26, scale=8), nullable=False))
        batch_op.add_column(sa.Column('refer_active_user_count', sa.Integer(), nullable=False))

    with op.batch_alter_table('monthly_business_ambassador_total_referral_report', schema=None) as batch_op:
        batch_op.add_column(sa.Column('refer_user_balance_usd', mysql.DECIMAL(precision=26, scale=8), nullable=False))
        batch_op.add_column(sa.Column('refer_active_user_count', sa.Integer(), nullable=False))

    with op.batch_alter_table('monthly_refer_type_report', schema=None) as batch_op:
        batch_op.add_column(sa.Column('refer_user_balance_usd', mysql.DECIMAL(precision=26, scale=8), nullable=False))
        batch_op.add_column(sa.Column('refer_active_user_count', sa.Integer(), nullable=False))

    with op.batch_alter_table('quarterly_business_ambassador_total_referral_report', schema=None) as batch_op:
        batch_op.add_column(sa.Column('refer_user_balance_usd', mysql.DECIMAL(precision=26, scale=8), nullable=False))
        batch_op.add_column(sa.Column('refer_active_user_count', sa.Integer(), nullable=False))

    with op.batch_alter_table('tree_ambassador_statistics', schema=None) as batch_op:
        batch_op.add_column(sa.Column('effect_refer_user_balance_usd', mysql.DECIMAL(precision=26, scale=8), nullable=False))
        batch_op.add_column(sa.Column('effect_refer_active_user_count', sa.Integer(), nullable=False))

    with op.batch_alter_table('weekly_business_ambassador_total_referral_report', schema=None) as batch_op:
        batch_op.add_column(sa.Column('refer_user_balance_usd', mysql.DECIMAL(precision=26, scale=8), nullable=False))
        batch_op.add_column(sa.Column('refer_active_user_count', sa.Integer(), nullable=False))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('weekly_business_ambassador_total_referral_report', schema=None) as batch_op:
        batch_op.drop_column('refer_active_user_count')
        batch_op.drop_column('refer_user_balance_usd')

    with op.batch_alter_table('tree_ambassador_statistics', schema=None) as batch_op:
        batch_op.drop_column('effect_refer_active_user_count')
        batch_op.drop_column('effect_refer_user_balance_usd')

    with op.batch_alter_table('quarterly_business_ambassador_total_referral_report', schema=None) as batch_op:
        batch_op.drop_column('refer_active_user_count')
        batch_op.drop_column('refer_user_balance_usd')

    with op.batch_alter_table('monthly_refer_type_report', schema=None) as batch_op:
        batch_op.drop_column('refer_active_user_count')
        batch_op.drop_column('refer_user_balance_usd')

    with op.batch_alter_table('monthly_business_ambassador_total_referral_report', schema=None) as batch_op:
        batch_op.drop_column('refer_active_user_count')
        batch_op.drop_column('refer_user_balance_usd')

    with op.batch_alter_table('monthly_ambassador_referral_report', schema=None) as batch_op:
        batch_op.drop_column('refer_active_user_count')
        batch_op.drop_column('refer_user_balance_usd')

    with op.batch_alter_table('daily_refer_type_report', schema=None) as batch_op:
        batch_op.drop_column('refer_active_user_count')
        batch_op.drop_column('refer_user_balance_usd')

    with op.batch_alter_table('daily_business_ambassador_total_referral_report', schema=None) as batch_op:
        batch_op.drop_column('refer_active_user_count')
        batch_op.drop_column('refer_user_balance_usd')

    with op.batch_alter_table('daily_ambassador_referral_report', schema=None) as batch_op:
        batch_op.drop_column('refer_active_user_count')
        batch_op.drop_column('refer_user_balance_usd')

    with op.batch_alter_table('business_user_statistics', schema=None) as batch_op:
        batch_op.drop_column('refer_active_user_count')
        batch_op.drop_column('refer_user_balance_usd')

    with op.batch_alter_table('business_ambassador_statistics', schema=None) as batch_op:
        batch_op.drop_column('new_refer_active_user_count')
        batch_op.drop_column('amb_refer_active_user_count')
        batch_op.drop_column('new_refer_user_balance_usd')
        batch_op.drop_column('amb_refer_user_balance_usd')

    with op.batch_alter_table('ambassador_statistics', schema=None) as batch_op:
        batch_op.drop_column('cur_refer_active_user_count')
        batch_op.drop_column('refer_active_user_count')
        batch_op.drop_column('cur_refer_user_balance_usd')
        batch_op.drop_column('refer_user_balance_usd')

    # ### end Alembic commands ###
