"""empty message

Revision ID: 43b7aa03edca
Revises: c8fae67c5cac
Create Date: 2025-09-22 13:19:07.322610

"""
from alembic import op
import sqlalchemy as sa

import app.models

# revision identifiers, used by Alembic.
revision = '43b7aa03edca'
down_revision = 'c8fae67c5cac'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('user_prevent_risk_control_configs', schema=None) as batch_op:
        batch_op.add_column(sa.Column('latest_risk_user_record_id', sa.Integer(), nullable=False))
        batch_op.add_column(sa.Column('remark', sa.String(length=512), nullable=True))
        batch_op.create_index(batch_op.f('ix_user_prevent_risk_control_configs_latest_risk_user_record_id'), ['latest_risk_user_record_id'], unique=False)

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('user_prevent_risk_control_configs', schema=None) as batch_op:
        batch_op.drop_index(batch_op.f('ix_user_prevent_risk_control_configs_latest_risk_user_record_id'))
        batch_op.drop_column('remark')
        batch_op.drop_column('latest_risk_user_record_id')

    # ### end Alembic commands ###
